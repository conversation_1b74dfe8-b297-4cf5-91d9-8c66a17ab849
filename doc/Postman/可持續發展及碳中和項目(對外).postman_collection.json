{"info": {"_postman_id": "73742778-2980-4910-8f3e-4468758aa12e", "name": "可持續發展及碳中和項目(對外)", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "16450286"}, "item": [{"name": "物聯網讀表", "item": [{"name": "儲存", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"accessid\": \"ba79e5e7-83d0-4def-8a8b-7d08dfcd95b8\",\r\n    \"vn1\": \"33\",\r\n    \"vn2\": \"34\",\r\n    \"recorddate\": \"2023-01-01\",\r\n    \"meterno\": \"DF20220101\",\r\n    \"meterreading\": \"226523.00\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}api/tzh/external/meter-reading/save", "host": ["{{url}}api"], "path": ["tzh", "external", "meter-reading", "save"]}}, "response": [{"name": "成功", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"accessid\": \"ba79e5e7-83d0-4def-8a8b-7d08dfcd95b8\",\r\n    \"vn1\": \"33\",\r\n    \"vn2\": \"34\",\r\n    \"recorddate\": \"2023-01-01\",\r\n    \"meterno\": \"DF20220101\",\r\n    \"meterreading\": \"226523.00\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}api/tzh/external/meter-reading/save", "host": ["{{url}}api"], "path": ["tzh", "external", "meter-reading", "save"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Thu, 12 Jan 2023 08:53:25 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"code\": 0,\n    \"msg\": \"操作成功\",\n    \"data\": {\n        \"id\": \"144d8d7b-def0-4968-af94-c85dff3f733f\",\n        \"region\": \"中國內地\",\n        \"materialname\": \"電力使用\",\n        \"sitename\": \"中海总部基地\",\n        \"carbonemissionlocation\": \"辦工室\",\n        \"recorddate\": \"2023-01-01\",\n        \"meterno\": \"DF20220101\",\n        \"meterreading\": 226523.00,\n        \"createdby\": \"ba79e5e7-83d0-4def-8a8b-7d08dfcd95b8\",\n        \"createdtime\": \"2023-02-21 09:09:30\"\n    }\n}"}, {"name": "失敗", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"accessid\": \"ba79e5e7-83d0-4de1-8a8b-7d08dfcd95b8\",\r\n    \"vn1\": \"33\",\r\n    \"vn2\": \"34\",\r\n    \"recorddate\": \"2023-01-01\",\r\n    \"meterno\": \"DF20220101\",\r\n    \"meterreading\": \"226523.00\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}api/tzh/external/meter-reading/save", "host": ["{{url}}api"], "path": ["tzh", "external", "meter-reading", "save"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.23.1"}, {"key": "Date", "value": "Mon, 20 Feb 2023 09:17:53 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}], "cookie": [], "body": "{\n    \"code\": -1,\n    \"msg\": \"沒有訪問權限。\",\n    \"data\": null\n}"}]}]}, {"name": "澳門電費", "item": [{"name": "電費單查找", "request": {"method": "GET", "header": []}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "url", "value": "https://api.csci.com.hk/esg-185-test", "type": "string"}]}