-- 在新一年復用舊有審批流程
DELETE FROM t_workflow_temp
GO

INSERT INTO t_workflow_temp
SELECT newid()
	  ,[id]
      ,[organization_id]
      ,[form_id]
      ,2024
      ,10
      ,[name]
      ,[code]
      ,[is_active]
      ,[representative_id]
      ,[creation_time]
      ,[create_username]
      ,[create_user_id]
      ,[last_update_time]
      ,[last_update_username]
      ,[last_update_user_id]
      ,[last_update_version]
FROM t_workflow wf
WHERE wf.year = 2024 AND wf.month = 9
AND wf.organization_id IN (SELECT id FROM t_organization _O WHERE _O.is_deleted = 0 AND _O.no LIKE N'001%')

GO

DELETE FROM t_workflow_node_temp

GO

INSERT INTO t_workflow_node_temp 
SELECT newid()
	  ,[id]
      ,[workflow_id]
      ,[name]
      ,[previous_node_id]
      ,[is_begin_node]
      ,[creation_time]
      ,[create_username]
      ,[create_user_id]
      ,[last_update_time]
      ,[last_update_username]
      ,[last_update_user_id]
      ,[last_update_version]
	  FROM t_workflow_node wn
WHERE wn.workflow_id IN (
SELECT wf.id
FROM t_workflow wf
WHERE wf.year = 2024 AND wf.month = 9
AND wf.organization_id IN (SELECT id FROM t_organization _O WHERE _O.is_deleted = 0 AND _O.no  LIKE N'001%')
)

GO

UPDATE wfn 
SET wfn.workflow_id = (SELECT wft.id FROM t_workflow_temp wft WHERE wft.orig_id = wfn.workflow_id) 
FROM t_workflow_node_temp AS wfn 

GO

UPDATE wfn 
SET wfn.previous_node_id = (SELECT wfnt.id FROM t_workflow_node_temp wfnt WHERE wfnt.orig_id = wfn.previous_node_id) 
FROM t_workflow_node_temp AS wfn 
WHERE wfn.previous_node_id IS NOT NULL

GO

DELETE FROM t_workflow_node_user_temp

GO

INSERT INTO t_workflow_node_user_temp
SELECT newid()
      ,(SELECT wfnt.id FROM t_workflow_node_temp wfnt WHERE wfnu.node_id = wfnt.orig_id)
      ,[user_id]
      ,[creation_time]
      ,[create_username]
      ,[create_user_id]
      ,[last_update_time]
      ,[last_update_username]
      ,[last_update_user_id]
      ,[last_update_version]
	  FROM t_workflow_node_user wfnu
WHERE node_id IN (SELECT orig_id FROM t_workflow_node_temp)

GO

INSERT INTO t_workflow
SELECT [id]
      ,[organization_id]
      ,[form_id]
      ,[year]
      ,[month]
      ,[name]
      ,[code]
      ,[is_active]
      ,[representative_id]
      ,getdate()
      ,NULL
      ,NULL
      ,getdate()
      ,NULL
      ,NULL
      ,[last_update_version]
	  FROM t_workflow_temp

GO

INSERT INTO t_workflow_node
SELECT [id]
      ,[workflow_id]
      ,[name]
      ,[previous_node_id]
      ,[is_begin_node]
      ,getdate()
      ,NULL
      ,NULL
      ,getdate()
      ,NULL
      ,NULL
      ,[last_update_version]
	  FROM t_workflow_node_temp

GO


INSERT INTO t_workflow_node_user
SELECT [id]
      ,[node_id]
      ,[user_id]
      ,getdate()
      ,NULL
      ,NULL
      ,getdate()
      ,NULL
      ,NULL
      ,[last_update_version]
	  FROM t_workflow_node_user_temp



