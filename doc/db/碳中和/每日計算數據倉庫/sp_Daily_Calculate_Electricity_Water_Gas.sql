USE [ESG_NEW]
GO
/****** Object:  StoredProcedure [dbo].[sp_Daily_Calculate_Electricity_Water_Gas]    Script Date: 9/1/2023 2:39:22 pm ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

ALTER PROCEDURE [dbo].[sp_Daily_Calculate_Electricity_Water_Gas]
AS
BEGIN

SET NOCOUNT ON
--F_Electricity
DELETE FROM F_Electricity WHERE CalculateDate = CONVERT(date,GETDATE(),120)

INSERT INTO F_Electricity  (
Region,SiteId,SiteName,CarbonEmissionLocation,RecordYearMonth,AccountNo,
CarbonFactorRecordYearMonth,ElectricConsumption,ElectricUnit,CarbonFactor,CarbonFactorUnit,Scope,
CarbonAmount,CalculateDate,Status)
SELECT E.Region,so.id,E.SiteName,E.CarbonEmissionLocation,E.CarbonFactorRecordYearMonth,E.<PERSON>,
E.CarbonFactorRecordYear<PERSON>,E<PERSON>ons<PERSON>, <PERSON><PERSON>,<PERSON><PERSON>CarbonFactor,E.CarbonF<PERSON>,<PERSON><PERSON>,
E.ElectricConsumption * E.CarbonFactor  AS CarbonAmount,CONVERT(date,GETDATE(),120) AS CalculateDate,'NORMAL'
From D_CDMS_ElectricityCarbonFactor E 
inner join  tzh_projectInfo  pr on pr.name = E.SiteName left join sysOrganization SO on so.code = pr.code WHERE E.IsDeleted = 0 and pr.IsDeleted = 0 

--F_Water 
DELETE FROM F_Water WHERE CalculateDate = CONVERT(date,GETDATE(),120)

INSERT INTO F_Water(
Region,SiteId,SiteName,CarbonEmissionLocation,RecordYearMonth,AccountNo,
WaterConsumption,WaterConsumption_Unit,CarbonFactor,CarbonFactorUnit,CarbonAmount,
Scope,CalculateDate,Status)
SELECT pr.Region,so.id,E.SiteName,E.CarbonEmissionLocation,E.RecordYearMonth,E.AccountNo,
E.WaterConsumption,E.WaterConsumption_Unit,E.CarbonFactor,E.CarbonFactorUnit,E.WaterConsumption * E.CarbonFactor AS CarbonAmount,
Scope,CONVERT(date,GETDATE(),120) AS CalculateDate,'NORMAL'
FROM F_CDMS_WaterCarbonFactor E 
inner join  tzh_projectInfo  pr on pr.name = E.SiteName left join sysOrganization SO on so.code = pr.code WHERE E.IsDeleted = 0 and pr.IsDeleted = 0 



--F_Gas --Manual
DELETE FROM F_Gas WHERE CalculateDate = CONVERT(date,GETDATE(),120)

INSERT INTO F_Gas (
Region,SiteId,SiteName,CarbonEmissionLocation,RecordYearMonth,
GasAmount,GasQuantity,GasQuantity_Unit,CarbonFactor,CarbonFactorUnit,CarbonAmount,
Scope,CalculateDate,Status)
SELECT pr.Region,so.id,E.SiteName,E.CarbonEmissionLocation,E.RecordYearMonth,
E.GasAmount,E.GasQuantity,E.GasQuantity_Unit,E.CarbonFactor,E.CarbonFactorUnit,E.GasAmount * E.GasQuantity* E.CarbonFactor AS CarbonAmount,
E.Scope,CONVERT(date,GETDATE(),120) AS CalculateDate,'NORMAL'
FROM F_CDMS_GasCarbonFactor  E
inner join  tzh_projectInfo  pr on pr.name = E.SiteName inner join sysOrganization SO on so.code = pr.code WHERE E.IsDeleted = 0 and pr.IsDeleted = 0  and so.IsDeleted = 0 


--F_CombineAll
DELETE FROM F_CombineAll WHERE CalculateDate= CONVERT(date,GETDATE(),120) AND MaterialName IN (N'地盤天然氣' ,N'水費',N'電費')  

INSERT INTO F_CombineAll (Region,SiteId,SiteName,CarbonEmissionLocation,RecordYearMonth,MaterialCode,MaterialName,Qty,QtyUnit,Scope,CarbonFactor,CarbonFactorUnit,
TransportFactor,CarbonAmount,CarbonAmount_Unit,CalculateDate,Status)
SELECT Region,SiteId,SiteName,CarbonEmissionLocation,RecordYearMonth,'' as MaterialCode,
N'電費' AS MaterialName, sum(ElectricConsumption) as Qty,ElectricUnit as QtyUnit,Scope,CarbonFactor, CarbonFactorUnit,
0 as TransportFactor,sum(CarbonAmount) as CarbonAmount, 'kgco2e' AS CarbonAmount_Unit,CalculateDate,Status
FROM [dbo].[F_Electricity] WHERE CalculateDate = CONVERT(date,GETDATE(),120) 
group by Region,SiteId,SiteName,CarbonEmissionLocation,RecordYearMonth,ElectricUnit,Scope,CarbonFactor, CarbonFactorUnit,CalculateDate,Status
UNION
SELECT Region,SiteId,SiteName,CarbonEmissionLocation,RecordYearMonth,'' as MaterialCode,
N'地盤天然氣' AS MaterialName,sum(GasQuantity* GasAmount) as Qty,GasQuantity_Unit AS QtyUnit,Scope,CarbonFactor,  CarbonFactorUnit, 
0 as TransportFactor,sum(CarbonAmount) as CarbonAmount,'kgco2e' AS CarbonAmount_Unit,CalculateDate,Status
FROM [dbo].F_Gas WHERE CalculateDate = CONVERT(date,GETDATE(),120) 
group by Region,SiteId,SiteName,CarbonEmissionLocation,RecordYearMonth,GasQuantity_Unit, Scope, CarbonFactor, CarbonFactorUnit,CalculateDate,Status 
UNION
SELECT Region,SiteId,SiteName,CarbonEmissionLocation,RecordYearMonth,'' as MaterialCode,
N'水費' AS MaterialName,  sum(WaterConsumption) as Qty,WaterConsumption_Unit AS QtyUnit,Scope,CarbonFactor, CarbonFactorUnit,
0 as TransportFactor,sum(CarbonAmount) as CarbonAmount,'kgco2e' AS CarbonAmount_Unit,CalculateDate,Status
FROM [dbo].F_Water WHERE CalculateDate = CONVERT(date,GETDATE(),120) 
group by Region,SiteId,SiteName,CarbonEmissionLocation,RecordYearMonth,WaterConsumption_Unit, Scope, CarbonFactor, CarbonFactorUnit,CalculateDate,Status

END

