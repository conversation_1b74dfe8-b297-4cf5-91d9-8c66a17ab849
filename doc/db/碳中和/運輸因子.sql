SELECT *
  FROM [ESG].[dbo].[F_Material]

SELECT *
FROM [ESG].[dbo].[F_Material_Detail]


SELECT N'香港' AS Region, NULL AS SiteId, t.SiteName, t.<PERSON>, f.CarbonEmissionLocation, t.MaterialCode, t.<PERSON>ame, t.Unit, NULL AS Desciption, 
f.CarbonFactor AS CarbonFactor, f.CarbonFactorUnit AS CarbonFactorUnit, t.TransportFactor AS TransportFactor, t.TransportFactorUnit AS TransportFactorUnit, 
t.TransportDistance, t.TransportDistanceUnit, t.Qty, NULL AS CDMS_Unit, (t.Qty * f.CarbonFactor) AS CarbonAmount, 
(t.TransportFactor * t.TransportDistance) AS TransportCarbonAmount, f.<PERSON>, N'建材、物資及機械設備' AS TransportScope, 
CAST(GETDATE() AS DATE) AS CalculateDate, t.BillNo, NULL AS BizDate, NULL AS DeliveryNoteNo, NULL AS DeliveryDate, t.<PERSON>o AS FubandanNo
FROM Tzh_MonthlyMaterialCarbon t 
LEFT JOIN D_CDMS_MaterialCarbonFactor f 
ON f.MaterialCode = t.MaterialCode 
AND (f.MaterialAttribute = t.MaterialAttribute OR (f.MaterialAttribute IS NULL AND f.MaterialAttribute IS NULL)) AND f.IsDeleted = 0
WHERE t.IsDeleted = 0 


SELECT
	N'香港' AS Region,
	NULL AS SiteId,
	t.SiteName,
	t.RecordYearMonth,
	f.CarbonEmissionLocation,
	t.MaterialCode,
	t.ChineseName,
	t.Qty,
	t.Unit,
	f.Description AS Desciption,
	f.CarbonFactor AS CarbonFactor,
	f.CarbonFactorUnit AS CarbonFactorUnit,
	t.TransportFactor AS TransportFactor,
	t.TransportFactorUnit AS TransportFactorUnit,
	t.TransportDistance,
	t.TransportDistanceUnit,
	NULL AS CDMS_Unit,--?
	( t.Qty * f.CarbonFactor ) AS CarbonAmount,
	( t.Qty * t.TransportFactor * t.TransportDistance ) AS TransportCarbonAmount,
	f.Scope AS Scope,
	N'建材、物資及機械設備' AS TransportScope,
	CAST ( GETDATE( ) AS DATE ) AS CalculateDate,
	t.BillNo,
	NULL AS BizDate,
	NULL AS DeliveryNoteNo,
	NULL AS DeliveryDate,
	t.BillNo AS FubandanNo 
FROM
	Tzh_MonthlyMaterialCarbon t
	LEFT JOIN D_CDMS_MaterialCarbonFactor f ON f.MaterialCode = t.MaterialCode AND f.SiteName = t.SiteName 
	AND (f.MaterialAttribute = t.MaterialAttribute OR (f.MaterialAttribute IS NULL AND f.MaterialAttribute IS NULL)) AND f.IsDeleted = 0
	AND	t.IsDeleted = 0

SELECT 
	Region,
	SiteId,
	SiteName,
	RecordYearMonth,
	CarbonEmissionLocation,
	MaterialCode,
	ChineseName,
	Desciption,
	CarbonFactor,
	CarbonFactorUnit,
	Scope,
	Unit,
	TransportScope,
	CalculateDate,
	SUM(qty) AS qtyTotal,
	SUM(CarbonAmount) AS CarbonAmountTotal,
	SUM(TransportCarbonAmount) AS TransportCarbonAmountTotal
FROM  (
SELECT
	N'香港' AS Region,
	NULL AS SiteId,
	t.SiteName,
	t.RecordYearMonth,
	f.CarbonEmissionLocation,
	t.MaterialCode,
	t.ChineseName,
	t.Qty,
	t.Unit,
	f.Description AS Desciption,
	f.CarbonFactor AS CarbonFactor,
	f.CarbonFactorUnit AS CarbonFactorUnit,
	t.TransportFactor AS TransportFactor,
	t.TransportFactorUnit AS TransportFactorUnit,
	t.TransportDistance,
	t.TransportDistanceUnit,
	NULL AS CDMS_Unit,--?
	( t.Qty * f.CarbonFactor ) AS CarbonAmount, 
	( t.Qty * t.TransportFactor * t.TransportDistance ) AS TransportCarbonAmount,
	f.Scope AS Scope,
	N'建材、物資及機械設備' AS TransportScope,
	CAST ( GETDATE( ) AS DATE ) AS CalculateDate,
	t.BillNo,
	NULL AS BizDate,
	NULL AS DeliveryNoteNo,
	NULL AS DeliveryDate,
	t.BillNo AS FubandanNo 
FROM
	Tzh_MonthlyMaterialCarbon t
	LEFT JOIN D_CDMS_MaterialCarbonFactor f ON f.MaterialCode = t.MaterialCode AND f.SiteName = t.SiteName 
    AND (f.MaterialAttribute = t.MaterialAttribute OR (f.MaterialAttribute IS NULL AND f.MaterialAttribute IS NULL)) 
	AND f.IsDeleted = 0 
	AND	t.IsDeleted = 0
	) t 
	group by Region,
	SiteId,
	SiteName,
	RecordYearMonth,
	CarbonEmissionLocation,
	MaterialCode,
	ChineseName,
	Desciption,
	CarbonFactor,
	CarbonFactorUnit,
	Scope,
	Unit,
	TransportScope,
	CalculateDate