-- 环境绩效head表 （存在多条的情况）(需要一条一条去对比删除)
SELECT organization_id, year, month, COUNT(*) AS Count
FROM t_ambient_head a
where is_active = 1
GROUP BY organization_id, year, month
HAVING COUNT(*) > 1;
-- 删除对应多条的
select * from t_ambient_head where
        is_active = 1
                               and year = 2022
                               and [month] = 12
                               and organization_id = '';
-- 删除对应多条的
delete from t_ambient_head where id in ('');
-- 把is_active = 0 的数据全删掉
delete from t_ambient_head where is_active = 0;
-- 创建唯一约束
ALTER TABLE t_ambient_head
    ADD CONSTRAINT UQ_ambient UNIQUE (organization_id, year, month, is_active);


--t_ce_basic_info_head （存在多条的情况）(需要一条一条去对比删除)
SELECT organization_id, year, month, COUNT(*) AS Count
FROM t_ce_basic_info_head a
where is_active = 1
GROUP BY organization_id, year, month
HAVING COUNT(*) > 1;
-- 删除对应多条的
select * from t_ce_basic_info_head where
        is_active = 1
                                     and year = 2023
                                     and [month] = 12
                                     and organization_id = '';
-- 删除对应多条的
delete from t_ce_basic_info_head where id in ('');
-- 把is_active = 0 的数据全删掉
delete from t_ce_basic_info_head where is_active = 0;
-- 创建唯一约束
ALTER TABLE t_ce_basic_info_head
    ADD CONSTRAINT UQ_ce_basic_info UNIQUE (organization_id, year, month, is_active);


--t_ce_identification_head （存在多条的情况）(需要一条一条去对比删除)
SELECT organization_id, year, month, COUNT(*) AS Count
FROM t_ce_identification_head a
where is_active = 1
GROUP BY organization_id, year, month
HAVING COUNT(*) > 1;
-- 删除对应多条的
select * from t_ce_identification_head where
        is_active = 1
                                         and year = 2023
                                         and [month] = 12
                                         and organization_id = '';
-- 删除对应多条的
delete from t_ce_identification_head where id in ('');
-- 把is_active = 0 的数据全删掉
delete from t_ce_identification_head where is_active = 0;
-- 创建唯一约束
ALTER TABLE t_ce_identification_head
    ADD CONSTRAINT UQ_ce_identification UNIQUE (organization_id, year, month, is_active);


--t_emission_reduction_head （存在多条的情况）(需要一条一条去对比删除)
SELECT organization_id, year, month, COUNT(*) AS Count
FROM t_emission_reduction_head a
where is_active = 1
GROUP BY organization_id, year, month
HAVING COUNT(*) > 1;
-- 删除对应多条的
select * from t_emission_reduction_head where
        is_active = 1
                                          and year = 2023
                                          and [month] = 12
                                          and organization_id = '';
-- 删除对应多条的
delete from t_emission_reduction_head where id in ('');
-- 把is_active = 0 的数据全删掉
delete from t_emission_reduction_head where is_active = 0;
-- 创建唯一约束
ALTER TABLE t_emission_reduction_head
    ADD CONSTRAINT UQ_emission_reduction UNIQUE (organization_id, year, month, is_active);


--t_emp_commuting_head （存在多条的情况）(需要一条一条去对比删除)
SELECT organization_id, year, month, COUNT(*) AS Count
FROM t_emp_commuting_head a
where is_active = 1
GROUP BY organization_id, year, month
HAVING COUNT(*) > 1;
-- 删除对应多条的
select * from t_emp_commuting_head where
        is_active = 1
                                     and year = 2023
                                     and [month] = 12
                                     and organization_id = '';
-- 删除对应多条的
delete from t_emp_commuting_head where id in ('');
-- 把is_active = 0 的数据全删掉
delete from t_emp_commuting_head where is_active = 0;
-- 创建唯一约束
ALTER TABLE t_emp_commuting_head
    ADD CONSTRAINT UQ_emp_commuting UNIQUE (organization_id, year, month, is_active);



--t_ff_cm_fixed_head （存在多条的情况）(需要一条一条去对比删除)
SELECT organization_id, year, month, COUNT(*) AS Count
FROM t_ff_cm_fixed_head a
where is_active = 1
GROUP BY organization_id, year, month
HAVING COUNT(*) > 1;
-- 删除对应多条的
select * from t_ff_cm_fixed_head where
        is_active = 1
                                   and year = 2023
                                   and [month] = 12
                                   and organization_id = 'db7654b5-0a4d-4d9c-acca-fae2263c1e9c';
-- 删除对应多条的
delete from t_ff_cm_fixed_head where id in ('');
-- 把is_active = 0 的数据全删掉
delete from t_ff_cm_fixed_head where is_active = 0;
-- 创建唯一约束
ALTER TABLE t_ff_cm_fixed_head
    ADD CONSTRAINT UQ_ff_cm_fixed UNIQUE (organization_id, year, month, is_active);




--t_ff_cm_mobile_head （存在多条的情况）(需要一条一条去对比删除)
SELECT organization_id, year, month, COUNT(*) AS Count
FROM t_ff_cm_mobile_head a
where is_active = 1
GROUP BY organization_id, year, month
HAVING COUNT(*) > 1;
-- 删除对应多条的
select * from t_ff_cm_mobile_head where
        is_active = 1
                                    and year = 2023
                                    and [month] = 12
                                    and organization_id = '';
-- 删除对应多条的
delete from t_ff_cm_mobile_head where id in ('');
-- 把is_active = 0 的数据全删掉
delete from t_ff_cm_mobile_head where is_active = 0;
-- 创建唯一约束
ALTER TABLE t_ff_cm_mobile_head
    ADD CONSTRAINT UQ_ff_cm_mobile UNIQUE (organization_id, year, month, is_active);



--t_material_trans_head （存在多条的情况）(需要一条一条去对比删除)
SELECT organization_id, year, month, COUNT(*) AS Count
FROM t_material_trans_head a
where is_active = 1
GROUP BY organization_id, year, month
HAVING COUNT(*) > 1;
-- 删除对应多条的
select * from t_material_trans_head where
        is_active = 1
                                      and year = 2023
                                      and [month] = 12
                                      and organization_id = '';
-- 删除对应多条的
delete from t_material_trans_head where id in ('');
-- 把is_active = 0 的数据全删掉
delete from t_material_trans_head where is_active = 0;
-- 创建唯一约束
ALTER TABLE t_material_trans_head
    ADD CONSTRAINT UQ_material_trans UNIQUE (organization_id, year, month, is_active);




--t_social_performance_head （存在多条的情况）(需要一条一条去对比删除)
SELECT organization_id, year, month, COUNT(*) AS Count
FROM t_social_performance_head a
where is_active = 1
GROUP BY organization_id, year, month
HAVING COUNT(*) > 1;
-- 删除对应多条的
select * from t_social_performance_head where
        is_active = 1
                                          and year = 2023
                                          and [month] = 12
                                          and organization_id = '';
-- 删除对应多条的
delete from t_social_performance_head where id in ('');
-- 把is_active = 0 的数据全删掉
delete from t_social_performance_head where is_active = 0;
-- 创建唯一约束
ALTER TABLE t_social_performance_head
    ADD CONSTRAINT UQ_social_performance UNIQUE (organization_id, year, month, is_active);



-- delete from t_social_performance_head
-- where is_active = 1
-- and organization_id in (
-- '00787fc2-0177-4eb4-b52b-fd39527d8b5a',
-- '01bd5987-3a36-4566-8362-7e8d1c09f7ef',
-- '01e3994b-1078-44cf-9464-47bf681f0ef7',
-- '02508acb-cade-484f-bee0-1dc670484d38',
-- '065153d1-3b45-4368-bc57-2e198b37988a',
-- '0687941f-f29f-46ea-83c3-0c9a94db39f4',
-- '0704dbca-35d2-4e93-84b5-8ff00df45159',
-- '07093c82-6468-4636-b3a5-60e5af7ddf66',
-- '08c6fa6d-dfab-4a25-af5d-acbb28ad56f7',
-- '0a28cc1d-5971-48fb-aa0a-2bfbc7a3dc8d',
-- '0b6ae8a1-0b8c-4d6b-9334-24a6041dc65e',
-- '0b79b062-543c-4491-a38a-982385d2f025',
-- '0b8c080d-2561-4c6c-91fd-dd7d6d5ef484',
-- '0e2126b8-f0ad-40dc-8b66-c25b738adc61',
-- '17087670-7555-48d2-a743-59a3ad413f04',
-- '17087670-7555-48d2-a743-59a3ad413f50',
-- '1a8428fa-f392-4ce5-9265-feed8b07200e',
-- '1abdbb91-b346-4372-85fc-308b6ad4ecf6',
-- '1e4765a6-fd84-4686-8462-11626ba6b918',
-- '21319d0a-dfb9-4e22-94cd-0e95d7a226cf',
-- '215f9907-2853-4714-942d-8322747b4408',
-- '23601b05-c317-487b-9567-3921dab1bb8d',
-- '244607f9-97e1-4717-8261-1430bf70c52d',
-- '2c16fec0-b201-4ddd-a7bb-92d67b2310b9',
-- '2ccc797e-7207-443b-a163-d925cdf60fe8',
-- '2ef171fd-51a9-480d-8070-55ae2180a6cd',
-- '345e4022-3d8b-40e6-87b7-85d6fea88881',
-- '3b7559b5-3ddf-4309-8c20-4aa2b40968d4',
-- '408eaf0f-9d5b-406d-a7c9-1f21daf32ae1',
-- '42db1669-39a4-4be2-8878-b5eeb49be5d1',
-- '478654d1-8ffd-4323-a195-e4855f5a5b26',
-- '4855c4de-36e5-47ba-b717-eb5324892333',
-- '4b0384d3-0e26-4885-b39a-895c89fbe65d',
-- '50f0df91-0ba3-4be2-8236-55c59e5a071e',
-- '55d76038-2458-414f-9280-f78b5e14e148',
-- '59799e00-c068-4285-a292-dfc72acaaed9',
-- '59e00a0f-4611-4e38-a003-cb7a379f9867',
-- '5bb60df3-cfb9-4a56-ba02-0f39b595e7ad',
-- '605b9904-8a16-4674-b5b3-61c2e9aa6480',
-- '62378a91-1583-42d4-9446-4aed1ba2781e',
-- '66d1aa43-e56e-4775-8893-0929767cbb87',
-- '682f9eb7-4531-4fd3-858d-c3efa11e23a5',
-- '6838823f-5fca-4c1f-a735-a603ec595df4',
-- '6b34e6fd-0c6f-4390-ab8e-1293fd2fd15d',
-- '6b5e5237-8edd-47fd-8156-94d3b799219c',
-- '6c8adce4-2911-41d7-9761-faf016d71904',
-- '73fe008a-0163-4ef8-a492-0fa3bc052f8f',
-- '74df22aa-01c8-4fba-8fc9-f12e9c403c34',
-- '80014c8f-1fbe-4954-b1c1-335fa17e015c',
-- '8062784f-a6c6-49ae-8751-2f5e9b36f7ff',
-- '81b070ca-d7c3-42af-aba0-0599a997af32',
-- '8bb7700d-73e4-4588-978a-17b1a4a33609',
-- '9bed34c0-b42f-46eb-b70f-3cd5bba553ae',
-- '9f1368e4-3dea-4679-b166-d899f01aea73',
-- '9f69bb44-19c8-45d7-a795-620707dfe5c1',
-- 'a04c0a2a-2751-4e2a-aa4c-f8b7b7207ea7',
-- 'a30b0811-a7b2-4d72-a440-2e6d81279683',
-- 'a605c446-a3b5-4b0b-8d5e-1721b5877f9e',
-- 'a75b01b9-fbe0-48b9-beff-c3c147904633',
-- 'a963ac05-02c1-42ee-bd54-5af08690a6bd',
-- 'b6ef88c0-de6e-4dab-a950-90d3fbdb0cc6',
-- 'b84b80bc-b2ef-4eeb-9ed3-da405b85ca8c',
-- 'b8756a14-3a8b-4fc4-98b0-9acaf9d38a5b',
-- 'b8fae397-13ac-442e-bb85-dd6e49372e0e',
-- 'ba916731-cab5-40a2-b883-9d2b00698713',
-- 'be24f315-44c7-47eb-aa6d-0b227479b75a',
-- 'bf4abf64-fe7e-4f55-afd0-be17f313df56',
-- 'c2a9ebf8-d131-4cd4-9640-23986f128a06',
-- 'c5c2e5df-4f98-4c7b-8b59-75ea181c51bf',
-- 'c9a3911a-9714-4da4-ba6d-cf3ecff894ec',
-- 'ccbe26ff-125b-434f-a4d3-8816f217e395',
-- 'cefd9fe6-bd00-4d7f-9886-29a959506c58',
-- 'cfb90384-7950-4817-bcd9-d435b061e77a',
-- 'd25032c0-4ad1-45e9-b116-e826315f977f',
-- 'd3565faa-7384-4474-a69d-b6fcdbecfe73',
-- 'd4e3e8ee-d993-4f5d-a0d3-33e54f5901be',
-- 'd5b9f5ac-f067-4011-9aad-10c6ee06f3ab',
-- 'da79a72e-397a-4755-9018-268e4f3dfc07',
-- 'e0099ead-8ba5-4b6c-81a3-9e7470ebe571',
-- 'e052f6b7-b335-4184-b84f-fe2c23390f02',
-- 'e9d1d745-57ed-4daa-8fd5-0ae67b4f779d',
-- 'ec1e6aee-79e2-4b73-acc0-cef5b11b7ee9',
-- 'ec6ba5af-6b44-4998-b2c8-0037c2eaed87',
-- 'f12a3568-e8cf-4a6b-a9a8-905be37b3bce',
-- 'ffe99c79-4528-4768-b9da-2e366a4a9ecb'
-- )
-- and [year] = 2022
-- and [month] = 12




--t_social_perf_two_head （存在多条的情况）(需要一条一条去对比删除)
SELECT organization_id, year, month, COUNT(*) AS Count
FROM t_social_perf_two_head a
where is_active = 1
GROUP BY organization_id, year, month
HAVING COUNT(*) > 1;
-- 删除对应多条的
select * from t_social_perf_two_head where
        is_active = 1
                                       and year = 2023
                                       and [month] = 12
                                       and organization_id = '9db7dabe-210b-4e6d-b49b-51a4dc45d10f';
-- 删除对应多条的
delete from t_social_perf_two_head where id in ('51f55fa6-ec82-4661-a59f-2dc3d6184ed3');
-- 把is_active = 0 的数据全删掉
delete from t_social_perf_two_head where is_active = 0;
-- 创建唯一约束
ALTER TABLE t_social_perf_two_head
    ADD CONSTRAINT UQ_social_perf_two UNIQUE (organization_id, year, month, is_active);



-- delete t_social_perf_two_head
-- where is_active = 1
-- and year = 2022
-- and [month] = 12
-- and organization_id in (
-- '01e3994b-1078-44cf-9464-47bf681f0ef7',
-- '0687941f-f29f-46ea-83c3-0c9a94db39f4',
-- '0704dbca-35d2-4e93-84b5-8ff00df45159',
-- '0b8c080d-2561-4c6c-91fd-dd7d6d5ef484',
-- '17087670-7555-48d2-a743-59a3ad413f04',
-- '175e6735-8dea-4efa-945d-2510499f9773',
-- '1a8428fa-f392-4ce5-9265-feed8b07200e',
-- '21319d0a-dfb9-4e22-94cd-0e95d7a226cf',
-- '215f9907-2853-4714-942d-8322747b4408',
-- '244607f9-97e1-4717-8261-1430bf70c52d',
-- '2a959f6c-adef-4d7e-948a-c1ac3df1017a',
-- '345e4022-3d8b-40e6-87b7-85d6fea88881',
-- '37999f7b-d88b-45af-8ec0-92b80b1dabc2',
-- '4855c4de-36e5-47ba-b717-eb5324892333',
-- '54a3d850-038a-49b7-b980-eb40d4983ed9',
-- '59e00a0f-4611-4e38-a003-cb7a379f9867',
-- '62378a91-1583-42d4-9446-4aed1ba2781e',
-- '6838823f-5fca-4c1f-a735-a603ec595df4',
-- '6b34e6fd-0c6f-4390-ab8e-1293fd2fd15d',
-- '6c8adce4-2911-41d7-9761-faf016d71904',
-- '73fe008a-0163-4ef8-a492-0fa3bc052f8f',
-- '8062784f-a6c6-49ae-8751-2f5e9b36f7ff',
-- '9db7dabe-210b-4e6d-b49b-51a4dc45d10f',
-- '9f1368e4-3dea-4679-b166-d899f01aea73',
-- 'a963ac05-02c1-42ee-bd54-5af08690a6bd',
-- 'c3a90b50-1e65-4aae-86db-e9f4f22e2be2',
-- 'd25032c0-4ad1-45e9-b116-e826315f977f',
-- 'd4e3e8ee-d993-4f5d-a0d3-33e54f5901be',
-- 'd591cac5-57f8-4ce9-836e-7b97cc4f9002',
-- 'da79a72e-397a-4755-9018-268e4f3dfc07',
-- 'e0099ead-8ba5-4b6c-81a3-9e7470ebe571',
-- 'e052f6b7-b335-4184-b84f-fe2c23390f02',
-- 'e9d1d745-57ed-4daa-8fd5-0ae67b4f779d',
-- 'ec1e6aee-79e2-4b73-acc0-cef5b11b7ee9'
-- )



--t_social_perf_three_head （存在多条的情况）(需要一条一条去对比删除)
SELECT organization_id, year, month, COUNT(*) AS Count
FROM t_social_perf_three_head a
where is_active = 1
GROUP BY organization_id, year, month
HAVING COUNT(*) > 1;
-- 删除对应多条的
select * from t_social_perf_three_head where
        is_active = 1
                                         and year = 2023
                                         and [month] = 12
                                         and organization_id = '9db7dabe-210b-4e6d-b49b-51a4dc45d10f';
-- 删除对应多条的
delete from t_social_perf_three_head where id in ('51f55fa6-ec82-4661-a59f-2dc3d6184ed3');
-- 把is_active = 0 的数据全删掉
delete from t_social_perf_three_head where is_active = 0;
-- 创建唯一约束
ALTER TABLE t_social_perf_three_head
    ADD CONSTRAINT UQ_social_perf_three UNIQUE (organization_id, year, month, is_active);

