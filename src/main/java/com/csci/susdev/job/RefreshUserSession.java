package com.csci.susdev.job;

import com.csci.common.util.DateUtils;
import com.csci.susdev.configuration.parallel.ParallelExecutor;
import com.csci.susdev.login.Oauth2Invoker;
import com.csci.susdev.login.vo.GetAccessTokenVO;
import com.csci.susdev.model.UserSession;
import com.csci.susdev.model.UserSessionExample;
import com.csci.susdev.service.UserSessionService;
import com.csci.susdev.util.SpringContextUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@ConditionalOnProperty(value = "app.scheduling.enable", havingValue = "true", matchIfMissing = true)
@EnableScheduling
@Configuration
public class RefreshUserSession {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(RefreshUserSession.class);

    private static final Long default_timout = 60 * 2L;
    @Resource
    private UserSessionService userSessionService;
    @Resource
    private Environment environment;

    @Scheduled(fixedDelay = 1000 * 60 * 60, initialDelay = 1000)
    public void deleteExpiredSession() {
        long timeout = environment.getProperty("user.session.timeout", Long.class, default_timout);
        UserSessionExample example = new UserSessionExample();
        example.or().andCreationTimeLessThan(LocalDateTime.now().minusMinutes(timeout));
        example.setOrderByClause("creation_time desc");

        List<UserSession> lstUserSession = userSessionService.selectByExample(example);
        for (UserSession userSession : lstUserSession) {
//            System.out.println(DateUtils.toDatetimeString(userSession.getCreationTime()));
            userSessionService.deleteByPrimaryKey(userSession.getId());
        }
    }

    // 每小时执行一次刷新token的操作
    @Scheduled(fixedDelay = 1000 * 60 * 60, initialDelay = 1000)
    public void refreshSession() {
        ParallelExecutor.submit(() -> {
            List<UserSession> lstUserSession = userSessionService.selectByExample(new UserSessionExample());
            Oauth2Invoker oauth2Invoker = SpringContextUtil.getBean(Oauth2Invoker.class);
            int iTotal = lstUserSession.size();
            int iSuccess = 0;
            int iFail = 0;
            for (UserSession userSession : lstUserSession) {
                try {
                    GetAccessTokenVO getAccessTokenVO = oauth2Invoker.refreshToken(userSession.getAccessToken(), userSession.getRefreshToken());
                    UserSession record = new UserSession();
                    record.setId(userSession.getId());
                    record.setAccessToken(getAccessTokenVO.getAccessToken());
                    record.setRefreshToken(getAccessTokenVO.getRefreshToken());
                    userSessionService.updateByPrimaryKeySelective(record);
                    iSuccess++;
                } catch (Exception e) {
                    logger.error("刷新token失败", e);
                    iFail++;
                }
            }
            logger.info("刷新token完成，总数：{}，成功：{}，失败：{}", iTotal, iSuccess, iFail);
        });
    }

    public static void main(String[] args) {
        System.out.println(60 * 24 * 5);
    }
}
