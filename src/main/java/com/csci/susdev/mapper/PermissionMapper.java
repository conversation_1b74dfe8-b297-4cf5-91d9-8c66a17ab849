package com.csci.susdev.mapper;

import com.csci.susdev.model.Permission;
import com.csci.susdev.model.PermissionExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface PermissionMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	long countByExample(PermissionExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	int deleteByExample(PermissionExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	int insert(Permission row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	int insertSelective(Permission row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	List<Permission> selectByExample(PermissionExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	Permission selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	int updateByExampleSelective(@Param("row") Permission row, @Param("example") PermissionExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	int updateByExample(@Param("row") Permission row, @Param("example") PermissionExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	int updateByPrimaryKeySelective(Permission row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission
	 * @mbg.generated  Thu Jun 09 17:05:24 HKT 2022
	 */
	int updateByPrimaryKey(Permission row);

	List<Permission> getPermissionByRoleList(@Param("roleList") List<String> roleList);
}
