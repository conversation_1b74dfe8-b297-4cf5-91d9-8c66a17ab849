package com.csci.susdev.mapper;

import com.csci.susdev.vo.FactorScopeVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface FactorScopeCustomMapper {

    @Select("""
            <script>
                select * from (
                    select fs.id, fs.protocol_detail_id as protocolDetailId, cel.id as carbonEmissionLocationId,
                    cel.name as carbonEmissionLocationName, cel.name_sc as carbonEmissionLocationNameSc,
                    cel.name_en as carbonEmissionLocationNameEn, p.id as protocolId, p.name as protocolName,
                    p.name_sc as protocolNameSc, p.name_en as protocolNameEn, pc.id as categoryId,
                    pc.category_name as categoryName, pc.category_name_sc as categoryNameSc,
                    pc.category_name_en as categoryNameEn, psc.id as subCategoryId,
                    psc.sub_category_name as subCategoryName, psc.sub_category_name_sc as subCategoryNameSc,
                    psc.sub_category_name_en as subCategoryNameEn, fs.form_detail_id as formDetailId,
                    fd.form_code as formCode, CASE fd.form_code WHEN 'ff-cm-mobile' THEN '分判商移动源' WHEN 'ff-cm-fixed' THEN '分判商固定源' ELSE '' END as formCodeEnum,
                    fd.year, fd.type_a as typeA, fd.type_b as typeB, fd.type_c as typeC,
                    fd.type_d as typeD, fd.type_e as typeE, fs.factor_type as factorType, fs.last_update_version as lastUpdateVersion
                    from t_factor_scope fs
                    left join t_protocol_detail pd on fs.protocol_detail_id = pd.id and pd.is_deleted = 0
                    left join t_carbon_emission_location cel on pd.carbon_emission_location_id = cel.id and cel.is_deleted = 0
                    left join t_protocol_sub_category psc on pd.sub_category_id = psc.id and psc.is_deleted = 0
                    left join t_protocol_category pc on psc.category_id = pc.id and pc.is_deleted = 0
                    left join t_protocol p on pc.protocol_id = p.id and p.is_deleted = 0
                    left join t_form_detail fd on fs.form_detail_id = fd.id and fd.is_deleted = 0
                    where fs.is_deleted = 0
                    
                    <if test="protocolId != null and protocolId != ''">
                    and p.id = #{protocolId}
                    </if>
                    
                    <if test="carbonEmissionLocationId != null and carbonEmissionLocationId != ''">
                    and cel.id = #{carbonEmissionLocationId}
                    </if>
                    
                    <if test="year != null and year != ''">
                    and fd.year = #{year}
                    </if>
                    <if test="formCode != null and formCode != ''">
                    and fd.form_code = #{formCode}
                    </if>
                ) t
                
                <if test="orderBy != null and orderBy != ''">
                order by ${orderBy}
                </if>
            </script>
                """)
    public List<FactorScopeVO> list(
            @Param("protocolId") String protocolId,
            @Param("carbonEmissionLocationId") String carbonEmissionLocationId,
            @Param("year") Integer year,
            @Param("orderBy") String orderBy,
            @Param("formCode") String formCode
    );
}
