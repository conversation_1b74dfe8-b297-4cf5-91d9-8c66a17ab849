package com.csci.susdev.mapper;

import com.csci.susdev.model.WorkflowNodeUser;
import com.csci.susdev.model.WorkflowNodeUserExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WorkflowNodeUserMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node_user
     *
     * @mbg.generated
     */
    long countByExample(WorkflowNodeUserExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node_user
     *
     * @mbg.generated
     */
    int deleteByExample(WorkflowNodeUserExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node_user
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node_user
     *
     * @mbg.generated
     */
    int insert(WorkflowNodeUser record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node_user
     *
     * @mbg.generated
     */
    int insertSelective(WorkflowNodeUser record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node_user
     *
     * @mbg.generated
     */
    List<WorkflowNodeUser> selectByExample(WorkflowNodeUserExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node_user
     *
     * @mbg.generated
     */
    WorkflowNodeUser selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node_user
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") WorkflowNodeUser record, @Param("example") WorkflowNodeUserExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node_user
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") WorkflowNodeUser record, @Param("example") WorkflowNodeUserExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node_user
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(WorkflowNodeUser record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow_node_user
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(WorkflowNodeUser record);
}