package com.csci.susdev.mapper;

import com.csci.susdev.model.CarbonEmissionLocation;
import com.csci.susdev.model.CarbonEmissionLocationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CarbonEmissionLocationMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_carbon_emission_location
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    long countByExample(CarbonEmissionLocationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_carbon_emission_location
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    int deleteByExample(CarbonEmissionLocationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_carbon_emission_location
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_carbon_emission_location
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    int insert(CarbonEmissionLocation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_carbon_emission_location
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    int insertSelective(CarbonEmissionLocation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_carbon_emission_location
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    List<CarbonEmissionLocation> selectByExample(CarbonEmissionLocationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_carbon_emission_location
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    CarbonEmissionLocation selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_carbon_emission_location
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    int updateByExampleSelective(@Param("record") CarbonEmissionLocation record, @Param("example") CarbonEmissionLocationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_carbon_emission_location
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    int updateByExample(@Param("record") CarbonEmissionLocation record, @Param("example") CarbonEmissionLocationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_carbon_emission_location
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    int updateByPrimaryKeySelective(CarbonEmissionLocation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_carbon_emission_location
     *
     * @mbg.generated Tue Apr 09 18:07:37 HKT 2024
     */
    int updateByPrimaryKey(CarbonEmissionLocation record);
}