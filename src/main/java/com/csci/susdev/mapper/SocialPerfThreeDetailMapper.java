package com.csci.susdev.mapper;

import com.csci.susdev.model.SocialPerfThreeDetail;
import com.csci.susdev.model.SocialPerfThreeDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SocialPerfThreeDetailMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_three_detail
     *
     * @mbg.generated Tue Jan 16 12:02:13 HKT 2024
     */
    long countByExample(SocialPerfThreeDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_three_detail
     *
     * @mbg.generated Tue Jan 16 12:02:13 HKT 2024
     */
    int deleteByExample(SocialPerfThreeDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_three_detail
     *
     * @mbg.generated Tue Jan 16 12:02:13 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_three_detail
     *
     * @mbg.generated Tue Jan 16 12:02:13 HKT 2024
     */
    int insert(SocialPerfThreeDetail row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_three_detail
     *
     * @mbg.generated Tue Jan 16 12:02:13 HKT 2024
     */
    int insertSelective(SocialPerfThreeDetail row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_three_detail
     *
     * @mbg.generated Tue Jan 16 12:02:13 HKT 2024
     */
    List<SocialPerfThreeDetail> selectByExample(SocialPerfThreeDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_three_detail
     *
     * @mbg.generated Tue Jan 16 12:02:13 HKT 2024
     */
    SocialPerfThreeDetail selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_three_detail
     *
     * @mbg.generated Tue Jan 16 12:02:13 HKT 2024
     */
    int updateByExampleSelective(@Param("row") SocialPerfThreeDetail row, @Param("example") SocialPerfThreeDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_three_detail
     *
     * @mbg.generated Tue Jan 16 12:02:13 HKT 2024
     */
    int updateByExample(@Param("row") SocialPerfThreeDetail row, @Param("example") SocialPerfThreeDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_three_detail
     *
     * @mbg.generated Tue Jan 16 12:02:13 HKT 2024
     */
    int updateByPrimaryKeySelective(SocialPerfThreeDetail row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_three_detail
     *
     * @mbg.generated Tue Jan 16 12:02:13 HKT 2024
     */
    int updateByPrimaryKey(SocialPerfThreeDetail row);
}