package com.csci.susdev.mapper;

import com.csci.susdev.model.*;
import com.csci.susdev.vo.*;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface AmbientDetailCustomMapper {

    public static String sql1 = """
            SELECT org.id, org.parent_id AS parentId, org.no, org.name, org.unit_code AS unitCode, t.data
            FROM t_organization org
            LEFT JOIN (
            SELECT o.id, 
            """;
    public static String sql2 = """ 
            AS data
            FROM t_ambient_head h
            LEFT JOIN t_ambient_detail d
            ON h.id = d.head_id
            LEFT JOIN t_organization o
            ON h.organization_id = o.id
            LEFT JOIN t_user_organization uo
            ON o.id = uo.organization_id
            LEFT JOIN t_user u
            ON u.id = uo.user_id
            LEFT JOIN t_workflow_control c
            ON h.id = c.business_id
            WHERE  h.year = #{year}
            AND h.month = #{month}
            AND d.category = #{category}
            AND d.type = #{type}
            AND d.type2 = #{type2}
            AND d.unit = #{unit}
            AND d.unit_code = #{unitCode}
            AND u.id = #{userId}
            AND h.is_active = 1
            AND c.is_active = 1
            ) AS t
            ON org.id = t.id
            WHERE org.is_deleted = 0 AND org.no LIKE CONCAT((SELECT _o.no FROM t_organization _o WHERE id = #{organizationId}), '', '%')
            ORDER BY org.no
            """;

    @Select(sql1 + "d.month_value_1 " + sql2)
    public List<AmbientSubOrgsDataVO> getMonthValue1Data(@Param("organizationId") String organizationId,
                                                         @Param("year") Integer year,
                                                         @Param("month") Integer month,
                                                         @Param("userId") String userId,
                                                         @Param("category") String category,
                                                         @Param("type") String type,
                                                         @Param("type2") String type2,
                                                         @Param("unit") String unit,
                                                         @Param("unitCode") String unitCode);

    @Select(sql1 + "d.month_value_2 " + sql2)
    public List<AmbientSubOrgsDataVO> getMonthValue2Data(@Param("organizationId") String organizationId,
                                                   @Param("year") Integer year,
                                                   @Param("month") Integer month,
                                                   @Param("userId") String userId,
                                                   @Param("category") String category,
                                                   @Param("type") String type,
                                                   @Param("type2") String type2,
                                                   @Param("unit") String unit,
                                                   @Param("unitCode") String unitCode);

    @Select(sql1 + "d.month_value_3 " + sql2)
    public List<AmbientSubOrgsDataVO> getMonthValue3Data(@Param("organizationId") String organizationId,
                                                   @Param("year") Integer year,
                                                   @Param("month") Integer month,
                                                   @Param("userId") String userId,
                                                   @Param("category") String category,
                                                   @Param("type") String type,
                                                   @Param("type2") String type2,
                                                   @Param("unit") String unit,
                                                   @Param("unitCode") String unitCode);

    @Select(sql1 + "d.month_value_4 " + sql2)
    public List<AmbientSubOrgsDataVO> getMonthValue4Data(@Param("organizationId") String organizationId,
                                                   @Param("year") Integer year,
                                                         @Param("month") Integer month,
                                                   @Param("userId") String userId,
                                                   @Param("category") String category,
                                                   @Param("type") String type,
                                                   @Param("type2") String type2,
                                                   @Param("unit") String unit,
                                                   @Param("unitCode") String unitCode);

    @Select(sql1 + "d.month_value_5 " + sql2)
    public List<AmbientSubOrgsDataVO> getMonthValue5Data(@Param("organizationId") String organizationId,
                                                   @Param("year") Integer year,
                                                         @Param("month") Integer month,
                                                   @Param("userId") String userId,
                                                   @Param("category") String category,
                                                   @Param("type") String type,
                                                   @Param("type2") String type2,
                                                   @Param("unit") String unit,
                                                   @Param("unitCode") String unitCode);

    @Select(sql1 + "d.month_value_6 " + sql2)
    public List<AmbientSubOrgsDataVO> getMonthValue6Data(@Param("organizationId") String organizationId,
                                                   @Param("year") Integer year,
                                                         @Param("month") Integer month,
                                                   @Param("userId") String userId,
                                                   @Param("category") String category,
                                                   @Param("type") String type,
                                                   @Param("type2") String type2,
                                                   @Param("unit") String unit,
                                                   @Param("unitCode") String unitCode);

    @Select(sql1 + "d.month_value_7 " + sql2)
    public List<AmbientSubOrgsDataVO> getMonthValue7Data(@Param("organizationId") String organizationId,
                                                   @Param("year") Integer year,
                                                         @Param("month") Integer month,
                                                   @Param("userId") String userId,
                                                   @Param("category") String category,
                                                   @Param("type") String type,
                                                   @Param("type2") String type2,
                                                   @Param("unit") String unit,
                                                   @Param("unitCode") String unitCode);

    @Select(sql1 + "d.month_value_8 " + sql2)
    public List<AmbientSubOrgsDataVO> getMonthValue8Data(@Param("organizationId") String organizationId,
                                                   @Param("year") Integer year,
                                                         @Param("month") Integer month,
                                                   @Param("userId") String userId,
                                                   @Param("category") String category,
                                                   @Param("type") String type,
                                                   @Param("type2") String type2,
                                                   @Param("unit") String unit,
                                                   @Param("unitCode") String unitCode);




    @Select(sql1 + "d.month_value_9 " + sql2)
    public List<AmbientSubOrgsDataVO> getMonthValue9Data(@Param("organizationId") String organizationId,
                                                   @Param("year") Integer year,
                                                         @Param("month") Integer month,
                                                   @Param("userId") String userId,
                                                   @Param("category") String category,
                                                   @Param("type") String type,
                                                   @Param("type2") String type2,
                                                   @Param("unit") String unit,
                                                   @Param("unitCode") String unitCode);




    @Select(sql1 + "d.month_value_10 " + sql2)
    public List<AmbientSubOrgsDataVO> getMonthValue10Data(@Param("organizationId") String organizationId,
                                                   @Param("year") Integer year,
                                                          @Param("month") Integer month,
                                                    @Param("userId") String userId,
                                                   @Param("category") String category,
                                                   @Param("type") String type,
                                                   @Param("type2") String type2,
                                                   @Param("unit") String unit,
                                                   @Param("unitCode") String unitCode);




    @Select(sql1 + "d.month_value_11 " + sql2)
    public List<AmbientSubOrgsDataVO> getMonthValue11Data(@Param("organizationId") String organizationId,
                                                   @Param("year") Integer year,
                                                          @Param("month") Integer month,
                                                   @Param("userId") String userId,
                                                   @Param("category") String category,
                                                   @Param("type") String type,
                                                   @Param("type2") String type2,
                                                   @Param("unit") String unit,
                                                   @Param("unitCode") String unitCode);

    @Select(sql1 + "d.month_value_12 " + sql2)
    public List<AmbientSubOrgsDataVO> getMonthValue12Data(@Param("organizationId") String organizationId,
                                                   @Param("year") Integer year,
                                                          @Param("month") Integer month,
                                                    @Param("userId") String userId,
                                                   @Param("category") String category,
                                                   @Param("type") String type,
                                                   @Param("type2") String type2,
                                                   @Param("unit") String unit,
                                                   @Param("unitCode") String unitCode);

    @Select(sql1 + "d.year_total_value " + sql2)
    public List<AmbientSubOrgsDataVO> getYearTotalData(@Param("organizationId") String organizationId,
                                                    @Param("year") Integer year,
                                                       @Param("month") Integer month,
                                                    @Param("userId") String userId,
                                                    @Param("category") String category,
                                                    @Param("type") String type,
                                                    @Param("type2") String type2,
                                                    @Param("unit") String unit,
                                                    @Param("unitCode") String unitCode);

    @Select(sql1 + "d.remark " + sql2)
    public List<AmbientSubOrgsDataVO> getRemarkData(@Param("organizationId") String organizationId,
                                                   @Param("year") Integer year,
                                                    @Param("month") Integer month,
                                                   @Param("userId") String userId,
                                                   @Param("category") String category,
                                                   @Param("type") String type,
                                                   @Param("type2") String type2,
                                                   @Param("unit") String unit,
                                                   @Param("unitCode") String unitCode);

    @Select("""
            select to2.name orgName,to2.no orgNo,tad.* from t_ambient_head ah
            join t_organization to2 on to2.id = ah.organization_id
            join t_ambient_detail tad on tad.head_id = ah.id
            where to2.no like concat(#{no},'%')
            and ah.is_active = 1
            and ah.year = #{year}
            and ah.month = #{month}
            ORDER BY CAST(no AS BIGINT), seq, tad.unit_code;
    """)
    List<AmbientDetailVO> queryAmbientDetailsByOrgNo(@Param("no") String no,@Param("year") Integer year,@Param("month") Integer month);


    @Insert("<script>" +
            "insert into t_ambient_detail (id, head_id, category," +
            "      category_digest, type, type2," +
            "      unit, unit_code, month_value_1," +
            "      month_value_2, month_value_3, season_value_1," +
            "      month_value_4, month_value_5, month_value_6," +
            "      season_value_2, month_value_7, month_value_8," +
            "      month_value_9, season_value_3, month_value_10," +
            "      month_value_11, month_value_12, season_value_4," +
            "      year_total_value, remark, seq," +
            "      creation_time) values" +
            "<foreach collection='recordList' item='item' separator=','>" +
            "(#{item.id,jdbcType=VARCHAR}, #{item.headId,jdbcType=VARCHAR}, #{item.category,jdbcType=VARCHAR}," +
            "      #{item.categoryDigest,jdbcType=VARCHAR}, #{item.type,jdbcType=VARCHAR}, #{item.type2,jdbcType=VARCHAR}," +
            "      #{item.unit,jdbcType=VARCHAR}, #{item.unitCode,jdbcType=VARCHAR}, #{item.monthValue1,jdbcType=VARCHAR}," +
            "      #{item.monthValue2,jdbcType=VARCHAR}, #{item.monthValue3,jdbcType=VARCHAR}, #{item.seasonValue1,jdbcType=VARCHAR}," +
            "      #{item.monthValue4,jdbcType=VARCHAR}, #{item.monthValue5,jdbcType=VARCHAR}, #{item.monthValue6,jdbcType=VARCHAR}," +
            "      #{item.seasonValue2,jdbcType=VARCHAR}, #{item.monthValue7,jdbcType=VARCHAR}, #{item.monthValue8,jdbcType=VARCHAR}," +
            "      #{item.monthValue9,jdbcType=VARCHAR}, #{item.seasonValue3,jdbcType=VARCHAR}, #{item.monthValue10,jdbcType=VARCHAR}," +
            "      #{item.monthValue11,jdbcType=VARCHAR}, #{item.monthValue12,jdbcType=VARCHAR}, #{item.seasonValue4,jdbcType=VARCHAR}," +
            "      #{item.yearTotalValue,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.seq,jdbcType=INTEGER}," +
            "      #{item.creationTime,jdbcType=TIMESTAMP})" +
            "</foreach>" +
            "</script>"
    )
    int batchInsert(@Param("recordList") List<AmbientDetail> recordList);
}
