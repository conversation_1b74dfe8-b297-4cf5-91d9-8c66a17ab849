package com.csci.susdev.mapper;

import com.csci.susdev.model.FcAirPollutionFactorEsgDataset;
import com.csci.susdev.model.FcAirPollutionFactorEsgDatasetExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FcAirPollutionFactorEsgDatasetMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_air_pollution_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:45:18 CST 2025
     */
    long countByExample(FcAirPollutionFactorEsgDatasetExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_air_pollution_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:45:18 CST 2025
     */
    int deleteByExample(FcAirPollutionFactorEsgDatasetExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_air_pollution_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:45:18 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_air_pollution_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:45:18 CST 2025
     */
    int insert(FcAirPollutionFactorEsgDataset record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_air_pollution_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:45:18 CST 2025
     */
    int insertSelective(FcAirPollutionFactorEsgDataset record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_air_pollution_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:45:18 CST 2025
     */
    List<FcAirPollutionFactorEsgDataset> selectByExample(FcAirPollutionFactorEsgDatasetExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_air_pollution_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:45:18 CST 2025
     */
    FcAirPollutionFactorEsgDataset selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_air_pollution_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:45:18 CST 2025
     */
    int updateByExampleSelective(@Param("record") FcAirPollutionFactorEsgDataset record, @Param("example") FcAirPollutionFactorEsgDatasetExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_air_pollution_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:45:18 CST 2025
     */
    int updateByExample(@Param("record") FcAirPollutionFactorEsgDataset record, @Param("example") FcAirPollutionFactorEsgDatasetExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_air_pollution_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:45:18 CST 2025
     */
    int updateByPrimaryKeySelective(FcAirPollutionFactorEsgDataset record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_air_pollution_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:45:18 CST 2025
     */
    int updateByPrimaryKey(FcAirPollutionFactorEsgDataset record);
}