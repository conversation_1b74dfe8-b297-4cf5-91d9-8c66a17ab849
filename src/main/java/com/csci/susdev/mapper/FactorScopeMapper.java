package com.csci.susdev.mapper;

import com.csci.susdev.model.FactorScope;
import com.csci.susdev.model.FactorScopeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FactorScopeMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_factor_scope
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    long countByExample(FactorScopeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_factor_scope
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    int deleteByExample(FactorScopeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_factor_scope
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_factor_scope
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    int insert(FactorScope record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_factor_scope
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    int insertSelective(FactorScope record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_factor_scope
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    List<FactorScope> selectByExample(FactorScopeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_factor_scope
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    FactorScope selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_factor_scope
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    int updateByExampleSelective(@Param("record") FactorScope record, @Param("example") FactorScopeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_factor_scope
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    int updateByExample(@Param("record") FactorScope record, @Param("example") FactorScopeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_factor_scope
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    int updateByPrimaryKeySelective(FactorScope record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_factor_scope
     *
     * @mbg.generated Thu Apr 11 19:56:53 HKT 2024
     */
    int updateByPrimaryKey(FactorScope record);
}