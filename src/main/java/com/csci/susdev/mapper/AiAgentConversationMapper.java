package com.csci.susdev.mapper;

import com.csci.susdev.model.AiAgentConversation;
import com.csci.susdev.model.AiAgentConversationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AiAgentConversationMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ai_agent_conversation
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    long countByExample(AiAgentConversationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ai_agent_conversation
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    int deleteByExample(AiAgentConversationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ai_agent_conversation
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ai_agent_conversation
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    int insert(AiAgentConversation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ai_agent_conversation
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    int insertSelective(AiAgentConversation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ai_agent_conversation
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    List<AiAgentConversation> selectByExample(AiAgentConversationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ai_agent_conversation
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    AiAgentConversation selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ai_agent_conversation
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    int updateByExampleSelective(@Param("record") AiAgentConversation record, @Param("example") AiAgentConversationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ai_agent_conversation
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    int updateByExample(@Param("record") AiAgentConversation record, @Param("example") AiAgentConversationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ai_agent_conversation
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    int updateByPrimaryKeySelective(AiAgentConversation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_ai_agent_conversation
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    int updateByPrimaryKey(AiAgentConversation record);
}