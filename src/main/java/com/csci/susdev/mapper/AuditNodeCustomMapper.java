package com.csci.susdev.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;

import com.csci.susdev.model.*;
import com.csci.susdev.provider.*;
import com.csci.susdev.vo.*;

@Mapper
public interface AuditNodeCustomMapper {
	
    @SelectProvider(type=AuditNodeSqlProvider.class,method="listAuditNodeDetailSql")
    public List<AuditNodeDetailVO> listAuditNodeDetail(@Param("userId") String userId);

}
