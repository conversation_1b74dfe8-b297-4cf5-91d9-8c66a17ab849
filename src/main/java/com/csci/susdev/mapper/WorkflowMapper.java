package com.csci.susdev.mapper;

import com.csci.susdev.model.Workflow;
import com.csci.susdev.model.WorkflowExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WorkflowMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow
     *
     * @mbg.generated Thu May 23 17:57:19 HKT 2024
     */
    long countByExample(WorkflowExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow
     *
     * @mbg.generated Thu May 23 17:57:19 HKT 2024
     */
    int deleteByExample(WorkflowExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow
     *
     * @mbg.generated Thu May 23 17:57:19 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow
     *
     * @mbg.generated Thu May 23 17:57:19 HKT 2024
     */
    int insert(Workflow record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow
     *
     * @mbg.generated Thu May 23 17:57:19 HKT 2024
     */
    int insertSelective(Workflow record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow
     *
     * @mbg.generated Thu May 23 17:57:19 HKT 2024
     */
    List<Workflow> selectByExample(WorkflowExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow
     *
     * @mbg.generated Thu May 23 17:57:19 HKT 2024
     */
    Workflow selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow
     *
     * @mbg.generated Thu May 23 17:57:19 HKT 2024
     */
    int updateByExampleSelective(@Param("record") Workflow record, @Param("example") WorkflowExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow
     *
     * @mbg.generated Thu May 23 17:57:19 HKT 2024
     */
    int updateByExample(@Param("record") Workflow record, @Param("example") WorkflowExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow
     *
     * @mbg.generated Thu May 23 17:57:19 HKT 2024
     */
    int updateByPrimaryKeySelective(Workflow record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_workflow
     *
     * @mbg.generated Thu May 23 17:57:19 HKT 2024
     */
    int updateByPrimaryKey(Workflow record);
}