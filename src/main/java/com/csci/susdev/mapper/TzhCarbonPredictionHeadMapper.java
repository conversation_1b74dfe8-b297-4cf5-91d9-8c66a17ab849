package com.csci.susdev.mapper;

import com.csci.susdev.model.TzhCarbonPredictionHead;
import com.csci.susdev.model.TzhCarbonPredictionHeadExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TzhCarbonPredictionHeadMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_head
     *
     * @mbg.generated Tue Jul 26 12:34:51 HKT 2022
     */
    long countByExample(TzhCarbonPredictionHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_head
     *
     * @mbg.generated Tue Jul 26 12:34:51 HKT 2022
     */
    int deleteByExample(TzhCarbonPredictionHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_head
     *
     * @mbg.generated Tue Jul 26 12:34:51 HKT 2022
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_head
     *
     * @mbg.generated Tue Jul 26 12:34:51 HKT 2022
     */
    int insert(TzhCarbonPredictionHead row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_head
     *
     * @mbg.generated Tue Jul 26 12:34:51 HKT 2022
     */
    int insertSelective(TzhCarbonPredictionHead row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_head
     *
     * @mbg.generated Tue Jul 26 12:34:51 HKT 2022
     */
    List<TzhCarbonPredictionHead> selectByExample(TzhCarbonPredictionHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_head
     *
     * @mbg.generated Tue Jul 26 12:34:51 HKT 2022
     */
    TzhCarbonPredictionHead selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_head
     *
     * @mbg.generated Tue Jul 26 12:34:51 HKT 2022
     */
    int updateByExampleSelective(@Param("row") TzhCarbonPredictionHead row, @Param("example") TzhCarbonPredictionHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_head
     *
     * @mbg.generated Tue Jul 26 12:34:51 HKT 2022
     */
    int updateByExample(@Param("row") TzhCarbonPredictionHead row, @Param("example") TzhCarbonPredictionHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_head
     *
     * @mbg.generated Tue Jul 26 12:34:51 HKT 2022
     */
    int updateByPrimaryKeySelective(TzhCarbonPredictionHead row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tzh_carbon_prediction_head
     *
     * @mbg.generated Tue Jul 26 12:34:51 HKT 2022
     */
    int updateByPrimaryKey(TzhCarbonPredictionHead row);
}