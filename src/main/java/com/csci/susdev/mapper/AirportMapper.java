package com.csci.susdev.mapper;

import com.csci.susdev.model.Airport;
import com.csci.susdev.model.AirportExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AirportMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_airport
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	long countByExample(AirportExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_airport
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	int deleteByExample(AirportExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_airport
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_airport
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	int insert(Airport row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_airport
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	int insertSelective(Airport row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_airport
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	List<Airport> selectByExample(AirportExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_airport
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	Airport selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_airport
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") Airport row, @Param("example") AirportExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_airport
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	int updateByExample(@Param("row") Airport row, @Param("example") AirportExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_airport
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	int updateByPrimaryKeySelective(Airport row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_airport
	 * @mbg.generated  Thu Jun 08 11:33:16 HKT 2023
	 */
	int updateByPrimaryKey(Airport row);
}