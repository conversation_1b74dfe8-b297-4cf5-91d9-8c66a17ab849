package com.csci.susdev.mapper;

import com.csci.susdev.model.VehicleFuelUsage;
import com.csci.susdev.model.VehicleFuelUsageExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface VehicleFuelUsageMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	long countByExample(VehicleFuelUsageExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	int deleteByExample(VehicleFuelUsageExample example);
	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	int insert(VehicleFuelUsage row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	int insertSelective(VehicleFuelUsage row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	List<VehicleFuelUsage> selectByExample(VehicleFuelUsageExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	VehicleFuelUsage selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") VehicleFuelUsage row, @Param("example") VehicleFuelUsageExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	int updateByExample(@Param("row") VehicleFuelUsage row, @Param("example") VehicleFuelUsageExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	int updateByPrimaryKeySelective(VehicleFuelUsage row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	int updateByPrimaryKey(VehicleFuelUsage row);
}