package com.csci.susdev.mapper;

import com.csci.susdev.model.FcUnitConversion;
import com.csci.susdev.model.FcUnitConversionExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FcUnitConversionMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_unit_conversion
     *
     * @mbg.generated Thu Mar 13 11:38:22 CST 2025
     */
    long countByExample(FcUnitConversionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_unit_conversion
     *
     * @mbg.generated Thu Mar 13 11:38:22 CST 2025
     */
    int deleteByExample(FcUnitConversionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_unit_conversion
     *
     * @mbg.generated Thu Mar 13 11:38:22 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_unit_conversion
     *
     * @mbg.generated Thu Mar 13 11:38:22 CST 2025
     */
    int insert(FcUnitConversion record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_unit_conversion
     *
     * @mbg.generated Thu Mar 13 11:38:22 CST 2025
     */
    int insertSelective(FcUnitConversion record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_unit_conversion
     *
     * @mbg.generated Thu Mar 13 11:38:22 CST 2025
     */
    List<FcUnitConversion> selectByExample(FcUnitConversionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_unit_conversion
     *
     * @mbg.generated Thu Mar 13 11:38:22 CST 2025
     */
    FcUnitConversion selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_unit_conversion
     *
     * @mbg.generated Thu Mar 13 11:38:22 CST 2025
     */
    int updateByExampleSelective(@Param("record") FcUnitConversion record, @Param("example") FcUnitConversionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_unit_conversion
     *
     * @mbg.generated Thu Mar 13 11:38:22 CST 2025
     */
    int updateByExample(@Param("record") FcUnitConversion record, @Param("example") FcUnitConversionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_unit_conversion
     *
     * @mbg.generated Thu Mar 13 11:38:22 CST 2025
     */
    int updateByPrimaryKeySelective(FcUnitConversion record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_unit_conversion
     *
     * @mbg.generated Thu Mar 13 11:38:22 CST 2025
     */
    int updateByPrimaryKey(FcUnitConversion record);
}