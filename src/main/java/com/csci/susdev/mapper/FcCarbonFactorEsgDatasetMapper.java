package com.csci.susdev.mapper;

import com.csci.susdev.model.FcCarbonFactorEsgDataset;
import com.csci.susdev.model.FcCarbonFactorEsgDatasetExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FcCarbonFactorEsgDatasetMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:44:09 CST 2025
     */
    long countByExample(FcCarbonFactorEsgDatasetExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:44:09 CST 2025
     */
    int deleteByExample(FcCarbonFactorEsgDatasetExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:44:09 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:44:09 CST 2025
     */
    int insert(FcCarbonFactorEsgDataset record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:44:09 CST 2025
     */
    int insertSelective(FcCarbonFactorEsgDataset record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:44:09 CST 2025
     */
    List<FcCarbonFactorEsgDataset> selectByExample(FcCarbonFactorEsgDatasetExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:44:09 CST 2025
     */
    FcCarbonFactorEsgDataset selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:44:09 CST 2025
     */
    int updateByExampleSelective(@Param("record") FcCarbonFactorEsgDataset record, @Param("example") FcCarbonFactorEsgDatasetExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:44:09 CST 2025
     */
    int updateByExample(@Param("record") FcCarbonFactorEsgDataset record, @Param("example") FcCarbonFactorEsgDatasetExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:44:09 CST 2025
     */
    int updateByPrimaryKeySelective(FcCarbonFactorEsgDataset record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_esg_dataset
     *
     * @mbg.generated Wed Feb 05 09:44:09 CST 2025
     */
    int updateByPrimaryKey(FcCarbonFactorEsgDataset record);
}