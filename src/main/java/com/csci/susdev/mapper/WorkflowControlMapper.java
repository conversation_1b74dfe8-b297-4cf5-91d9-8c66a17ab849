package com.csci.susdev.mapper;

import com.csci.susdev.model.WorkflowControl;
import com.csci.susdev.model.WorkflowControlExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WorkflowControlMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_workflow_control
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	long countByExample(WorkflowControlExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_workflow_control
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	int deleteByExample(WorkflowControlExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_workflow_control
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_workflow_control
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	int insert(WorkflowControl row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_workflow_control
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	int insertSelective(WorkflowControl row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_workflow_control
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	List<WorkflowControl> selectByExample(WorkflowControlExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_workflow_control
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	WorkflowControl selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_workflow_control
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") WorkflowControl row, @Param("example") WorkflowControlExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_workflow_control
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	int updateByExample(@Param("row") WorkflowControl row, @Param("example") WorkflowControlExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_workflow_control
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	int updateByPrimaryKeySelective(WorkflowControl row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_workflow_control
	 * @mbg.generated  Thu Jun 15 16:20:15 HKT 2023
	 */
	int updateByPrimaryKey(WorkflowControl row);
}