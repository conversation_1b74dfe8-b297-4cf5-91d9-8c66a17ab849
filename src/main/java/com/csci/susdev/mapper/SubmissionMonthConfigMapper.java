package com.csci.susdev.mapper;

import com.csci.susdev.model.SubmissionMonthConfig;
import com.csci.susdev.model.SubmissionMonthConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SubmissionMonthConfigMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    long countByExample(SubmissionMonthConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    int deleteByExample(SubmissionMonthConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    int insert(SubmissionMonthConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    int insertSelective(SubmissionMonthConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    List<SubmissionMonthConfig> selectByExample(SubmissionMonthConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    SubmissionMonthConfig selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    int updateByExampleSelective(@Param("record") SubmissionMonthConfig record, @Param("example") SubmissionMonthConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    int updateByExample(@Param("record") SubmissionMonthConfig record, @Param("example") SubmissionMonthConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    int updateByPrimaryKeySelective(SubmissionMonthConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_submission_month_config
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    int updateByPrimaryKey(SubmissionMonthConfig record);
}