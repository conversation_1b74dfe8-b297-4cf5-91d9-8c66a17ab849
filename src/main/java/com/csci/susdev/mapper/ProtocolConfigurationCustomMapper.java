package com.csci.susdev.mapper;

import com.csci.susdev.model.FactorSelection;
import com.csci.susdev.qo.FactorSelectionQO;
import com.csci.susdev.qo.ProtocolConfigurationQO;
import com.csci.susdev.vo.FactorSelectionVO;
import com.csci.susdev.vo.ProtocolConfigurationVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ProtocolConfigurationCustomMapper {




    @Select("""
	<script>
		select
			tpc.id,
			tpc.form_type as formType,
			tpc.fc_factor_id as fcFactorId,
			tpc.fc_factor_type as fcFactorType,
			p.id as protocolId,
			p.name as protocolName,
			p.name_sc as protocolNameSc,
			p.name_en as protocolNameEn,
			pc.id as categoryId,
			pc.category_name as categoryName,
			pc.category_name_sc as categoryNameSc,
			pc.category_name_en as categoryNameEn,
			psc.id as subCategoryId,
			psc.sub_category_name as subCategoryName,
			psc.sub_category_name_sc as subCategoryNameSc,
			psc.sub_category_name_en as subCategoryNameEn,
			fd.id as formDetailId,
			fd.form_code as formCode,
			f.name as formName,
			fd.year as year,
			fd.type_a as typeA,
			fd.type_b as typeB,
			fd.type_c as typeC,
			fd.type_d as typeD,
			fd.type_e as typeE,
			tpc.is_active as isActive,
			tpc.last_update_version as lastUpdateVersion
			from t_protocol_configuration tpc
		  left join t_protocol_sub_category psc on tpc.sub_category_id = psc.id and psc.is_deleted = 0
		  left join t_protocol_category pc on psc.category_id = pc.id and pc.is_deleted = 0
		  left join t_protocol p on pc.protocol_id = p.id and p.is_deleted = 0
		  left join t_form_detail fd on tpc.form_detail_id = fd.id and fd.is_deleted = 0
			left join t_form f on fd.form_code = f.code
			where tpc.is_deleted = 0
			<if test="protocolConfigurationQO.protocolId != null and protocolConfigurationQO.protocolId != ''">
				and p.id = #{protocolConfigurationQO.protocolId}
			</if>
			<if test="protocolConfigurationQO.categoryId != null and protocolConfigurationQO.categoryId != ''">
				and pc.id = #{protocolConfigurationQO.categoryId}
			</if>
			<if test="protocolConfigurationQO.subCategoryId != null and protocolConfigurationQO.subCategoryId != ''">
				and psc.id = #{protocolConfigurationQO.subCategoryId}
			</if>
			<if test="protocolConfigurationQO.year != null and protocolConfigurationQO.year != ''">
			and fd.year = #{protocolConfigurationQO.year}
			</if>
			
			order by fd.year, p.name_en, pc.category_name_en, psc.sub_category_name_en
	 </script>
	""")
    public List<ProtocolConfigurationVO> listVOInherited(@Param("protocolConfigurationQO") ProtocolConfigurationQO protocolConfigurationQO);


}
