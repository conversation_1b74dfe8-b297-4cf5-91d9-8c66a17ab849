package com.csci.susdev.mapper;

import com.csci.susdev.model.PermissionMenu;
import com.csci.susdev.model.PermissionMenuExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface PermissionMenuMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission_menu
	 * @mbg.generated  Thu Jun 09 17:05:38 HKT 2022
	 */
	long countByExample(PermissionMenuExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission_menu
	 * @mbg.generated  Thu Jun 09 17:05:38 HKT 2022
	 */
	int deleteByExample(PermissionMenuExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission_menu
	 * @mbg.generated  Thu Jun 09 17:05:38 HKT 2022
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission_menu
	 * @mbg.generated  Thu Jun 09 17:05:38 HKT 2022
	 */
	int insert(PermissionMenu row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission_menu
	 * @mbg.generated  Thu Jun 09 17:05:38 HKT 2022
	 */
	int insertSelective(PermissionMenu row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission_menu
	 * @mbg.generated  Thu Jun 09 17:05:38 HKT 2022
	 */
	List<PermissionMenu> selectByExample(PermissionMenuExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission_menu
	 * @mbg.generated  Thu Jun 09 17:05:38 HKT 2022
	 */
	PermissionMenu selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission_menu
	 * @mbg.generated  Thu Jun 09 17:05:38 HKT 2022
	 */
	int updateByExampleSelective(@Param("row") PermissionMenu row, @Param("example") PermissionMenuExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission_menu
	 * @mbg.generated  Thu Jun 09 17:05:38 HKT 2022
	 */
	int updateByExample(@Param("row") PermissionMenu row, @Param("example") PermissionMenuExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission_menu
	 * @mbg.generated  Thu Jun 09 17:05:38 HKT 2022
	 */
	int updateByPrimaryKeySelective(PermissionMenu row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_permission_menu
	 * @mbg.generated  Thu Jun 09 17:05:38 HKT 2022
	 */
	int updateByPrimaryKey(PermissionMenu row);
}