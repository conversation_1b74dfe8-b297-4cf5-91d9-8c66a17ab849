package com.csci.susdev.mapper;

import com.csci.susdev.model.CdmsMaterialInvoice;
import com.csci.susdev.model.CdmsMaterialInvoiceExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CdmsMaterialInvoiceMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	long countByExample(CdmsMaterialInvoiceExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	int deleteByExample(CdmsMaterialInvoiceExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	int deleteByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	int insert(CdmsMaterialInvoice row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	int insertSelective(CdmsMaterialInvoice row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	List<CdmsMaterialInvoice> selectByExample(CdmsMaterialInvoiceExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	CdmsMaterialInvoice selectByPrimaryKey(String id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	int updateByExampleSelective(@Param("row") CdmsMaterialInvoice row,
			@Param("example") CdmsMaterialInvoiceExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	int updateByExample(@Param("row") CdmsMaterialInvoice row, @Param("example") CdmsMaterialInvoiceExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	int updateByPrimaryKeySelective(CdmsMaterialInvoice row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	int updateByPrimaryKey(CdmsMaterialInvoice row);
}