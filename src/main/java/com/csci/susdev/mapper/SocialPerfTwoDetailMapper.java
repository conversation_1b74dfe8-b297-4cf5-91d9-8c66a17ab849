package com.csci.susdev.mapper;

import com.csci.susdev.model.SocialPerfTwoDetail;
import com.csci.susdev.model.SocialPerfTwoDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SocialPerfTwoDetailMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    long countByExample(SocialPerfTwoDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    int deleteByExample(SocialPerfTwoDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    int insert(SocialPerfTwoDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    int insertSelective(SocialPerfTwoDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    List<SocialPerfTwoDetail> selectByExample(SocialPerfTwoDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    SocialPerfTwoDetail selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") SocialPerfTwoDetail record, @Param("example") SocialPerfTwoDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") SocialPerfTwoDetail record, @Param("example") SocialPerfTwoDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(SocialPerfTwoDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_two_detail
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(SocialPerfTwoDetail record);
}