package com.csci.susdev.mapper;

import com.csci.susdev.model.SocialPerfThreeHead;
import com.csci.susdev.model.SocialPerfThreeHeadExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SocialPerfThreeHeadMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_three_head
     *
     * @mbg.generated Tue Jan 16 12:02:00 HKT 2024
     */
    long countByExample(SocialPerfThreeHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_three_head
     *
     * @mbg.generated Tue Jan 16 12:02:00 HKT 2024
     */
    int deleteByExample(SocialPerfThreeHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_three_head
     *
     * @mbg.generated Tue Jan 16 12:02:00 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_three_head
     *
     * @mbg.generated Tue Jan 16 12:02:00 HKT 2024
     */
    int insert(SocialPerfThreeHead row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_three_head
     *
     * @mbg.generated Tue Jan 16 12:02:00 HKT 2024
     */
    int insertSelective(SocialPerfThreeHead row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_three_head
     *
     * @mbg.generated Tue Jan 16 12:02:00 HKT 2024
     */
    List<SocialPerfThreeHead> selectByExample(SocialPerfThreeHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_three_head
     *
     * @mbg.generated Tue Jan 16 12:02:00 HKT 2024
     */
    SocialPerfThreeHead selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_three_head
     *
     * @mbg.generated Tue Jan 16 12:02:00 HKT 2024
     */
    int updateByExampleSelective(@Param("row") SocialPerfThreeHead row, @Param("example") SocialPerfThreeHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_three_head
     *
     * @mbg.generated Tue Jan 16 12:02:00 HKT 2024
     */
    int updateByExample(@Param("row") SocialPerfThreeHead row, @Param("example") SocialPerfThreeHeadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_three_head
     *
     * @mbg.generated Tue Jan 16 12:02:00 HKT 2024
     */
    int updateByPrimaryKeySelective(SocialPerfThreeHead row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_social_perf_three_head
     *
     * @mbg.generated Tue Jan 16 12:02:00 HKT 2024
     */
    int updateByPrimaryKey(SocialPerfThreeHead row);
}