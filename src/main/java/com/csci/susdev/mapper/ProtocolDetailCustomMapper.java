package com.csci.susdev.mapper;

import com.csci.susdev.model.ProtocolDetail;
import com.csci.susdev.vo.ProtocolDetailVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface ProtocolDetailCustomMapper {

    @Select("""
            <script>
            select * from (
                select pd.id, pd.carbon_emission_location_id as carbonEmissionLocationId,  
                pd.sub_category_id as subCategoryId, pd.description, pd.seq, 
                pd.last_update_version as lastUpdateVersion,
                cel.name as carbonEmissionLocationName, cel.name_sc as carbonEmissionLocationNameSc,
                cel.name_en as carbonEmissionLocationNameEn, p.id as protocolId, p.name as protocolName,
                p.name_sc as protocolNameSc, p.name_en as protocolNameEn,pc.id as categoryId,
                pc.category_name as categoryName, pc.category_name_sc as categoryNameSc,
                pc.category_name_en as categoryNameEn, psc.sub_category_name as subCategoryName,
                psc.sub_category_name_sc as subCategoryNameSc, psc.sub_category_name_en as subCategoryNameEn
                from t_protocol_detail pd
                inner join t_protocol_sub_category psc on pd.sub_category_id = psc.id and psc.is_deleted = 0
                left join t_protocol_category pc on psc.category_id = pc.id and pc.is_deleted = 0
                left join t_protocol p on pc.protocol_id = p.id and p.is_deleted = 0
                left join t_carbon_emission_location cel on pd.carbon_emission_location_id = cel.id and cel.is_deleted = 0
                where pd.is_deleted = 0
                
                <if test="protocolId != null and protocolId != ''">
                and p.id = #{protocolId}
                </if>
                
                <if test="carbonEmissionLocationId != null and carbonEmissionLocationId != ''">
                and cel.id = #{carbonEmissionLocationId}
                </if>
                
            ) t
            
            <if test="orderBy != null and orderBy != ''">
            order by ${orderBy}
            </if>
            
            </script>
            """)
    public List<ProtocolDetailVO> list(@Param("protocolId") String protocolId,
                                       @Param("carbonEmissionLocationId") String carbonEmissionLocationId,
                                       @Param("orderBy") String orderBy);

}
