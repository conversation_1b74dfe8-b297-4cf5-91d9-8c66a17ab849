package com.csci.susdev.mapper;

import com.csci.susdev.vo.OrganizationVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 组织机构持
 * <AUTHOR>
 * @date 20250210
 */
public interface OrganizationCustomMapper {
    /**
     * 分页查询
     * @param listOrganizationId 指定的组织id列表
     * @return
     */
    List<OrganizationVO> listOrganization(@Param("listOrganizationId") List<String> listOrganizationId, String parentId,
                                          String name, @Param("showCompanyOnly") Boolean showCompanyOnly);

}