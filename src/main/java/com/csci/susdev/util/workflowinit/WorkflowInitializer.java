package com.csci.susdev.util.workflowinit;

import com.alibaba.excel.EasyExcel;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.vo.FlowInitDataVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Files;
import java.nio.file.Paths;

public class WorkflowInitializer {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(WorkflowInitializer.class);

    public static void init(String filePath) {
        if (!Files.exists(Paths.get(filePath))) {
            logger.error("流程初始化文件不存在：{}", filePath);
            throw new ServiceException("文件不存在");
        }
        try {
            EasyExcel.read(filePath, FlowInitDataVO.class, new WorkflowDataListener()).sheet().doRead();
        } catch (Exception e) {
            logger.error("流程初始化出错", e);
            throw new ServiceException("流程初始化出错");
        }
    }
}
