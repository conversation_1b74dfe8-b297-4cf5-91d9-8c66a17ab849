package com.csci.susdev.util.orginit;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.csci.common.util.CustomGsonBuilder;
import com.csci.susdev.model.Organization;
import com.csci.susdev.service.OrganizationService;
import com.csci.susdev.service.ServiceHelper;
import com.csci.susdev.util.SpringContextUtil;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class OrganizationListener implements ReadListener<OrganizationData> {

    private final Gson gson = CustomGsonBuilder.createGson();

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(OrganizationListener.class);

    @Override
    public void invoke(OrganizationData data, AnalysisContext context) {
        logger.info("解析到一条数据:{}", gson.toJson(data));
        try {
            if (StringUtils.isBlank(data.getParentName())) {
                initTopOrg(data);
            } else {
                initSubOrg(data);
            }
        } catch (Exception e) {
            logger.error("初始化出错", e);
        }
    }

    public void initTopOrg(OrganizationData data) {
        Organization existedOrg = getService().getOrganizationByName(data.getName());
        if (existedOrg != null) {
            logger.info("组织机构已存在，不再初始化:{}", gson.toJson(data));
            return;
        }
        Organization record = new Organization();
        record.setIsDeleted(false);
        record.setName(data.getName());
        record.setParentId(null);
        record.setNo(getService().generateNo(null));
        getService().insertSelective(record);
    }

    public void initSubOrg(OrganizationData data) {

        Organization existedOrg = ServiceHelper.getOrganizationByNameAndCompanyName(data.getName(), data.getParentName());
        if (existedOrg != null) {
            logger.info("组织机构已存在，不再初始化:{}", gson.toJson(data));
            return;
        }
        Organization parentOrg = getService().getOrganizationByName(data.getParentName());
        if (parentOrg == null) {
            logger.error("父组织机构不存在，无法初始化子组织机构:{}", gson.toJson(data));
            return;
        }
        Organization record = new Organization();
        record.setIsDeleted(false);
        record.setName(data.getName());
        record.setParentId(parentOrg.getId());
        record.setNo(getService().generateNo(parentOrg.getId()));
        getService().insertSelective(record);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }

    private OrganizationService getService() {
        return SpringContextUtil.getBean(OrganizationService.class);
    }
}