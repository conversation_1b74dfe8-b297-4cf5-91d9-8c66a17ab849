package com.csci.susdev.util.factory;


import com.csci.common.util.DateUtils;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;

public class ObjectMapperFactory {
    public ObjectMapperFactory() {
    }

    public static ObjectMapper getInstance(boolean beautifulPrint) {
        return beautifulPrint ? ObjectMapperFactory.SingletonHolder.INSTANCE_BEAUTIFUL_PRINT : ObjectMapperFactory.SingletonHolder.INSTANCE;
    }

    private static class SingletonHolder {
        private static final ObjectMapper INSTANCE = createObjectMapper(false);
        private static final ObjectMapper INSTANCE_BEAUTIFUL_PRINT = createObjectMapper(true);

        private SingletonHolder() {
        }

        private static ObjectMapper createObjectMapper(boolean beautifulPrint) {
            JsonMapper objectMapper = (JsonMapper)((JsonMapper.Builder)((JsonMapper.Builder)((JsonMapper.Builder)((JsonMapper.Builder)JsonMapper.builder().disable(new SerializationFeature[]{SerializationFeature.WRITE_DATES_AS_TIMESTAMPS})).disable(new DeserializationFeature[]{DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE})).disable(new DeserializationFeature[]{DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES})).enable(new MapperFeature[]{MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES})).build();
            if (beautifulPrint) {
                objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
            }

            JavaTimeModule javaTimeModule = new JavaTimeModule();
            javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) {
                public void serialize(LocalDateTime value, JsonGenerator g, SerializerProvider provider) throws IOException {
                    super.serialize(value, g, provider);
                }
            });
            javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd")) {
                public void serialize(LocalDate value, JsonGenerator g, SerializerProvider provider) throws IOException {
                    super.serialize(value, g, provider);
                }
            });
            javaTimeModule.addSerializer(LocalTime.class, new LocalTimeSerializer(DateTimeFormatter.ofPattern("HH:mm:ss")));
            javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) {
                public LocalDateTime deserialize(JsonParser parser, DeserializationContext context) throws IOException {
                    String date = parser.getText();
                    if (StringUtils.isBlank(date)) {
                        return null;
                    } else {
                        LocalDateTime dateTime = DateUtils.toLocalDateTime(date);
                        if (dateTime == null) {
                            dateTime = super.deserialize(parser, context);
                        }

                        return dateTime;
                    }
                }
            });
            javaTimeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd")) {
                public LocalDate deserialize(JsonParser parser, DeserializationContext context) throws IOException {
                    String date = parser.getText();
                    if (StringUtils.isBlank(date)) {
                        return null;
                    } else {
                        LocalDate localDate = (LocalDate)Optional.ofNullable(DateUtils.toLocalDateTime(date)).map(LocalDateTime::toLocalDate).orElse((LocalDate) null);
                        if (localDate == null) {
                            localDate = super.deserialize(parser, context);
                        }

                        return localDate;
                    }
                }
            });
            javaTimeModule.addDeserializer(LocalTime.class, new LocalTimeDeserializer(DateTimeFormatter.ofPattern("HH:mm:ss")));
            javaTimeModule.addSerializer(Date.class, new JsonSerializer<Date>() {
                public void serialize(Date date, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String formattedDate = formatter.format(date);
                    jsonGenerator.writeString(formattedDate);
                }
            });
            javaTimeModule.addDeserializer(Date.class, new JsonDeserializer<Date>() {
                public Date deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String date = jsonParser.getText();

                    try {
                        return format.parse(date);
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                }
            });
            objectMapper.registerModule(javaTimeModule);
            return objectMapper;
        }
    }
}