package com.csci.susdev.util;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.vo.OrganizationTreeVO;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/3/4 11:03
 * @description 构建组织架构 树形结构构建工具类
 */
public class OrganizationTreeBuilder {

    // 构建树形结构入口方法
    public static OrganizationTreeVO buildTree(String rootId, List<OrganizationTreeVO> allNodes) {
        // 创建快速查找的Map
        Map<String, OrganizationTreeVO> nodeMap = new HashMap<>();
        Map<String, List<OrganizationTreeVO>> parentChildMap = new LinkedHashMap<>();

        // 第一次遍历建立索引
        for (OrganizationTreeVO node : allNodes) {
            nodeMap.put(node.getId(), node);
            parentChildMap.computeIfAbsent(node.getParentId(), k -> new ArrayList<>()).add(node);
        }

        // 获取根节点
        OrganizationTreeVO root = nodeMap.get(rootId);
        if (root == null) {
            throw new ServiceException("组织架构根节点不存在");
        }

        // 递归构建子树
        buildSubTree(root, parentChildMap);

        return root;
    }

    private static void buildSubTree(OrganizationTreeVO parentNode,
                                     Map<String, List<OrganizationTreeVO>> parentChildMap) {
        List<OrganizationTreeVO> children = parentChildMap.get(parentNode.getId());
        if (children == null) return;

        // 检测循环引用
        if (children.stream().anyMatch(c -> c.getId().equals(parentNode.getParentId()))) {
            throw new ServiceException("发现循环引用：" + parentNode.getId());
        }

        // 设置子节点并递归
        parentNode.setChildren(children);
        for (OrganizationTreeVO child : children) {
            buildSubTree(child, parentChildMap);
        }
    }
}
