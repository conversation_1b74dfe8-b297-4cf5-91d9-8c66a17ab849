package com.csci.susdev.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.read.metadata.holder.ReadSheetHolder;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.csci.common.exception.ServiceException;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.channels.Channels;
import java.nio.channels.ReadableByteChannel;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 上传文件工具类
 */
public class FileUtil {

    /**
     * 魔数到文件类型的映射集合
     */
    public static final Map<String, String> TYPES = new HashMap<>();

    static {
        TYPES.put("FFD8FFE0", "jpg");
        TYPES.put("89504E47", "png");
        TYPES.put("47494638", "gif");
        TYPES.put("49492A00", "tif");
        TYPES.put("41433130", "dwg"); // CAD
        TYPES.put("38425053", "psd");
        TYPES.put("7B5C7274", "rtf"); // 日记本
        TYPES.put("3C3F786D", "xml");
        TYPES.put("68746D6C", "html");
        TYPES.put("44656C69", "eml"); // 邮件
        TYPES.put("D0CF11E0", "doc");
        TYPES.put("D0CF11E0", "xls");//excel2003版本文件
        TYPES.put("5374616E", "mdb");
        TYPES.put("25215053", "ps");
        TYPES.put("25504446", "pdf");
        TYPES.put("504B0304", "docx");
        TYPES.put("504B0304", "xlsx");//excel2007以上版本文件
        TYPES.put("52617221", "rar");
        TYPES.put("57415645", "wav");
        TYPES.put("41564920", "avi");
        TYPES.put("2E524D46", "rm");
        TYPES.put("000001BA", "mpg");
        TYPES.put("000001B3", "mpg");
        TYPES.put("6D6F6F76", "mov");
        TYPES.put("3026B275", "asf");
        TYPES.put("4D546864", "mid");
    }

    /**
     * 根据文件的字节数据获取文件类型
     * @param filePath 文件路径
     * @return
     */
    public static String getFileType(String filePath) throws IOException {
        //提取前六位作为魔数
        String magicNumberHex = getHex(getFileBytes(filePath), 8);
        return TYPES.get(magicNumberHex);
    }

    /**
     * 根据文件的字节数据获取文件类型
     * @param data 字节数组形式的文件数据
     * @return
     */
    public static String getFileType(byte[] data) throws IOException {
        //提取前六位作为魔数
        String magicNumberHex = getHex(data, 8);
        return TYPES.get(magicNumberHex);
    }

    /**
     * 上传文件(单)
     * @param file 文件对象
     * @param path 保存路径
     * @return
     * @throws IOException
     */
    public static String upload(MultipartFile file, String path) throws IOException {
        String originalFilename = file.getOriginalFilename();// 原文件名称
        String date = new SimpleDateFormat("yyyy/MM").format(new Date()).toString();
        String fileName = date + "/" + UUID.randomUUID().toString().replace("-","") + originalFilename.substring(originalFilename.lastIndexOf("."));
        File dest = new File(path + "/" + fileName);
        if (!dest.getParentFile().exists()){
            dest.setWritable(true);
            dest.getParentFile().mkdirs();
        }
        file.transferTo(dest);
        return fileName;
    }

    /**
     * 上传文件(多)
     * @param files file数组
     * @param path  保存路径
     * @return
     */
    public static List<String> upload(MultipartFile[] files, String path) throws IOException {
        List<String> imagePathList = new ArrayList<>();
        //判断file数组不能为空并且长度大于0
        if (files != null && files.length > 0){
            String date = new SimpleDateFormat("yyyy/MM").format(new Date()).toString();
            // 处理路径并判断目录是否存在
            File dir = new File(path + "/" + date);
            if (!dir.exists()) {
                // 设置写权限
                dir.setWritable(true);
                dir.mkdirs();
            }
            for (MultipartFile file : files){
                if (!file.isEmpty()){
                    // 原文件名称
                    String fileName = file.getOriginalFilename();
                    String newFileName = date + "/" + UUID.randomUUID().toString().replace("-","") + fileName.substring(fileName.lastIndexOf("."));
                    file.transferTo(new File(path + "/" + newFileName));
                    imagePathList.add(newFileName);
                }
            }
        }
        return imagePathList;
    }

    /**
     * 删除文件
     * @param filePath 文件路径
     * @return
     */
    public static boolean delete(String filePath){
        File file = new File(filePath);
        if (file.isFile()){
            return file.delete();
        }
        return false;
    }

    /**
     * 验证文件大小
     * @param fileLen 文件大小
     * @param fileSize 规定文件大小
     * @param fileUnit 单位
     * @return
     */
    public static boolean checkFileSize(Long fileLen, int fileSize, String fileUnit) {
        double fileSizeCom = 0;
        if ("B".equals(fileUnit.toUpperCase())) {
            fileSizeCom = (double) fileLen;
        } else if ("K".equals(fileUnit.toUpperCase())) {
            fileSizeCom = (double) fileLen / 1024;
        } else if ("M".equals(fileUnit.toUpperCase())) {
            fileSizeCom = (double) fileLen / (1024 * 1024);
        } else if ("G".equals(fileUnit.toUpperCase())) {
            fileSizeCom = (double) fileLen / (1024 * 1024 * 1024);
        }
        if (fileSizeCom > fileSize) {
            return false;
        }
        return true;
    }

    /**
     * 读取文件字节数据
     * @param filePath
     * @return
     * @throws IOException
     */
    private static byte[] getFileBytes(String filePath) throws IOException {
        InputStream fs = new FileInputStream(filePath);
        byte[] b = new byte[fs.available()];
        fs.read(b);
        return b;
    }

    /**
     * 获取16进制表示的魔数
     * @param data              字节数组形式的文件数据
     * @param magicNumberLength 魔数长度
     * @return
     */
    public static String getHex(byte[] data, int magicNumberLength) {
        //提取文件的魔数
        StringBuilder magicNumber = new StringBuilder();
        //一个字节对应魔数的两位
        int magicNumberByteLength = magicNumberLength / 2;
        for (int i = 0; i < magicNumberByteLength; i++) {
            magicNumber.append(Integer.toHexString(data[i] >> 4 & 0xF));
            magicNumber.append(Integer.toHexString(data[i] & 0xF));
        }
        return magicNumber.toString().toUpperCase();
    }


    //取得副檔名
    public static String getExtension(String filename, String orElse) {
        return Optional.ofNullable(filename)
                .filter(s -> s.contains("."))
                .map(s -> s.substring(s.lastIndexOf(".") + 1))
                .orElse(orElse).toLowerCase();
    }

    /**
     * 1.得到该路径下的所有文件路径
     */
    public static List<String> getAllFilePath(String path, List<String> allFilePath) {
        if (allFilePath == null) {
            allFilePath = new ArrayList<String>();
        }
        File file = new File(path);
        if (!file.isDirectory()) {
            allFilePath.add(path);
            return allFilePath;
        } else if (file.isDirectory()) {
            String[] fileList = file.list();
            for (String fileName : fileList) {
                String subFileName = path + File.separator + fileName;
                File subFile = new File(subFileName);
                if (!subFile.isDirectory()) {
                    allFilePath.add(subFileName);
                } else if (subFile.isDirectory()) {
                    getAllFilePath(subFileName, allFilePath);
                }
            }
            return allFilePath;
        }
        return null;
    }

    /**
     * 2.得到该路径下的所有文件夹路径
     * @param path
     * @param allFolderPath
     * @return
     */
    public static List<String> getAllFolderPath(String path, List<String> allFolderPath) {
        if (allFolderPath == null) {
            allFolderPath = new ArrayList<String>();
        }
        File file = new File(path);
        if (!file.isDirectory()) {
            return allFolderPath;
        } else if (file.isDirectory()) {
            String[] fileList = file.list();
            for (String fileName : fileList) {
                String subFilePath = path + File.separator + fileName;
                File subFile = new File(subFilePath);
                if (!subFile.isDirectory()) {
                    continue;
                } else if (subFile.isDirectory()) {
                    allFolderPath.add(subFilePath);
                    getAllFolderPath(subFilePath, allFolderPath);
                }
            }
            return allFolderPath;
        }
        return null;
    }

    public static void downloadFiles(List<String> filePaths) throws MalformedURLException {
        List<File> files = new ArrayList<>();
        for (String filePath : filePaths) {
            files.add(new File(filePath));
        }
        for (File file : files) {
            if (file.isFile()) {
                // 判断是否为文件
                String fileUrl = "文件下载链接";
                URL url = new URL(fileUrl);
                // 使用文件名作为下载的保存路径
                String savePath = file.getName();
                Path outputPath = Paths.get(savePath);
                // 下载文件
                try (InputStream in = url.openStream();
                     ReadableByteChannel channel = Channels.newChannel(in);
                     FileOutputStream out = new FileOutputStream(outputPath.toFile())
                ) {
                    out.getChannel().transferFrom(channel, 0, Long.MAX_VALUE);
                    System.out.println("文件下载成功：" + file.getName());
                } catch (IOException e) {
                    System.out.println("文件下载失败：" + file.getName()); e.printStackTrace();
                }
            }
        }
    }

    /**
     * 编码文件名
     * 日期路径 + UUID
     * 示例：fileName=2022_11_18/统计报表1668758006562.txt
     */
    public static final String extractUploadFilename(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        // 注意，这里需要加上 \\ 将 特殊字符 . 转意 \\. ,否则异常
        String[] fileArray = fileName.split("\\.");
        fileName = datePath() + "/" + fileArray[0] + System.currentTimeMillis() + "." + fileArray[1];
        return fileName;
    }

    /**
     * 日期路径 即年_月_日 如2024_12_18
     */
    public static final String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy_MM_dd");
    }

    /**
     * 根据文件读取excel 转成Java对象集合
     */
    public static Map<String, List<?>> readExcel(MultipartFile file, Map<Integer, Class<?>> sheetClasses) {
        // 检查文件是否为空
        if (file.isEmpty()) {
            throw new IllegalArgumentException("文件为空，请上传有效的文件");
        }
        // 用于存储每个 Sheet 页的数据
        Map<String, List<?>> result = new HashMap<>();
        try {
            // 动态设置 excelType
            ExcelTypeEnum excelType = getExcelType(file);

            // 遍历每个 Sheet 页
            for (Map.Entry<Integer, Class<?>> entry : sheetClasses.entrySet()) {
                int sheetIndex = entry.getKey();
                Class<?> clazz = entry.getValue();

                // 用于存储当前 Sheet 页的数据
                List<Object> dataList = new ArrayList<>();

                // 创建 ExcelReader
                InputStream inputStream = file.getInputStream();
                ExcelReader reader = EasyExcel.read(inputStream)
                        .excelType(excelType)
                        .registerReadListener(new AnalysisEventListener<Object>() {
                            @Override
                            public void invoke(Object data, AnalysisContext context) {
                                // 将每行数据添加到集合中
                                dataList.add(data);
                            }

                            @Override
                            public void doAfterAllAnalysed(AnalysisContext context) {
                                // 当前 Sheet 页读取完成
                                ReadSheetHolder sheetHolder = context.readSheetHolder();
                                System.out.println("Sheet 页 " + sheetHolder.getSheetName() + " 读取完成");
                            }
                        })
                        .build();
                // 读取当前 Sheet 页
                ReadSheet readSheet = EasyExcel.readSheet(sheetIndex).head(clazz).build();
                reader.read(readSheet);

                // 将当前 Sheet 页的数据存入结果集
                result.put("Sheet" + (sheetIndex + 1), dataList);
            }
        }catch (Exception e) {
            throw new ServiceException("fail to parse Excel file: " + e.getMessage());
        }
        return result;
    }

    /**
     * 动态判断 Excel 文件格式
     */
    public static ExcelTypeEnum getExcelType(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        if (fileName != null && fileName.endsWith(".xls")) {
            return ExcelTypeEnum.XLS;
        } else {
            return ExcelTypeEnum.XLSX;
        }
    }
}