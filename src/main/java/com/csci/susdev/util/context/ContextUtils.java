package com.csci.susdev.util.context;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.service.DCdmsElectricityCarbonFactorService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.susdev.vo.UserBasicVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 上下文帮助类
 * <p>
 *
 * <AUTHOR> terrytao
 * @date : 10/12/2019
 */
public class ContextUtils {
    /**
     * 获取当前用户信息
     *
     * @return
     */
    public static UserInfo getCurrentUser() {
        IUserPrincipal userPrincipal = Optional.ofNullable(RequestContextManager.getCurrent()).map(IRequestContext::getCurrentUser)
                .orElseGet(ContextUtils::getSystemUser);
        if (userPrincipal instanceof UserInfo) {
            return (UserInfo) userPrincipal;
        } else {
            throw new ServiceException("获取当前用户信息失败");
        }
    }

    /**
     * 获取当前线程上下文中存放的值
     *
     * @param key
     * @return
     */
    public static Object getContextValue(String key) {
        return Optional.ofNullable(RequestContextManager.getCurrent()).map(IRequestContext::getContext).map(x -> x.get(key)).orElse(null);
    }

    public static UserInfo getSystemUser() {
        UserInfo userInfo = new UserInfo();
        userInfo.setId("00000000-0000-0000-0000-000000000000");
        userInfo.setRoles(new ArrayList<>());
        userInfo.setName("System User");
        userInfo.setUsername("SystemUser");
        return userInfo;
    }

    /**
     * 判断当前用户是否是超级管理员
     *
     * @return
     */
    public static boolean isSuperAdmin() {
        List<String> roles = getCurrentUser().getRoles();
        if (CollectionUtils.isEmpty(roles)) {
            return false;
        }
        for (String role : roles) {
            if (StringUtils.equalsIgnoreCase(role, "SuperAdmin")) {
                return true;
            }
        }
        return false;
    }

}
