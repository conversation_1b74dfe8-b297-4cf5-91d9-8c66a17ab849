package com.csci.susdev.util.context;

import org.springframework.core.NamedInheritableThreadLocal;

/**
 * 管理上下文
 * <p>
 *
 * <AUTHOR> terrytao
 * @date : 10/12/2019
 */
public class RequestContextManager {

    private static final ThreadLocal<IRequestContext> namedThreadLocal = new NamedInheritableThreadLocal<>("local-contextInfo");

    public static IRequestContext getCurrent() {
        return namedThreadLocal.get();
    }

    public static void setCurrent(IRequestContext requestContext) {
        namedThreadLocal.set(requestContext);
    }

    public static void removeCurrent() {
        namedThreadLocal.remove();
    }
}
