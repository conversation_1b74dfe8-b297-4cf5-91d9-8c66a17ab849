package com.csci.susdev.util;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.util.factory.ObjectMapperFactory;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

public class CommonUtil {

    private static final ObjectMapper objectMapper = ObjectMapperFactory.getInstance(false);
    private static final ObjectMapper objectMapperBeautifulPrint = ObjectMapperFactory.getInstance(true);

    public static String toJson(Object o) {
        return toJson(o, false);
    }

    public static String toJson(Object o, boolean beautifulPrint) {
        try {
            return beautifulPrint ? objectMapperBeautifulPrint.writeValueAsString(o) : objectMapper.writeValueAsString(o);
        } catch (JsonProcessingException e) {
            throw new ServiceException("转换对象为json出错", e);
        }
    }
}
