package com.csci.susdev.constant;

public enum WorkflowOperateType {

    SUBMIT(1, "提交"),
    APPROVE(2, "审批"),
    REJECT(3, "驳回"),
    CANCEL(4, "取消"),
    DELETE(5, "删除"),
    SUSPEND(6, "挂起"),
    TERMINATE(7, "终止"),
    RECALL(8, "撤回"),
    RECOVER(9, "恢复");

    private final Integer code;

    private final String name;

    WorkflowOperateType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
