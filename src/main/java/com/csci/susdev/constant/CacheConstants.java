package com.csci.susdev.constant;

/**
 * 缓存的key 常量
 */
public class CacheConstants {
    /**
     * 查询能源使用排行榜
     */
    public static final String ENERGY_USE_RANKING = "tzh_bs_ambient:energy_use_ranking:";

    /**
     * 废弃物产生量分布统计
     */
    public static final String Waste_Generation_Distribution_Statistics = "tzh_bs_ambient:waste_generation_distribution_statistics:";

    /**
     * 查询碳排放（按年统计，1+5年）
     */
    public static final String CARBON_EMISSION_YEAR_SUMMARY = "tzh_bs_ambient:carbon_emission_year_summary:";

    /**
     * 查询原材料使用占比统计
     */
    public static final String RAW_MATERIAL_USE_PROPORTION = "tzh_bs_ambient:raw_material_use_proportion:";

    /**
     * 查询资源使用趋势统计
     */
    public static final String RESOURCE_USE_TREND_STATISTICS = "tzh_bs_ambient:resource_use_trend_statistics:";

    /**
     * 查询各类别密度
     */
    public static final String CATEGORY_DENSITY = "tzh_bs_ambient:category_density:";

    /**
     * 查询地图各省份数据
     */
    public static final String MAP_PROVINCE_DATA = "tzh_bs_ambient:map_province_data:";

    /**
     * 查询用水与污染
     */
    public static final String WATER_USE_AND_POLLUTION = "tzh_bs_ambient:water_use_and_pollution:";

    /**
     * 查询项目分布
     */
    public static final String PROJECT_DISTRIBUTION = "tzh_bs_ambient:project_distribution:";

    /**
     * 查询电力使用排行榜
     */
    public static final String ELECTRICITY_USE_RANKING = "tzh_bs_ambient:electricity_use_ranking:";

    /**
     * 查询水资源使用排行榜
     */
    public static final String WATER_RESOURCES_USE_RANKING = "tzh_bs_ambient:water_resources_use_ranking:";

    /**
     * 查询温室气体排放总量排行榜
     */
    public static final String GREENHOUSE_GAS_EMISSIONS_TOTAL_RANKING = "tzh_bs_ambient:greenhouse_gas_emissions_total_ranking:";


    /**
     * 查询温室气体排放量总计
     */
    public static final String GREENHOUSE_GAS_EMISSIONS_TOTAL = "tzh_bs_ambient:greenhouse_gas_emissions_total:";

    /**
     * 查询碳排放强度
     */
    public static final String CARBON_EMISSION_INTENSITY = "tzh_bs_ambient:carbon_emission_intensity:";

    /**
     * OCR智能体会话缓存key
     */
    public static final String OCR_AGENT_CONVERSATION = "ocr:agent:conversation:";



}
