package com.csci.susdev.constant;

/**
 * 本项目中的通用常量
 *
 * <AUTHOR>
 * @date 9/19/2019
 */
public interface SusDevConsts {

    String AUTH_TOKEN_KEY = "AuthTokenKey";

    /**
     * 常用连接池大小
     */
    int COMMON_POOL_SIZE = Runtime.getRuntime().availableProcessors() * 2;

    interface CommonField {
        String ID = "id";
        String CREATION_TIME = "creationTime";
        String CREATE_USER_ID = "createUserId";
        String CREATE_USERNAME = "createUsername";
        String LAST_UPDATE_TIME = "lastUpdateTime";
        String LAST_UPDATE_USER_ID = "lastUpdateUserId";
        String LAST_UPDATE_USERNAME = "lastUpdateUsername";
        String LAST_UPDATE_VERSION = "lastUpdateVersion";
    }

    /**
     * http请求响应状态
     */
    interface RespStatus {
        /**
         * 接口操作成功返回code
         */
        int SUCCESS = 0;
        /**
         * 接口操作失败返回code
         */
        int FAIL = -1;
    }

    /**
     * 分页查询默认配置
     */
    interface PageDefault {
        /**
         * 默认查询页数
         */
        int START_PAGE = 1;
        /**
         * 默认查询每页条数
         */
        int PAGE_SIZE = 15;
    }

    /**
     * 消息格式
     */
    interface MessageFormat {
        String TEXT = "text";
        String XML = "xml";
        String JSON = "json";
    }

    /**
     * 消息状态
     */
    interface MessageStatus {

        /**
         * 草稿
         */
        Integer DRAFT = 0;

        /**
         * 新建
         */
        Integer NEW = 1;
    }

    /**
     * 紧急程度
     */
    interface UrgencyLevel {

        /**
         * 紧急
         */
        Integer URGENT = 1;

        /**
         * 上班时间
         */
        Integer WorkingTime = 2;

        /**
         * 下班时间
         */
        Integer OffWorkTime = 3;

    }

    String HEADER_TOKEN_KEY = "x-auth-token";

    String Menu_Route_Path = "Menu-Route-Path";

    String External_App_Id = "External-App-Id";

    String External_App_Key = "External-App-Key";

    /**
     * 项目前缀
     */
    String PROJECT_PREFIX = "sus-dev";
    /**
     * 分别表示前缀:业务:组织ID:年:月
     * 如SusDevConsts.PROJECT_PREFIX + "ambient:getOrInit"  + "-" + organizationId  + "-" + year + "-" + month
     */
    String DISTRIBUTE_LOCK_KEY_FORMAT = "{}:{}:{}:{}:{}";

    interface FormCode {
        /**
         * 社会绩效一
         */
        String social_perf_one = "sociology-index-one";

        /**
         * 社会绩效二
         */
        String social_perf_two = "sociology-index-two";

        /**
         * 环境绩效
         */
        String ambient = "ambient";
    }

}
