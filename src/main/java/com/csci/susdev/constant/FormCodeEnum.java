package com.csci.susdev.constant;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.csci.common.exception.ServiceException;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum FormCodeEnum {

    fixed("ff-cm-fixed", "分判商固定源"),
    ambient("ambient", "環境積效"),
    mobile("ff-cm-mobile", "分判商移動源");
    @EnumValue
    private String code;
    @JsonValue
    private String desc;
    public static FormCodeEnum getEnumByCode(String code) {
        if (code == null) {
            return null;
        }
        for (FormCodeEnum value : FormCodeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new ServiceException("FormCode not found, code: " + code);
    }

    public static String getCodeByDesc(String desc) {
        for (FormCodeEnum formCode : FormCodeEnum.values()) {
            if (StringUtils.equals(formCode.desc, desc)) {
                return formCode.code;
            }
        }
        return null; // 或抛出 IllegalArgumentException
    }


}
