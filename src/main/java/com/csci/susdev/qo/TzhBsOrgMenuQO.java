package com.csci.susdev.qo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.UUID;

@Data
@Accessors(chain = true)
@SuperBuilder
@NoArgsConstructor
public class TzhBsOrgMenuQO implements Serializable {
    private String id;
    private String organizationName;
    /**
     * 机构id
     */
    @NotBlank(message = "机构id不能为空")
    private String organizationId;

    /**
     * 协议id
     */
    @NotBlank(message = "协议id不能为空")
    private String protocolId;

    /**
     * 模块 1:esg,2:碳中和
     */
    @NotBlank(message = "模块不能为空")
    private String module;

    /**
     * 菜单id
     */
    @NotBlank(message = "菜单id不能为空")
    private String menuId;

    /**
     * 备注
     */
    @Size(max = 255,message = "备注最大长度为255")
    private String remark;

    /**
     * 排序
     */
    @Min(value = 0,message = "排序最小值为0")
    @NotNull(message = "排序不能为空")
    private Integer seq;

    /**
     * 逻辑删除，1为删除
     */
    @NotNull
    private boolean isDeleted;

}
