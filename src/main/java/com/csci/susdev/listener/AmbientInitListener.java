package com.csci.susdev.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.csci.common.util.CustomGsonBuilder;
import com.csci.susdev.model.AmbientDetail;
import com.csci.susdev.service.AmbientDetailService;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.vo.AmbientInitDataVO;
import com.google.gson.Gson;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

public class AmbientInitListener implements ReadListener<AmbientInitDataVO> {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(AmbientInitListener.class);

    private final Gson gson = CustomGsonBuilder.createGson();

    private final String headId;

    private String lastCategory;

    private int seq = 1;

    public AmbientInitListener(String headId) {
        this.headId = headId;
    }

    @Override
    public void invoke(AmbientInitDataVO data, AnalysisContext context) {
        logger.info("解析到一条数据:{}", gson.toJson(data));
        if (StringUtils.isNotBlank(data.getCategory())) {
            lastCategory = data.getCategory();
        }

        AmbientDetailService ambientDetailService = SpringContextUtil.getBean(AmbientDetailService.class);
        AmbientDetail ambientDetail = new AmbientDetail();
        BeanUtils.copyProperties(data, ambientDetail);
        ambientDetail.setHeadId(headId);
        ambientDetail.setSeq(seq++);
        if (StringUtils.isBlank(ambientDetail.getCategory())) {
            ambientDetail.setCategory(lastCategory);
        }
        ambientDetail.setCategoryDigest(DigestUtils.md5Hex(ambientDetail.getCategory()));
        ambientDetailService.insertSelective(ambientDetail);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }
}
