package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.FcMaterialFactorMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.FcMaterialFactorConverter;
import com.csci.susdev.qo.FcMaterialFactorQO;
import com.csci.susdev.util.NamingConventionUtils;
import com.csci.susdev.util.SimpTradUtil;
import com.csci.susdev.vo.FcMaterialFactorVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class FcMaterialFactorService {

    @Resource
    private FcMaterialFactorMapper fcMaterialFactorMapper;

    @Resource
    private ProtocolConfigurationService protocolConfigurationService;


    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(FcMaterialFactor record) {
        return fcMaterialFactorMapper.insertSelective(record);
    }

    public FcMaterialFactor selectByPrimaryKey(String id) {
        return fcMaterialFactorMapper.selectByPrimaryKey(id);
    }

    public FcMaterialFactorVO getFcMaterialFactor(String id) {
        checkExist(id, "id不能为空");
        FcMaterialFactor fcMaterialFactor = selectByPrimaryKey(id);
        checkExist(fcMaterialFactor, "未找到对应的记录");
        if(fcMaterialFactor.getIsDeleted()) {
            throw new ServiceException("紀錄已被刪除");
        }
        return FcMaterialFactorConverter.convertToVO(fcMaterialFactor);
    }


    @Transactional(rollbackFor = Exception.class)
    public String saveFcMaterialFactor(FcMaterialFactorVO fcMaterialFactorVO) {
        if (StringUtils.isBlank(fcMaterialFactorVO.getId())) {
            // 新增
            return doAdd(fcMaterialFactorVO);
        } else {
            checkExist(fcMaterialFactorVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(fcMaterialFactorVO);
        }

    }

    public ResultPage<FcMaterialFactorVO> listFcMaterialFactor(FcMaterialFactorQO fcMaterialFactorQO) {
        FcMaterialFactorExample example = new FcMaterialFactorExample();

        SimpTradUtil simpTradUtil = new SimpTradUtil();
        String chineseNameTc = simpTradUtil.convert2Trad(fcMaterialFactorQO.getChineseName());
        String chineseNameSc = simpTradUtil.convert2Simp(fcMaterialFactorQO.getChineseName());

        if(StringUtils.isNotBlank(fcMaterialFactorQO.getChineseName())) {
            example.or().andChineseNameLike("%" + chineseNameTc + "%").andIsDeletedEqualTo(false);
            example.or().andChineseNameLike("%" + chineseNameSc + "%").andIsDeletedEqualTo(false);
        } else {
            example.or().andIsDeletedEqualTo(false);
        }

        if (StringUtils.isNotBlank(fcMaterialFactorQO.getOrderBy())) {
            example.setOrderByClause(NamingConventionUtils.snakeCaseToCamelCase(fcMaterialFactorQO.getOrderBy()));
        }else {
            example.setOrderByClause("creation_time desc");
        }

        PageHelper.startPage(fcMaterialFactorQO.getCurPage(), fcMaterialFactorQO.getPageSize());
        List<FcMaterialFactor> fcMaterialFactors = fcMaterialFactorMapper.selectByExample(example);
        return new ResultPage<>(fcMaterialFactors, fcMaterialFactors.stream().map(FcMaterialFactorConverter::convertToVO).collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteFcMaterialFactor(String id) {
        ProtocolConfigurationExample configurationExample = new ProtocolConfigurationExample();
        configurationExample.or().andFcFactorIdEqualTo(id).andIsDeletedEqualTo(false).andFcFactorTypeEqualTo("物料因子");
        List<ProtocolConfiguration> protocolConfigurations = protocolConfigurationService.selectByExample(configurationExample);
        if (CollectionUtils.isNotEmpty(protocolConfigurations)) {
            throw new ServiceException("已在协议配置中被使用，不能删除！");
        }

        FcMaterialFactor record = selectByPrimaryKey(id);
        record.setIsDeleted(true);
        fcMaterialFactorMapper.updateByPrimaryKeySelective(record);
    }

    private String doAdd(FcMaterialFactorVO fcMaterialFactorVO) {
        FcMaterialFactor fcMaterialFactor = FcMaterialFactorConverter.convertToModel(fcMaterialFactorVO);
        fcMaterialFactorMapper.insertSelective(fcMaterialFactor);
        return fcMaterialFactor.getId();
    }

    private String doUpdate(FcMaterialFactorVO fcMaterialFactorVO) {
        FcMaterialFactor originalRecord = selectByPrimaryKey(fcMaterialFactorVO.getId());
        FcMaterialFactor fcMaterialFactor = FcMaterialFactorConverter.convertToModelWithBase(fcMaterialFactorVO, originalRecord);

        FcMaterialFactorExample example = new FcMaterialFactorExample();
        example.or().andIdEqualTo(fcMaterialFactorVO.getId()).andLastUpdateVersionEqualTo(fcMaterialFactorVO.getLastUpdateVersion());
        int updateCount = fcMaterialFactorMapper.updateByExample(fcMaterialFactor, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return fcMaterialFactor.getId();
    }

    public List<FcMaterialFactor> selectByExample(FcMaterialFactorExample example) {
        return fcMaterialFactorMapper.selectByExample(example);
    }
}
