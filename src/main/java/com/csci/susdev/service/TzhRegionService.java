package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.tzh.mapper.TzhRegionMapper;
import com.csci.tzh.model.TzhRegion;
import com.csci.tzh.model.TzhRegionExample;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class TzhRegionService {

	@Autowired
	private TzhRegionMapper mapper;

	public List<TzhRegion> selectByExample(TzhRegionExample example) {
		return mapper.selectByExample(example);
	}

	public List<TzhRegion> list() {
		TzhRegionExample example = new TzhRegionExample();

		TzhRegionExample.Criteria criteria = example.or();
		example.setOrderByClause("Name");

		List<TzhRegion> lst = mapper.selectByExample(example);
		return lst;
	}

	public TzhRegion get(String id) {
		TzhRegionExample example = new TzhRegionExample();

		TzhRegionExample.Criteria criteria = example.or();
		criteria.andIdEqualTo(id);

		List<TzhRegion> lst = mapper.selectByExample(example);
		if(lst.size() > 0) {
			return lst.get(0);
		} else {
			return null;
		}
	}
}
