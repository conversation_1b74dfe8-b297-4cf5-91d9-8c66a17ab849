package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.tzh.mapper.TzhRpaAccountCustomMapper;
import com.csci.tzh.mapper.TzhRpaAccountMapper;
import com.csci.tzh.model.*;
import com.csci.tzh.model.TzhRpaAccountExample.Criteria;
import com.csci.tzh.qo.TzhRpaAccountPageableQO;
import com.csci.tzh.vo.TzhRpaAccountVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class TzhRpaAccountService {

	@Autowired
	private TzhRpaAccountMapper mapper;

	@Autowired
	private TzhRpaAccountCustomMapper customMapper;

	public List<TzhRpaAccount> selectByExample(TzhRpaAccountExample example) {
		return mapper.selectByExample(example);
	}

	public ResultPage<TzhRpaAccountVO> list(TzhRpaAccountPageableQO qo) {
		List<TzhRpaAccountVO> lst = customMapper.list(qo.getProjectName(), qo.getAccountType());
		ResultPage<TzhRpaAccountVO> resultPage = new ResultPage<>(lst, true);
		return resultPage;
	}

	@Transactional(rollbackFor = Exception.class)
	public TzhRpaAccount save(TzhRpaAccount newModel) {
		UserInfo currentUser = ContextUtils.getCurrentUser();

		if (newModel.getIsdeleted() == false) {
			if (StringUtils.isNotBlank(newModel.getId())) {
				// 備份已刪除數據
				TzhRpaAccountExample originalExample = new TzhRpaAccountExample();
				TzhRpaAccountExample.Criteria originalCriteria = originalExample.or();
				originalCriteria.andIdEqualTo(newModel.getId());
				List<TzhRpaAccount> lstOriginalModel = mapper.selectByExample(originalExample);
				if (lstOriginalModel.size() > 0) {
					TzhRpaAccount originalModel = lstOriginalModel.get(0);
					originalModel.setId(UUID.randomUUID().toString());
					originalModel.setIsdeleted(true);
					originalModel.setDeletedby(currentUser.getUsername());
					originalModel.setDeletedtime(LocalDateTime.now());
					mapper.insertSelective(originalModel);
				}

				// 新增數據(保留舊ID)
				TzhRpaAccountExample newExample = new TzhRpaAccountExample();
				TzhRpaAccountExample.Criteria newCriteria = newExample.or();
				newCriteria.andIdEqualTo(newModel.getId());
				newCriteria.andIsdeletedEqualTo(false);
				newModel.setCreatedby(currentUser.getUsername());
				newModel.setCreatedtime(LocalDateTime.now());
				newModel.setDeletedby(null);
				newModel.setDeletedtime(null);
				mapper.deleteByExample(newExample);
				mapper.insertSelective(newModel);
			} else {
				// 新增數據(新建ID)
				newModel.setId(UUID.randomUUID().toString());
				newModel.setCreatedby(currentUser.getUsername());
				newModel.setCreatedtime(LocalDateTime.now());
				mapper.insertSelective(newModel);
			}
		} else {
			// 刪除數據
			TzhRpaAccountExample example = new TzhRpaAccountExample();
			TzhRpaAccountExample.Criteria criteria = example.or();
			criteria.andIdEqualTo(newModel.getId());
			newModel.setIsdeleted(true);
			newModel.setDeletedby(currentUser.getUsername());
			newModel.setDeletedtime(LocalDateTime.now());
			mapper.updateByExampleSelective(newModel, example);
		}

		return newModel;
	}
}
