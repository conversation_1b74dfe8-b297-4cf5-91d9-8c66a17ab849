package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.CarbonEmissionLocationMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.CarbonEmissionLocationConverter;
import com.csci.susdev.qo.CarbonEmissionLocationQO;
import com.csci.susdev.util.NamingConventionUtils;
import com.csci.susdev.util.SimpTradUtil;
import com.csci.susdev.vo.CarbonEmissionLocationVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class CarbonEmissionLocationService {

    @Resource
    private CarbonEmissionLocationMapper carbonEmissionLocationMapper;

    @Resource
    private ProtocolDetailService protocolDetailService;



    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(CarbonEmissionLocation record) {
        return carbonEmissionLocationMapper.insertSelective(record);
    }

    public CarbonEmissionLocation selectByPrimaryKey(String id) {
        return carbonEmissionLocationMapper.selectByPrimaryKey(id);
    }

    public CarbonEmissionLocationVO getCarbonEmissionLocation(String id) {
        checkExist(id, "id不能为空");
        CarbonEmissionLocation carbonEmissionLocation = selectByPrimaryKey(id);
        checkExist(carbonEmissionLocation, "未找到对应的记录");
        if(carbonEmissionLocation.getIsDeleted()) {
            throw new ServiceException("紀錄已被刪除");
        }
        return CarbonEmissionLocationConverter.convertToVO(carbonEmissionLocation);
    }


    @Transactional(rollbackFor = Exception.class)
    public String saveCarbonEmissionLocation(CarbonEmissionLocationVO carbonEmissionLocationVO) {
        if (StringUtils.isBlank(carbonEmissionLocationVO.getId())) {
            // 新增
            return doAdd(carbonEmissionLocationVO);
        } else {
            checkExist(carbonEmissionLocationVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(carbonEmissionLocationVO);
        }
    }

    public ResultPage<CarbonEmissionLocationVO> listCarbonEmissionLocation(CarbonEmissionLocationQO carbonEmissionLocationQO) {
        CarbonEmissionLocationExample example = new CarbonEmissionLocationExample();

        SimpTradUtil simpTradUtil = new SimpTradUtil();
        String nameTc = simpTradUtil.convert2Trad(carbonEmissionLocationQO.getName());
        String nameSc = simpTradUtil.convert2Simp(carbonEmissionLocationQO.getName());

        if(StringUtils.isNotBlank(carbonEmissionLocationQO.getName())) {
            example.or().andNameLike("%" + nameTc + "%").andIsDeletedEqualTo(false);
            example.or().andNameScLike("%" + nameSc + "%").andIsDeletedEqualTo(false);
            example.or().andNameEnLike("%" + carbonEmissionLocationQO.getName() + "%").andIsDeletedEqualTo(false);
        } else {
            example.or().andIsDeletedEqualTo(false);
        }

        if (StringUtils.isNotBlank(carbonEmissionLocationQO.getOrderBy())) {
            example.setOrderByClause(NamingConventionUtils.snakeCaseToCamelCase(carbonEmissionLocationQO.getOrderBy()));
        }

        PageHelper.startPage(carbonEmissionLocationQO.getCurPage(), carbonEmissionLocationQO.getPageSize());
        List<CarbonEmissionLocation> carbonEmissionLocations = carbonEmissionLocationMapper.selectByExample(example);
        return new ResultPage<>(carbonEmissionLocations, carbonEmissionLocations.stream().map(CarbonEmissionLocationConverter::convertToVO).collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteCarbonEmissionLocation(String id) {
        CarbonEmissionLocation record = selectByPrimaryKey(id);

        ProtocolDetailExample example = new ProtocolDetailExample();
        example.or().andCarbonEmissionLocationIdEqualTo(id).andIsDeletedEqualTo(false);
        List<ProtocolDetail> protocolDetails = protocolDetailService.selectByExample(example);
        if(protocolDetails.size() > 0) {
            throw new ServiceException("已在協議明細中被使用，不能刪除");
        }

        record.setIsDeleted(true);
        carbonEmissionLocationMapper.updateByPrimaryKeySelective(record);
    }

    private String doAdd(CarbonEmissionLocationVO carbonEmissionLocationVO) {
        CarbonEmissionLocation carbonEmissionLocation = CarbonEmissionLocationConverter.convertToModel(carbonEmissionLocationVO);
        carbonEmissionLocationMapper.insertSelective(carbonEmissionLocation);
        return carbonEmissionLocation.getId();
    }

    private String doUpdate(CarbonEmissionLocationVO carbonEmissionLocationVO) {
        CarbonEmissionLocation originalRecord = selectByPrimaryKey(carbonEmissionLocationVO.getId());
        CarbonEmissionLocation carbonEmissionLocation = CarbonEmissionLocationConverter.convertToModelWithBase(carbonEmissionLocationVO, originalRecord);

        CarbonEmissionLocationExample example = new CarbonEmissionLocationExample();
        example.or().andIdEqualTo(carbonEmissionLocationVO.getId()).andLastUpdateVersionEqualTo(carbonEmissionLocationVO.getLastUpdateVersion());
        int updateCount = carbonEmissionLocationMapper.updateByExample(carbonEmissionLocation, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return carbonEmissionLocation.getId();
    }

    public List<CarbonEmissionLocation> selectByExample(CarbonEmissionLocationExample example) {
        return carbonEmissionLocationMapper.selectByExample(example);
    }
}
