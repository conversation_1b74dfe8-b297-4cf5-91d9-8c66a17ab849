package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.tzh.mapper.AmbientDataIntegrationResultCustomMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
@LogMethod
@DS(DatasourceContextEnum.TANZHONGHE)
public class AmbientDataIntegrationService {

    @Resource
    private AmbientDataIntegrationResultCustomMapper ambientDataIntegrationResultCustomMapper;

    public List<Map<String, String>> getDataIntegrationResult(String organizationId, Integer year) {
        return ambientDataIntegrationResultCustomMapper.list(organizationId, year);
    }
}
