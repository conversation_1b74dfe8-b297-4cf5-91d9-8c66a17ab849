package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.common.model.PageableVO;
import com.csci.susdev.mapper.*;
import com.csci.susdev.model.*;
import com.csci.susdev.util.DateUtils;
import com.csci.susdev.util.DemoUtils;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.vo.WorkflowNodeVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ServiceHelper {

    /**
     * 根據 號碼 查詢指定 組織 数据
     *
     * @param no
     * @return
     */
    public static Organization getOrganizationByNo(String no,OrganizationMapper organizationMapper) {
    	OrganizationExample example = new OrganizationExample();
    	example.or().andNoEqualTo(no);
        return organizationMapper.selectByExample(example).stream().findFirst().orElse(null);
    }
	
    /**
     * 查询指定用户名的用户记录，排除已删除（删除标志为true）的记录
     *
     * @param username   用户名，可以是域账号，也可以是没有域前缀的账号
     * @param userMapper 数据库操作对象
     * @return
     */
    public static User getUserByUsername(String username, UserMapper userMapper) {
        // 如果username是一个域账号，将前缀移除
        Map.Entry<String, String> entry = DemoUtils.splitDomainUsername(username);
        UserExample userExample = new UserExample();
        userExample.or().andUsernameEqualTo(entry.getValue());
        return userMapper.selectByExample(userExample).stream().findFirst().orElse(null);
    }

    public static User getUserByUsername(String username) {
        return getUserByUsername(username, SpringContextUtil.getBean(UserMapper.class));
    }

    public static Organization getOrganizationByName(String name) {
        OrganizationMapper organizationMapper = SpringContextUtil.getBean(OrganizationMapper.class);
        OrganizationExample example = new OrganizationExample();
        example.or().andNameEqualTo(name).andIsDeletedEqualTo(false);
        List<Organization> organizationList = organizationMapper.selectByExample(example);
        if (organizationList.size() > 1) {
            throw new ServiceException("组织机构名称重复");
        }
        if (organizationList.size() > 0) {
            return organizationList.get(0);
        }
        return null;
    }

    /**
     * 查询指定角色記錄
     *
     * @param code
     * @return
     */
    public static Role getRoleByCode(String code) {
        RoleMapper roleMapper = SpringContextUtil.getBean(RoleMapper.class);
        RoleExample roleExample = new RoleExample();
        roleExample.or().andCodeEqualTo(code);
        return roleMapper.selectByExample(roleExample).stream().findFirst().orElse(null);
    }
    
    /**
     * 查询指定權限記錄
     *
     * @param code
     * @param permissionMapper
     * @return
     */
    public static Permission getPermissionByCode(String code, PermissionMapper permissionMapper) {
        PermissionExample permissionExample = new PermissionExample();
        permissionExample.or().andCodeEqualTo(code);
        return permissionMapper.selectByExample(permissionExample).stream().findFirst().orElse(null);
    }

    public static Organization getOrganizationByNameAndCompanyName(String name, String topOrgName) {
        OrganizationExample topExample = new OrganizationExample();
        topExample.or().andNameEqualTo(topOrgName).andIsDeletedEqualTo(false);
        OrganizationMapper organizationMapper = SpringContextUtil.getBean(OrganizationMapper.class);
        List<Organization> topOrgList = organizationMapper.selectByExample(topExample);
        if (topOrgList.size() > 1) {
            throw new ServiceException("顶级组织机构名称重复");
        }
        if (topOrgList.size() == 0) {
            throw new ServiceException("顶级组织机构不存在");
        }
        Organization topOrg = topOrgList.get(0);
        OrganizationExample example = new OrganizationExample();
        example.or().andNameEqualTo(name).andIsDeletedEqualTo(false).andNoLike(topOrg.getNo() + "%");
        List<Organization> organizationList = organizationMapper.selectByExample(example);
        if (organizationList.size() > 1) {
            throw new ServiceException("组织机构名称重复");
        }
        if (organizationList.size() > 0) {
            return organizationList.get(0);
        }
        return null;
    }

    /**
     * 判断指定的组织机构在组织机构树中是否是叶子节点
     *
     * @param orgId
     * @return
     */
    public static boolean isOrganizationLeaf(String orgId) {
        OrganizationMapper organizationMapper = SpringContextUtil.getBean(OrganizationMapper.class);
        Organization organization = organizationMapper.selectByPrimaryKey(orgId);
        checkExist(organization, "组织机构不存在");

        OrganizationExample example = new OrganizationExample();
        example.or().andIsDeletedEqualTo(false).andParentIdEqualTo(orgId);
        return organizationMapper.countByExample(example) == 0;
    }

    public static void addOrgToUser(String orgId, String userId) {
        // 先判断是否已经存在
        UserOrganizationMapper userOrganizationMapper = SpringContextUtil.getBean(UserOrganizationMapper.class);
        UserOrganizationExample userOrganizationExample = new UserOrganizationExample();
        userOrganizationExample.or().andUserIdEqualTo(userId).andOrganizationIdEqualTo(orgId).andIsDeletedEqualTo(false);
        if (userOrganizationMapper.countByExample(userOrganizationExample) > 0) {
            return;
        }
        UserOrganization userOrganization = new UserOrganization();
        userOrganization.setUserId(userId);
        userOrganization.setOrganizationId(orgId);
        userOrganization.setIsDeleted(false);
        userOrganizationMapper.insertSelective(userOrganization);
    }

    /**
     * 用戶組織關係 数据列表
     *
     * @param userId
     * @param userOrganizationMapper
     * @return
     */
    public static List<UserOrganization> listUserOrganization(String userId, UserOrganizationMapper userOrganizationMapper) {
        UserOrganizationExample example = new UserOrganizationExample();
        example.or().andUserIdEqualTo(userId);
        return userOrganizationMapper.selectByExample(example);
    }
    
    /**
     * 用戶角色 数据列表
     *
     * @param userId
     * @param userRoleMapper
     * @return
     */
    public static List<UserRole> listUserRole(String userId, UserRoleMapper userRoleMapper) {
    	UserRoleExample example = new UserRoleExample();
        example.or().andUserIdEqualTo(userId);
        return userRoleMapper.selectByExample(example);
    }
    /**
     * 角色權限 数据列表
     *
     * @param roleId
     * @param rolePermissionMapper
     * @return
     */
    public static List<RolePermission> listRolePermission(String roleId, RolePermissionMapper rolePermissionMapper) {
    	RolePermissionExample example = new RolePermissionExample();
        example.or().andRoleIdEqualTo(roleId);
        return rolePermissionMapper.selectByExample(example);
    }
    
    /**
     * 權限菜單 数据列表
     *
     * @param permissionId
     * @param permissionMenuMapper
     * @return
     */
    public static List<PermissionMenu> listPermissionMenu(String permissionId, PermissionMenuMapper permissionMenuMapper) {
    	PermissionMenuExample example = new PermissionMenuExample();
        example.or().andPermissionIdEqualTo(permissionId);
        example.or().andIsDeletedEqualTo(false);
        return permissionMenuMapper.selectByExample(example);
    }
    
    /**
     * 權限操作 数据列表
     *
     * @param permissionId
     * @param permissionOperationMapper
     * @return
     */
    public static List<PermissionOperation> listPermissionOperation(String permissionId, PermissionOperationMapper permissionOperationMapper) {
    	PermissionOperationExample example = new PermissionOperationExample();
        example.or().andPermissionIdEqualTo(permissionId);
        return permissionOperationMapper.selectByExample(example);
    }
    

    public static void checkExist(String param, String errorMessage) {
        if (StringUtils.isBlank(param) || "undefined".equals(param)) {
            throw new ServiceException(errorMessage);
        }
    }

    public static void checkExist(Object param, String errorMessage) {
        if (Objects.isNull(param)) {
            throw new ServiceException(errorMessage);
        }
    }

    public static void initPageableVO(PageableVO pageableVO) {
        if (Objects.isNull(pageableVO)) {
            pageableVO = new PageableVO();
        }
        if (Objects.isNull(pageableVO.getCurPage())) {
            pageableVO.setCurPage(1);
        }
        if (Objects.isNull(pageableVO.getPageSize())) {
            pageableVO.setPageSize(10);
        }
    }

    public static List<WorkflowNodeVO> listSortedWorkflowNodes(String workflowId) {
        WorkflowCustomMapper workflowCustomMapper = SpringContextUtil.getBean(WorkflowCustomMapper.class);
        List<WorkflowNodeVO> lstWorkflowNode = workflowCustomMapper.selectWorkflowNodeByWorkflowId(workflowId);

        WorkflowNodeVO beginNode = lstWorkflowNode.stream().filter(node -> Objects.equals(node.getIsBeginNode(), Boolean.TRUE)).findFirst().orElse(null);
        List<WorkflowNodeVO> resultList = new ArrayList<>();
        WorkflowNodeVO nextNode = beginNode;
        while (nextNode != null) {
            String previousNodeId = nextNode.getId();
            if (StringUtils.isBlank(previousNodeId)) {
                break;
            }
            nextNode.setLastUpdateTimeStr("");
            if(nextNode.getLastUpdateTime() != null){
                nextNode.setLastUpdateTimeStr(nextNode.getLastUpdateTime()
                        .format(DateTimeFormatter.ofPattern(DateUtils.DEFAULT_DATE_TIME_FORMAT)));
            }
            nextNode = lstWorkflowNode.stream().filter(node -> StringUtils.equals(node.getPreviousNodeId(), previousNodeId)).findFirst().orElse(null);
            Optional.ofNullable(nextNode).ifPresent(resultList::add);
        }
        return resultList;
    }

    /**
     * 删除指定头表id的环境绩效明细
     *
     * @param headId 头表id
     */
    public static void deleteAmbientDetailByHeadId(String headId) {
        checkExist(headId, "headId is null");
        AmbientDetailMapper ambientDetailMapper = SpringContextUtil.getBean(AmbientDetailMapper.class);
        AmbientDetailExample example = new AmbientDetailExample();
        example.or().andHeadIdEqualTo(headId);
        ambientDetailMapper.deleteByExample(example);
    }

    public static List<Menu> listAllMenu() {
        MenuMapper menuMapper = SpringContextUtil.getBean(MenuMapper.class);
        MenuExample example = new MenuExample();
        example.or().andIsActiveEqualTo(Boolean.TRUE);
        return menuMapper.selectByExample(example);
    }

    /**
     * 根据角色id删除角色用户关系
     *
     * @param roleId 角色id
     */
    public static void deleteRoleUserByRoleId(String roleId) {
        checkExist(roleId, "roleId is null");
        UserRoleMapper userRoleMapper = SpringContextUtil.getBean(UserRoleMapper.class);
        UserRoleExample example = new UserRoleExample();
        example.or().andRoleIdEqualTo(roleId);

        userRoleMapper.deleteByExample(example);
    }

    /**
     * 判断指定角色是否包含用户
     *
     * @param roleId 角色id
     * @return
     */
    public static boolean hasUser(String roleId) {
        UserRoleMapper userRoleMapper = SpringContextUtil.getBean(UserRoleMapper.class);
        UserRoleExample example = new UserRoleExample();
        example.or().andRoleIdEqualTo(roleId);
        return userRoleMapper.countByExample(example) > 0;
    }

    /**
     * 查询指定用户的角色
     *
     * @param userId 用户id
     * @return
     */
    public static List<Role> listRolesOfUser(String userId) {
        UserRoleMapper userRoleMapper = SpringContextUtil.getBean(UserRoleMapper.class);
        RoleMapper roleMapper = SpringContextUtil.getBean(RoleMapper.class);
        UserRoleExample example = new UserRoleExample();
        example.or().andUserIdEqualTo(userId).andIsDeletedEqualTo(Boolean.FALSE);
        List<UserRole> lstUserRole = userRoleMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(lstUserRole)) {
            return Collections.emptyList();
        }
        List<String> lstRoleId = lstUserRole.stream().map(UserRole::getRoleId).collect(Collectors.toList());
        RoleExample roleExample = new RoleExample();
        roleExample.or().andIdIn(lstRoleId);
        return roleMapper.selectByExample(roleExample);
    }

    /**
     * 查询指定大屏用户的角色名稱
     *
     * @param username 用户名稱
     * @return
     */
    public static List<TzhBsRole> listBsRoleNamesOfUser(String username) {
        TzhBsUserRoleMapper tzhBsUserRoleMapper = SpringContextUtil.getBean(TzhBsUserRoleMapper.class);
        TzhBsRoleMapper tzhBsRoleMapper = SpringContextUtil.getBean(TzhBsRoleMapper.class);
        TzhBsUserRoleExample example = new TzhBsUserRoleExample();
        example.or().andUsernameEqualTo(username);
        List<TzhBsUserRole> lstTzhBsUserRole = tzhBsUserRoleMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(lstTzhBsUserRole)) {
            return Collections.emptyList();
        }
        List<String> lstRoleName = lstTzhBsUserRole.stream().map(TzhBsUserRole::getRolename).collect(Collectors.toList());
        TzhBsRoleExample tzhBsRoleExample = new TzhBsRoleExample();
        tzhBsRoleExample.or().andRolenameIn(lstRoleName);
        return tzhBsRoleMapper.selectByExample(tzhBsRoleExample);
    }

    /**
     * 查询指定用户的组织机构
     *
     * @param userId 用户id
     * @return
     */
    public static List<Organization> listOrganizationsOfUser(String userId) {
        UserOrganizationMapper userOrganizationMapper = SpringContextUtil.getBean(UserOrganizationMapper.class);
        OrganizationMapper organizationMapper = SpringContextUtil.getBean(OrganizationMapper.class);
        UserOrganizationExample example = new UserOrganizationExample();
        example.or().andUserIdEqualTo(userId).andIsDeletedEqualTo(Boolean.FALSE);
        List<UserOrganization> lstUserOrganization = userOrganizationMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(lstUserOrganization)) {
            return Collections.emptyList();
        }
        List<String> lstOrganizationId = lstUserOrganization.stream().map(UserOrganization::getOrganizationId).collect(Collectors.toList());
        OrganizationExample organizationExample = new OrganizationExample();
        organizationExample.or().andIdIn(lstOrganizationId);
        return organizationMapper.selectByExample(organizationExample);
    }

    /**
     * 查询指定用户的大屏组织机构
     *
     * @param username 用户名稱
     * @return
     */
    public static List<Organization> listBsOrganizationsOfUser(String username) {
        TzhBsUserSiteMapper tzhBsUserSiteMapper = SpringContextUtil.getBean(TzhBsUserSiteMapper.class);
        OrganizationMapper organizationMapper = SpringContextUtil.getBean(OrganizationMapper.class);
        TzhBsUserSiteExample example = new TzhBsUserSiteExample();
        example.or().andUsernameEqualTo(username);
        List<TzhBsUserSite> lstTzhBsUserSite = tzhBsUserSiteMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(lstTzhBsUserSite)) {
            return Collections.emptyList();
        }
        List<String> lstOrganizationId = lstTzhBsUserSite.stream().map(TzhBsUserSite::getSiteid).collect(Collectors.toList());
        OrganizationExample organizationExample = new OrganizationExample();
        organizationExample.or().andIdIn(lstOrganizationId);
        return organizationMapper.selectByExample(organizationExample);
    }

    /**
     * 判断用户组织机构关系是否存在
     *
     * @param userId
     * @param organizationId
     * @return
     */
    public static boolean isUserOrganizationExist(String userId, String organizationId) {
        UserOrganizationMapper userOrganizationMapper = SpringContextUtil.getBean(UserOrganizationMapper.class);
        UserOrganizationExample example = new UserOrganizationExample();
        example.or().andUserIdEqualTo(userId).andOrganizationIdEqualTo(organizationId).andIsDeletedEqualTo(false);
        return userOrganizationMapper.countByExample(example) > 0;
    }

    public static void validateMonth(Integer month) {
        if (Objects.isNull(month) || month < 1 || month > 12) {
            throw new ServiceException("月份不合法");
        }
    }
}
