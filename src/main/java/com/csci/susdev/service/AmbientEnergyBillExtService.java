package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.AmbientEnergyBillExtMapper;
import com.csci.susdev.model.AmbientEnergyBillExt;
import com.csci.susdev.model.AmbientEnergyBillExtExample;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.modelcovt.AmbientEnergyBillExtVOConverter;
import com.csci.susdev.qo.AmbientEnergyBillExtQO;
import com.csci.susdev.vo.AmbientEnergyBillExtVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class AmbientEnergyBillExtService {

    @Resource
    private AmbientEnergyBillExtMapper ambientEnergyBillExtMapper;

    public String saveAmbientEnergyBillExt(AmbientEnergyBillExtVO ambientEnergyBillExtVO) {
        ambientEnergyBillExtVO.setId(UUID.randomUUID().toString());

        if(ObjectUtils.isEmpty(ambientEnergyBillExtVO.getHeadId())) {
            throw new ServiceException("主表记录ID不能为空");
        }

        return doAdd(ambientEnergyBillExtVO);
    }

    private String getColumnName(String sortName) {
        if (StringUtils.isBlank(sortName)) {
            return null;
        }
        switch (sortName) {
            case "type":
                return "type";
            case "billNo":
                return "bill_no";
            case "fromTime":
                return "from_time";
            case "toTime":
                return "to_time";
            case "consumption":
                return "consumption";
            case "attachmentId":
                return "attachment_id";
            default:
                return null;
        }
    }

    private String doAdd(AmbientEnergyBillExtVO ambientEnergyBillExtVO) {
        AmbientEnergyBillExt ambientEnergyBill = AmbientEnergyBillExtVOConverter.convert(ambientEnergyBillExtVO);
        ambientEnergyBillExtMapper.insertSelective(ambientEnergyBill);
        return ambientEnergyBill.getId();
    }

    private String doUpdate(AmbientEnergyBillExtVO ambientEnergyBillExtVO) {
        AmbientEnergyBillExt ambientEnergyBill = AmbientEnergyBillExtVOConverter.convert(ambientEnergyBillExtVO);
        AmbientEnergyBillExtExample example = new AmbientEnergyBillExtExample();
        example.or().andIdEqualTo(ambientEnergyBillExtVO.getId()).andLastUpdateVersionEqualTo(ambientEnergyBillExtVO.getLastUpdateVersion());
        int updateCount = ambientEnergyBillExtMapper.updateByExampleSelective(ambientEnergyBill, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return ambientEnergyBill.getId();
    }

    public AmbientEnergyBillExtVO getAmbientEnergyBillExtById(String id) {
        AmbientEnergyBillExt ambientEnergyBill = ambientEnergyBillExtMapper.selectByPrimaryKey(id);
        if (ambientEnergyBill == null) {
            throw new ServiceException("数据不存在");
        }
        return AmbientEnergyBillExtVOConverter.convert(ambientEnergyBill);
    }

    public void deleteAmbientEnergyBillExtById(String id) {
        checkExist(id, "id不能为空");
        ambientEnergyBillExtMapper.deleteByPrimaryKey(id);
    }

    public List<AmbientEnergyBillExt> selectByExample(AmbientEnergyBillExtExample example) {
        return ambientEnergyBillExtMapper.selectByExample(example);
    }
}
