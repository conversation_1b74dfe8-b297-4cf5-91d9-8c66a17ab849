package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.tzh.mapper.TzhProjectDetailCustomMapper;
import com.csci.tzh.mapper.TzhProjectDetailMapper;
import com.csci.tzh.model.TzhProjectDetail;
import com.csci.tzh.model.TzhProjectDetailExample;
import com.csci.tzh.qo.SiteNameQO;
import com.csci.tzh.vo.TzhProjectDetailVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class TzhProjectDetailService {

	@Autowired
	private TzhProjectDetailMapper mapper;

	@Autowired
	private TzhProjectDetailCustomMapper customMapper;

	public List<TzhProjectDetail> selectByExample(TzhProjectDetailExample example) {
		return mapper.selectByExample(example);
	}

	public List<TzhProjectDetailVO> list(SiteNameQO qo) {
		return customMapper.list(qo.getSiteid());
	}

	@Transactional(rollbackFor = Exception.class)
	public TzhProjectDetail save(TzhProjectDetail newModel) {
		UserInfo currentUser = ContextUtils.getCurrentUser();

		if(newModel.getIsdeleted() == false) {
			if(StringUtils.isNotBlank(newModel.getId())) {
				// 備份已刪除數據
				TzhProjectDetailExample originalExample = new TzhProjectDetailExample();
				TzhProjectDetailExample.Criteria originalCriteria = originalExample.or();
				originalCriteria.andIdEqualTo(newModel.getId());
				List<TzhProjectDetail> lstOriginalModel = mapper.selectByExample(originalExample);
				if(lstOriginalModel.size() > 0) {
					TzhProjectDetail originalModel = lstOriginalModel.get(0);
					originalModel.setId(UUID.randomUUID().toString());
					originalModel.setIsdeleted(true);
					originalModel.setDeletedby(currentUser.getUsername());
					originalModel.setDeletedtime(LocalDateTime.now());
					mapper.insertSelective(originalModel);
				}

				// 新增數據(保留舊ID)
				TzhProjectDetailExample newExample = new TzhProjectDetailExample();
				TzhProjectDetailExample.Criteria newCriteria = newExample.or();
				newCriteria.andIdEqualTo(newModel.getId());
				newCriteria.andIsdeletedEqualTo(false);
				newModel.setCreatedby(currentUser.getUsername());
				newModel.setCreatedtime(LocalDateTime.now());
				newModel.setDeletedby(null);
				newModel.setDeletedtime(null);
				mapper.deleteByExample(newExample);
				mapper.insertSelective(newModel);
			} else {
				// 新增數據(新建ID)
				newModel.setId(UUID.randomUUID().toString());
				newModel.setCreatedby(currentUser.getUsername());
				newModel.setCreatedtime(LocalDateTime.now());
				mapper.insertSelective(newModel);
			}
		} else {
			// 刪除數據
			TzhProjectDetailExample example = new TzhProjectDetailExample();
			TzhProjectDetailExample.Criteria criteria = example.or();
			criteria.andIdEqualTo(newModel.getId());
			newModel.setIsdeleted(true);
			newModel.setDeletedby(currentUser.getUsername());
			newModel.setDeletedtime(LocalDateTime.now());
			mapper.updateByExampleSelective(newModel, example);
		}

		return newModel;
	}
}
