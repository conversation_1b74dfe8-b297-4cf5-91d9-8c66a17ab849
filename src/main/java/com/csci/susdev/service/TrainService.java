package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.mapper.TrainCustomMapper;
import com.csci.susdev.mapper.TrainMapper;
import com.csci.susdev.model.Train;
import com.csci.susdev.qo.KeywordQO;
import com.csci.susdev.qo.TrainQO;
import com.csci.susdev.util.JsonUtils;
import com.csci.susdev.vo.TrainVO;
import io.swagger.v3.core.util.Json;
import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
@DS(DatasourceContextEnum.SUSDEV)
@LogMethod
public class TrainService {
	@Autowired
	private TrainMapper trainMapper;
	@Autowired
	private TrainCustomMapper trainCustomMapper;
	private static final Logger logger = LoggerFactory.getLogger(TrainService.class);


	public List<TrainVO> list(KeywordQO qo) {
		List<TrainVO> trainVOList = trainCustomMapper.list(qo.getKeyword());
		return trainVOList;
	}


	public List<String> listAllName() {
		return trainCustomMapper.listAllName();
	}

	public Train calCarbonEmission(TrainQO qo) {
		Train train = trainCustomMapper.calCarbonEmission(qo.getStartPlace(), qo.getDestination());
		if(train != null) {
			// 計算人數和來回 里數和碳排
			train.setCarbonEmission(train.getCarbonEmission().multiply(new BigDecimal(qo.getPersonCount())));
			if(qo.getTicketType() == 1) {
				train.setCarbonEmission(train.getCarbonEmission().multiply(new BigDecimal(2)));
			}
		}
		return train;
	}
}
