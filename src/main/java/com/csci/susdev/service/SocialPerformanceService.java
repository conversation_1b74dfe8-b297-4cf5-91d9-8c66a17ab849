package com.csci.susdev.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.csci.cohl.model.EmpCommutingHead;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.ApproveStatus;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.constant.WorkflowControlState;
import com.csci.susdev.listener.SocialPerfImportListener;
import com.csci.susdev.mapper.SocialPerformanceDetailCustomMapper;
import com.csci.susdev.mapper.SocialPerformanceDetailMapper;
import com.csci.susdev.mapper.SocialPerformanceHeadMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.SocialPerfOneTableConverter;
import com.csci.susdev.modelcovt.SocialPerformanceDetailConverter;
import com.csci.susdev.modelcovt.SocialPerformanceHeadConverter;
import com.csci.susdev.qo.EmpCommutingQO;
import com.csci.susdev.qo.SocialPerformanceQO;
import com.csci.susdev.util.redis.RedisLockUtil;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.vo.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class SocialPerformanceService {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(SocialPerformanceService.class);
    /**
     * 初始化记录的过期时间
     */
    @Value("${lock.init.head.expire.milis:1000}")
    private long lockInitHeadExpireMilis;
    /**
     * 查询head记录时，获取锁失败循环等待的次数
     */
    @Value("${query.head.wait.loop.times:5}")
    private int lockQueryHeadWaitLoopTimes;
    /**
     * 查询head记录时，获取锁失败循环时每次休眠的时间
     */
    @Value("${query.head.wait.sleep.milis:50}")
    private int lockQueryHeadWaitSleepMilis;

    @Resource
    private OrganizationService organizationService;

    @Resource
    private SocialPerformanceHeadMapper socialPerformanceHeadMapper;

    @Resource
    private SocialPerformanceDetailMapper socialPerformanceDetailMapper;

    @Resource
    private SocialPerformanceDetailCustomMapper socialPerformanceDetailCustomMapper;

    /**
     * 根据查询条件查询社会绩效信息
     * 只会查询 active = true 的记录
     *
     * @param organizationId 组织 id
     * @param year           年份
     * @param month          月份
     * @return
     */
    SocialPerformanceHead getSocialPerformanceHeadBy(String organizationId, Integer year, Integer month) {
        SocialPerformanceHeadExample example = new SocialPerformanceHeadExample();
        example.or().andOrganizationIdEqualTo(organizationId).andYearEqualTo(year).andMonthEqualTo(month).andIsActiveEqualTo(Boolean.TRUE);
        return socialPerformanceHeadMapper.selectByExample(example).stream().findFirst().orElse(null);
    }

    /**
     * @param qo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public SocialPerformanceHead createSocialPerformanceHead(SocialPerformanceQO qo) {
        String key = "createSocialPerformanceHead-" + qo.getOrganizationId() + qo.getYear();
        boolean getLock = RedisLockUtil.lock(key);
        if (!getLock) {
            throw new ServiceException("获取锁失败");
        }
        try {
            SocialPerformanceHead socialPerformanceHead = getSocialPerformanceHeadBy(qo.getOrganizationId(), qo.getYear(),
                    qo.getMonth());
            if (socialPerformanceHead == null) {
                socialPerformanceHead = new SocialPerformanceHead();
                socialPerformanceHead.setOrganizationId(qo.getOrganizationId());
                socialPerformanceHead.setYear(qo.getYear());
                socialPerformanceHead.setMonth(qo.getMonth());
                socialPerformanceHead.setIsActive(Boolean.TRUE);
                socialPerformanceHead.setApproveStatus(0);
                socialPerformanceHead.setLastUpdateVersion(0);
                socialPerformanceHeadMapper.insertSelective(socialPerformanceHead);
            }
            return socialPerformanceHead;
        } finally {
            RedisLockUtil.unlock(key);
        }
    }

    /**
     * 根据查询条件查询社会绩效信息，如果不存在则创建
     *
     * @param socialPerformanceQO 查询条件
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public SocialPerformanceHeadVO getOrInit(SocialPerformanceQO socialPerformanceQO) {
        checkExist(socialPerformanceQO.getOrganizationId(), "组织编号不能为空");
        checkExist(socialPerformanceQO.getYear(), "年份不能为空");
        if(ObjectUtils.isEmpty(socialPerformanceQO.getMonth())) {
            socialPerformanceQO.setMonth(12);
        }

        // checkExist(socialPerformanceQO.getMonth(), "月份不能为空");
        /*if (socialPerformanceQO.getMonth() < 1 || socialPerformanceQO.getMonth() > 12) {
            throw new ServiceException("月份必须在 1-12 之间");
        }*/
        if (socialPerformanceQO.getYear() < 1900 || socialPerformanceQO.getYear() > 2100) {
            throw new ServiceException("年份必须在 1900-2100 之间");
        }

        //查询或初始化记录，如果获取失败，直接抛出异常退出
        SocialPerformanceHead socialPerformanceHead = getOrInitHeadRecord(socialPerformanceQO);

        OrganizationVO orgVO = organizationService.getOrganizationById(socialPerformanceQO.getOrganizationId());
        // 初始化明细数据
        SocialPerformanceHead prevHead = getPrevHead(socialPerformanceQO.getOrganizationId(), socialPerformanceQO.getYear(),
                socialPerformanceQO.getMonth());
        initSocialPerformanceDetail(socialPerformanceHead.getId(), orgVO.getNo());

        // 查询流程审批状态
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        WorkflowControl workflowControl = workflowControlService.findWorkflowControlByBusinessId(socialPerformanceHead.getId());
        SocialPerformanceHeadVO socialPerformanceHeadVO = SocialPerformanceHeadConverter.convert(socialPerformanceHead);
        if (Objects.nonNull(workflowControl)) {
            socialPerformanceHeadVO.setWorkflowControlState(workflowControl.getState());
            socialPerformanceHeadVO.setWorkflowControlStateName(Optional.ofNullable(WorkflowControlState.getWorkflowControlState(workflowControl.getState())).map(WorkflowControlState::getName).orElse(null));
        }
        return socialPerformanceHeadVO;
    }

    /**
     * 查询或初始化记录，如果获取失败，直接抛出异常退出
     * @param
     * @return
     */
    private SocialPerformanceHead getOrInitHeadRecord(SocialPerformanceQO socialPerformanceQO) {
        SocialPerformanceHead socialPerformanceHead = getSocialPerformanceHeadBy(socialPerformanceQO.getOrganizationId(),
                socialPerformanceQO.getYear(), socialPerformanceQO.getMonth());
        if (Objects.isNull(socialPerformanceHead)) {
            //分布式锁的key：分别表示前缀:业务:组织ID:年:月
            String key = StrUtil.format(SusDevConsts.DISTRIBUTE_LOCK_KEY_FORMAT, SusDevConsts.PROJECT_PREFIX,
                    "SocialPerformanceHead:getOrInit", socialPerformanceQO.getOrganizationId(),
                    socialPerformanceQO.getYear(), socialPerformanceQO.getMonth());
            //超时时间1s
            boolean redisLock = RedisLockUtil.lock(key, lockInitHeadExpireMilis);
            if (!redisLock) {
                //获取锁失败，停顿50ms一次，停顿5次
                int count = lockQueryHeadWaitLoopTimes;
                while(count-- > 0){
                    socialPerformanceHead = getSocialPerformanceHeadBy(socialPerformanceQO.getOrganizationId(),
                            socialPerformanceQO.getYear(), socialPerformanceQO.getMonth());
                    if(socialPerformanceHead != null){
                        break;
                    }
                    try {
                        Thread.sleep(lockQueryHeadWaitSleepMilis);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
            }else{
                try{
                    socialPerformanceHead = createSocialPerformanceHead(socialPerformanceQO);
                }
                finally {
                    RedisLockUtil.unlock(key);
                }
            }
        }
        //如果最终没拿到数据，直接返回
        if(socialPerformanceHead == null){
            throw new ServiceException("生成记录失败, 请稍后再尝试！");
        }
        return socialPerformanceHead;
    }



    public SocialPerformanceHead getPrevHead(String organizationId, Integer year, Integer month) {
        SocialPerformanceHeadExample example = new SocialPerformanceHeadExample();
        example.or().andIsActiveEqualTo(true).andOrganizationIdEqualTo(organizationId)
                .andYearEqualTo(year).andMonthLessThan(month);
        example.setOrderByClause("month desc");
        List<SocialPerformanceHead> lst = selectByExample(example);
        if(lst.size() > 0) {
            return lst.get(0);
        } else {
            return null;
        }
    }

    void initDetailsWithPrevData(String prevHeadId, String curHeadId) {
        SocialPerformanceDetailExample example = new SocialPerformanceDetailExample();
        example.or().andHeadIdEqualTo(prevHeadId);
        List<SocialPerformanceDetail> details = socialPerformanceDetailMapper.selectByExample(example);
        for (SocialPerformanceDetail detail : details) {
            detail.setId(null);
            detail.setHeadId(curHeadId);
            //socialPerformanceDetailMapper.insert(detail);
        }
        int batchSize = 100;
        for (int i = 0; i < details.size(); i += batchSize) {
            List<SocialPerformanceDetail> batch = details.subList(i, Math.min(i + batchSize, details.size()));
            socialPerformanceDetailCustomMapper.batchInsert(batch);
        }
    }


    /**
     * 根据主表id查询社会绩效明细信息
     *
     * @param headId 社会绩效头 id
     * @return
     */
    public List<SocialPerformanceDetailVO> listSocialPerformanceDetail(String headId) {
        checkExist(headId, "社会绩效主表id不能为空");
        SocialPerformanceDetailExample example = new SocialPerformanceDetailExample();
        example.or().andHeadIdEqualTo(headId);
        example.setOrderByClause("seq");
        return socialPerformanceDetailMapper.selectByExample(example).stream().map(new SocialPerformanceDetailConverter()::convert).collect(Collectors.toList());
    }

    /**
     * 查询用于前端页面handsontable使用的数据
     *
     * @param headId 社会绩效头 id
     * @return
     */
    public List<SocialPerfOneTableVO> listSocialPerfOneTable(String headId) {
        checkExist(headId, "社会绩效主表id不能为空");
        SocialPerformanceDetailExample example = new SocialPerformanceDetailExample();
        example.or().andHeadIdEqualTo(headId);
        example.setOrderByClause("seq");
        return socialPerformanceDetailMapper.selectByExample(example).stream().map(SocialPerfOneTableConverter::convert).collect(Collectors.toList());
    }

    void initSocialPerformanceDetail(String headId, String no) {
        checkExist(headId, "社会绩效主表id不能为空");

        String templatePath = "template/social_perf_init.xlsx";
        /*
        if(no != null && no.startsWith("001")) {
            templatePath = "template/social_perf_init_hk.xlsx";
        }
         */
        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(templatePath)) {
            SocialPerformanceDetailExample example = new SocialPerformanceDetailExample();
            example.or().andHeadIdEqualTo(headId);
            long count = socialPerformanceDetailMapper.countByExample(example);
            if (count > 0) {
                logger.info("社会绩效明细表已经存在数据，不需要初始化");
                return;
            }
            EasyExcel.read(is, SocialPerformanceImportDataVO.class, new SocialPerfImportListener(headId)).sheet().doRead();
        } catch (IOException e) {
            logger.info("社会绩效初始化失败", e);
            throw new ServiceException("社会绩效初始化失败", e);
        }

    }

    /**
     * proxy for mapper method
     *
     * @param record
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertDetailSelective(SocialPerformanceDetail record) {
        return socialPerformanceDetailMapper.insertSelective(record);
    }

    /**
     * 保存社会绩效明细信息
     *
     * @param socialPerformanceHeadVO 社会绩效头信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSocialPerfWithDetail(SocialPerformanceHeadVO socialPerformanceHeadVO) {
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        if (workflowControlService.isNotAllowEdit(socialPerformanceHeadVO.getId())) {
            throw new ServiceException("已经发起审批流程的单据不允许进行修改");
        }
        // 更新主表信息
        updateHead(socialPerformanceHeadVO);
        // 删除明细
        deleteDetailByHeadId(socialPerformanceHeadVO.getId());
        List<SocialPerformanceDetail> lstDetails = socialPerformanceHeadVO.getDetails().stream().map(SocialPerfOneTableConverter::convert).collect(Collectors.toList());

        addDetails(socialPerformanceHeadVO.getId(), lstDetails);
    }

    /**
     * 提交社会绩效
     *
     * @param idVersionVO
     */
    public void submit(IdVersionVO idVersionVO) {
        // 验证逻辑---------
        checkExist(idVersionVO.getId(), "社会绩效主表id不能为空");
        checkExist(idVersionVO.getLastUpdateVersion(), "社会绩效主表版本号不能为空");

        SocialPerformanceHead existedRecord = socialPerformanceHeadMapper.selectByPrimaryKey(idVersionVO.getId());
        Optional.ofNullable(existedRecord).orElseThrow(() -> new ServiceException("指定的社会绩效记录不存在"));
        // 验证逻辑---------

        SocialPerformanceHeadExample example = new SocialPerformanceHeadExample();
        example.or().andIdEqualTo(idVersionVO.getId()).andLastUpdateVersionEqualTo(idVersionVO.getLastUpdateVersion());

        SocialPerformanceHead record = new SocialPerformanceHead();
        record.setApproveStatus(ApproveStatus.SUBMIT.getCode());
        record.setLastUpdateVersion(idVersionVO.getLastUpdateVersion());

        int count = socialPerformanceHeadMapper.updateByExampleSelective(record, example);
        if (count == 0) {
            throw new ServiceException("社会绩效已经被修改，请刷新后重试");
        }
        // todo 增加流程审批记录
    }

    private void addDetails(String headId, List<SocialPerformanceDetail> socialPerformanceDetails) {
        if (CollectionUtils.isEmpty(socialPerformanceDetails)) {
            throw new ServiceException("社会绩效明细不能为空");
        }
        int seq = 0;
        SocialPerformanceDetail lastDetail = socialPerformanceDetails.get(0);
        for (SocialPerformanceDetail socialPerformanceDetail : socialPerformanceDetails) {
            if (StringUtils.isNotBlank(socialPerformanceDetail.getCategory()) && !StringUtils.equals(socialPerformanceDetail.getCategory(), lastDetail.getCategory())) {
                lastDetail = socialPerformanceDetail;
            }
            if (StringUtils.isBlank(socialPerformanceDetail.getCategory())) {
                socialPerformanceDetail.setCategory(lastDetail.getCategory());
            }
            socialPerformanceDetail.setId(UUID.randomUUID().toString());
            socialPerformanceDetail.setHeadId(headId);
            socialPerformanceDetail.setSeq(seq++);
            socialPerformanceDetailMapper.insertSelective(socialPerformanceDetail);
        }
    }

    private void deleteDetailByHeadId(String headId) {
        SocialPerformanceDetailExample example = new SocialPerformanceDetailExample();
        example.or().andHeadIdEqualTo(headId);
        socialPerformanceDetailMapper.deleteByExample(example);
    }

    private void updateHead(SocialPerformanceHeadVO socialPerformanceHeadVO) {
        checkExist(socialPerformanceHeadVO.getId(), "社会绩效主表id不能为空");
        checkExist(socialPerformanceHeadVO.getOrganizationId(), "组织编号不能为空");
        checkExist(socialPerformanceHeadVO.getLastUpdateVersion(), "版本号不能为空");

        SocialPerformanceHeadExample headExample = new SocialPerformanceHeadExample();
        headExample.or().andIdEqualTo(socialPerformanceHeadVO.getId()).andLastUpdateVersionEqualTo(socialPerformanceHeadVO.getLastUpdateVersion());

        SocialPerformanceHead socialPerformanceHead = SocialPerformanceHeadConverter.convert(socialPerformanceHeadVO);
        int updateCount = socialPerformanceHeadMapper.updateByExampleSelective(socialPerformanceHead, headExample);
        if (updateCount == 0) {
            throw new ServiceException("社会绩效主表信息已经被修改，请刷新后重试");
        }
    }

    /**
     * proxy for mapper method
     *
     * @param id 社会绩效主表id
     * @return
     */
    public SocialPerformanceHead selectByPrimaryKey(String id) {
        return socialPerformanceHeadMapper.selectByPrimaryKey(id);
    }

    public List<SocialPerformanceHead> selectByExample(SocialPerformanceHeadExample example) {
        return socialPerformanceHeadMapper.selectByExample(example);
    }

}


