package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.NamingConventionEnum;
import com.csci.susdev.mapper.ConnectionConfigCustomMapper;
import com.csci.susdev.mapper.ConnectionConfigMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.ConnectionConfigConverter;
import com.csci.susdev.qo.ConnectionConfigQO;
import com.csci.susdev.util.MybatisHelper;
import com.csci.susdev.util.NamingConventionUtils;
import com.csci.susdev.util.RsaUtil;
import com.csci.susdev.vo.ConnectionConfigVO;
import com.csci.susdev.vo.ConnectionConfigVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.rmi.ServerException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class ConnectionConfigService {

    @Resource
    private ConnectionConfigMapper connectionConfigMapper;

    @Resource
    private ConnectionConfigCustomMapper connectionConfigCustomMapper;

    @Resource
    private ProtocolSubCategoryService protocolSubCategoryService;

    @Value("${rsa.ext.publicKey}")
    private String publicExtKeyString;

    @Value(("${rsa.ext.privateKey}"))
    private String privateExtKeyString;


    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(ConnectionConfig record) {
        return connectionConfigMapper.insertSelective(record);
    }

    public ConnectionConfig selectByPrimaryKey(String id) {
        return connectionConfigMapper.selectByPrimaryKey(id);
    }

    public ConnectionConfigVO getConnectionConfig(String id) {
        checkExist(id, "id不能为空");
        ConnectionConfig connectionConfig = selectByPrimaryKey(id);
        checkExist(connectionConfig, "未找到对应的记录");
        if(connectionConfig.getIsDeleted()) {
            throw new ServiceException("紀錄已被刪除");
        }
        return ConnectionConfigConverter.convertToVO(connectionConfig);
    }

    public ConnectionConfigVO getConnectionConfig(String appId, String appKey) {
        checkExist(appId, "appId不能为空");
        ConnectionConfigExample example = new ConnectionConfigExample();
        example.or().andIsDeletedEqualTo(false).andAppIdEqualTo(appId).andAppKeyEqualTo(appKey)
                .andIsDeletedEqualTo(false).andIsActiveEqualTo(true);
        List<ConnectionConfig> lstConnectionConfig = selectByExample(example);
        ConnectionConfig connectionConfig = null;
        if(lstConnectionConfig.size() > 0) {
            connectionConfig = lstConnectionConfig.get(0);
        }
        checkExist(connectionConfig, "未找到对应的记录");
        return ConnectionConfigConverter.convertToVO(connectionConfig);
    }


    @Transactional(rollbackFor = Exception.class)
    public String saveConnectionConfig(ConnectionConfigVO connectionConfigVO) {
        if (StringUtils.isBlank(connectionConfigVO.getId())) {
            //檢查該AppID是否已存在
            ConnectionConfigExample example = new ConnectionConfigExample();
            example.or().andIsDeletedEqualTo(false).andAppIdEqualTo(connectionConfigVO.getAppId());
            if(ObjectUtils.isNotEmpty(selectByExample(example))) {
                throw new ServiceException("已有該AppID的配置，請選擇別的AppID");
            }
            // 新增
            return doAdd(connectionConfigVO);
        } else {
            checkExist(connectionConfigVO.getLastUpdateVersion(), "更新时版本号不能为空");
            //檢查該AppID是否已存在
            ConnectionConfigExample example = new ConnectionConfigExample();
            example.or().andIsDeletedEqualTo(false)
                    .andAppIdEqualTo(connectionConfigVO.getAppId()).andIdNotEqualTo(connectionConfigVO.getId());
            if(ObjectUtils.isNotEmpty(selectByExample(example))) {
                throw new ServiceException("已有該AppID的配置，請選擇別的AppID");
            }
            // 修改
            return doUpdate(connectionConfigVO);
        }

    }

    public ResultPage<ConnectionConfigVO> listConnectionConfig(ConnectionConfigQO connectionConfigQO) {
        if (StringUtils.isNotBlank(connectionConfigQO.getOrderBy())) {
            List<String> fieldNames = Arrays.stream(ConnectionConfigVO.class.getDeclaredFields()).map(field -> field.getName()).collect(Collectors.toList());
            if(MybatisHelper.checkSqlInjectionForOrderBy(connectionConfigQO.getOrderBy(), fieldNames, NamingConventionEnum.CAMO)) {
                throw new ServiceException("orderBy 有SQL注入行為");
            }
        }

        PageHelper.startPage(connectionConfigQO.getCurPage(), connectionConfigQO.getPageSize());

        List<ConnectionConfigVO> connectionConfigVOs = connectionConfigCustomMapper.list(
                connectionConfigQO.getKeyword(),
                connectionConfigQO.getOrderBy());
        return new ResultPage<>(connectionConfigVOs, true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteConnectionConfig(String id) {
        ConnectionConfig record = selectByPrimaryKey(id);

        ProtocolSubCategoryExample example = new ProtocolSubCategoryExample();
        example.or().andCategoryIdEqualTo(id).andIsDeletedEqualTo(false);
        List<ProtocolSubCategory> protocolSubCategories = protocolSubCategoryService.selectByExample(example);
        if(protocolSubCategories.size() > 0) {
            throw new ServiceException("已在協議小類中被使用，不能刪除");
        }

        record.setIsDeleted(true);
        connectionConfigMapper.updateByPrimaryKeySelective(record);
    }

    private String doAdd(ConnectionConfigVO connectionConfigVO) {
        ConnectionConfig connectionConfig = ConnectionConfigConverter.convertToModel(connectionConfigVO);
        connectionConfigMapper.insertSelective(connectionConfig);
        return connectionConfig.getId();
    }

    private String doUpdate(ConnectionConfigVO connectionConfigVO) {
        ConnectionConfig originalRecord = selectByPrimaryKey(connectionConfigVO.getId());
        ConnectionConfig connectionConfig = ConnectionConfigConverter.convertToModelWithBase(connectionConfigVO, originalRecord);

        ConnectionConfigExample example = new ConnectionConfigExample();
        example.or().andIdEqualTo(connectionConfigVO.getId()).andLastUpdateVersionEqualTo(connectionConfigVO.getLastUpdateVersion());
        int updateCount = connectionConfigMapper.updateByExample(connectionConfig, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return connectionConfig.getId();
    }

    public List<ConnectionConfig> selectByExample(ConnectionConfigExample example) {
        return connectionConfigMapper.selectByExample(example);
    }

    public void checkValid(String encryptAppId, String encryptAppKey) throws Exception {
        if(ObjectUtils.isEmpty(encryptAppId) || ObjectUtils.isEmpty(encryptAppKey)) {
            throw new ServiceException("請求需要輸入 AppID 及 AppKey");
        }
        String appId = RsaUtil.decrypt(encryptAppId,privateExtKeyString);
        String appKey = RsaUtil.decrypt(encryptAppKey,privateExtKeyString);


        ConnectionConfigExample example = new ConnectionConfigExample();
        example.or().andIsDeletedEqualTo(false).andIsActiveEqualTo(true)
                .andAppIdEqualTo(appId).andAppKeyEqualTo(appKey);
        List<ConnectionConfig> list = selectByExample(example);
        if(list.size() == 0) {
            throw new ServiceException("沒有訪問權限");
        }
    }
}
