package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.AirPollutantsMapper;
import com.csci.susdev.model.AirPollutants;
import com.csci.susdev.model.AirPollutantsExample;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.modelcovt.AirPollutantsConverter;
import com.csci.susdev.qo.BatchIdPageQO;
import com.csci.susdev.vo.AirPollutantsVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;
import static com.csci.susdev.service.ServiceHelper.initPageableVO;

@Service
@LogMethod
public class AirPollutantsService {

    @Resource
    private AirPollutantsMapper airPollutantsMapper;

    /**
     * 校验更新空气污染物排放因子必填字段
     *
     * @param airPollutantsVO 空气污染物排放因子
     */
    static void validateUpdateAirPollutants(AirPollutantsVO airPollutantsVO) {
        validateRequiredFieldForAirPollutants(airPollutantsVO);
        checkExist(airPollutantsVO.getLastUpdateVersion(), "空气污染物排放因子版本号不能为空");
    }

    static void validateRequiredFieldForAirPollutants(AirPollutantsVO airPollutantsVO) {
        checkExist(airPollutantsVO.getBatchId(), "批次 id 不能为空");
        checkExist(airPollutantsVO.getEmissionSource(), "排放来源不能为空");
        checkExist(airPollutantsVO.getType(), "污染来源类别不能为空");
        checkExist(airPollutantsVO.getUnit(), "单位不能为空");
        checkExist(airPollutantsVO.getAirPollutionType(), "空气污染物类别不能为空");
        checkExist(airPollutantsVO.getAirPolEmiFactor(), "空气污染排放因子不能为空");
        if (!NumberUtils.isParsable(airPollutantsVO.getAirPolEmiFactor())) {
            throw new ServiceException("空气污染排放因子必须为数字");
        }
        if (StringUtils.equalsIgnoreCase("移动源", airPollutantsVO.getCategory())) {
            checkExist(airPollutantsVO.getVehicleType(), "来源种类为移动源时，车辆类型不能为空");
            checkExist(airPollutantsVO.getVehicleEmissionStandard(), "来源种类为移动源时，车辆排放标准不能为空");
        }
        checkExist(airPollutantsVO.getAirPolEmiUnit(), "空气污染物排放单位不能为空");
    }

    static void validateAddAirPollutants(AirPollutantsVO airPollutantsVO) {
        validateRequiredFieldForAirPollutants(airPollutantsVO);
    }

    /**
     * 添加一条空气污染物排放因子记录
     *
     * @param airPollutantsVO 空气污染物排放因子
     * @return 空气污染物排放因子 id
     */
    @Transactional(rollbackFor = Exception.class)
    public String addAirPollutants(AirPollutantsVO airPollutantsVO) {
        validateAddAirPollutants(airPollutantsVO);
        AirPollutants airPollutants = new AirPollutantsConverter().revert(airPollutantsVO);
        int count = airPollutantsMapper.insertSelective(airPollutants);
        if (Objects.equals(count, 0)) {
            throw new ServiceException("添加空气污染物失败");
        }
        return airPollutants.getId();
    }

    /**
     * 分页查找空气污染物
     *
     * @param batchIdPageQO 批次 id 及分页信息
     * @return
     */
    public ResultPage<AirPollutantsVO> listAirPollutantsByPage(BatchIdPageQO batchIdPageQO) {
        checkExist(batchIdPageQO.getBatchId(), "批次 id 不能为空");
        initPageableVO(batchIdPageQO);
        AirPollutantsExample example = new AirPollutantsExample();
        example.or().andBatchIdEqualTo(batchIdPageQO.getBatchId());
        example.setOrderByClause("creation_time desc");
        PageHelper.startPage(batchIdPageQO.getCurPage(), batchIdPageQO.getPageSize());
        List<AirPollutants> lstAirPollutants = airPollutantsMapper.selectByExample(example);
        ResultPage<AirPollutantsVO> resultPage = new ResultPage<>(lstAirPollutants);
        resultPage.setList(lstAirPollutants.stream().map(x -> new AirPollutantsConverter().convert(x)).collect(Collectors.toList()));
        return resultPage;
    }

    /**
     * 删除空气污染物排放因子
     *
     * @param id 空气污染物排放因子 id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteAirPollutants(String id) {
        checkExist(id, "空气污染物 id 不能为空");
        return airPollutantsMapper.deleteByPrimaryKey(id);
    }

    public AirPollutantsVO getAirPollutantsById(String id) {
        checkExist(id, "空气污染物 id 不能为空");
        AirPollutants airPollutants = airPollutantsMapper.selectByPrimaryKey(id);
        if (Objects.isNull(airPollutants)) {
            throw new ServiceException("指定的空气污染物不存在");
        }
        return new AirPollutantsConverter().convert(airPollutants);
    }

    /**
     * 保存空气污染物排放因子
     *
     * @param airPollutantsVO 空气污染物排放因子
     * @return
     */
    public AirPollutants saveAirPollutants(AirPollutantsVO airPollutantsVO) {
        validateRequiredFieldForAirPollutants(airPollutantsVO);
        AirPollutants airPollutants = new AirPollutantsConverter().revert(airPollutantsVO);

        if (StringUtils.isBlank(airPollutants.getId())) {
            doInsertAirPollutants(airPollutants);
        } else {
            doUpdateAirPollutants(airPollutants);
        }
        return airPollutants;
    }

    private void doUpdateAirPollutants(AirPollutants airPollutants) {
        checkExist(airPollutants.getLastUpdateVersion(), "空气污染物排放因子版本号不能为空");
        AirPollutantsExample example = new AirPollutantsExample();
        example.or().andIdEqualTo(airPollutants.getId()).andLastUpdateVersionEqualTo(airPollutants.getLastUpdateVersion());
        int count = airPollutantsMapper.updateByExampleSelective(airPollutants, example);
        if (Objects.equals(count, 0)) {
            throw new ServiceException("更新空气污染物失败");
        }
    }

    private void doInsertAirPollutants(AirPollutants airPollutants) {
        int count = airPollutantsMapper.insertSelective(airPollutants);
        if (Objects.equals(count, 0)) {
            throw new ServiceException("添加空气污染物失败");
        }
    }
}
