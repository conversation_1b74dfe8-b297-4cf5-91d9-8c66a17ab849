package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.mapper.UserSessionMapper;
import com.csci.susdev.model.User;
import com.csci.susdev.model.UserSession;
import com.csci.susdev.model.UserSessionExample;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@LogMethod
@DS(DatasourceContextEnum.SUSDEV)
public class UserSessionService {

    @Resource
    private Environment environment;

    @Resource
    private UserSessionMapper userSessionMapper;

    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(UserSession record) {
        return userSessionMapper.insertSelective(record);
    }

    @Transactional(rollbackFor = Exception.class)
    public int updateByPrimaryKeySelective(UserSession record) {
        return userSessionMapper.updateByPrimaryKeySelective(record);
    }

    public List<UserSession> selectByExample(UserSessionExample example) {
        return userSessionMapper.selectByExample(example);
    }

    /**
     * 根据 token 查询用户 session，该 token 可以是 token 或者 accessToken
     *
     * @param token
     * @return
     */
    public UserSession getUserSessionByTokenOrAccessToken(String token) {

        long timeout = environment.getProperty("user.session.timeout", Long.class, 60 * 2L);

        UserSessionExample example = new UserSessionExample();
        example.or().andTokenEqualTo(token).andCreationTimeGreaterThan(LocalDateTime.now().minusMinutes(timeout));
        example.or().andAccessTokenEqualTo(token).andCreationTimeGreaterThan(LocalDateTime.now().minusMinutes(timeout));
        return userSessionMapper.selectByExample(example).stream().findFirst().orElse(null);
    }

    /**
     * 根据 username 查询用户 session
     *
     * @param username
     * @return
     */
    public UserSession getUserSessionByUsername(String username) {

        long timeout = environment.getProperty("user.session.timeout", Long.class, 60 * 2L);

        UserSessionExample example = new UserSessionExample();
        example.or().andUsernameEqualTo(username).andCreationTimeGreaterThan(LocalDateTime.now().minusMinutes(timeout));
        return userSessionMapper.selectByExample(example).stream().findFirst().orElse(null);
    }

    /**
     * 创建一个用户 session
     *
     * @param user         用户
     * @param token        token
     * @param accessToken  统一认证token
     * @param refreshToken 刷新统一认证token
     * @return
     */
    public String createUserSession(User user, String token, String accessToken, String refreshToken) {
        UserSession userSession = new UserSession();
        userSession.setUserId(user.getId());
        userSession.setUsername(user.getUsername());
        userSession.setName(user.getName());
        userSession.setToken(token);
        userSession.setAccessToken(accessToken);
        userSession.setRefreshToken(refreshToken);
        userSessionMapper.insertSelective(userSession);
        return userSession.getId();
    }

    /**
     * 删除用户 session，并保留指定数量的 session
     *
     * @param username
     * @param keepCounts
     */
    public void deleteUserSession(String username, int keepCounts) {
        UserSessionExample example = new UserSessionExample();
        example.setOrderByClause("creation_time desc");
        example.or().andUsernameEqualTo(username);
        List<UserSession> userSessions = userSessionMapper.selectByExample(example);
        if (userSessions.size() > keepCounts) {
            for (int i = keepCounts; i < userSessions.size(); i++) {
                userSessionMapper.deleteByPrimaryKey(userSessions.get(i).getId());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteByPrimaryKey(String id) {
        return userSessionMapper.deleteByPrimaryKey(id);
    }

}
