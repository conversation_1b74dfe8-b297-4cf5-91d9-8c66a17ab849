package com.csci.susdev.service;


import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.util.FileUtil;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.susdev.mapper.TzhBsFileMapper;
import com.csci.susdev.model.TzhBsFile;
import com.csci.susdev.model.TzhBsFileExample;
import com.csci.susdev.qo.TzhBsFileListQO;
import com.csci.susdev.qo.TzhBsFileQO;
import com.csci.susdev.vo.TzhBsFileVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;


@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class TzhBsFileService  {

    @Resource
    private TzhBsFileMapper mapper;

    @Transactional(rollbackFor = Exception.class)
    public String upload(TzhBsFileQO qo, MultipartFile multipartFile) throws IOException {
        //String[] availableTypes = {"jpg","jpeg","png","gif", "pdf", "doc", "docx", "xls", "xlsx", "rar", "gz", "ps"};
        //if (!Arrays.asList(availableTypes).contains(FileUtil.getFileType(multipartFile.getBytes()))){
        //    throw new ServiceException("文件類型錯誤");
        //}

//
    	int result = 0;

        UserInfo userInfo = ContextUtils.getCurrentUser();
        String username = userInfo.getUsername();

        if(StringUtils.isNotEmpty(qo.getId())) {
            this.delete(qo.getId());
        }

    	// 生成模型
        String newId = UUID.randomUUID().toString();
    	TzhBsFile x = new TzhBsFile();
    	BeanUtils.copyProperties(qo, x);
    	x.setId(newId);
        x.setProtocolname(qo.getProtocolName());
        x.setCategory(qo.getCategory());
        x.setSection(qo.getSection());
    	x.setData(multipartFile.getBytes());
    	x.setCreatedby(username);
    	x.setCreatedtime(LocalDateTime.now());
    	x.setIsdeleted(false);

    	// 新增新的記錄
    	result += mapper.insert(x);

        return newId;
    }

    @Transactional(rollbackFor = Exception.class)
    public int delete(String id) {
    	int result = 0;

        UserInfo userInfo = ContextUtils.getCurrentUser();
        String username = userInfo.getUsername();

        TzhBsFileExample example = new TzhBsFileExample();

        TzhBsFileExample.Criteria criteria = example.or();
        criteria.andIdEqualTo(id);
        criteria.andIsdeletedEqualTo(false);

        TzhBsFile x = new TzhBsFile();
        x.setId(null);
        x.setIsdeleted(true);
        x.setDeletedby(username);
        x.setDeletedtime(LocalDateTime.now());
        result += mapper.updateByExampleSelective(x, example);

        return result;
    }

    public TzhBsFile get(String id) {
        TzhBsFileExample example = new TzhBsFileExample();

        TzhBsFileExample.Criteria criteria = example.or();
        criteria.andIdEqualTo(id);
        criteria.andIsdeletedEqualTo(false);

        List<TzhBsFile> lst = mapper.selectByExampleWithBLOBs(example);
        if(lst.size() > 0) {
            return lst.get(0);
        } else {
            return null;
        }
    }

    public List<TzhBsFileVO> list(TzhBsFileListQO qo) {
        TzhBsFileExample example = new TzhBsFileExample();

        TzhBsFileExample.Criteria criteria = example.or();
        if(StringUtils.isNotBlank(qo.getSitename())) {
            criteria.andSitenameEqualTo(qo.getSitename());
        }
        if(StringUtils.isNotBlank(qo.getProtocolName())) {
            criteria.andProtocolnameEqualTo(qo.getProtocolName());
        }
        if(StringUtils.isNotBlank(qo.getSection())) {
            criteria.andSectionEqualTo(qo.getSection());
        }
        if(StringUtils.isNotBlank(qo.getCategory())) {
            criteria.andCategoryEqualTo(qo.getCategory());
        }
        criteria.andIsdeletedEqualTo(false);

        List<TzhBsFile> lst = mapper.selectByExampleWithBLOBs(example);

		List<TzhBsFileVO> lstVO = lst.stream().map(x -> {
            TzhBsFileVO vo = new TzhBsFileVO();
            BeanUtils.copyProperties(x, vo);
            return vo;
        }).collect(Collectors.toList());
        return lstVO;
    }
}
