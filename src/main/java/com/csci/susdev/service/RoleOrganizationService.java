package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.mapper.RoleOrganizationMapper;
import com.csci.susdev.model.RoleOrganization;
import com.csci.susdev.model.RoleOrganizationExample;
import com.csci.susdev.model.UserOrganizationExample;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@DS(DatasourceContextEnum.SUSDEV)
@LogMethod
public class RoleOrganizationService {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(RoleOrganizationService.class);

    @Autowired
    private RoleOrganizationMapper mapper;


    public List<RoleOrganization> selectByExample(RoleOrganizationExample example) {
        return mapper.selectByExample(example);
    }


    /**
     * 角色组织关系 数据列表
     *
     * @param roleId
     * @return
     */
    public List<RoleOrganization> listRoleOrganization(String roleId) {
        RoleOrganizationExample example = new RoleOrganizationExample();
        example.or().andRoleIdEqualTo(roleId);
        return mapper.selectByExample(example);
    }

    /**
     * 保存 角色组织关系 信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param roleOrganizationLst
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<String> saveRoleOrganizationList(List<RoleOrganization> roleOrganizationLst) {
    	List<String> idLst = new ArrayList<>();
    	for(RoleOrganization roleOrganization : roleOrganizationLst) {
    		this.saveRoleOrganization(roleOrganization);
            idLst.add(roleOrganization.getId());
    	}
        return idLst;
    }

    /**
     * 保存 角色组织关系 信息, 如果是已经存在的记录就保存，否则新增
     * 传入的参数id为空表示新增，否则表示更新
     *
     * @param roleOrganization
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveRoleOrganization(RoleOrganization roleOrganization) {
        if (Objects.equals(roleOrganization.getIsDeleted(), Boolean.TRUE)) {
            this.deleteRoleOrganization(roleOrganization.getId());
        } else {
            UserInfo currentUser = ContextUtils.getCurrentUser();
            LocalDateTime now = LocalDateTime.now();
            if (StringUtils.isBlank(roleOrganization.getId())) {
                roleOrganization.setCreationTime(now);
                roleOrganization.setCreateUsername(currentUser.getUsername());
                roleOrganization.setLastUpdateTime(now);
                roleOrganization.setLastUpdateUsername(currentUser.getUsername());
                mapper.insertSelective(roleOrganization);
            } else {
                roleOrganization.setLastUpdateTime(now);
                roleOrganization.setLastUpdateUsername(currentUser.getUsername());
                mapper.updateByPrimaryKeySelective(roleOrganization);
            }
        }
        return roleOrganization.getId();
    }



    /**
     * 刪除 角色组织关系 信息
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteRoleOrganization(String id) {
        return mapper.deleteByPrimaryKey(id);
    }


    /**
     * 刪除指定角色的所有组织关系
     *
     * @param roleId 角色id
     * @return
     */
    public int deleteRoleOrganizationByRoleId(String roleId) {
        RoleOrganizationExample example = new RoleOrganizationExample();
        example.or().andRoleIdEqualTo(roleId);
        return mapper.deleteByExample(example);
    }


}
