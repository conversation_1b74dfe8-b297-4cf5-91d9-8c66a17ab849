package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.model.CdmsMaterialInvoice;
import com.csci.susdev.util.HttpClientUtils;
import com.csci.susdev.util.JsonUtils;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.tzh.mapper.*;
import com.csci.tzh.model.*;
import com.csci.susdev.model.ResultPage;
import com.csci.tzh.qo.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@DS(DatasourceContextEnum.TANZHONGHE)
@LogMethod
public class FMaterialDetailService {

	@Autowired
	private FMaterialDetailCustomMapper mapper;

	@Autowired
	private TzhMaterialInvoiceTransportMapper tzhMaterialInvoiceTransportMapper;

	public ResultPage<Map<String, Object>> list(FMaterialDetailPageableQO qo) {
		PageHelper.startPage(qo.getCurPage(), qo.getPageSize());

		if(qo.getRecordYearMonthFrom() == null) {
			qo.setRecordYearMonthFrom(0);
		}

		if(qo.getRecordYearMonthTo() == null) {
			qo.setRecordYearMonthTo(999999);
		}

		List<Map<String, Object>> lst = mapper.list(qo.getSiteName(),
				qo.getProtocol(),
				qo.getRecordYearMonthFrom(),
				qo.getRecordYearMonthTo());
		ResultPage<Map<String, Object>> resultPage = new ResultPage<>(lst, true);

		return resultPage;
	}


	@Transactional(rollbackFor = Exception.class)
	public TzhMaterialInvoiceTransport save(TzhMaterialInvoiceTransport model) {
		UserInfo currentUser = ContextUtils.getCurrentUser();

		model.setCreatedtime(LocalDateTime.now());
		model.setCreatedby(currentUser.getUsername());

		this.delete(model.getSitename(), model.getProtocolid(), model.getBillno(), model.getMaterialcode());

		if(model.getIsdeleted() == false) {
			model.setCreatedby(currentUser.getUsername());
			model.setCreatedtime(LocalDateTime.now());
			tzhMaterialInvoiceTransportMapper.insertSelective(model);
		}

		return model;
	}

	@Transactional(rollbackFor = Exception.class)
	public int delete(String siteName, String protocolId, String billNo, String materialCode) {
		int result = 0;

		TzhMaterialInvoiceTransportExample example = new TzhMaterialInvoiceTransportExample();
		TzhMaterialInvoiceTransportExample.Criteria criteria = example.or();
		criteria.andBillnoEqualTo(billNo);
		criteria.andMaterialcodeEqualTo(materialCode);
		criteria.andSitenameEqualTo(siteName);
		criteria.andProtocolidEqualTo(protocolId);
		criteria.andIsdeletedEqualTo(false);

		TzhMaterialInvoiceTransport x = new TzhMaterialInvoiceTransport();
		x.setIsdeleted(true);

		result += tzhMaterialInvoiceTransportMapper.updateByExampleSelective(x, example);

		return result;
	}

	public String downloadCdmsInvoicePdf(String billNo) throws Exception {
		ObjectMapper objectMapper = JsonUtils.getObjectMapper();
		//用户登录
		String loginUrl = "https://cdms.3311csci.com/csci.perms/api/services/csci/permission/Login";
		String loginPayload = "{\"UserName\":\"report_download\",\"PassWord\":\"5nrtiLSjIqYeaPZ3q7jrWm2RtCI/3ypC5i3NXPaENEtobXUtPEe5qFHiNE8NQ4FHnfyswH1aLKUQKsJjtdcEb9nzlmEs7cLLuLM73e3p+Mjp4iGyORta3CvMYGKEQOOBzvlgAHfNtRgshE/9mUpZRjCTY0VMBTjCeEYCgKl0vFM=\",\"TenantId\":\"8\",\"OrganId\":\"35\"}";
		String loginResponse = HttpClientUtils.doPostJson(loginUrl, loginPayload);
		Map<String, Object> cdmsLoginResponseVO =  objectMapper.readValue(loginResponse, new TypeReference<Map<String, Object>>(){});
		Map<String, Object> cdmsLoginResponseTokenVO = (Map<String, Object>)cdmsLoginResponseVO.get("Token");

		//获取一次性访问凭据
		String onceTicketUrl = "https://cdms.3311csci.com/csci.perms/api/services/csci/permission/OnceTicket";
		String onceTicketPayload = "{}";
		Map<String, String> onceTicketHeader = new HashMap<>();
		onceTicketHeader.put("Authorization", (String) cdmsLoginResponseTokenVO.get("AccessToken"));
		String onceTicketResponse = HttpClientUtils.doPostJson(onceTicketUrl, onceTicketPayload, onceTicketHeader).replace("\"", "");

		if(!onceTicketResponse.contains("ot-")) {
			throw new Exception("获取一次性访问凭据失敗:" + onceTicketResponse);
		}
		String id = mapper.getInvoiceId(billNo);
		String pdfLink = "https://cdms.3311csci.com/cdms4.reports/view/43/" + id + "?token=" + onceTicketResponse;

		return pdfLink;
	}

	public List<CdmsMaterialInvoice> getCdmsMaterialInvoiceList(String organizationId, int year) {
		List<CdmsMaterialInvoice> cdmsMaterialInvoiceList = mapper.getMaterialInvoiceList(organizationId, year);
		return cdmsMaterialInvoiceList;
	}
}
