package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.NamingConventionEnum;
import com.csci.susdev.mapper.ProtocolConfigurationCustomMapper;
import com.csci.susdev.mapper.ProtocolConfigurationMapper;
import com.csci.susdev.model.ProtocolConfiguration;
import com.csci.susdev.model.ProtocolConfigurationExample;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.modelcovt.ProtocolConfigurationConverter;
import com.csci.susdev.qo.ProtocolConfigurationQO;
import com.csci.susdev.util.MybatisHelper;
import com.csci.susdev.vo.ProtocolConfigurationVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class ProtocolConfigurationService {

    @Resource
    private ProtocolConfigurationMapper protocolConfigurationMapper;

    @Resource
    private ProtocolConfigurationCustomMapper protocolConfigurationCustomMapper;

    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(ProtocolConfiguration record) {
        return protocolConfigurationMapper.insertSelective(record);
    }

    public ProtocolConfiguration selectByPrimaryKey(String id) {
        return protocolConfigurationMapper.selectByPrimaryKey(id);
    }

    public ProtocolConfigurationVO getProtocolConfiguration(String id) {
        checkExist(id, "id不能为空");
        ProtocolConfiguration protocolConfiguration = selectByPrimaryKey(id);
        checkExist(protocolConfiguration, "未找到对应的记录");
        if(protocolConfiguration.getIsDeleted()) {
            throw new ServiceException("紀錄已被刪除");
        }
        return ProtocolConfigurationConverter.convertToVO(protocolConfiguration);
    }

    @Transactional(rollbackFor = Exception.class)
    public String saveProtocolConfiguration(ProtocolConfigurationVO protocolConfigurationVO) {
        if (StringUtils.isBlank(protocolConfigurationVO.getId())) {
            // 新增
            return doAdd(protocolConfigurationVO);
        } else {
            checkExist(protocolConfigurationVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(protocolConfigurationVO);
        }

    }

    public ResultPage<ProtocolConfigurationVO> listProtocolConfiguration(ProtocolConfigurationQO protocolConfigurationQO) {
        List<ProtocolConfigurationVO> protocolConfigurationVOList = new ArrayList<>();
        if (StringUtils.isNotBlank(protocolConfigurationQO.getOrderBy())) {
            List<String> fieldNames = Arrays.stream(ProtocolConfigurationVO.class.getDeclaredFields()).map(field -> field.getName()).collect(Collectors.toList());
            if(MybatisHelper.checkSqlInjectionForOrderBy(protocolConfigurationQO.getOrderBy(), fieldNames, NamingConventionEnum.CAMO)) {
                throw new ServiceException("orderBy 有SQL注入行為");
            }
        }

        List<ProtocolConfigurationVO> voList = protocolConfigurationCustomMapper.listVOInherited(protocolConfigurationQO);

        for(ProtocolConfigurationVO vo : voList) {
            protocolConfigurationVOList.add(ProtocolConfigurationConverter.fillInDetail(vo));
        }
        return new ResultPage<>(voList, protocolConfigurationVOList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteProtocolConfiguration(String id) {
        ProtocolConfiguration record = selectByPrimaryKey(id);
        record.setIsDeleted(true);
        protocolConfigurationMapper.updateByPrimaryKeySelective(record);
    }

    private String doAdd(ProtocolConfigurationVO protocolConfigurationVO) {
        ProtocolConfiguration protocolConfiguration = ProtocolConfigurationConverter.convertToModel(protocolConfigurationVO);
        protocolConfigurationMapper.insertSelective(protocolConfiguration);
        return protocolConfiguration.getId();
    }

    private String doUpdate(ProtocolConfigurationVO protocolConfigurationVO) {
        ProtocolConfiguration originalRecord = selectByPrimaryKey(protocolConfigurationVO.getId());
        ProtocolConfiguration protocolConfiguration = ProtocolConfigurationConverter.convertToModelWithBase(protocolConfigurationVO, originalRecord);

        ProtocolConfigurationExample example = new ProtocolConfigurationExample();
        example.or().andIdEqualTo(protocolConfigurationVO.getId()).andLastUpdateVersionEqualTo(protocolConfigurationVO.getLastUpdateVersion());
        int updateCount = protocolConfigurationMapper.updateByExample(protocolConfiguration, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return protocolConfiguration.getId();
    }

    public List<ProtocolConfiguration> selectByExample(ProtocolConfigurationExample example) {
        return protocolConfigurationMapper.selectByExample(example);
    }









}
