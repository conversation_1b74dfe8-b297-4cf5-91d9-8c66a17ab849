package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.NamingConventionEnum;
import com.csci.susdev.mapper.ProtocolManagementCustomMapper;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.ProtocolManagementQO;
import com.csci.susdev.util.MybatisHelper;
import com.csci.susdev.vo.*;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@LogMethod
public class ProtocolManagementService {

    @Resource
    private ProtocolManagementCustomMapper protocolManagementCustomMapper;


    public ResultPage<ProtocolManagementVO> list(ProtocolManagementQO protocolManagementQO) {
        if (StringUtils.isNotBlank(protocolManagementQO.getOrderBy())) {
            List<String> fieldNames = Arrays.stream(ProtocolDetailVO.class.getDeclaredFields()).map(field -> field.getName()).collect(Collectors.toList());
            if(MybatisHelper.checkSqlInjectionForOrderBy(protocolManagementQO.getOrderBy(), fieldNames, NamingConventionEnum.CAMO)) {
                throw new ServiceException("orderBy 有SQL注入行為");
            }
        }
        PageHelper.startPage(protocolManagementQO.getCurPage(), protocolManagementQO.getPageSize());
        List<ProtocolManagementVO> list = protocolManagementCustomMapper.list(protocolManagementQO);
        return new ResultPage<>(list, true);
    }

    public List<ProtocolVO> listProtocolManagement(ProtocolManagementQO protocolManagementQO) {
        if (StringUtils.isNotBlank(protocolManagementQO.getOrderBy())) {
            List<String> fieldNames = Arrays.stream(ProtocolDetailVO.class.getDeclaredFields()).map(field -> field.getName()).collect(Collectors.toList());
            if(MybatisHelper.checkSqlInjectionForOrderBy(protocolManagementQO.getOrderBy(), fieldNames, NamingConventionEnum.CAMO)) {
                throw new ServiceException("orderBy 有SQL注入行為");
            }
        }

        List<ProtocolManagementVO> protocolManagementVOS = protocolManagementCustomMapper.list(protocolManagementQO);
        return convert(protocolManagementVOS);
    }


    public List<ProtocolVO> convert(List<ProtocolManagementVO> protocolManagementVOs) {
        // 按protocolId分组
        Map<String, List<ProtocolManagementVO>> protocolGroups = protocolManagementVOs.stream()
                .collect(Collectors.groupingBy(ProtocolManagementVO::getProtocolId));

        return protocolGroups.values().stream()
                .map(group -> {
                    // 构建ProtocolVO
                    ProtocolVO protocolVO = new ProtocolVO();
                    ProtocolManagementVO first = group.get(0);
                    protocolVO.setId(first.getProtocolId());
                    protocolVO.setName(first.getProtocolName());
                    protocolVO.setNameSc(first.getProtocolNameSc());
                    protocolVO.setNameEn(first.getProtocolNameEn());
                    protocolVO.setDescription(first.getProtocolDescription());
                    protocolVO.setLastUpdateVersion(first.getProtocolLastUpdateVersion());
                    // 处理大类
                    Map<String, List<ProtocolManagementVO>> categoryGroups = group.stream().filter(e -> StringUtils.isNotEmpty(e.getCategoryId()))
                            .collect(Collectors.groupingBy(ProtocolManagementVO::getCategoryId));

                    List<ProtocolCategoryVO> categories = categoryGroups.values().stream()
                            .map(categoryGroup -> {
                                ProtocolCategoryVO categoryVO = new ProtocolCategoryVO();
                                ProtocolManagementVO firstCategory = categoryGroup.get(0);
                                categoryVO.setId(firstCategory.getCategoryId());
                                categoryVO.setProtocolId(firstCategory.getProtocolId());
                                categoryVO.setCategoryName(firstCategory.getCategoryName());
                                categoryVO.setCategoryNameSc(firstCategory.getCategoryNameSc());
                                categoryVO.setCategoryNameEn(firstCategory.getCategoryNameEn());
                                categoryVO.setLastUpdateVersion(firstCategory.getCategoryLastUpdateVersion());

                                // 处理小类
                                Map<String, List<ProtocolManagementVO>> subCategoryGroups = categoryGroup.stream().filter(e -> StringUtils.isNotEmpty(e.getSubCategoryId()))
                                        .collect(Collectors.groupingBy(ProtocolManagementVO::getSubCategoryId));

                                List<ProtocolSubCategoryVO> subCategories = subCategoryGroups.values().stream()
                                        .map(subCategoryGroup -> {
                                            ProtocolSubCategoryVO subCategoryVO = new ProtocolSubCategoryVO();
                                            ProtocolManagementVO firstSub = subCategoryGroup.get(0);
                                            subCategoryVO.setId(firstSub.getSubCategoryId());
                                            subCategoryVO.setCategoryId(firstSub.getCategoryId());
                                            subCategoryVO.setSubCategoryName(firstSub.getSubCategoryName());
                                            subCategoryVO.setSubCategoryNameSc(firstSub.getSubCategoryNameSc());
                                            subCategoryVO.setSubCategoryNameEn(firstSub.getSubCategoryNameEn());
                                            subCategoryVO.setLastUpdateVersion(firstSub.getSubCategoryLastUpdateVersion());
                                            return subCategoryVO;
                                        })
                                        .collect(Collectors.toList());

                                categoryVO.setProtocolSubCategoryVOList(subCategories);
                                return categoryVO;
                            })
                            .collect(Collectors.toList());

                    protocolVO.setProtocolCategoryVOList(categories);
                    return protocolVO;
                })
                .collect(Collectors.toList());
    }


    public ProtocolManagementVO getBySubCategoryId(String subCategoryId) {
        return protocolManagementCustomMapper.getBySubCategoryId(subCategoryId);
    }
}
