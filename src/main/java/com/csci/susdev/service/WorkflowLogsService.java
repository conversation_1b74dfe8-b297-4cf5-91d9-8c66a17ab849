package com.csci.susdev.service;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.WorkflowLogsMapper;
import com.csci.susdev.model.WorkflowLogs;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@LogMethod
public class WorkflowLogsService {

    @Resource
    private WorkflowLogsMapper workflowLogsMapper;

    /**
     * proxy for {@link WorkflowLogsMapper#insertSelective(WorkflowLogs)}
     *
     * @param record record
     * @return
     */
    public int insertSelective(WorkflowLogs record) {
        return workflowLogsMapper.insertSelective(record);
    }
}
