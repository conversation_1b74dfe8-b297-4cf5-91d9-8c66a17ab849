package com.csci.susdev.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.handler.CeIdentificationRunawayEmissionHandler;
import com.csci.susdev.handler.ColumnMergeStrategy;
import com.csci.susdev.handler.ImageCellWriteHandler;
import com.csci.susdev.mapper.CeIdentificationRunawayEmissionMapper;
import com.csci.susdev.mapper.MinioAttachmentMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.CeIdentificationRunawayEmissionConverter;
import com.csci.susdev.qo.CeIdentificationRunawayEmissionQO;
import com.csci.susdev.vo.CeIdentificationRunawayEmissionVO;
import com.csci.susdev.vo.ExcelCeIdentificationRunawayEmissionVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class CeIdentificationRunawayEmissionService {

    @Resource
    private CeIdentificationRunawayEmissionMapper ceIdentificationRunawayEmissionMapper;
    @Resource
    private CeIdentificationService ceIdentificationService;
    @Resource
    private OrganizationService organizationService;
    @Resource
    private MinioAttachmentService minioAttachmentService;
    @Resource
    private MinioAttachmentMapper mapper;



    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(CeIdentificationRunawayEmission record) {
        return ceIdentificationRunawayEmissionMapper.insertSelective(record);
    }

    public CeIdentificationRunawayEmission selectByPrimaryKey(String id) {
        return ceIdentificationRunawayEmissionMapper.selectByPrimaryKey(id);
    }

    public CeIdentificationRunawayEmissionVO getCeIdentificationRunawayEmission(String id) {
        checkExist(id, "id不能为空");
        CeIdentificationRunawayEmission ceIdentificationRunawayEmission = selectByPrimaryKey(id);
        checkExist(ceIdentificationRunawayEmission, "未找到对应的记录");
        if(ceIdentificationRunawayEmission.getIsDeleted()) {
            throw new ServiceException("紀錄已被刪除");
        }
        return CeIdentificationRunawayEmissionConverter.convertToVO(ceIdentificationRunawayEmission);
    }


    @Transactional(rollbackFor = Exception.class)
    public String saveCeIdentificationRunawayEmission(CeIdentificationRunawayEmissionVO ceIdentificationRunawayEmissionVO) {
        if (StringUtils.isBlank(ceIdentificationRunawayEmissionVO.getId())) {
            // 新增
            return doAdd(ceIdentificationRunawayEmissionVO);
        } else {
            checkExist(ceIdentificationRunawayEmissionVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(ceIdentificationRunawayEmissionVO);
        }

    }

    public ResultPage<CeIdentificationRunawayEmissionVO> listCeIdentificationRunawayEmission(CeIdentificationRunawayEmissionQO ceIdentificationRunawayEmissionQO) {

        checkExist(ceIdentificationRunawayEmissionQO.getCeIdentificationHeadId(), "分判商主表记录ID不能为空");
        CeIdentificationRunawayEmissionExample example = new CeIdentificationRunawayEmissionExample();
        example.or().andCeIdentificationHeadIdEqualTo(ceIdentificationRunawayEmissionQO.getCeIdentificationHeadId()).andIsDeletedEqualTo(false);

        if (StringUtils.isBlank(ceIdentificationRunawayEmissionQO.getSortName())) {
            example.setOrderByClause("creation_time asc");
        }

        PageHelper.startPage(ceIdentificationRunawayEmissionQO.getCurPage(), ceIdentificationRunawayEmissionQO.getPageSize());
        List<CeIdentificationRunawayEmission> runawayEmissions = ceIdentificationRunawayEmissionMapper.selectByExample(example);
        return new ResultPage<>(runawayEmissions, runawayEmissions.stream().map(CeIdentificationRunawayEmissionConverter::convertToVO).collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteCeIdentificationRunawayEmission(String id) {
        CeIdentificationRunawayEmission record = selectByPrimaryKey(id);

        record.setIsDeleted(true);
        ceIdentificationRunawayEmissionMapper.updateByPrimaryKeySelective(record);
    }

    private String doAdd(CeIdentificationRunawayEmissionVO ceIdentificationRunawayEmissionVO) {
        CeIdentificationRunawayEmission ceIdentificationRunawayEmission = CeIdentificationRunawayEmissionConverter.convertToModel(ceIdentificationRunawayEmissionVO);
        ceIdentificationRunawayEmissionMapper.insertSelective(ceIdentificationRunawayEmission);
        return ceIdentificationRunawayEmission.getId();
    }

    private String doUpdate(CeIdentificationRunawayEmissionVO ceIdentificationRunawayEmissionVO) {
        CeIdentificationRunawayEmission originalRecord = selectByPrimaryKey(ceIdentificationRunawayEmissionVO.getId());
        CeIdentificationRunawayEmission ceIdentificationRunawayEmission = CeIdentificationRunawayEmissionConverter.convertToModelWithBase(ceIdentificationRunawayEmissionVO, originalRecord);

        CeIdentificationRunawayEmissionExample example = new CeIdentificationRunawayEmissionExample();
        example.or().andIdEqualTo(ceIdentificationRunawayEmissionVO.getId()).andLastUpdateVersionEqualTo(ceIdentificationRunawayEmissionVO.getLastUpdateVersion());
        int updateCount = ceIdentificationRunawayEmissionMapper.updateByExample(ceIdentificationRunawayEmission, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return ceIdentificationRunawayEmission.getId();
    }

    public List<CeIdentificationRunawayEmission> selectByExample(CeIdentificationRunawayEmissionExample example) {
        return ceIdentificationRunawayEmissionMapper.selectByExample(example);
    }

    public byte[] exportCeIdentificationRunawayEmission(CeIdentificationRunawayEmissionQO ceIdentificationRunawayEmissionQO, HttpHeaders headers) {

        CeIdentificationHead ceIdentificationHead = ceIdentificationService.selectByPrimaryKey(ceIdentificationRunawayEmissionQO.getCeIdentificationHeadId());
        if(ceIdentificationHead == null) {
            throw new ServiceException("head不存在");
        }
        Organization organization = organizationService.selectByPrimaryKey(ceIdentificationHead.getOrganizationId());
        if(organization == null) {
            throw new ServiceException("organization不存在");
        }

        List<CeIdentificationRunawayEmissionVO> list = this.listCeIdentificationRunawayEmission(ceIdentificationRunawayEmissionQO).getList();
        if (CollectionUtils.isEmpty(list)) {
            throw new ServiceException("无数据可导出！");
        }
        String filename = new StringBuilder()
                .append("碳排识别_逸散排放填写").append("_")
                .append(String.valueOf(ceIdentificationHead.getYear()*100 + ceIdentificationHead.getMonth())).append("_")
                .append(DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()))
                .append(".xlsx").toString();
        String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8);
        headers.setContentDispositionFormData("attachment", encodedFilename);

        try (ByteArrayOutputStream out = new ByteArrayOutputStream();
             ExcelWriter excelWriter = EasyExcel.write(out).build()) {


            // 数据转换
            List<ExcelCeIdentificationRunawayEmissionVO> excelData = new ArrayList<>();
            CeIdentificationRunawayEmissionVO firstRecord = list.get(0);
            //
            ExcelCeIdentificationRunawayEmissionVO excelOne = new ExcelCeIdentificationRunawayEmissionVO();
            excelOne.setModule("化粪池逸散");
            excelOne.setConfirmationItemOne("是否有自己运营管理的化粪池");
            excelOne.setConfirmationItemTwo("是否有自己运营管理的化粪池");
            excelOne.setConfirmationItemThree("是否有自己运营管理的化粪池");
            excelOne.setInstructions("1）如果项目是作为业主或主承保商，自己负责请第三方清运化粪池、储粪缸等，算作自己运营的；\n" +
                    "2）如果是用的第三方的厕所或者其他业主的厕所，也不负责卫生、清运等维护工作，就不算自己运营的");
            excelOne.setValue(firstRecord.getIsOwnSepticTank());
            excelData.add(excelOne);
            //
            ExcelCeIdentificationRunawayEmissionVO excelTwo = new ExcelCeIdentificationRunawayEmissionVO();
            excelTwo.setModule("化粪池逸散");
            excelTwo.setConfirmationItemOne("如果有自己运营管理的化粪池，请填报右侧员工信息和数据");
            excelTwo.setConfirmationItemTwo("如果有自己运营管理的化粪池，请填报右侧员工信息和数据");
            excelTwo.setConfirmationItemThree("正式员工人数（个）");
            excelTwo.setInstructions("仅需统计自己的员工数，分包商人员不包含在内");
            excelTwo.setValue(firstRecord.getRegularEmployeeNum());
            excelData.add(excelTwo);
            //
            ExcelCeIdentificationRunawayEmissionVO excelThree = new ExcelCeIdentificationRunawayEmissionVO();
            excelThree.setModule("化粪池逸散");
            excelThree.setConfirmationItemOne("如果有自己运营管理的化粪池，请填报右侧员工信息和数据");
            excelThree.setConfirmationItemTwo("如果有自己运营管理的化粪池，请填报右侧员工信息和数据");
            excelThree.setConfirmationItemThree("年平均工作天数（天）");
            excelThree.setInstructions("可以通过扣除法定假期、周末等方式计算");
            excelThree.setValue(firstRecord.getAverageWorkDayYear());
            excelData.add(excelThree);
            //
            ExcelCeIdentificationRunawayEmissionVO excelFour = new ExcelCeIdentificationRunawayEmissionVO();
            excelFour.setModule("化粪池逸散");
            excelFour.setConfirmationItemOne("如果有自己运营管理的化粪池，请填报右侧员工信息和数据");
            excelFour.setConfirmationItemTwo("如果有自己运营管理的化粪池，请填报右侧员工信息和数据");
            excelFour.setConfirmationItemThree("日平均工作小时数-需通勤（h）");
            excelFour.setInstructions("如果员工不居住在项目工地上，需估算每日工作时长如8小时、10小时等");
            excelFour.setValue(firstRecord.getAverageDailyCommuting());
            excelData.add(excelFour);
            //
            ExcelCeIdentificationRunawayEmissionVO excelFive = new ExcelCeIdentificationRunawayEmissionVO();
            excelFive.setModule("化粪池逸散");
            excelFive.setConfirmationItemOne("如果有自己运营管理的化粪池，请填报右侧员工信息和数据");
            excelFive.setConfirmationItemTwo("如果有自己运营管理的化粪池，请填报右侧员工信息和数据");
            excelFive.setConfirmationItemThree("日平均工作小时数-需住宿（h）");
            excelFive.setInstructions("如部分员工直接住在项目工地上即夜间也会使用厕所，时长按24小时处理");
            excelFive.setValue(firstRecord.getAverageDailyAccommodation());
            excelData.add(excelFive);
            // 查找附件
            MinioAttachmentExample example = new MinioAttachmentExample();

            MinioAttachmentExample.Criteria criteria = example.or();
            criteria.andRefIdEqualTo(ceIdentificationRunawayEmissionQO.getCeIdentificationHeadId());
            List<String> sectionList = new ArrayList<>();
            sectionList.add("逸散排放-空调铭牌");
            sectionList.add("逸散排放-冰箱冰柜铭牌");
            sectionList.add("逸散排放-公务车");
            criteria.andSectionIn(sectionList);
            example.setOrderByClause("last_update_time desc");
            List<MinioAttachment> minioAttachments = mapper.selectByExample(example);
            Map<String, List<MinioAttachment>> fileMap = minioAttachments.stream().collect(Collectors.groupingBy(e -> e.getCategory() + "#" + e.getSection()));
            int i = 1;
            for (CeIdentificationRunawayEmissionVO runawayEmissionVO : list) {
                if (StringUtils.isNotEmpty(runawayEmissionVO.getAirconditionRefrigerantType())) {
                    ExcelCeIdentificationRunawayEmissionVO excelAirOne = new ExcelCeIdentificationRunawayEmissionVO();
                    excelAirOne.setModule("制冷剂逸散");
                    excelAirOne.setConfirmationItemOne("空调");
                    excelAirOne.setConfirmationItemTwo("请拍摄所有自己运营管理的空调、冰箱冷柜的铭牌信息(如下图，放大可查看），需包含填充的制冷剂型号和重量信息，填写并粘贴在右侧表格中");
                    excelAirOne.setConfirmationItemThree("空调铭牌"+ i +"-制冷剂型号");
                    excelAirOne.setInstructions("1）如果是自己购买、自己负责维保的空调和冰柜需要纳入统计；\n" +
                            "2）如果是租赁的设施归属于业主或其他第三方，且自己不负责保修维护等，就不需要纳入统计");
                    excelAirOne.setValue(runawayEmissionVO.getAirconditionRefrigerantType());
                    excelData.add(excelAirOne);
                    ExcelCeIdentificationRunawayEmissionVO excelAirTwo = new ExcelCeIdentificationRunawayEmissionVO();
                    excelAirTwo.setModule("制冷剂逸散");
                    excelAirTwo.setConfirmationItemOne("空调");
                    excelAirTwo.setConfirmationItemTwo("请拍摄所有自己运营管理的空调、冰箱冷柜的铭牌信息(如下图，放大可查看），需包含填充的制冷剂型号和重量信息，填写并粘贴在右侧表格中");
                    excelAirTwo.setConfirmationItemThree("空调铭牌"+ i +"制冷剂充注量");
                    excelAirTwo.setInstructions("1）如果是自己购买、自己负责维保的空调和冰柜需要纳入统计；\n" +
                            "2）如果是租赁的设施归属于业主或其他第三方，且自己不负责保修维护等，就不需要纳入统计");
                    excelAirTwo.setValue(runawayEmissionVO.getAirconditionRefrigerantChargeAmount());
                    excelData.add(excelAirTwo);
                    ExcelCeIdentificationRunawayEmissionVO excelAirThree = new ExcelCeIdentificationRunawayEmissionVO();
                    excelAirThree.setModule("制冷剂逸散");
                    excelAirThree.setConfirmationItemOne("空调");
                    excelAirThree.setConfirmationItemTwo("请拍摄所有自己运营管理的空调、冰箱冷柜的铭牌信息(如下图，放大可查看），需包含填充的制冷剂型号和重量信息，填写并粘贴在右侧表格中");
                    excelAirThree.setConfirmationItemThree("空调铭牌"+ i +"拍摄图片");
                    excelAirThree.setInstructions("1）如果是自己购买、自己负责维保的空调和冰柜需要纳入统计；\n" +
                            "2）如果是租赁的设施归属于业主或其他第三方，且自己不负责保修维护等，就不需要纳入统计");
                    excelAirThree.setValue(runawayEmissionVO.getAirconditionTakePhoto());
                    if (fileMap.containsKey(runawayEmissionVO.getId() + "#逸散排放-空调铭牌")) {
                        List<MinioAttachment> attachments = fileMap.get(runawayEmissionVO.getId() + "#逸散排放-空调铭牌");
                        byte[] serviceObject = minioAttachmentService.getObject(attachments.get(0).getMinioFileName());
                        excelAirThree.setImgData(serviceObject);
                    }
                    excelData.add(excelAirThree);
                    i++;
                }

            }
            i = 1;
            for (CeIdentificationRunawayEmissionVO runawayEmissionVO : list) {
                if (StringUtils.isNotEmpty(runawayEmissionVO.getFridgeRefrigerantType())) {
                    ExcelCeIdentificationRunawayEmissionVO excelAirOne = new ExcelCeIdentificationRunawayEmissionVO();
                    excelAirOne.setModule("制冷剂逸散");
                    excelAirOne.setConfirmationItemOne("冰箱/冷柜");
                    excelAirOne.setConfirmationItemTwo("请拍摄所有自己运营管理的空调、冰箱冷柜的铭牌信息(如下图，放大可查看），需包含填充的制冷剂型号和重量信息，填写并粘贴在右侧表格中");
                    excelAirOne.setConfirmationItemThree("冰柜铭牌"+ i +"-制冷剂型号");
                    excelAirOne.setInstructions("1）如果是自己购买、自己负责维保的空调和冰柜需要纳入统计；\n" +
                            "2）如果是租赁的设施归属于业主或其他第三方，且自己不负责保修维护等，就不需要纳入统计");
                    excelAirOne.setValue(runawayEmissionVO.getFridgeRefrigerantType());
                    excelData.add(excelAirOne);
                    ExcelCeIdentificationRunawayEmissionVO excelAirTwo = new ExcelCeIdentificationRunawayEmissionVO();
                    excelAirTwo.setModule("制冷剂逸散");
                    excelAirTwo.setConfirmationItemOne("冰箱/冷柜");
                    excelAirTwo.setConfirmationItemTwo("请拍摄所有自己运营管理的空调、冰箱冷柜的铭牌信息(如下图，放大可查看），需包含填充的制冷剂型号和重量信息，填写并粘贴在右侧表格中");
                    excelAirTwo.setConfirmationItemThree("冰柜铭牌"+ i +"制冷剂充注量");
                    excelAirTwo.setInstructions("1）如果是自己购买、自己负责维保的空调和冰柜需要纳入统计；\n" +
                            "2）如果是租赁的设施归属于业主或其他第三方，且自己不负责保修维护等，就不需要纳入统计");
                    excelAirTwo.setValue(runawayEmissionVO.getFridgeRefrigerantChargeAmount());
                    excelData.add(excelAirTwo);
                    ExcelCeIdentificationRunawayEmissionVO excelAirThree = new ExcelCeIdentificationRunawayEmissionVO();
                    excelAirThree.setModule("制冷剂逸散");
                    excelAirThree.setConfirmationItemOne("冰箱/冷柜");
                    excelAirThree.setConfirmationItemTwo("请拍摄所有自己运营管理的空调、冰箱冷柜的铭牌信息(如下图，放大可查看），需包含填充的制冷剂型号和重量信息，填写并粘贴在右侧表格中");
                    excelAirThree.setConfirmationItemThree("冰柜铭牌"+ i +"拍摄图片");
                    excelAirThree.setInstructions("1）如果是自己购买、自己负责维保的空调和冰柜需要纳入统计；\n" +
                            "2）如果是租赁的设施归属于业主或其他第三方，且自己不负责保修维护等，就不需要纳入统计");
                    excelAirThree.setValue(runawayEmissionVO.getFridgeTakePhoto());
                    if (fileMap.containsKey(runawayEmissionVO.getId() + "#逸散排放-冰箱冰柜铭牌")) {
                        List<MinioAttachment> attachments = fileMap.get(runawayEmissionVO.getId() + "#逸散排放-冰箱冰柜铭牌");
                        byte[] serviceObject = minioAttachmentService.getObject(attachments.get(0).getMinioFileName());
                        excelAirThree.setImgData(serviceObject);
                    }
                    excelData.add(excelAirThree);
                    i++;
                }

            }
            // 公务车
            i = 1;
            for (CeIdentificationRunawayEmissionVO runawayEmissionVO : list) {
                if (StringUtils.isNotEmpty(runawayEmissionVO.getServiceCarRefrigerantType())) {
                    ExcelCeIdentificationRunawayEmissionVO excelAirOne = new ExcelCeIdentificationRunawayEmissionVO();
                    excelAirOne.setModule("制冷剂逸散");
                    excelAirOne.setConfirmationItemOne("自有车辆");
                    excelAirOne.setConfirmationItemTwo("请确认公务车的制冷剂型号和填充量并填写，如下图所示信息");
                    excelAirOne.setConfirmationItemThree("公务车"+ i +"-制冷剂型号");
                    excelAirOne.setInstructions("请打开车辆发动机前盖拍摄制冷剂信息图片，如未找到制冷剂信息可通过4S店获取车辆保养添加的制冷剂图片和容量（一般车辆制冷剂为R134a）");
                    excelAirOne.setValue(runawayEmissionVO.getServiceCarRefrigerantType());
                    excelData.add(excelAirOne);
                    ExcelCeIdentificationRunawayEmissionVO excelAirTwo = new ExcelCeIdentificationRunawayEmissionVO();
                    excelAirTwo.setModule("制冷剂逸散");
                    excelAirTwo.setConfirmationItemOne("自有车辆");
                    excelAirTwo.setConfirmationItemTwo("请确认公务车的制冷剂型号和填充量并填写，如下图所示信息");
                    excelAirTwo.setConfirmationItemThree("公务车"+ i +"制冷剂充注量");
                    excelAirTwo.setInstructions("请打开车辆发动机前盖拍摄制冷剂信息图片，如未找到制冷剂信息可通过4S店获取车辆保养添加的制冷剂图片和容量（一般车辆制冷剂为R134a）");
                    excelAirTwo.setValue(runawayEmissionVO.getServiceCarRefrigerantChargeAmount());
                    excelData.add(excelAirTwo);
                    ExcelCeIdentificationRunawayEmissionVO excelAirThree = new ExcelCeIdentificationRunawayEmissionVO();
                    excelAirThree.setModule("制冷剂逸散");
                    excelAirThree.setConfirmationItemOne("自有车辆");
                    excelAirThree.setConfirmationItemTwo("请确认公务车的制冷剂型号和填充量并填写，如下图所示信息");
                    excelAirThree.setConfirmationItemThree("公务车"+ i +"拍摄图片");
                    excelAirThree.setInstructions("请打开车辆发动机前盖拍摄制冷剂信息图片，如未找到制冷剂信息可通过4S店获取车辆保养添加的制冷剂图片和容量（一般车辆制冷剂为R134a）");
                    excelAirThree.setValue(runawayEmissionVO.getServiceCarTakePhoto());
                    if (fileMap.containsKey(runawayEmissionVO.getId() + "#逸散排放-公务车")) {
                        List<MinioAttachment> attachments = fileMap.get(runawayEmissionVO.getId() + "#逸散排放-公务车");
                        byte[] serviceObject = minioAttachmentService.getObject(attachments.get(0).getMinioFileName());
                        excelAirThree.setImgData(serviceObject);
                    }
                    excelData.add(excelAirThree);
                    i++;
                }

            }

            // 创建Sheet并注册合并策略（合并第0列和第1列）
            // 注册处理器
            WriteSheet writeSheet = EasyExcel.writerSheet("逸散排放填写")
                    .head(ExcelCeIdentificationRunawayEmissionVO.class)
                    .registerWriteHandler(new CeIdentificationRunawayEmissionHandler(excelData))
                    .registerWriteHandler(new ImageCellWriteHandler())
                    .build();

            excelWriter.write(excelData, writeSheet);
            excelWriter.finish();
            return out.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
