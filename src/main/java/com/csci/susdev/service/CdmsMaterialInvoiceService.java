package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.mapper.CdmsMaterialInvoiceMapper;
import com.csci.susdev.model.CdmsMaterialInvoice;
import com.csci.susdev.model.CdmsMaterialInvoiceExample;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.model.Workflow;
import com.csci.susdev.qo.CdmsMaterailInvoiceQO;
import com.csci.susdev.util.HttpClientUtils;
import com.csci.susdev.vo.WorkflowVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@LogMethod
@DS(DatasourceContextEnum.SUSDEV)
public class CdmsMaterialInvoiceService {

    @Resource
    private FMaterialDetailService fMaterialDetailService;

    @Resource
    private CdmsMaterialInvoiceMapper cdmsMaterialInvoiceMapper;

    public List<CdmsMaterialInvoice> syncCdmsMaterialInvoice(String organizationId, Integer year) throws Exception {
        List<CdmsMaterialInvoice> cdmsMaterialInvoiceList = fMaterialDetailService.getCdmsMaterialInvoiceList(organizationId, year);
        CdmsMaterialInvoiceExample example = new CdmsMaterialInvoiceExample();
        example.or().andOrganizationIdEqualTo(organizationId)
                .andRecordYearMonthGreaterThanOrEqualTo(year*100 + 1)
                .andRecordYearMonthLessThanOrEqualTo(year*100 + 12);
        cdmsMaterialInvoiceMapper.deleteByExample(new CdmsMaterialInvoiceExample());
        for(CdmsMaterialInvoice cdmsMaterialInvoice : cdmsMaterialInvoiceList) {
            cdmsMaterialInvoiceMapper.insert(cdmsMaterialInvoice);
        }
        return cdmsMaterialInvoiceList;
    }

    public ResultPage<CdmsMaterialInvoice> listCdmsMaterialInvoice(CdmsMaterailInvoiceQO qo) throws Exception {
        CdmsMaterialInvoiceExample example = new CdmsMaterialInvoiceExample();
        example.or().andOrganizationIdEqualTo(qo.getOrganizationId())
                .andClassificationEqualTo(qo.getClassification())
                .andRecordYearMonthGreaterThanOrEqualTo(qo.getYear()*100 + 1)
                .andRecordYearMonthLessThanOrEqualTo(qo.getYear()*100 + 12);
        PageHelper.startPage(qo.getCurPage(), qo.getPageSize());
        List<CdmsMaterialInvoice> cdmsMaterialInvoiceList = cdmsMaterialInvoiceMapper.selectByExample(example);

        for(CdmsMaterialInvoice x : cdmsMaterialInvoiceList) {
            if(StringUtils.isNotBlank(x.getInvoiceFile())) {
                String urls = HttpClientUtils.doGet(x.getInvoiceFile());
                if(StringUtils.isNotBlank(urls)) {
                    x.setInvoiceFile(urls.replace("[\"", "").replace("\"]", ""));
                }
            }
        }

        ResultPage<CdmsMaterialInvoice> resultPage = new ResultPage<>(cdmsMaterialInvoiceList);
        resultPage.setList(cdmsMaterialInvoiceList);
        return resultPage;
    }

    public int deleteCdmsMaterialInvoice(String id) {
        return cdmsMaterialInvoiceMapper.deleteByPrimaryKey(id);
    }
}
