package com.csci.susdev.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.math.MathUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.FormCodeEnum;
import com.csci.susdev.constant.NamingConventionEnum;
import com.csci.susdev.mapper.*;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.FactorScopeConverter;
import com.csci.susdev.modelcovt.FactorSelectionConverter;
import com.csci.susdev.modelcovt.FcCarbonFactorHkConverter;
import com.csci.susdev.modelcovt.ProtocolDetailConverter;
import com.csci.susdev.qo.FactorSelectionQO;
import com.csci.susdev.util.ConvertBeanUtils;
import com.csci.susdev.util.FileUtil;
import com.csci.susdev.util.MybatisHelper;
import com.csci.susdev.util.StringUtil;
import com.csci.susdev.vo.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.MathUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class FactorSelectionService {

    @Resource
    private FactorSelectionMapper factorSelectionMapper;

    @Resource
    private FactorSelectionCustomMapper factorSelectionCustomMapper;

    @Resource
    private OrganizationService organizationService;

    @Resource
    private ProtocolMapper protocolMapper;

    @Resource
    private CarbonEmissionLocationMapper carbonEmissionLocationMapper;

    @Resource
    private ProtocolCategoryMapper protocolCategoryMapper;

    @Resource
    private ProtocolSubCategoryMapper protocolSubCategoryMapper;

    @Resource
    private ProtocolDetailCustomMapper protocolDetailCustomMapper;

    @Resource
    private ProtocolDetailMapper protocolDetailMapper;

    @Resource
    private FcCarbonFactorHkMapper fcCarbonFactorHkMapper;

    @Resource
    private FormDetailCustomMapper formDetailCustomMapper;

    @Resource
    private FactorScopeCustomMapper factorScopeCustomMapper;

    @Resource
    private FactorScopeMapper factorScopeMapper;

    @Autowired
    private OrganizationMapper organizationMapper;

    @Resource
    private FcCarbonFactorGbt51366Mapper fcCarbonFactorGbt51366Mapper;

    @Resource
    private FcCarbonFactorFlcMapper fcCarbonFactorFlcMapper;

    @Resource
    private FcCarbonFactorEsgDatasetMapper fcCarbonFactorEsgDatasetMapper;

    @Resource
    private FcEnergyFactorEsgDatasetMapper fcEnergyFactorEsgDatasetMapper;

    @Resource
    private FcAirPollutionFactorEsgDatasetMapper fcAirPollutionFactorEsgDatasetMapper;

    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(FactorSelection record) {
        return factorSelectionMapper.insertSelective(record);
    }

    public FactorSelection selectByPrimaryKey(String id) {
        return factorSelectionMapper.selectByPrimaryKey(id);
    }

    public FactorSelectionVO getFactorSelection(String id) {
        checkExist(id, "id不能为空");
        FactorSelection factorSelection = selectByPrimaryKey(id);
        checkExist(factorSelection, "未找到对应的记录");
        if(factorSelection.getIsDeleted()) {
            throw new ServiceException("紀錄已被刪除");
        }
        return FactorSelectionConverter.convertToVO(factorSelection);
    }

    @Transactional(rollbackFor = Exception.class)
    public String saveFactorSelection(FactorSelectionVO factorSelectionVO) {
        if (StringUtils.isBlank(factorSelectionVO.getId())) {
            // 新增
            return doAdd(factorSelectionVO);
        } else {
            checkExist(factorSelectionVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(factorSelectionVO);
        }

    }

    public ResultPage<FactorSelectionVO> listFactorSelection(FactorSelectionQO factorSelectionQO) {
        List<FactorSelectionVO> factorSelectionVOList = new ArrayList<>();
        if (StringUtils.isNotBlank(factorSelectionQO.getOrderBy())) {
            List<String> fieldNames = Arrays.stream(FactorSelectionVO.class.getDeclaredFields()).map(field -> field.getName()).collect(Collectors.toList());
            if(MybatisHelper.checkSqlInjectionForOrderBy(factorSelectionQO.getOrderBy(), fieldNames, NamingConventionEnum.CAMO)) {
                throw new ServiceException("orderBy 有SQL注入行為");
            }
        }

        //取得當前組織的數據
        List<FactorSelectionVO> voList = factorSelectionCustomMapper.listVOInherited(factorSelectionQO);

        for(FactorSelectionVO vo : voList) {
            factorSelectionVOList.add(FactorSelectionConverter.fillInDetail(vo));
        }
        return new ResultPage<>(voList, factorSelectionVOList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteFactorSelection(String id) {
        FactorSelection record = selectByPrimaryKey(id);
        record.setIsDeleted(true);
        factorSelectionMapper.updateByPrimaryKeySelective(record);
    }

    private String doAdd(FactorSelectionVO factorSelectionVO) {
        FactorSelection factorSelection = FactorSelectionConverter.convertToModel(factorSelectionVO);
        factorSelectionMapper.insertSelective(factorSelection);
        return factorSelection.getId();
    }

    private String doUpdate(FactorSelectionVO factorSelectionVO) {
        FactorSelection originalRecord = selectByPrimaryKey(factorSelectionVO.getId());
        FactorSelection factorSelection = FactorSelectionConverter.convertToModelWithBase(factorSelectionVO, originalRecord);

        FactorSelectionExample example = new FactorSelectionExample();
        example.or().andIdEqualTo(factorSelectionVO.getId()).andLastUpdateVersionEqualTo(factorSelectionVO.getLastUpdateVersion());
        int updateCount = factorSelectionMapper.updateByExample(factorSelection, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return factorSelection.getId();
    }

    public List<FactorSelection> selectByExample(FactorSelectionExample example) {
        return factorSelectionMapper.selectByExample(example);
    }

    public byte[] exportListFactorSelection(FactorSelectionQO factorSelectionQO, HttpHeaders headers) {
        List<FactorSelectionVO> list = this.listFactorSelection(factorSelectionQO).getList();
        if (ObjectUtils.isEmpty(list)) {
            throw new ServiceException("無數據可匯出");
        }
        FactorSelectionVO factorSelectionVO1 = list.get(0);
        String siteCode = factorSelectionVO1.getOrganizationCode();
        String protocolName = factorSelectionVO1.getProtocolNameEn();
        String carbonEmissionLocationName = factorSelectionVO1.getCarbonEmissionLocationName();
        String filename = new StringBuilder()
                .append(siteCode)
                .append("_")
                .append(protocolName)
                .append("_")
                .append("因子選擇")
                .append("_")
                .append(carbonEmissionLocationName)
                .append(".xlsx").toString();
        String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8);
        headers.setContentDispositionFormData("attachment", encodedFilename);
        try (ByteArrayOutputStream out = new ByteArrayOutputStream();
             ExcelWriter excelWriter = EasyExcel.write(out).build()) {

        List<ExcleFcCarbonFactorHkFactorSelectionVO> hkFactorSelectionVOS = new ArrayList<>();
        List<ExcleFcCarbonFactorGbt51366VOFactorSelectionVO> gbt51366VOFactorSelectionVOS = new ArrayList<>();
        List<ExcleFcCarbonFactorFlcFactorSelectionVO> flcFactorSelectionVOS = new ArrayList<>();
        List<ExcleFcCarbonFactorEsgDatasetFactorSelectionVO> esgDatasetFactorSelectionVOS = new ArrayList<>();
        List<ExcleFcEnergyFactorEsgDatasetFactorSelectionVO> energyFactorEsgDatasetFactorSelectionVOS = new ArrayList<>();
        List<ExcleFcAirPollutionFactorEsgDatasetFactorSelectionVO> airPollutionFactorEsgDatasetFactorSelectionVOS = new ArrayList<>();

        for (FactorSelectionVO factorSelectionVO : list) {
            String type = factorSelectionVO.getFcCarbonFactorType() + factorSelectionVO.getFcCarbonFactorDatasource();
            switch (type) {
                case "碳排因子香港碳排數據":
                    ExcleFcCarbonFactorHkFactorSelectionVO hkFactorSelectionVO = ConvertBeanUtils.convert(factorSelectionVO, ExcleFcCarbonFactorHkFactorSelectionVO.class);
                    BeanUtils.copyProperties(factorSelectionVO.getFcCarbonFactorVO(), hkFactorSelectionVO);
                    hkFactorSelectionVOS.add(hkFactorSelectionVO);
                    break;
                case "碳排因子GBT51366":
                    ExcleFcCarbonFactorGbt51366VOFactorSelectionVO gbt51366VOFactorSelectionVO = ConvertBeanUtils.convert(factorSelectionVO, ExcleFcCarbonFactorGbt51366VOFactorSelectionVO.class);
                    BeanUtils.copyProperties(factorSelectionVO.getFcCarbonFactorVO(), gbt51366VOFactorSelectionVO);
                    gbt51366VOFactorSelectionVOS.add(gbt51366VOFactorSelectionVO);
                    break;
                case "碳排因子全生命周期數據集":
                    ExcleFcCarbonFactorFlcFactorSelectionVO flcFactorSelectionVO = ConvertBeanUtils.convert(factorSelectionVO, ExcleFcCarbonFactorFlcFactorSelectionVO.class);
                    BeanUtils.copyProperties(factorSelectionVO.getFcCarbonFactorVO(), flcFactorSelectionVO);
                    flcFactorSelectionVOS.add(flcFactorSelectionVO);
                    break;
                case "碳排因子ESG數據集":
                    ExcleFcCarbonFactorEsgDatasetFactorSelectionVO esgDatasetFactorSelectionVO = ConvertBeanUtils.convert(factorSelectionVO, ExcleFcCarbonFactorEsgDatasetFactorSelectionVO.class);
                    BeanUtils.copyProperties(factorSelectionVO.getFcCarbonFactorVO(), esgDatasetFactorSelectionVO);
                    esgDatasetFactorSelectionVOS.add(esgDatasetFactorSelectionVO);
                    break;
                case "能源因子ESG數據集":
                    ExcleFcEnergyFactorEsgDatasetFactorSelectionVO energyFactorEsgDatasetFactorSelectionVO = ConvertBeanUtils.convert(factorSelectionVO, ExcleFcEnergyFactorEsgDatasetFactorSelectionVO.class);
                    BeanUtils.copyProperties(factorSelectionVO.getFcCarbonFactorVO(), energyFactorEsgDatasetFactorSelectionVO);
                    energyFactorEsgDatasetFactorSelectionVOS.add(energyFactorEsgDatasetFactorSelectionVO);
                    break;
                case "空氣污染因子ESG數據集":
                    ExcleFcAirPollutionFactorEsgDatasetFactorSelectionVO airPollutionFactorEsgDatasetFactorSelectionVO = ConvertBeanUtils.convert(factorSelectionVO, ExcleFcAirPollutionFactorEsgDatasetFactorSelectionVO.class);
                    BeanUtils.copyProperties(factorSelectionVO.getFcCarbonFactorVO(), airPollutionFactorEsgDatasetFactorSelectionVO);
                    airPollutionFactorEsgDatasetFactorSelectionVOS.add(airPollutionFactorEsgDatasetFactorSelectionVO);
                    break;
                default:
                    break;
            }
        }

            WriteSheet hkSheet = EasyExcel.writerSheet("碳排因子_香港碳排數據").head(ExcleFcCarbonFactorHkFactorSelectionVO.class).build();
            excelWriter.write(hkFactorSelectionVOS, hkSheet);
            WriteSheet gbt51366Sheet = EasyExcel.writerSheet("碳排因子_GBT51366").head(ExcleFcCarbonFactorGbt51366VOFactorSelectionVO.class).build();
            excelWriter.write(gbt51366VOFactorSelectionVOS, gbt51366Sheet);
            WriteSheet flcSheet = EasyExcel.writerSheet("碳排因子_全生命周期數據集").head(ExcleFcCarbonFactorFlcFactorSelectionVO.class).build();
            excelWriter.write(flcFactorSelectionVOS, flcSheet);
            WriteSheet esgSheet = EasyExcel.writerSheet("碳排因子_ESG數據集").head(ExcleFcCarbonFactorEsgDatasetFactorSelectionVO.class).build();
            excelWriter.write(esgDatasetFactorSelectionVOS, esgSheet);
            WriteSheet energySheet = EasyExcel.writerSheet("能源因子_ESG數據集").head(ExcleFcEnergyFactorEsgDatasetFactorSelectionVO.class).build();
            excelWriter.write(energyFactorEsgDatasetFactorSelectionVOS, energySheet);
            WriteSheet airPollutionSheet = EasyExcel.writerSheet("空氣污染因子_ESG數據集").head(ExcleFcAirPollutionFactorEsgDatasetFactorSelectionVO.class).build();
            excelWriter.write(airPollutionFactorEsgDatasetFactorSelectionVOS, airPollutionSheet);

        excelWriter.finish();
        return out.toByteArray();
    } catch (IOException e) {
        throw new RuntimeException(e);
    }
}

    /**
     * 导入因子选择数据（Excel）
     * <AUTHOR>
     * @date 2025/1/22 11:46
     * @param file
     */
    @Transactional(rollbackFor = Exception.class)
    public void importExcel(MultipartFile file) {
        // 1、导入模版以因子选择导出的模版为基准，转成VO对象数据
        FactorSelectionImportExcelVO factorSelectionImportExcelVO = this.excelToData(file);
        // 碳排因子_香港碳排數據
        List<ExcleFcCarbonFactorHkFactorSelectionVO> hkFactorSelectionVOS = factorSelectionImportExcelVO.getHkFactorSelectionVOS();
        // 碳排因子_GBT51366
        List<ExcleFcCarbonFactorGbt51366VOFactorSelectionVO> gbt51366VOFactorSelectionVOS = factorSelectionImportExcelVO.getGbt51366VOFactorSelectionVOS();
        // 碳排因子_全生命周期數據集
        List<ExcleFcCarbonFactorFlcFactorSelectionVO> flcFactorSelectionVOS = factorSelectionImportExcelVO.getFlcFactorSelectionVOS();
        // 碳排因子_ESG數據集
        List<ExcleFcCarbonFactorEsgDatasetFactorSelectionVO> esgDatasetFactorSelectionVOS = factorSelectionImportExcelVO.getEsgDatasetFactorSelectionVOS();
        // 能源因子_ESG數據集
        List<ExcleFcEnergyFactorEsgDatasetFactorSelectionVO> energyFactorEsgDatasetFactorSelectionVOS = factorSelectionImportExcelVO.getEnergyFactorEsgDatasetFactorSelectionVOS();
        // 空氣污染因子_ESG數據集
        List<ExcleFcAirPollutionFactorEsgDatasetFactorSelectionVO> airPollutionFactorEsgDatasetFactorSelectionVOS = factorSelectionImportExcelVO.getAirPollutionFactorEsgDatasetFactorSelectionVOS();
        // 2、根据：組織機構、年份、協議、場景、協議大類、協議小類 判断（对应标准管理模块下的：协议简介、场景信息、协议大类、协议小类是否存在）
        // 不存在则直接提示对应不存在，需手动维护，因为涉及简繁体、英文等信息
        // 查询协议
        ProtocolExample protocolExample = new ProtocolExample();
        protocolExample.or().andIsDeletedEqualTo(false);
        List<Protocol> protocols = protocolMapper.selectByExample(protocolExample);
        Map<String, String> protocolMap = protocols.stream().collect(Collectors.toMap(Protocol::getNameEn, Protocol::getId));
        // 查询场景
        CarbonEmissionLocationExample locationExample = new CarbonEmissionLocationExample();
        locationExample.or().andIsDeletedEqualTo(false);
        List<CarbonEmissionLocation> carbonEmissionLocations = carbonEmissionLocationMapper.selectByExample(locationExample);
        Map<String, String> locationMap = carbonEmissionLocations.stream().collect(Collectors.toMap(CarbonEmissionLocation::getName, CarbonEmissionLocation::getId));
        // 查询协议大类
        ProtocolCategoryExample protocolCategoryExample = new ProtocolCategoryExample();
        protocolCategoryExample.or().andIsDeletedEqualTo(false);
        List<ProtocolCategory> protocolCategoryList = protocolCategoryMapper.selectByExample(protocolCategoryExample);
        Map<String, String> protocolCategoryMap = protocolCategoryList.stream().collect(Collectors.toMap(e -> e.getProtocolId() + "#" + e.getCategoryName(), ProtocolCategory::getId));
        // 查询协议小类
        ProtocolSubCategoryExample protocolSubCategoryExample = new ProtocolSubCategoryExample();
        protocolSubCategoryExample.or().andIsDeletedEqualTo(false);
        List<ProtocolSubCategory> protocolSubCategoryList = protocolSubCategoryMapper.selectByExample(protocolSubCategoryExample);
        Map<String, String> protocolSubCategoryMap = protocolSubCategoryList.stream().collect(Collectors.toMap(e -> e.getCategoryId() + "#" + e.getSubCategoryName(), ProtocolSubCategory::getId));
        // 碳排因子_香港碳排數據
        List<FactorSelectionVO> hkFactorSelectionList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(hkFactorSelectionVOS)) {
            hkFactorSelectionList = ConvertBeanUtils.convertList(hkFactorSelectionVOS, FactorSelectionVO.class);
            protocolCheckBefore(hkFactorSelectionList, protocolMap, locationMap, protocolCategoryMap, protocolSubCategoryMap);
            for (int i = 0; i < hkFactorSelectionList.size(); i++) {
                FcCarbonFactorHk fcCarbonFactorHk = new FcCarbonFactorHk();
                ExcleFcCarbonFactorHkFactorSelectionVO excleFcCarbonFactorHkFactorSelectionVO = hkFactorSelectionVOS.get(i);
                BeanUtils.copyProperties(excleFcCarbonFactorHkFactorSelectionVO, fcCarbonFactorHk);
                hkFactorSelectionList.get(i).setFcCarbonFactorVO(fcCarbonFactorHk);
            }
        }
        // 碳排因子_GBT51366
        List<FactorSelectionVO> gbt51366VOFactorSelectionList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(gbt51366VOFactorSelectionVOS)) {
            gbt51366VOFactorSelectionList = ConvertBeanUtils.convertList(gbt51366VOFactorSelectionVOS, FactorSelectionVO.class);
            protocolCheckBefore(gbt51366VOFactorSelectionList, protocolMap, locationMap, protocolCategoryMap, protocolSubCategoryMap);
            for (int i = 0; i < gbt51366VOFactorSelectionList.size(); i++) {
                FcCarbonFactorGbt51366 fcCarbonFactorGbt51366 = new FcCarbonFactorGbt51366();
                ExcleFcCarbonFactorGbt51366VOFactorSelectionVO excleFcCarbonFactorGbt51366VOFactorSelectionVO = gbt51366VOFactorSelectionVOS.get(i);
                BeanUtils.copyProperties(excleFcCarbonFactorGbt51366VOFactorSelectionVO, fcCarbonFactorGbt51366);
                gbt51366VOFactorSelectionList.get(i).setFcCarbonFactorVO(fcCarbonFactorGbt51366);
            }
        }
        // 碳排因子_全生命周期數據集
        List<FactorSelectionVO> flcFactorSelectionList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(flcFactorSelectionVOS)) {
            flcFactorSelectionList = ConvertBeanUtils.convertList(flcFactorSelectionVOS, FactorSelectionVO.class);
            protocolCheckBefore(flcFactorSelectionList, protocolMap, locationMap, protocolCategoryMap, protocolSubCategoryMap);
            for (int i = 0; i < flcFactorSelectionList.size(); i++) {
                FcCarbonFactorFlc fcCarbonFactorFlc = new FcCarbonFactorFlc();
                ExcleFcCarbonFactorFlcFactorSelectionVO excleFcCarbonFactorFlcFactorSelectionVO = flcFactorSelectionVOS.get(i);
                BeanUtils.copyProperties(excleFcCarbonFactorFlcFactorSelectionVO, fcCarbonFactorFlc);
                flcFactorSelectionList.get(i).setFcCarbonFactorVO(fcCarbonFactorFlc);
            }
        }
        // 碳排因子_ESG數據集
        List<FactorSelectionVO> esgDatasetFactorSelectionList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(esgDatasetFactorSelectionVOS)) {
            esgDatasetFactorSelectionList = ConvertBeanUtils.convertList(esgDatasetFactorSelectionVOS, FactorSelectionVO.class);
            protocolCheckBefore(esgDatasetFactorSelectionList, protocolMap, locationMap, protocolCategoryMap, protocolSubCategoryMap);
            for (int i = 0; i < esgDatasetFactorSelectionList.size(); i++) {
                FcCarbonFactorEsgDataset fcCarbonFactorEsgDataset = new FcCarbonFactorEsgDataset();
                ExcleFcCarbonFactorEsgDatasetFactorSelectionVO excleFcCarbonFactorEsgDatasetFactorSelectionVO = esgDatasetFactorSelectionVOS.get(i);
                BeanUtils.copyProperties(excleFcCarbonFactorEsgDatasetFactorSelectionVO, fcCarbonFactorEsgDataset);
                esgDatasetFactorSelectionList.get(i).setFcCarbonFactorVO(fcCarbonFactorEsgDataset);
            }
        }
        // 能源因子_ESG數據集
        List<FactorSelectionVO> energyFactorEsgFactorSelectionList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(energyFactorEsgDatasetFactorSelectionVOS)) {
            energyFactorEsgFactorSelectionList = ConvertBeanUtils.convertList(energyFactorEsgDatasetFactorSelectionVOS, FactorSelectionVO.class);
            protocolCheckBefore(energyFactorEsgFactorSelectionList, protocolMap, locationMap, protocolCategoryMap, protocolSubCategoryMap);
            for (int i = 0; i < energyFactorEsgFactorSelectionList.size(); i++) {
                FcEnergyFactorEsgDataset fcEnergyFactorEsgDataset = new FcEnergyFactorEsgDataset();
                ExcleFcEnergyFactorEsgDatasetFactorSelectionVO excleFcEnergyFactorEsgDatasetFactorSelectionVO = energyFactorEsgDatasetFactorSelectionVOS.get(i);
                BeanUtils.copyProperties(excleFcEnergyFactorEsgDatasetFactorSelectionVO, fcEnergyFactorEsgDataset);
                energyFactorEsgFactorSelectionList.get(i).setFcCarbonFactorVO(fcEnergyFactorEsgDataset);
            }
        }
        // 空氣污染因子_ESG數據集
        List<FactorSelectionVO> airPollutionFactorEsgDatasetFactorSelectionList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(airPollutionFactorEsgDatasetFactorSelectionVOS)) {
            airPollutionFactorEsgDatasetFactorSelectionList = ConvertBeanUtils.convertList(airPollutionFactorEsgDatasetFactorSelectionVOS, FactorSelectionVO.class);
            protocolCheckBefore(airPollutionFactorEsgDatasetFactorSelectionList, protocolMap, locationMap, protocolCategoryMap, protocolSubCategoryMap);
            for (int i = 0; i < airPollutionFactorEsgDatasetFactorSelectionList.size(); i++) {
                FcAirPollutionFactorEsgDataset fcAirPollutionFactorEsgDataset = new FcAirPollutionFactorEsgDataset();
                ExcleFcAirPollutionFactorEsgDatasetFactorSelectionVO excleFcAirPollutionFactorEsgDatasetFactorSelectionVO = airPollutionFactorEsgDatasetFactorSelectionVOS.get(i);
                BeanUtils.copyProperties(excleFcAirPollutionFactorEsgDatasetFactorSelectionVO, fcAirPollutionFactorEsgDataset);
                airPollutionFactorEsgDatasetFactorSelectionList.get(i).setFcCarbonFactorVO(fcAirPollutionFactorEsgDataset);
            }
        }
        // 3、上面通过之后，则根据協議、場景、協議大類、協議小類 去找对应协议明细是否存在，不存在则新增  protocolDetail
        // 4、因子管理数据校验新增，不通Sheet页对应不同数据集（物料中文名稱往后的一些列）
        // 5、根据协议明细、表单明细 查找因子范围，不存在则新增
        // 6、新增因子选择数据（由因子范围、因子管理结合新增）
        // 查询协议明细
        List<ProtocolDetailVO> protocolDetailVOs = protocolDetailCustomMapper.list(null,null,null);
        Map<String, String> protocolDetailMap = protocolDetailVOs.stream().collect(
                Collectors.toMap(e -> e.getProtocolId() + "#" + e.getCarbonEmissionLocationId() + "#" + e.getCategoryId() + "#" + e.getSubCategoryId(), ProtocolDetailVO::getId));
        // 查找表单明细
        List<FormDetailVO> formDetailVOS = formDetailCustomMapper.listFormDetailMaxCurrentMonth(null, null, null);
        Map<String, String> formDetailMap = formDetailVOS.stream().collect(Collectors.toMap(e -> StringUtil.valueOf(e.getYear()) + "#" + StringUtil.valueOf(e.getFormCode()) + "#" + StringUtil.valueOf(e.getTypeE()), FormDetailVO::getId));
        // 查找因子范围
        List<FactorScopeVO> factorScopeVOS = factorScopeCustomMapper.list(null, null, null, null, null);
        Map<String, String> factorScopeMap = factorScopeVOS.stream().collect(Collectors.toMap(e -> e.getProtocolDetailId() + "#" + e.getFormDetailId(), FactorScopeVO::getId));
        // 创建碳排因子_香港碳排數據  因子选择数据
        if (CollectionUtils.isNotEmpty(hkFactorSelectionList)) {
            createHkFactorSelection(hkFactorSelectionList, protocolDetailMap, formDetailMap, factorScopeMap);
        }
        // 创建碳排因子_GBT51366 因子选择数据
        if (CollectionUtils.isNotEmpty(gbt51366VOFactorSelectionList)) {
            createGbt51366VOFactorSelection(gbt51366VOFactorSelectionList, protocolDetailMap, formDetailMap, factorScopeMap);
        }
        // 创建碳排因子_全生命周期數據集 因子选择数据
        if (CollectionUtils.isNotEmpty(flcFactorSelectionList)) {
            createFlcFactorSelection(flcFactorSelectionList, protocolDetailMap, formDetailMap, factorScopeMap);
        }
        // 创建碳排因子_ESG數據集 因子选择数据
        if (CollectionUtils.isNotEmpty(esgDatasetFactorSelectionList)) {
            createEsgDatasetFactorSelection(esgDatasetFactorSelectionList, protocolDetailMap, formDetailMap, factorScopeMap);
        }
        // 创建能源因子_ESG數據集 因子选择数据
        if (CollectionUtils.isNotEmpty(energyFactorEsgFactorSelectionList)) {
            createEnergyFactorEsgFactorSelection(energyFactorEsgFactorSelectionList, protocolDetailMap, formDetailMap, factorScopeMap);
        }
        // 创建空氣污染因子_ESG數據集 因子选择数据
        if (CollectionUtils.isNotEmpty(airPollutionFactorEsgDatasetFactorSelectionList)) {
            createAirPollutionFactorEsgDatasetFactorSelection(airPollutionFactorEsgDatasetFactorSelectionList, protocolDetailMap, formDetailMap, factorScopeMap);
        }

    }

    /**
     * 创建空氣污染因子_ESG數據集 因子选择数据
     * <AUTHOR>
     * @date 2025/1/27 10:24
     * @param airPollutionFactorEsgDatasetFactorSelectionList
     * @param protocolDetailMap
     * @param formDetailMap
     * @param factorScopeMap
     */
    private void createAirPollutionFactorEsgDatasetFactorSelection(List<FactorSelectionVO> airPollutionFactorEsgDatasetFactorSelectionList,
                                                                   Map<String, String> protocolDetailMap,
                                                                   Map<String, String> formDetailMap,
                                                                   Map<String, String> factorScopeMap) {
        OrganizationExample organizationExample = new OrganizationExample();
        List<String> orgList = airPollutionFactorEsgDatasetFactorSelectionList.stream().map(FactorSelectionVO::getOrganizationName).distinct().collect(Collectors.toList());
        for (String orgName : orgList) {
            organizationExample.or().andNameEqualTo(orgName).andIsDeletedEqualTo(false);
        }
        List<Organization> lstOrganization = organizationMapper.selectByExample(organizationExample);
        if (CollectionUtils.isEmpty(lstOrganization)) {
            throw new ServiceException("组织架构不存在！");
        }
        Map<String, String> orgMap = lstOrganization.stream().collect(Collectors.toMap(e -> e.getName(), Organization::getId));
        // 查找因子管理（空氣污染因子_ESG數據集）
        FcAirPollutionFactorEsgDatasetExample fcAirPollutionFactorEsgDatasetExample = new FcAirPollutionFactorEsgDatasetExample();
        fcAirPollutionFactorEsgDatasetExample.or().andIsDeletedEqualTo(false);
        List<FcAirPollutionFactorEsgDataset> fcAirPollutionFactorEsgDatasets = fcAirPollutionFactorEsgDatasetMapper.selectByExample(fcAirPollutionFactorEsgDatasetExample);

        Map<String, String> fcAirPollutionFactorEsgDatasetMap = fcAirPollutionFactorEsgDatasets.stream().collect(
                Collectors.toMap(e -> StringUtil.valueOf(e.getRecordYear()) + "#" + StringUtil.valueOf(e.getPlatform()) + "#" + StringUtil.valueOf(e.getChineseName()) +
                                "#" + StringUtil.valueOf(e.getCategory()) + "#" + StringUtil.valueOf(e.getEmissionSource()) +
                                "#" + StringUtil.valueOf(e.getUnit()) + "#" +  StringUtil.valueOf(e.getAirPollutionCategory()) +
                                "#" + StringUtil.valueOf(e.getVehicleType()) + "#" +  StringUtil.valueOf(e.getVehicleEmissionStandard()) +
                                "#" + StringUtil.removeTrailingZeros(e.getAirPollutionFactor()) + "#" + StringUtil.valueOf(e.getAirPollutionFactorUnit()) +
                                "#" + StringUtil.valueOf(e.getSourceDescription())
                        , FcAirPollutionFactorEsgDataset::getId));
        airPollutionFactorEsgDatasetFactorSelectionList.forEach(e -> {
            if (orgMap.containsKey(e.getOrganizationName())) {
                String organizationId = orgMap.get(e.getOrganizationName());
                e.setOrganizationId(organizationId);
            }else {
                throw new ServiceException("组织架构不存在！");
            }
            if (protocolDetailMap.containsKey(e.getProtocolId() + "#" + e.getCarbonEmissionLocationId() + "#" + e.getCategoryId() + "#" + e.getSubCategoryId())) {
                String protocolDetailId = protocolDetailMap.get(e.getProtocolId() + "#" + e.getCarbonEmissionLocationId() + "#" + e.getCategoryId() + "#" + e.getSubCategoryId());
                e.setProtocolDetailId(protocolDetailId);
            }else {
                // 增加协议明细
                ProtocolDetail protocolDetail = new ProtocolDetail();
                protocolDetail.setCarbonEmissionLocationId(e.getCarbonEmissionLocationId());
                protocolDetail.setSubCategoryId(e.getSubCategoryId());
                protocolDetail.setLastUpdateVersion(0);
                protocolDetailMapper.insertSelective(protocolDetail);
                e.setProtocolDetailId(protocolDetail.getId());
                protocolDetailMap.put(e.getProtocolId() + "#" + e.getCarbonEmissionLocationId() + "#" + e.getCategoryId() + "#" + e.getSubCategoryId(), e.getProtocolDetailId());
            }
            // 因子管理（空氣污染因子_ESG數據集）
            FcAirPollutionFactorEsgDataset fcCarbonFactor = (FcAirPollutionFactorEsgDataset) e.getFcCarbonFactorVO();
            fcCarbonFactor.setRecordYear(e.getYear());
            if (fcAirPollutionFactorEsgDatasetMap.containsKey(StringUtil.valueOf(fcCarbonFactor.getRecordYear()) + "#" + StringUtil.valueOf(fcCarbonFactor.getPlatform()) + "#" + StringUtil.valueOf(fcCarbonFactor.getChineseName()) +
                    "#" + StringUtil.valueOf(fcCarbonFactor.getCategory()) + "#" + StringUtil.valueOf(fcCarbonFactor.getEmissionSource()) +
                    "#" + StringUtil.valueOf(fcCarbonFactor.getUnit()) + "#" +  StringUtil.valueOf(fcCarbonFactor.getAirPollutionCategory()) +
                    "#" + StringUtil.valueOf(fcCarbonFactor.getVehicleType()) + "#" +  StringUtil.valueOf(fcCarbonFactor.getVehicleEmissionStandard()) +
                    "#" + StringUtil.removeTrailingZeros(fcCarbonFactor.getAirPollutionFactor()) + "#" + StringUtil.valueOf(fcCarbonFactor.getAirPollutionFactorUnit()) +
                    "#" + StringUtil.valueOf(fcCarbonFactor.getSourceDescription()))) {
                String fcCarbonFactorId = fcAirPollutionFactorEsgDatasetMap.get(StringUtil.valueOf(fcCarbonFactor.getRecordYear()) + "#" + StringUtil.valueOf(fcCarbonFactor.getPlatform()) + "#" + StringUtil.valueOf(fcCarbonFactor.getChineseName()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getCategory()) + "#" + StringUtil.valueOf(fcCarbonFactor.getEmissionSource()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getUnit()) + "#" +  StringUtil.valueOf(fcCarbonFactor.getAirPollutionCategory()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getVehicleType()) + "#" +  StringUtil.valueOf(fcCarbonFactor.getVehicleEmissionStandard()) +
                        "#" + StringUtil.removeTrailingZeros(fcCarbonFactor.getAirPollutionFactor()) + "#" + StringUtil.valueOf(fcCarbonFactor.getAirPollutionFactorUnit()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getSourceDescription()));
                e.setFcCarbonFactorId(fcCarbonFactorId);
            }else {
                fcAirPollutionFactorEsgDatasetMapper.insertSelective(fcCarbonFactor);
                e.setFcCarbonFactorId(fcCarbonFactor.getId());
                fcAirPollutionFactorEsgDatasetMap.put(StringUtil.valueOf(fcCarbonFactor.getRecordYear()) + "#" + StringUtil.valueOf(fcCarbonFactor.getPlatform()) + "#" + StringUtil.valueOf(fcCarbonFactor.getChineseName()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getCategory()) + "#" + StringUtil.valueOf(fcCarbonFactor.getEmissionSource()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getUnit()) + "#" +  StringUtil.valueOf(fcCarbonFactor.getAirPollutionCategory()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getVehicleType()) + "#" +  StringUtil.valueOf(fcCarbonFactor.getVehicleEmissionStandard()) +
                        "#" + StringUtil.removeTrailingZeros(fcCarbonFactor.getAirPollutionFactor()) + "#" + StringUtil.valueOf(fcCarbonFactor.getAirPollutionFactorUnit()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getSourceDescription()), e.getFcCarbonFactorId());
            }
            e.setFcCarbonFactorType("空氣污染因子");
            e.setFcCarbonFactorDatasource("ESG數據集");
            // 赋值表单明细id
            if (formDetailMap.containsKey(StringUtil.valueOf(e.getYear()) + "#" + StringUtil.valueOf(e.getFormCode()) + "#" + StringUtil.valueOf(e.getTypeE()))) {
                String formDetailId = formDetailMap.get(StringUtil.valueOf(e.getYear()) + "#" + StringUtil.valueOf(e.getFormCode()) + "#" + StringUtil.valueOf(e.getTypeE()));
                e.setFormDetailId(formDetailId);
                // 查找因子范围
                if (factorScopeMap.containsKey(e.getProtocolDetailId() + "#" + e.getFormDetailId())) {
                    String factorScopeId = factorScopeMap.get(e.getProtocolDetailId() + "#" + e.getFormDetailId());
                    e.setFactorScopeId(factorScopeId);
                }else {
                    FactorScope factorScope = new FactorScope();
                    factorScope.setFactorType(e.getFcCarbonFactorType());
                    factorScope.setProtocolDetailId(e.getProtocolDetailId());
                    factorScope.setFormDetailId(e.getFormDetailId());
                    factorScopeMapper.insertSelective(factorScope);
                    e.setFactorScopeId(factorScope.getId());
                    factorScopeMap.put(e.getProtocolDetailId() + "#" + e.getFormDetailId(), e.getFactorScopeId());
                }
                // 新增因子选择数据
                FactorSelection factorSelection = FactorSelectionConverter.convertToModel(e);
                factorSelection.setIsActive(true);
                factorSelection.setLastUpdateVersion(0);
                factorSelectionMapper.insertSelective(factorSelection);
            }else {
                throw new ServiceException("空氣污染因子_ESG數據集 表单明细编码：" + e.getTypeE() + "不存在，请检查");
            }
        });
    }

    /**
     * 创建能源因子_ESG數據集 因子选择数据
     * <AUTHOR>
     * @date 2025/1/27 10:24
     * @param energyFactorEsgFactorSelectionList
     * @param protocolDetailMap
     * @param formDetailMap
     * @param factorScopeMap
     */
    private void createEnergyFactorEsgFactorSelection(List<FactorSelectionVO> energyFactorEsgFactorSelectionList,
                                                      Map<String, String> protocolDetailMap,
                                                      Map<String, String> formDetailMap,
                                                      Map<String, String> factorScopeMap) {
        OrganizationExample organizationExample = new OrganizationExample();
        List<String> orgList = energyFactorEsgFactorSelectionList.stream().map(FactorSelectionVO::getOrganizationName).distinct().collect(Collectors.toList());
        for (String orgName : orgList) {
            organizationExample.or().andNameEqualTo(orgName).andIsDeletedEqualTo(false);
        }
        List<Organization> lstOrganization = organizationMapper.selectByExample(organizationExample);
        if (CollectionUtils.isEmpty(lstOrganization)) {
            throw new ServiceException("组织架构不存在！");
        }
        Map<String, String> orgMap = lstOrganization.stream().collect(Collectors.toMap(e -> e.getName(), Organization::getId));
        // 查找因子管理（能源因子_ESG數據集）
        FcEnergyFactorEsgDatasetExample fcEnergyFactorEsgDatasetExample = new FcEnergyFactorEsgDatasetExample();
        fcEnergyFactorEsgDatasetExample.or().andIsDeletedEqualTo(false);
        List<FcEnergyFactorEsgDataset> fcEnergyFactorEsgDatasets = fcEnergyFactorEsgDatasetMapper.selectByExample(fcEnergyFactorEsgDatasetExample);

        Map<String, String> fcEnergyFactorEsgDatasetMap = fcEnergyFactorEsgDatasets.stream().collect(
                Collectors.toMap(e -> StringUtil.valueOf(e.getRecordYear()) + "#" + StringUtil.valueOf(e.getPlatform()) + "#" + StringUtil.valueOf(e.getChineseName()) +
                                "#" + StringUtil.valueOf(e.getCategory()) + "#" + StringUtil.valueOf(e.getEmissionSource()) +
                                "#" + StringUtil.valueOf(e.getUnit()) + "#" +  StringUtil.removeTrailingZeros(e.getEnergyFactor()) +
                                "#" + StringUtil.valueOf(e.getEnergyFactorUnit()) + "#" + StringUtil.valueOf(e.getSourceDescription())
                        , FcEnergyFactorEsgDataset::getId));
        energyFactorEsgFactorSelectionList.forEach(e -> {
            if (orgMap.containsKey(e.getOrganizationName())) {
                String organizationId = orgMap.get(e.getOrganizationName());
                e.setOrganizationId(organizationId);
            }else {
                throw new ServiceException("组织架构不存在！");
            }
            if (protocolDetailMap.containsKey(e.getProtocolId() + "#" + e.getCarbonEmissionLocationId() + "#" + e.getCategoryId() + "#" + e.getSubCategoryId())) {
                String protocolDetailId = protocolDetailMap.get(e.getProtocolId() + "#" + e.getCarbonEmissionLocationId() + "#" + e.getCategoryId() + "#" + e.getSubCategoryId());
                e.setProtocolDetailId(protocolDetailId);
            }else {
                // 增加协议明细
                ProtocolDetail protocolDetail = new ProtocolDetail();
                protocolDetail.setCarbonEmissionLocationId(e.getCarbonEmissionLocationId());
                protocolDetail.setSubCategoryId(e.getSubCategoryId());
                protocolDetail.setLastUpdateVersion(0);
                protocolDetailMapper.insertSelective(protocolDetail);
                e.setProtocolDetailId(protocolDetail.getId());
                protocolDetailMap.put(e.getProtocolId() + "#" + e.getCarbonEmissionLocationId() + "#" + e.getCategoryId() + "#" + e.getSubCategoryId(), e.getProtocolDetailId());
            }
            // 因子管理（能源因子_ESG數據集）
            FcEnergyFactorEsgDataset fcCarbonFactor = (FcEnergyFactorEsgDataset) e.getFcCarbonFactorVO();
            fcCarbonFactor.setRecordYear(e.getYear());
            if (fcEnergyFactorEsgDatasetMap.containsKey(StringUtil.valueOf(fcCarbonFactor.getRecordYear()) + "#" + StringUtil.valueOf(fcCarbonFactor.getPlatform()) + "#" + StringUtil.valueOf(fcCarbonFactor.getChineseName()) +
                    "#" + StringUtil.valueOf(fcCarbonFactor.getCategory()) + "#" + StringUtil.valueOf(fcCarbonFactor.getEmissionSource()) +
                    "#" + StringUtil.valueOf(fcCarbonFactor.getUnit()) + "#" +  StringUtil.removeTrailingZeros(fcCarbonFactor.getEnergyFactor()) +
                    "#" + StringUtil.valueOf(fcCarbonFactor.getEnergyFactorUnit()) + "#" + StringUtil.valueOf(fcCarbonFactor.getSourceDescription()))) {
                String fcCarbonFactorId = fcEnergyFactorEsgDatasetMap.get(StringUtil.valueOf(fcCarbonFactor.getRecordYear()) + "#" + StringUtil.valueOf(fcCarbonFactor.getPlatform()) + "#" + StringUtil.valueOf(fcCarbonFactor.getChineseName()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getCategory()) + "#" + StringUtil.valueOf(fcCarbonFactor.getEmissionSource()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getUnit()) + "#" +  StringUtil.removeTrailingZeros(fcCarbonFactor.getEnergyFactor()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getEnergyFactorUnit()) + "#" + StringUtil.valueOf(fcCarbonFactor.getSourceDescription()));
                e.setFcCarbonFactorId(fcCarbonFactorId);
            }else {
                fcEnergyFactorEsgDatasetMapper.insertSelective(fcCarbonFactor);
                e.setFcCarbonFactorId(fcCarbonFactor.getId());
                fcEnergyFactorEsgDatasetMap.put(StringUtil.valueOf(fcCarbonFactor.getRecordYear()) + "#" + StringUtil.valueOf(fcCarbonFactor.getPlatform()) + "#" + StringUtil.valueOf(fcCarbonFactor.getChineseName()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getCategory()) + "#" + StringUtil.valueOf(fcCarbonFactor.getEmissionSource()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getUnit()) + "#" +  StringUtil.removeTrailingZeros(fcCarbonFactor.getEnergyFactor()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getEnergyFactorUnit()) + "#" + StringUtil.valueOf(fcCarbonFactor.getSourceDescription()), e.getFcCarbonFactorId());
            }
            e.setFcCarbonFactorType("能源因子");
            e.setFcCarbonFactorDatasource("ESG數據集");
            // 赋值表单明细id
            if (formDetailMap.containsKey(StringUtil.valueOf(e.getYear()) + "#" + StringUtil.valueOf(e.getFormCode()) + "#" + StringUtil.valueOf(e.getTypeE()))) {
                String formDetailId = formDetailMap.get(StringUtil.valueOf(e.getYear()) + "#" + StringUtil.valueOf(e.getFormCode()) + "#" + StringUtil.valueOf(e.getTypeE()));
                e.setFormDetailId(formDetailId);
                // 查找因子范围
                if (factorScopeMap.containsKey(e.getProtocolDetailId() + "#" + e.getFormDetailId())) {
                    String factorScopeId = factorScopeMap.get(e.getProtocolDetailId() + "#" + e.getFormDetailId());
                    e.setFactorScopeId(factorScopeId);
                }else {
                    FactorScope factorScope = new FactorScope();
                    factorScope.setFactorType(e.getFcCarbonFactorType());
                    factorScope.setProtocolDetailId(e.getProtocolDetailId());
                    factorScope.setFormDetailId(e.getFormDetailId());
                    factorScopeMapper.insertSelective(factorScope);
                    e.setFactorScopeId(factorScope.getId());
                    factorScopeMap.put(e.getProtocolDetailId() + "#" + e.getFormDetailId(), e.getFactorScopeId());
                }
                // 新增因子选择数据
                FactorSelection factorSelection = FactorSelectionConverter.convertToModel(e);
                factorSelection.setIsActive(true);
                factorSelection.setLastUpdateVersion(0);
                factorSelectionMapper.insertSelective(factorSelection);
            }else {
                throw new ServiceException("能源因子_ESG數據集 表单明细编码：" + e.getTypeE() + "不存在，请检查");
            }
        });

    }

    /**
     * 创建碳排因子_ESG數據集 因子选择数据
     * <AUTHOR>
     * @date 2025/1/27 10:23
     * @param esgDatasetFactorSelectionList
     * @param protocolDetailMap
     * @param formDetailMap
     * @param factorScopeMap
     */
    private void createEsgDatasetFactorSelection(List<FactorSelectionVO> esgDatasetFactorSelectionList,
                                                 Map<String, String> protocolDetailMap,
                                                 Map<String, String> formDetailMap,
                                                 Map<String, String> factorScopeMap) {
        OrganizationExample organizationExample = new OrganizationExample();
        List<String> orgList = esgDatasetFactorSelectionList.stream().map(FactorSelectionVO::getOrganizationName).distinct().collect(Collectors.toList());
        for (String orgName : orgList) {
            organizationExample.or().andNameEqualTo(orgName).andIsDeletedEqualTo(false);
        }
        List<Organization> lstOrganization = organizationMapper.selectByExample(organizationExample);
        if (CollectionUtils.isEmpty(lstOrganization)) {
            throw new ServiceException("组织架构不存在！");
        }
        Map<String, String> orgMap = lstOrganization.stream().collect(Collectors.toMap(e -> e.getName(), Organization::getId));
        // 查找因子管理（碳排因子_ESG數據集）
        FcCarbonFactorEsgDatasetExample fcCarbonFactorEsgDatasetExample = new FcCarbonFactorEsgDatasetExample();
        fcCarbonFactorEsgDatasetExample.or().andIsDeletedEqualTo(false);
        List<FcCarbonFactorEsgDataset> fcCarbonFactorEsgDatasets = fcCarbonFactorEsgDatasetMapper.selectByExample(fcCarbonFactorEsgDatasetExample);

        Map<String, String> fcCarbonFactorEsgDatasetMap = fcCarbonFactorEsgDatasets.stream().collect(
                Collectors.toMap(e -> StringUtil.valueOf(e.getRecordYear()) + "#" + StringUtil.valueOf(e.getPlatform()) + "#" + StringUtil.valueOf(e.getChineseName()) +
                                "#" + StringUtil.valueOf(e.getCategory()) + "#" + StringUtil.valueOf(e.getEmissionSource()) +
                                "#" + StringUtil.valueOf(e.getUnit()) + "#" +  StringUtil.removeTrailingZeros(e.getCarbonFactor()) +
                                "#" + StringUtil.valueOf(e.getCarbonFactorUnit()) + "#" + StringUtil.valueOf(e.getSourceDescription())
                        , FcCarbonFactorEsgDataset::getId));
        esgDatasetFactorSelectionList.forEach(e -> {
            if (orgMap.containsKey(e.getOrganizationName())) {
                String organizationId = orgMap.get(e.getOrganizationName());
                e.setOrganizationId(organizationId);
            }else {
                throw new ServiceException("组织架构不存在！");
            }
            if (protocolDetailMap.containsKey(e.getProtocolId() + "#" + e.getCarbonEmissionLocationId() + "#" + e.getCategoryId() + "#" + e.getSubCategoryId())) {
                String protocolDetailId = protocolDetailMap.get(e.getProtocolId() + "#" + e.getCarbonEmissionLocationId() + "#" + e.getCategoryId() + "#" + e.getSubCategoryId());
                e.setProtocolDetailId(protocolDetailId);
            }else {
                // 增加协议明细
                ProtocolDetail protocolDetail = new ProtocolDetail();
                protocolDetail.setCarbonEmissionLocationId(e.getCarbonEmissionLocationId());
                protocolDetail.setSubCategoryId(e.getSubCategoryId());
                protocolDetail.setLastUpdateVersion(0);
                protocolDetailMapper.insertSelective(protocolDetail);
                e.setProtocolDetailId(protocolDetail.getId());
                protocolDetailMap.put(e.getProtocolId() + "#" + e.getCarbonEmissionLocationId() + "#" + e.getCategoryId() + "#" + e.getSubCategoryId(), e.getProtocolDetailId());
            }
            // 因子管理（碳排因子_ESG數據集）
            FcCarbonFactorEsgDataset fcCarbonFactor = (FcCarbonFactorEsgDataset) e.getFcCarbonFactorVO();
            fcCarbonFactor.setRecordYear(e.getYear());
            if (fcCarbonFactorEsgDatasetMap.containsKey(StringUtil.valueOf(fcCarbonFactor.getRecordYear()) + "#" + StringUtil.valueOf(fcCarbonFactor.getPlatform()) + "#" + StringUtil.valueOf(fcCarbonFactor.getChineseName()) +
                    "#" + StringUtil.valueOf(fcCarbonFactor.getCategory()) + "#" + StringUtil.valueOf(fcCarbonFactor.getEmissionSource()) +
                    "#" + StringUtil.valueOf(fcCarbonFactor.getUnit()) + "#" +  StringUtil.removeTrailingZeros(fcCarbonFactor.getCarbonFactor()) +
                    "#" + StringUtil.valueOf(fcCarbonFactor.getCarbonFactorUnit()) + "#" + StringUtil.valueOf(fcCarbonFactor.getSourceDescription()))) {
                String fcCarbonFactorId = fcCarbonFactorEsgDatasetMap.get(StringUtil.valueOf(fcCarbonFactor.getRecordYear()) + "#" + StringUtil.valueOf(fcCarbonFactor.getPlatform()) + "#" + StringUtil.valueOf(fcCarbonFactor.getChineseName()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getCategory()) + "#" + StringUtil.valueOf(fcCarbonFactor.getEmissionSource()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getUnit()) + "#" +  StringUtil.removeTrailingZeros(fcCarbonFactor.getCarbonFactor()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getCarbonFactorUnit()) + "#" + StringUtil.valueOf(fcCarbonFactor.getSourceDescription()));
                e.setFcCarbonFactorId(fcCarbonFactorId);
            }else {
                fcCarbonFactorEsgDatasetMapper.insertSelective(fcCarbonFactor);
                e.setFcCarbonFactorId(fcCarbonFactor.getId());
                fcCarbonFactorEsgDatasetMap.put(StringUtil.valueOf(fcCarbonFactor.getRecordYear()) + "#" + StringUtil.valueOf(fcCarbonFactor.getPlatform()) + "#" + StringUtil.valueOf(fcCarbonFactor.getChineseName()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getCategory()) + "#" + StringUtil.valueOf(fcCarbonFactor.getEmissionSource()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getUnit()) + "#" +  StringUtil.removeTrailingZeros(fcCarbonFactor.getCarbonFactor()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getCarbonFactorUnit()) + "#" + StringUtil.valueOf(fcCarbonFactor.getSourceDescription()), e.getFcCarbonFactorId());
            }
            e.setFcCarbonFactorType("碳排因子");
            e.setFcCarbonFactorDatasource("ESG數據集");
            // 赋值表单明细id
            if (formDetailMap.containsKey(StringUtil.valueOf(e.getYear()) + "#" + StringUtil.valueOf(e.getFormCode()) + "#" + StringUtil.valueOf(e.getTypeE()))) {
                String formDetailId = formDetailMap.get(StringUtil.valueOf(e.getYear()) + "#" + StringUtil.valueOf(e.getFormCode()) + "#" + StringUtil.valueOf(e.getTypeE()));
                e.setFormDetailId(formDetailId);
                // 查找因子范围
                if (factorScopeMap.containsKey(e.getProtocolDetailId() + "#" + e.getFormDetailId())) {
                    String factorScopeId = factorScopeMap.get(e.getProtocolDetailId() + "#" + e.getFormDetailId());
                    e.setFactorScopeId(factorScopeId);
                }else {
                    FactorScope factorScope = new FactorScope();
                    factorScope.setFactorType(e.getFcCarbonFactorType());
                    factorScope.setProtocolDetailId(e.getProtocolDetailId());
                    factorScope.setFormDetailId(e.getFormDetailId());
                    factorScopeMapper.insertSelective(factorScope);
                    e.setFactorScopeId(factorScope.getId());
                    factorScopeMap.put(e.getProtocolDetailId() + "#" + e.getFormDetailId(), e.getFactorScopeId());
                }
                // 新增因子选择数据
                FactorSelection factorSelection = FactorSelectionConverter.convertToModel(e);
                factorSelection.setIsActive(true);
                factorSelection.setLastUpdateVersion(0);
                factorSelectionMapper.insertSelective(factorSelection);
            }else {
                throw new ServiceException("碳排因子_ESG數據集 表单明细编码：" + e.getTypeE() + "不存在，请检查");
            }
        });
    }

    /**
     * 创建碳排因子_全生命周期數據集 因子选择数据
     * <AUTHOR>
     * @date 2025/1/27 10:22
     * @param flcFactorSelectionList
     * @param protocolDetailMap
     * @param formDetailMap
     * @param factorScopeMap
     */
    private void createFlcFactorSelection(List<FactorSelectionVO> flcFactorSelectionList,
                                          Map<String, String> protocolDetailMap,
                                          Map<String, String> formDetailMap,
                                          Map<String, String> factorScopeMap) {
        OrganizationExample organizationExample = new OrganizationExample();
        List<String> orgList = flcFactorSelectionList.stream().map(FactorSelectionVO::getOrganizationName).distinct().collect(Collectors.toList());
        for (String orgName : orgList) {
            organizationExample.or().andNameEqualTo(orgName).andIsDeletedEqualTo(false);
        }
        List<Organization> lstOrganization = organizationMapper.selectByExample(organizationExample);
        if (CollectionUtils.isEmpty(lstOrganization)) {
            throw new ServiceException("组织架构不存在！");
        }
        Map<String, String> orgMap = lstOrganization.stream().collect(Collectors.toMap(e -> e.getName(), Organization::getId));
        // 查找因子管理（碳排因子_全生命周期數據集）
        FcCarbonFactorFlcExample fcCarbonFactorFlcExample = new FcCarbonFactorFlcExample();
        fcCarbonFactorFlcExample.or().andIsDeletedEqualTo(false);
        List<FcCarbonFactorFlc> fcCarbonFactorFlcs = fcCarbonFactorFlcMapper.selectByExample(fcCarbonFactorFlcExample);

        Map<String, String> fcCarbonFactorFlcMap = fcCarbonFactorFlcs.stream().collect(
                Collectors.toMap(e -> StringUtil.valueOf(e.getRecordYear()) + "#" + StringUtil.valueOf(e.getChineseName()) + "#" + StringUtil.removeTrailingZeros(e.getUpstreamEmission()) +
                                "#" + StringUtil.removeTrailingZeros(e.getDownstreamEmission()) + "#" + StringUtil.valueOf(e.getEmissionUnit()) +
                                "#" +  StringUtil.removeTrailingZeros(e.getCarbonFactor()) + "#" + StringUtil.valueOf(e.getCarbonFactorUnit()) +
                                "#" + StringUtil.valueOf(e.getDatasource())
                        , FcCarbonFactorFlc::getId));
        flcFactorSelectionList.forEach(e -> {
            if (orgMap.containsKey(e.getOrganizationName())) {
                String organizationId = orgMap.get(e.getOrganizationName());
                e.setOrganizationId(organizationId);
            }else {
                throw new ServiceException("组织架构不存在！");
            }
            if (protocolDetailMap.containsKey(e.getProtocolId() + "#" + e.getCarbonEmissionLocationId() + "#" + e.getCategoryId() + "#" + e.getSubCategoryId())) {
                String protocolDetailId = protocolDetailMap.get(e.getProtocolId() + "#" + e.getCarbonEmissionLocationId() + "#" + e.getCategoryId() + "#" + e.getSubCategoryId());
                e.setProtocolDetailId(protocolDetailId);
            }else {
                // 增加协议明细
                ProtocolDetail protocolDetail = new ProtocolDetail();
                protocolDetail.setCarbonEmissionLocationId(e.getCarbonEmissionLocationId());
                protocolDetail.setSubCategoryId(e.getSubCategoryId());
                protocolDetail.setLastUpdateVersion(0);
                protocolDetailMapper.insertSelective(protocolDetail);
                e.setProtocolDetailId(protocolDetail.getId());
                protocolDetailMap.put(e.getProtocolId() + "#" + e.getCarbonEmissionLocationId() + "#" + e.getCategoryId() + "#" + e.getSubCategoryId(), e.getProtocolDetailId());
            }
            // 因子管理（碳排因子_全生命周期數據集）
            FcCarbonFactorFlc fcCarbonFactor = (FcCarbonFactorFlc) e.getFcCarbonFactorVO();
            fcCarbonFactor.setRecordYear(e.getYear());
            if (fcCarbonFactorFlcMap.containsKey(StringUtil.valueOf(fcCarbonFactor.getRecordYear()) + "#" + StringUtil.valueOf(fcCarbonFactor.getChineseName()) + "#" + StringUtil.removeTrailingZeros(fcCarbonFactor.getUpstreamEmission()) +
                    "#" + StringUtil.removeTrailingZeros(fcCarbonFactor.getDownstreamEmission()) + "#" + StringUtil.valueOf(fcCarbonFactor.getEmissionUnit()) +
                    "#" +  StringUtil.removeTrailingZeros(fcCarbonFactor.getCarbonFactor()) + "#" + StringUtil.valueOf(fcCarbonFactor.getCarbonFactorUnit()) +
                    "#" + StringUtil.valueOf(fcCarbonFactor.getDatasource()))) {
                String fcCarbonFactorId = fcCarbonFactorFlcMap.get(StringUtil.valueOf(fcCarbonFactor.getRecordYear()) + "#" + StringUtil.valueOf(fcCarbonFactor.getChineseName()) + "#" + StringUtil.removeTrailingZeros(fcCarbonFactor.getUpstreamEmission()) +
                        "#" + StringUtil.removeTrailingZeros(fcCarbonFactor.getDownstreamEmission()) + "#" + StringUtil.valueOf(fcCarbonFactor.getEmissionUnit()) +
                        "#" +  StringUtil.removeTrailingZeros(fcCarbonFactor.getCarbonFactor()) + "#" + StringUtil.valueOf(fcCarbonFactor.getCarbonFactorUnit()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getDatasource()));
                e.setFcCarbonFactorId(fcCarbonFactorId);
            }else {
                fcCarbonFactorFlcMapper.insertSelective(fcCarbonFactor);
                e.setFcCarbonFactorId(fcCarbonFactor.getId());
                fcCarbonFactorFlcMap.put(StringUtil.valueOf(fcCarbonFactor.getRecordYear()) + "#" + StringUtil.valueOf(fcCarbonFactor.getChineseName()) + "#" + StringUtil.removeTrailingZeros(fcCarbonFactor.getUpstreamEmission()) +
                        "#" + StringUtil.removeTrailingZeros(fcCarbonFactor.getDownstreamEmission()) + "#" + StringUtil.valueOf(fcCarbonFactor.getEmissionUnit()) +
                        "#" +  StringUtil.removeTrailingZeros(fcCarbonFactor.getCarbonFactor()) + "#" + StringUtil.valueOf(fcCarbonFactor.getCarbonFactorUnit()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getDatasource()), e.getFcCarbonFactorId());
            }
            e.setFcCarbonFactorType("碳排因子");
            e.setFcCarbonFactorDatasource("全生命周期數據集");
            // 赋值表单明细id
            if (formDetailMap.containsKey(StringUtil.valueOf(e.getYear()) + "#" + StringUtil.valueOf(e.getFormCode()) + "#" + StringUtil.valueOf(e.getTypeE()))) {
                String formDetailId = formDetailMap.get(StringUtil.valueOf(e.getYear()) + "#" + StringUtil.valueOf(e.getFormCode()) + "#" + StringUtil.valueOf(e.getTypeE()));
                e.setFormDetailId(formDetailId);
                // 查找因子范围
                if (factorScopeMap.containsKey(e.getProtocolDetailId() + "#" + e.getFormDetailId())) {
                    String factorScopeId = factorScopeMap.get(e.getProtocolDetailId() + "#" + e.getFormDetailId());
                    e.setFactorScopeId(factorScopeId);
                }else {
                    FactorScope factorScope = new FactorScope();
                    factorScope.setFactorType(e.getFcCarbonFactorType());
                    factorScope.setProtocolDetailId(e.getProtocolDetailId());
                    factorScope.setFormDetailId(e.getFormDetailId());
                    factorScopeMapper.insertSelective(factorScope);
                    e.setFactorScopeId(factorScope.getId());
                    factorScopeMap.put(e.getProtocolDetailId() + "#" + e.getFormDetailId(), e.getFactorScopeId());
                }
                // 新增因子选择数据
                FactorSelection factorSelection = FactorSelectionConverter.convertToModel(e);
                factorSelection.setIsActive(true);
                factorSelection.setLastUpdateVersion(0);
                factorSelectionMapper.insertSelective(factorSelection);
            }else {
                throw new ServiceException("碳排因子_全生命周期數據集 表单明细编码：" + e.getTypeE() + "不存在，请检查");
            }
        });

    }

    /**
     * 创建碳排因子_GBT51366 因子选择数据
     * <AUTHOR>
     * @date 2025/1/27 10:22
     * @param gbt51366VOFactorSelectionList
     * @param protocolDetailMap
     * @param formDetailMap
     * @param factorScopeMap
     */
    private void createGbt51366VOFactorSelection(List<FactorSelectionVO> gbt51366VOFactorSelectionList,
                                                 Map<String, String> protocolDetailMap,
                                                 Map<String, String> formDetailMap,
                                                 Map<String, String> factorScopeMap) {
        OrganizationExample organizationExample = new OrganizationExample();
        List<String> orgList = gbt51366VOFactorSelectionList.stream().map(FactorSelectionVO::getOrganizationName).distinct().collect(Collectors.toList());
        for (String orgName : orgList) {
            organizationExample.or().andNameEqualTo(orgName).andIsDeletedEqualTo(false);
        }
        List<Organization> lstOrganization = organizationMapper.selectByExample(organizationExample);
        if (CollectionUtils.isEmpty(lstOrganization)) {
            throw new ServiceException("组织架构不存在！");
        }
        Map<String, String> orgMap = lstOrganization.stream().collect(Collectors.toMap(e -> e.getName(), Organization::getId));
        // 查找因子管理（碳排因子_GBT51366）
        FcCarbonFactorGbt51366Example fcCarbonFactorGbt51366Example = new FcCarbonFactorGbt51366Example();
        fcCarbonFactorGbt51366Example.or().andIsDeletedEqualTo(false);
        List<FcCarbonFactorGbt51366> fcCarbonFactorGbt51366s = fcCarbonFactorGbt51366Mapper.selectByExample(fcCarbonFactorGbt51366Example);

        Map<String, String> fcCarbonFactorGbt51366Map = fcCarbonFactorGbt51366s.stream().collect(
                Collectors.toMap(e -> StringUtil.valueOf(e.getRecordYear()) + "#" + StringUtil.valueOf(e.getChineseName()) + "#" + StringUtil.valueOf(e.getSpecification()) +
                                "#" + StringUtil.valueOf(e.getEnergyConsumption()) + "#" + StringUtil.removeTrailingZeros(e.getHeatCo2Factor()) +
                                "#" + StringUtil.removeTrailingZeros(e.getEffectiveCo2FactorDefault()) + "#" + StringUtil.removeTrailingZeros(e.getEffectiveCo2FactorLower()) +
                                "#" + StringUtil.removeTrailingZeros(e.getEffectiveCo2FactorUpper()) + "#" + StringUtil.removeTrailingZeros(e.getCarbonFactor()) +
                                "#" + StringUtil.valueOf(e.getCarbonFactorUnit()) + "#" + StringUtil.valueOf(e.getSpecification()) + "#" + StringUtil.valueOf(e.getSourceDescription())
                        , FcCarbonFactorGbt51366::getId));
        gbt51366VOFactorSelectionList.forEach(e -> {
            if (orgMap.containsKey(e.getOrganizationName())) {
                String organizationId = orgMap.get(e.getOrganizationName());
                e.setOrganizationId(organizationId);
            }else {
                throw new ServiceException("组织架构不存在！");
            }
            if (protocolDetailMap.containsKey(e.getProtocolId() + "#" + e.getCarbonEmissionLocationId() + "#" + e.getCategoryId() + "#" + e.getSubCategoryId())) {
                String protocolDetailId = protocolDetailMap.get(e.getProtocolId() + "#" + e.getCarbonEmissionLocationId() + "#" + e.getCategoryId() + "#" + e.getSubCategoryId());
                e.setProtocolDetailId(protocolDetailId);
            }else {
                // 增加协议明细
                ProtocolDetail protocolDetail = new ProtocolDetail();
                protocolDetail.setCarbonEmissionLocationId(e.getCarbonEmissionLocationId());
                protocolDetail.setSubCategoryId(e.getSubCategoryId());
                protocolDetail.setLastUpdateVersion(0);
                protocolDetailMapper.insertSelective(protocolDetail);
                e.setProtocolDetailId(protocolDetail.getId());
                protocolDetailMap.put(e.getProtocolId() + "#" + e.getCarbonEmissionLocationId() + "#" + e.getCategoryId() + "#" + e.getSubCategoryId(), e.getProtocolDetailId());
            }
            // 因子管理（碳排因子_GBT51366）
            FcCarbonFactorGbt51366 fcCarbonFactor = (FcCarbonFactorGbt51366) e.getFcCarbonFactorVO();
            fcCarbonFactor.setRecordYear(e.getYear());
            if (fcCarbonFactorGbt51366Map.containsKey(StringUtil.valueOf(fcCarbonFactor.getRecordYear()) + "#" + StringUtil.valueOf(fcCarbonFactor.getChineseName()) + "#" + StringUtil.valueOf(fcCarbonFactor.getSpecification()) +
                    "#" + StringUtil.valueOf(fcCarbonFactor.getEnergyConsumption()) + "#" + StringUtil.removeTrailingZeros(fcCarbonFactor.getHeatCo2Factor()) +
                    "#" + StringUtil.removeTrailingZeros(fcCarbonFactor.getEffectiveCo2FactorDefault()) + "#" + StringUtil.removeTrailingZeros(fcCarbonFactor.getEffectiveCo2FactorLower()) +
                    "#" + StringUtil.removeTrailingZeros(fcCarbonFactor.getEffectiveCo2FactorUpper()) + "#" + StringUtil.removeTrailingZeros(fcCarbonFactor.getCarbonFactor()) +
                    "#" + StringUtil.valueOf(fcCarbonFactor.getCarbonFactorUnit()) + "#" + StringUtil.valueOf(fcCarbonFactor.getSpecification()) + "#" + StringUtil.valueOf(fcCarbonFactor.getSourceDescription()))) {
                String fcCarbonFactorId = fcCarbonFactorGbt51366Map.get(StringUtil.valueOf(fcCarbonFactor.getRecordYear()) + "#" + StringUtil.valueOf(fcCarbonFactor.getChineseName()) + "#" + StringUtil.valueOf(fcCarbonFactor.getSpecification()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getEnergyConsumption()) + "#" + StringUtil.removeTrailingZeros(fcCarbonFactor.getHeatCo2Factor()) +
                        "#" + StringUtil.removeTrailingZeros(fcCarbonFactor.getEffectiveCo2FactorDefault()) + "#" + StringUtil.removeTrailingZeros(fcCarbonFactor.getEffectiveCo2FactorLower()) +
                        "#" + StringUtil.removeTrailingZeros(fcCarbonFactor.getEffectiveCo2FactorUpper()) + "#" + StringUtil.removeTrailingZeros(fcCarbonFactor.getCarbonFactor()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getCarbonFactorUnit()) + "#" + StringUtil.valueOf(fcCarbonFactor.getSpecification()) + "#" + StringUtil.valueOf(fcCarbonFactor.getSourceDescription()));
                e.setFcCarbonFactorId(fcCarbonFactorId);
            }else {
                fcCarbonFactorGbt51366Mapper.insertSelective(fcCarbonFactor);
                e.setFcCarbonFactorId(fcCarbonFactor.getId());
                fcCarbonFactorGbt51366Map.put(StringUtil.valueOf(fcCarbonFactor.getRecordYear()) + "#" + StringUtil.valueOf(fcCarbonFactor.getChineseName()) + "#" + StringUtil.valueOf(fcCarbonFactor.getSpecification()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getEnergyConsumption()) + "#" + StringUtil.removeTrailingZeros(fcCarbonFactor.getHeatCo2Factor()) +
                        "#" + StringUtil.removeTrailingZeros(fcCarbonFactor.getEffectiveCo2FactorDefault()) + "#" + StringUtil.removeTrailingZeros(fcCarbonFactor.getEffectiveCo2FactorLower()) +
                        "#" + StringUtil.removeTrailingZeros(fcCarbonFactor.getEffectiveCo2FactorUpper()) + "#" + StringUtil.removeTrailingZeros(fcCarbonFactor.getCarbonFactor()) +
                        "#" + StringUtil.valueOf(fcCarbonFactor.getCarbonFactorUnit()) + "#" + StringUtil.valueOf(fcCarbonFactor.getSpecification()) + "#" + StringUtil.valueOf(fcCarbonFactor.getSourceDescription()), e.getFcCarbonFactorId());
            }
            e.setFcCarbonFactorType("碳排因子");
            e.setFcCarbonFactorDatasource("GBT51366");
            // 赋值表单明细id
            if (formDetailMap.containsKey(StringUtil.valueOf(e.getYear()) + "#" + StringUtil.valueOf(e.getFormCode()) + "#" + StringUtil.valueOf(e.getTypeE()))) {
                String formDetailId = formDetailMap.get(StringUtil.valueOf(e.getYear()) + "#" + StringUtil.valueOf(e.getFormCode()) + "#" + StringUtil.valueOf(e.getTypeE()));
                e.setFormDetailId(formDetailId);
                // 查找因子范围
                if (factorScopeMap.containsKey(e.getProtocolDetailId() + "#" + e.getFormDetailId())) {
                    String factorScopeId = factorScopeMap.get(e.getProtocolDetailId() + "#" + e.getFormDetailId());
                    e.setFactorScopeId(factorScopeId);
                }else {
                    FactorScope factorScope = new FactorScope();
                    factorScope.setFactorType(e.getFcCarbonFactorType());
                    factorScope.setProtocolDetailId(e.getProtocolDetailId());
                    factorScope.setFormDetailId(e.getFormDetailId());
                    factorScopeMapper.insertSelective(factorScope);
                    e.setFactorScopeId(factorScope.getId());
                    factorScopeMap.put(e.getProtocolDetailId() + "#" + e.getFormDetailId(), e.getFactorScopeId());
                }
                // 新增因子选择数据
                FactorSelection factorSelection = FactorSelectionConverter.convertToModel(e);
                factorSelection.setIsActive(true);
                factorSelection.setLastUpdateVersion(0);
                factorSelectionMapper.insertSelective(factorSelection);
            }else {
                throw new ServiceException("碳排因子_GBT51366 表单明细编码：" + e.getTypeE() + "不存在，请检查");
            }
        });
    }

    /**
     * 创建碳排因子_香港碳排數據  因子选择数据
     * <AUTHOR>
     * @date 2025/1/27 10:16
     * @param hkFactorSelectionList
     * @param protocolDetailMap
     * @param formDetailMap
     * @param factorScopeMap
     */
    private void createHkFactorSelection(List<FactorSelectionVO> hkFactorSelectionList,
                                         Map<String, String> protocolDetailMap,
                                         Map<String, String> formDetailMap,
                                         Map<String, String> factorScopeMap) {
        OrganizationExample organizationExample = new OrganizationExample();
        List<String> orgList = hkFactorSelectionList.stream().map(FactorSelectionVO::getOrganizationName).distinct().collect(Collectors.toList());
        for (String orgName : orgList) {
            organizationExample.or().andNameEqualTo(orgName).andIsDeletedEqualTo(false);
        }
        List<Organization> lstOrganization = organizationMapper.selectByExample(organizationExample);
        if (CollectionUtils.isEmpty(lstOrganization)) {
            throw new ServiceException("组织架构不存在！");
        }
        Map<String, String> orgMap = lstOrganization.stream().collect(Collectors.toMap(e -> e.getName(), Organization::getId));
        // 查找因子管理（碳排因子_香港碳排數據）
        FcCarbonFactorHkExample fcCarbonFactorHkExample = new FcCarbonFactorHkExample();
        fcCarbonFactorHkExample.or().andIsDeletedEqualTo(false);
        List<FcCarbonFactorHk> fcCarbonFactorHks = fcCarbonFactorHkMapper.selectByExample(fcCarbonFactorHkExample);

        Map<String, String> fcCarbonFactorHkMap = fcCarbonFactorHks.stream().collect(
                Collectors.toMap(e -> StringUtil.valueOf(e.getRecordYear()) + "#" + StringUtil.valueOf(e.getChineseName()) + "#" + StringUtil.valueOf(e.getMaterialAttribute()) +
                                "#" + StringUtil.valueOf(e.getProductionProcess()) + "#" + StringUtil.valueOf(e.getUnit()) +
                                "#" + StringUtil.valueOf(e.getMaterialBelongsTo()) + "#" + StringUtil.removeTrailingZeros(e.getCarbonFactor()) +
                                "#" + StringUtil.valueOf(e.getCarbonFactorUnit()) + "#" + StringUtil.valueOf(e.getSourceDescription())
                        , FcCarbonFactorHk::getId));
        hkFactorSelectionList.stream().forEach(e -> {
            if (orgMap.containsKey(e.getOrganizationName())) {
                String organizationId = orgMap.get(e.getOrganizationName());
                e.setOrganizationId(organizationId);
            }else {
                throw new ServiceException("组织架构不存在！");
            }
            if (protocolDetailMap.containsKey(e.getProtocolId() + "#" + e.getCarbonEmissionLocationId() + "#" + e.getCategoryId() + "#" + e.getSubCategoryId())) {
                String protocolDetailId = protocolDetailMap.get(e.getProtocolId() + "#" + e.getCarbonEmissionLocationId() + "#" + e.getCategoryId() + "#" + e.getSubCategoryId());
                e.setProtocolDetailId(protocolDetailId);
            }else {
                // 增加协议明细
                ProtocolDetail protocolDetail = new ProtocolDetail();
                protocolDetail.setCarbonEmissionLocationId(e.getCarbonEmissionLocationId());
                protocolDetail.setSubCategoryId(e.getSubCategoryId());
                protocolDetail.setLastUpdateVersion(0);
                protocolDetailMapper.insertSelective(protocolDetail);
                e.setProtocolDetailId(protocolDetail.getId());
                protocolDetailMap.put(e.getProtocolId() + "#" + e.getCarbonEmissionLocationId() + "#" + e.getCategoryId() + "#" + e.getSubCategoryId(), e.getProtocolDetailId());
            }
            // 因子管理（碳排因子_香港碳排數據）
            FcCarbonFactorHk fcCarbonFactorHk = (FcCarbonFactorHk) e.getFcCarbonFactorVO();
            fcCarbonFactorHk.setRecordYear(e.getYear());
            if (fcCarbonFactorHkMap.containsKey( StringUtil.valueOf(fcCarbonFactorHk.getRecordYear()) + "#" + StringUtil.valueOf(fcCarbonFactorHk.getChineseName()) + "#" + StringUtil.valueOf(fcCarbonFactorHk.getMaterialAttribute()) +
                    "#" + StringUtil.valueOf(fcCarbonFactorHk.getProductionProcess()) + "#" + StringUtil.valueOf(fcCarbonFactorHk.getUnit()) +
                    "#" + StringUtil.valueOf(fcCarbonFactorHk.getMaterialBelongsTo()) + "#" + StringUtil.removeTrailingZeros(fcCarbonFactorHk.getCarbonFactor()) +
                    "#" + StringUtil.valueOf(fcCarbonFactorHk.getCarbonFactorUnit()) + "#" + StringUtil.valueOf(fcCarbonFactorHk.getSourceDescription()))) {
                String fcCarbonFactorId = fcCarbonFactorHkMap.get(StringUtil.valueOf(e.getYear()) + "#" + StringUtil.valueOf(fcCarbonFactorHk.getChineseName()) + "#" + StringUtil.valueOf(fcCarbonFactorHk.getMaterialAttribute()) +
                        "#" + StringUtil.valueOf(fcCarbonFactorHk.getProductionProcess()) + "#" + StringUtil.valueOf(fcCarbonFactorHk.getUnit()) +
                        "#" + StringUtil.valueOf(fcCarbonFactorHk.getMaterialBelongsTo()) + "#" + StringUtil.removeTrailingZeros(fcCarbonFactorHk.getCarbonFactor()) +
                        "#" + StringUtil.valueOf(fcCarbonFactorHk.getCarbonFactorUnit()) + "#" + StringUtil.valueOf(fcCarbonFactorHk.getSourceDescription()));
                e.setFcCarbonFactorId(fcCarbonFactorId);
            }else {
                fcCarbonFactorHkMapper.insertSelective(fcCarbonFactorHk);
                e.setFcCarbonFactorId(fcCarbonFactorHk.getId());
                fcCarbonFactorHkMap.put(StringUtil.valueOf(fcCarbonFactorHk.getRecordYear()) + "#" + StringUtil.valueOf(fcCarbonFactorHk.getChineseName()) + "#" + StringUtil.valueOf(fcCarbonFactorHk.getMaterialAttribute()) +
                        "#" + StringUtil.valueOf(fcCarbonFactorHk.getProductionProcess()) + "#" + StringUtil.valueOf(fcCarbonFactorHk.getUnit()) +
                        "#" + StringUtil.valueOf(fcCarbonFactorHk.getMaterialBelongsTo()) + "#" + StringUtil.removeTrailingZeros(fcCarbonFactorHk.getCarbonFactor()) +
                        "#" + StringUtil.valueOf(fcCarbonFactorHk.getCarbonFactorUnit()) + "#" + StringUtil.valueOf(fcCarbonFactorHk.getSourceDescription()), e.getFcCarbonFactorId());
            }
            e.setFcCarbonFactorType("碳排因子");
            e.setFcCarbonFactorDatasource("香港碳排數據");
            // 赋值表单明细id
            if (formDetailMap.containsKey(StringUtil.valueOf(e.getYear()) + "#" + StringUtil.valueOf(e.getFormCode()) + "#" + StringUtil.valueOf(e.getTypeE()))) {
                String formDetailId = formDetailMap.get(StringUtil.valueOf(e.getYear()) + "#" + StringUtil.valueOf(e.getFormCode()) + "#" + StringUtil.valueOf(e.getTypeE()));
                e.setFormDetailId(formDetailId);
                // 查找因子范围
                if (factorScopeMap.containsKey(e.getProtocolDetailId() + "#" + e.getFormDetailId())) {
                    String factorScopeId = factorScopeMap.get(e.getProtocolDetailId() + "#" + e.getFormDetailId());
                    e.setFactorScopeId(factorScopeId);
                }else {
                    FactorScope factorScope = new FactorScope();
                    factorScope.setFactorType(e.getFcCarbonFactorType());
                    factorScope.setProtocolDetailId(e.getProtocolDetailId());
                    factorScope.setFormDetailId(e.getFormDetailId());
                    factorScopeMapper.insertSelective(factorScope);
                    e.setFactorScopeId(factorScope.getId());
                    factorScopeMap.put(e.getProtocolDetailId() + "#" + e.getFormDetailId(), e.getFactorScopeId());
                }
                // 新增因子选择数据
                FactorSelection factorSelection = FactorSelectionConverter.convertToModel(e);
                factorSelection.setIsActive(true);
                factorSelection.setLastUpdateVersion(0);
                factorSelectionMapper.insertSelective(factorSelection);
            }else {
                throw new ServiceException("碳排因子_香港碳排數據 表单明细编码：" + e.getTypeE() + "不存在，请检查");
            }
        });
    }

    /**
     * 标准管理各协议、场景、大类、小类数据校验
     */
    private void protocolCheckBefore(List<FactorSelectionVO> factorSelectionVOS, Map<String, String> protocolMap, Map<String, String> locationMap, Map<String, String> protocolCategoryMap, Map<String, String> protocolSubCategoryMap) {
        factorSelectionVOS.stream().forEach(e -> {
            if (protocolMap.containsKey(e.getProtocolNameEn())) {
                String protocolId = protocolMap.get(e.getProtocolNameEn());
                e.setProtocolId(protocolId);
                if (locationMap.containsKey(e.getCarbonEmissionLocationName())) {
                    String carbonEmissionLocationId = locationMap.get(e.getCarbonEmissionLocationName());
                    e.setCarbonEmissionLocationId(carbonEmissionLocationId);
                }else {
                    throw new ServiceException("场景繁体：" + e.getCarbonEmissionLocationName() + "不存在，请维护！");
                }
                if (protocolCategoryMap.containsKey(e.getProtocolId() + "#" + e.getCategoryName())) {
                    String categoryId = protocolCategoryMap.get(e.getProtocolId() + "#" + e.getCategoryName());
                    e.setCategoryId(categoryId);
                    if (protocolSubCategoryMap.containsKey(e.getCategoryId() + "#" + e.getSubCategoryName())) {
                        String subCategoryId = protocolSubCategoryMap.get(e.getCategoryId() + "#" + e.getSubCategoryName());
                        e.setSubCategoryId(subCategoryId);
                    }else {
                        throw new ServiceException("协议小类繁体：" + e.getSubCategoryName() + "不存在，请维护！");
                    }
                }else {
                    throw new ServiceException("协议大类繁体：" + e.getCategoryName() + "不存在，请维护！");
                }

            }else {
                throw new ServiceException("协议英文：" + e.getProtocolNameEn() + "不存在，请维护！");
            }
            // 设置表单编码
            e.setFormCode(FormCodeEnum.getCodeByDesc(e.getFormName()));
        });
    }

    /**
     * 解析因子选择导入文件excel，转化成VO对象
     */
    private FactorSelectionImportExcelVO excelToData(MultipartFile file) {
        // 检查文件是否为空
        if (file.isEmpty()) {
            throw new IllegalArgumentException("文件为空，请上传有效的文件");
        }
        Map<Integer, Class<?>> sheetClasses = new HashMap<>();
        sheetClasses.put(0, ExcleFcCarbonFactorHkFactorSelectionVO.class); // 第一个 Sheet 页对应 ExcleFcCarbonFactorHkFactorSelectionVO 类
        sheetClasses.put(1, ExcleFcCarbonFactorGbt51366VOFactorSelectionVO.class); // 第二个 Sheet 页对应 ExcleFcCarbonFactorGbt51366VOFactorSelectionVO 类
        sheetClasses.put(2, ExcleFcCarbonFactorFlcFactorSelectionVO.class); // 第三个 Sheet 页对应 ExcleFcCarbonFactorFlcFactorSelectionVO 类
        sheetClasses.put(3, ExcleFcCarbonFactorEsgDatasetFactorSelectionVO.class); // 第四个 Sheet 页对应 ExcleFcCarbonFactorEsgDatasetFactorSelectionVO 类
        sheetClasses.put(4, ExcleFcEnergyFactorEsgDatasetFactorSelectionVO.class); // 第五个 Sheet 页对应 ExcleFcEnergyFactorEsgDatasetFactorSelectionVO 类
        sheetClasses.put(5, ExcleFcAirPollutionFactorEsgDatasetFactorSelectionVO.class); // 第六个 Sheet 页对应 ExcleFcAirPollutionFactorEsgDatasetFactorSelectionVO 类
        // 读取 Excel 文件
        Map<String, List<?>> result = FileUtil.readExcel(file, sheetClasses);
        FactorSelectionImportExcelVO factorSelectionImportExcelVO = new FactorSelectionImportExcelVO();
        factorSelectionImportExcelVO.setHkFactorSelectionVOS((List<ExcleFcCarbonFactorHkFactorSelectionVO>) result.get("Sheet1"));
        factorSelectionImportExcelVO.setGbt51366VOFactorSelectionVOS((List<ExcleFcCarbonFactorGbt51366VOFactorSelectionVO>) result.get("Sheet2"));
        factorSelectionImportExcelVO.setFlcFactorSelectionVOS((List<ExcleFcCarbonFactorFlcFactorSelectionVO>) result.get("Sheet3"));
        factorSelectionImportExcelVO.setEsgDatasetFactorSelectionVOS((List<ExcleFcCarbonFactorEsgDatasetFactorSelectionVO>) result.get("Sheet4"));
        factorSelectionImportExcelVO.setEnergyFactorEsgDatasetFactorSelectionVOS((List<ExcleFcEnergyFactorEsgDatasetFactorSelectionVO>) result.get("Sheet5"));
        factorSelectionImportExcelVO.setAirPollutionFactorEsgDatasetFactorSelectionVOS((List<ExcleFcAirPollutionFactorEsgDatasetFactorSelectionVO>) result.get("Sheet6"));
        return factorSelectionImportExcelVO;
    }





}
