package com.csci.susdev.service;

import com.csci.susdev.annotation.DS;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.DatasourceContextEnum;
import com.csci.susdev.mapper.FfCmFixedDetailMapper;
import com.csci.susdev.mapper.UserMapper;
import com.csci.susdev.model.FfCmFixedDetail;
import com.csci.susdev.model.FfCmFixedDetailExample;
import com.csci.susdev.modelcovt.FfCmFixedDetailVOConverter;
import com.csci.susdev.vo.FfCmFixedDetailVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
@LogMethod
@DS(DatasourceContextEnum.SUSDEV)
public class FfCmFixedDetailService {

    @Resource
    private FfCmFixedDetailMapper ffCmFixedDetailMapper;

    @Resource
    private UserMapper userMapper;

    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(FfCmFixedDetail record) {
        return ffCmFixedDetailMapper.insertSelective(record);
    }

    public List<FfCmFixedDetailVO> listFfCmFixedDetailByHeadId(String headId) {
        List<FfCmFixedDetail> lstDetail = listFfCmFixedDetails(headId);
        return lstDetail.stream().map(FfCmFixedDetailVOConverter::convert).collect(Collectors.toList());
    }

    private List<FfCmFixedDetail> listFfCmFixedDetails(String headId) {
        ServiceHelper.checkExist(headId, "headId不能为空");
        FfCmFixedDetailExample example = new FfCmFixedDetailExample();
        example.or().andHeadIdEqualTo(headId);
        example.setOrderByClause("seq, col_5");
        return ffCmFixedDetailMapper.selectByExample(example);
    }
    public List<FfCmFixedDetail> listFfCmFixedDetailByHeadIds(List<String> headIds) {
        FfCmFixedDetailExample example = new FfCmFixedDetailExample();
        example.or().andHeadIdIn(headIds);
        return ffCmFixedDetailMapper.selectByExample(example);
    }

}
