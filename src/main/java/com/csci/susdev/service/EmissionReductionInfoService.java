package com.csci.susdev.service;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.mapper.EmissionReductionInfoCustomMapper;
import com.csci.susdev.mapper.EmissionReductionInfoMapper;
import com.csci.susdev.model.EmissionReductionInfo;
import com.csci.susdev.model.EmissionReductionInfoExample;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.modelcovt.EmissionReductionInfoConverter;
import com.csci.susdev.qo.EmissionReductionInfoQO;
import com.csci.susdev.util.NamingConventionUtils;
import com.csci.susdev.vo.EmissionReductionBatchDeleteVO;
import com.csci.susdev.vo.EmissionReductionInfoVO;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class EmissionReductionInfoService {

    @Resource
    private EmissionReductionInfoMapper emissionReductionInfoMapper;
    @Resource
    private EmissionReductionInfoCustomMapper emissionReductionInfoCustomMapper;

    /**
     * 保存节能减排信息采集表记录
     *
     * @param emissionReductionInfoVO
     * @return
     */
    public String saveEmissionReductionInfo(EmissionReductionInfoVO emissionReductionInfoVO) {

        /*
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        workflowControlService.throwIfHasWorkflowControl(emissionReductionInfoVO.getAmbientHeadId(), "本期节能减排信息采集表数据已经提交，不能进行新增或修改");
        */

        if (StringUtils.isBlank(emissionReductionInfoVO.getId())) {
            // 新增
            return doAdd(emissionReductionInfoVO);
        } else {
            checkExist(emissionReductionInfoVO.getLastUpdateVersion(), "更新时版本号不能为空");
            // 修改
            return doUpdate(emissionReductionInfoVO);
        }
    }

    /**
     *   批量删除节能减排记录
     * @param emissionReductionBatchDeleteVO
     * @return
     */
    public String batchDeleteEmissionReductionInfo(EmissionReductionBatchDeleteVO emissionReductionBatchDeleteVO) {
        if(emissionReductionBatchDeleteVO == null || CollectionUtils.isEmpty(emissionReductionBatchDeleteVO.getDeleteIdList())){
            return "";
        }
        emissionReductionInfoCustomMapper.batchDeleteByIdList(emissionReductionBatchDeleteVO.getDeleteIdList());
        return "";
    }

    /**
     * 复制指定的节能减排信息采集表记录
     *
     * @param id
     * @return
     */
    public EmissionReductionInfo duplicateEmissionReductionInfo(String id) {
        EmissionReductionInfo emissionReductionInfo = emissionReductionInfoMapper.selectByPrimaryKey(id);
        checkExist(emissionReductionInfo, "未找到对应的节能减排信息采集表记录");

        /*
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        workflowControlService.throwIfHasWorkflowControl(emissionReductionInfo.getCeIdentificationHeadId(), "本期节能减排信息采集表数据已经提交，不能进行复制操作");
        */

        emissionReductionInfo.setId(null);
        emissionReductionInfo.setCreationTime(null);
        emissionReductionInfo.setCreateUserId(null);
        emissionReductionInfo.setCreateUsername(null);
        emissionReductionInfo.setLastUpdateVersion(0);
        emissionReductionInfo.setLastUpdateTime(null);
        emissionReductionInfo.setLastUpdateUserId(null);
        emissionReductionInfo.setLastUpdateUsername(null);
        emissionReductionInfoMapper.insert(emissionReductionInfo);
        return emissionReductionInfo;
    }

    /**
     * 查询节能减排信息采集表记录
     *
     * @return
     */
    public ResultPage<EmissionReductionInfoVO> listEmissionReductionInfo(EmissionReductionInfoQO emissionReductionInfoQO) {
        checkExist(emissionReductionInfoQO.getHeadId(), "节能减排信息采集表主表记录ID不能为空");
        EmissionReductionInfoExample example = new EmissionReductionInfoExample();
        example.or().andHeadIdEqualTo(emissionReductionInfoQO.getHeadId());

        if (StringUtils.isBlank(emissionReductionInfoQO.getOrderBy())) {
            example.setOrderByClause("creation_time desc");
        } else {
            String columnName = NamingConventionUtils.cameCaseToSnakeCase(emissionReductionInfoQO.getOrderBy());
            if (StringUtils.isBlank(columnName)) {
                throw new ServiceException("排序字段不正确");
            }
            example.setOrderByClause(columnName);
        }

        PageHelper.startPage(emissionReductionInfoQO.getCurPage(), emissionReductionInfoQO.getPageSize());
        List<EmissionReductionInfo> emissionReductionInfos = emissionReductionInfoMapper.selectByExample(example);
        return new ResultPage<>(emissionReductionInfos, emissionReductionInfos.stream().map(EmissionReductionInfoConverter::convert).collect(Collectors.toList()));
    }

    private String doAdd(EmissionReductionInfoVO emissionReductionInfoVO) {
        EmissionReductionInfo emissionReductionInfo = EmissionReductionInfoConverter.convert(emissionReductionInfoVO);
        emissionReductionInfoMapper.insertSelective(emissionReductionInfo);
        return emissionReductionInfo.getId();
    }

    private String doUpdate(EmissionReductionInfoVO emissionReductionInfoVO) {
        EmissionReductionInfo emissionReductionInfo = EmissionReductionInfoConverter.convert(emissionReductionInfoVO);
        EmissionReductionInfoExample example = new EmissionReductionInfoExample();
        example.or().andIdEqualTo(emissionReductionInfoVO.getId()).andLastUpdateVersionEqualTo(emissionReductionInfoVO.getLastUpdateVersion());
        int updateCount = emissionReductionInfoMapper.updateByExampleSelective(emissionReductionInfo, example);
        if (updateCount == 0) {
            throw new ServiceException("更新失败，可能是数据已被修改，请刷新后重试");
        }
        return emissionReductionInfo.getId();
    }

    public EmissionReductionInfoVO getEmissionReductionInfoById(String id) {
        EmissionReductionInfo emissionReductionInfo = emissionReductionInfoMapper.selectByPrimaryKey(id);
        if (emissionReductionInfo == null) {
            throw new ServiceException("数据不存在");
        }
        return EmissionReductionInfoConverter.convert(emissionReductionInfo);
    }

    public void deleteEmissionReductionInfoById(String id) {
        checkExist(id, "id不能为空");
        emissionReductionInfoMapper.deleteByPrimaryKey(id);
    }

    public List<EmissionReductionInfo> selectByExample(EmissionReductionInfoExample example) {
        return emissionReductionInfoMapper.selectByExample(example);
    }

    // 根据组织机构id及年份查询节能减排信息采集表记录
    public List<EmissionReductionInfo> listEmissionReductionInfoByOrgIdAndYear(String orgId, Integer year) {
        // first, query head record by org id and year
        /*AmbientHeadExample headExample = new AmbientHeadExample();
        headExample.or().and

        EmissionReductionInfoExample example = new EmissionReductionInfoExample();
        example.or().andOrgIdEqualTo(orgId).andYearValueEqualTo(year);
        return emissionReductionInfoMapper.selectByExample(example);*/
        throw new ServiceException("未实现");
    }

    public List<EmissionReductionInfoVO> listEmissionReductionInfoByHeadId(String headId) {
        checkExist(headId, "节能减排信息采集表主表记录ID不能为空");
        EmissionReductionInfoExample example = new EmissionReductionInfoExample();
        example.or().andHeadIdEqualTo(headId);
        example.setOrderByClause("creation_time desc");
        List<EmissionReductionInfo> emissionReductionInfos = emissionReductionInfoMapper.selectByExample(example);
        return emissionReductionInfos.stream().map(EmissionReductionInfoConverter::convert).collect(Collectors.toList());

    }
}
