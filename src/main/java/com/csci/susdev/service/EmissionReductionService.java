package com.csci.susdev.service;

import cn.hutool.core.util.StrUtil;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.constant.WorkflowControlState;
import com.csci.susdev.mapper.EmissionReductionDetailMapper;
import com.csci.susdev.mapper.EmissionReductionHeadMapper;
import com.csci.susdev.model.*;
import com.csci.susdev.modelcovt.EmissionReductionDetailConverter;
import com.csci.susdev.modelcovt.EmissionReductionHeadConverter;
import com.csci.susdev.modelcovt.EmissionReductionTableDataConverter;
import com.csci.susdev.qo.EmissionReductionQO;
import com.csci.susdev.qo.FfCmMobileQO;
import com.csci.susdev.util.redis.RedisLockUtil;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.vo.EmissionReductionDetailVO;
import com.csci.susdev.vo.EmissionReductionHeadVO;
import com.csci.susdev.vo.EmissionReductionTableDataVO;
import com.csci.susdev.vo.IdVersionVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Service
@LogMethod
public class EmissionReductionService {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(EmissionReductionService.class);
    /**
     * 初始化记录的过期时间
     */
    @Value("${lock.init.head.expire.milis:1000}")
    private long lockInitHeadExpireMilis;
    /**
     * 查询head记录时，获取锁失败循环等待的次数
     */
    @Value("${query.head.wait.loop.times:5}")
    private int lockQueryHeadWaitLoopTimes;
    /**
     * 查询head记录时，获取锁失败循环时每次休眠的时间
     */
    @Value("${query.head.wait.sleep.milis:50}")
    private int lockQueryHeadWaitSleepMilis;

    @Resource
    private EmissionReductionHeadMapper emissionReductionHeadMapper;

    @Resource
    private EmissionReductionDetailMapper emissionReductionDetailMapper;

    /**
     * 根据查询条件查询厌氧废水处理信息信息
     * 只会查询 active = true 的记录
     *
     * @param organizationId 组织 id
     * @param year           年份
     * @param month          月份
     * @return
     */
    EmissionReductionHead getEmissionReductionHeadBy(String organizationId, Integer year, Integer month) {
        EmissionReductionHeadExample example = new EmissionReductionHeadExample();
        example.or().andOrganizationIdEqualTo(organizationId).andYearEqualTo(year)
                .andMonthEqualTo(month).andIsActiveEqualTo(Boolean.TRUE);
        return emissionReductionHeadMapper.selectByExample(example).stream().findFirst().orElse(null);
    }

    /**
     * @param qo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public EmissionReductionHead createEmissionReductionHead(EmissionReductionQO qo) {
        EmissionReductionHead emissionReductionHead = getEmissionReductionHeadBy(qo.getOrganizationId(), qo.getYear(),
                qo.getMonth());
        if (emissionReductionHead == null) {
            emissionReductionHead = new EmissionReductionHead();
            emissionReductionHead.setOrganizationId(qo.getOrganizationId());
            emissionReductionHead.setYear(qo.getYear());
            emissionReductionHead.setMonth(qo.getMonth());
            emissionReductionHead.setIsActive(Boolean.TRUE);
            emissionReductionHead.setLastUpdateVersion(0);
            emissionReductionHeadMapper.insertSelective(emissionReductionHead);
        }
        return emissionReductionHead;
    }

    /**
     * 根据查询条件查询厌氧废水处理信息信息，如果不存在则创建
     *
     * @param emissionReductionQO 查询条件
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public EmissionReductionHeadVO getOrInit(EmissionReductionQO emissionReductionQO) {
        checkExist(emissionReductionQO.getOrganizationId(), "组织编号不能为空");
        checkExist(emissionReductionQO.getYear(), "年份不能为空");
        if(ObjectUtils.isEmpty(emissionReductionQO.getMonth())) {
            emissionReductionQO.setMonth(12);
        }

        if (emissionReductionQO.getYear() < 1900 || emissionReductionQO.getYear() > 2100) {
            throw new ServiceException("年份必须在 1900-2100 之间");
        }

        //查询或初始化记录，如果获取失败，直接抛出异常退出
        EmissionReductionHead emissionReductionHead = getOrInitHeadRecord(emissionReductionQO);

        // 查询流程审批状态
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        WorkflowControl workflowControl = workflowControlService.findWorkflowControlByBusinessId(emissionReductionHead.getId());
        EmissionReductionHeadVO emissionReductionHeadVO = EmissionReductionHeadConverter.convert(emissionReductionHead);
        if (Objects.nonNull(workflowControl)) {
            emissionReductionHeadVO.setWorkflowControlState(workflowControl.getState());
            emissionReductionHeadVO.setWorkflowControlStateName(Optional.ofNullable(WorkflowControlState.getWorkflowControlState(workflowControl.getState())).map(WorkflowControlState::getName).orElse(null));
        }
        return emissionReductionHeadVO;
    }

    /**
     * 查询或初始化记录，如果获取失败，直接抛出异常退出
     * @param
     * @return
     */
    private EmissionReductionHead getOrInitHeadRecord(EmissionReductionQO emissionReductionQO) {
        EmissionReductionHead emissionReductionHead = getEmissionReductionHeadBy(emissionReductionQO.getOrganizationId(),
                emissionReductionQO.getYear(), emissionReductionQO.getMonth());
        if (Objects.isNull(emissionReductionHead)) {
            //分布式锁的key：分别表示前缀:业务:组织ID:年:月
            String key = StrUtil.format(SusDevConsts.DISTRIBUTE_LOCK_KEY_FORMAT, SusDevConsts.PROJECT_PREFIX,
                    "EmissionReductionHead:getOrInit", emissionReductionQO.getOrganizationId(),
                    emissionReductionQO.getYear(), emissionReductionQO.getMonth());
            //超时时间1s
            boolean redisLock = RedisLockUtil.lock(key, lockInitHeadExpireMilis);
            if (!redisLock) {
                //获取锁失败，停顿50ms一次，停顿5次
                int count = lockQueryHeadWaitLoopTimes;
                while(count-- > 0){
                    emissionReductionHead = getEmissionReductionHeadBy(emissionReductionQO.getOrganizationId(),
                            emissionReductionQO.getYear(), emissionReductionQO.getMonth());
                    if(emissionReductionHead != null){
                        break;
                    }
                    try {
                        Thread.sleep(lockQueryHeadWaitSleepMilis);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
            }else{
                try{
                    emissionReductionHead = createEmissionReductionHead(emissionReductionQO);
                    // 初始化明细数据
                    EmissionReductionHead prevHead = getPrevHead(emissionReductionQO.getOrganizationId(), emissionReductionQO.getYear(),
                            emissionReductionQO.getMonth());
                    if(ObjectUtils.isNotEmpty(prevHead)) {
                        initDetailsWithPrevData(prevHead.getId(), emissionReductionHead.getId());
                    }
                }
                finally {
                    RedisLockUtil.unlock(key);
                }
            }
        }
        //如果最终没拿到数据，直接返回
        if(emissionReductionHead == null){
            throw new ServiceException("生成记录失败, 请稍后再尝试！");
        }
        return emissionReductionHead;
    }
    
    public EmissionReductionHead getPrevHead(String organizationId, Integer year, Integer month) {
        EmissionReductionHeadExample example = new EmissionReductionHeadExample();
        example.or().andIsActiveEqualTo(true).andOrganizationIdEqualTo(organizationId)
                .andYearEqualTo(year).andMonthLessThan(month);
        example.setOrderByClause("month desc");
        List<EmissionReductionHead> lst = selectByExample(example);
        if(lst.size() > 0) {
            return lst.get(0);
        } else {
            return null;
        }
    }

    void initDetailsWithPrevData(String prevHeadId, String curHeadId) {
        EmissionReductionDetailExample example = new EmissionReductionDetailExample();
        example.or().andHeadIdEqualTo(prevHeadId);
        List<EmissionReductionDetail> details = emissionReductionDetailMapper.selectByExample(example);
        for(EmissionReductionDetail detail : details) {
            detail.setId(null);
            detail.setHeadId(curHeadId);
            emissionReductionDetailMapper.insert(detail);
        }
    }

    /**
     * 根据主表id查询厌氧废水处理信息明细信息
     *
     * @param headId 厌氧废水处理信息头 id
     * @return
     */
    public List<EmissionReductionDetailVO> listEmissionReductionDetail(String headId) {
        checkExist(headId, "厌氧废水处理信息主表id不能为空");
        EmissionReductionDetailExample example = new EmissionReductionDetailExample();
        example.or().andHeadIdEqualTo(headId);
        example.setOrderByClause("seq");
        return emissionReductionDetailMapper.selectByExample(example).stream().map(new EmissionReductionDetailConverter()::convert).collect(Collectors.toList());
    }

    /**
     * 查询用于前端页面handsontable使用的数据
     *
     * @param headId 厌氧废水处理信息头 id
     * @return
     */
    public List<EmissionReductionTableDataVO> listEmissionReductionTable(String headId) {
        checkExist(headId, "厌氧废水处理信息主表id不能为空");
        EmissionReductionDetailExample example = new EmissionReductionDetailExample();
        example.or().andHeadIdEqualTo(headId);
        example.setOrderByClause("seq");
        return emissionReductionDetailMapper.selectByExample(example).stream().map(EmissionReductionTableDataConverter::convert).collect(Collectors.toList());
    }


    /**
     * proxy for mapper method
     *
     * @param record
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertDetailSelective(EmissionReductionDetail record) {
        return emissionReductionDetailMapper.insertSelective(record);
    }

    /**
     * 保存厌氧废水处理信息明细信息
     *
     * @param emissionReductionHeadVO 厌氧废水处理信息头信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateEmissionReductionWithDetail(EmissionReductionHeadVO emissionReductionHeadVO) {
        WorkflowControlService workflowControlService = SpringContextUtil.getBean(WorkflowControlService.class);
        if (workflowControlService.isNotAllowEdit(emissionReductionHeadVO.getId())) {
            throw new ServiceException("已经发起审批流程的单据不允许进行修改");
        }
        // 更新主表信息
        updateHead(emissionReductionHeadVO);
        // 删除明细
        deleteDetailByHeadId(emissionReductionHeadVO.getId());
        List<EmissionReductionDetail> lstDetails = emissionReductionHeadVO.getDetails().stream().map(EmissionReductionTableDataConverter::convert).collect(Collectors.toList());

        addDetails(emissionReductionHeadVO.getId(), lstDetails);
    }

    /**
     * 提交厌氧废水处理信息
     *
     * @param idVersionVO
     */
    public void submit(IdVersionVO idVersionVO) {
        // 验证逻辑---------
        checkExist(idVersionVO.getId(), "厌氧废水处理信息主表id不能为空");
        checkExist(idVersionVO.getLastUpdateVersion(), "厌氧废水处理信息主表版本号不能为空");

        EmissionReductionHead existedRecord = emissionReductionHeadMapper.selectByPrimaryKey(idVersionVO.getId());
        Optional.ofNullable(existedRecord).orElseThrow(() -> new ServiceException("指定的厌氧废水处理信息记录不存在"));
        // 验证逻辑---------

        EmissionReductionHeadExample example = new EmissionReductionHeadExample();
        example.or().andIdEqualTo(idVersionVO.getId()).andLastUpdateVersionEqualTo(idVersionVO.getLastUpdateVersion());

        EmissionReductionHead record = new EmissionReductionHead();
        //record.setApproveStatus(ApproveStatus.SUBMIT.getCode());
        record.setLastUpdateVersion(idVersionVO.getLastUpdateVersion());

        int count = emissionReductionHeadMapper.updateByExampleSelective(record, example);
        if (count == 0) {
            throw new ServiceException("厌氧废水处理信息已经被修改，请刷新后重试");
        }
        // todo 增加流程审批记录
    }

    private void addDetails(String headId, List<EmissionReductionDetail> emissionReductionDetails) {
        if (CollectionUtils.isEmpty(emissionReductionDetails)) {
            throw new ServiceException("厌氧废水处理信息明细不能为空");
        }
        int seq = 0;
        EmissionReductionDetail lastDetail = emissionReductionDetails.get(0);
        for (EmissionReductionDetail emissionReductionDetail : emissionReductionDetails) {
            /*
            if (StringUtils.isNotBlank(emissionReductionDetail.getCategory()) && !StringUtils.equals(emissionReductionDetail.getCategory(), lastDetail.getCategory())) {
                lastDetail = emissionReductionDetail;
            }
            if (StringUtils.isBlank(emissionReductionDetail.getCategory())) {
                emissionReductionDetail.setCategory(lastDetail.getCategory());
            }
             */
            emissionReductionDetail.setId(UUID.randomUUID().toString());
            emissionReductionDetail.setHeadId(headId);
            emissionReductionDetail.setSeq(seq++);
            emissionReductionDetailMapper.insertSelective(emissionReductionDetail);
        }
    }

    private void deleteDetailByHeadId(String headId) {
        EmissionReductionDetailExample example = new EmissionReductionDetailExample();
        example.or().andHeadIdEqualTo(headId);
        emissionReductionDetailMapper.deleteByExample(example);
    }

    private void updateHead(EmissionReductionHeadVO emissionReductionHeadVO) {
        checkExist(emissionReductionHeadVO.getId(), "厌氧废水处理信息主表id不能为空");
        checkExist(emissionReductionHeadVO.getOrganizationId(), "组织编号不能为空");
        checkExist(emissionReductionHeadVO.getLastUpdateVersion(), "版本号不能为空");

        EmissionReductionHeadExample headExample = new EmissionReductionHeadExample();
        headExample.or().andIdEqualTo(emissionReductionHeadVO.getId()).andLastUpdateVersionEqualTo(emissionReductionHeadVO.getLastUpdateVersion());

        EmissionReductionHead emissionReductionHead = EmissionReductionHeadConverter.convert(emissionReductionHeadVO);
        int updateCount = emissionReductionHeadMapper.updateByExampleSelective(emissionReductionHead, headExample);
        if (updateCount == 0) {
            throw new ServiceException("厌氧废水处理信息主表信息已经被修改，请刷新后重试");
        }
    }

    /**
     * proxy for mapper method
     *
     * @param id 厌氧废水处理信息主表id
     * @return
     */
    public EmissionReductionHead selectByPrimaryKey(String id) {
        return emissionReductionHeadMapper.selectByPrimaryKey(id);
    }

    public List<EmissionReductionHead> selectByExample(EmissionReductionHeadExample example) {
        return emissionReductionHeadMapper.selectByExample(example);
    }

}


