package com.csci.susdev.annotation;

import com.csci.susdev.constant.DatasourceContextEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Generated Comments
 * <p>
 *
 * <AUTHOR> terrytao
 * @date : 10/17/2019
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface DS {
    DatasourceContextEnum value() default DatasourceContextEnum.SUSDEV;
}
