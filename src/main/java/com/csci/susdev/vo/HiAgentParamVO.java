package com.csci.susdev.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

@Data
@Schema(description = "HiAgent参数对象")
public class HiAgentParamVO {

    @Schema(description = "1=esg-AI助手SQL-api;2=trip-ocr;3=energy-ocr", example = "1")
    private int app = 1;

    @Schema(description = "hiagent api的参数" , example = "{\"Query\": \"中建香港有多少人\",\"AppConversationID\": \"co6deaa1hp0kieia13bg\",\"ResponseMode\": \"streaming\",\"MessageID\": \"01HTH35SQHX8WGS006NTWVBSN7\"}")
    Map<String, Object> data;

}
