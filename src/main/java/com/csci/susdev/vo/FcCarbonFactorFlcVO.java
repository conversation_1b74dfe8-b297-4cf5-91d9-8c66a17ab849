package com.csci.susdev.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class FcCarbonFactorFlcVO {

    private String id;

    private String chineseName;

    private BigDecimal upstreamEmission;

    private BigDecimal downstreamEmission;

    private String emissionUnit;

    private String emissionStage;

    private String uncertainty;

    private String caption;

    private String dataTime;

    private String description;

    private BigDecimal carbonFactor;

    private String carbonFactorUnit;

    private String datasource;

    private Integer lastUpdateVersion;

    private Integer recordYear;

}
