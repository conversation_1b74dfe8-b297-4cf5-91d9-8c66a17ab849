package com.csci.susdev.vo;

import lombok.Data;

import java.util.List;

@Data
public class UserBasicVO {
	
	private String id;

    private String name;
    
    private int gender;
    
    private String domainPrefix;

    private String username;
    
    private String password;
    
    private String email;

    private String mobile;

    private String staffNo;

    private String companyName;

    private String birthDate;

    private String description;

    private Integer lastUpdateVersion;

    private Boolean isAdAccount;

    private Boolean isEnabled;

    private Boolean isDeleted;

    private Boolean isReadonly;

    private List<RoleBasicVO> lstRoleVO;

    private List<OrganizationBasicVO> lstOrganization;
}
