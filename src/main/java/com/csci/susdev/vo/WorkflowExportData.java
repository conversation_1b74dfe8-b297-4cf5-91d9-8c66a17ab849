package com.csci.susdev.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class WorkflowExportData {

    @ExcelProperty("编号")
    private String id;

    @ExcelProperty("單位")
    private String organizationName;

    @ExcelProperty("年份")
    private String year;

    @ExcelProperty("月份")
    private String month;

    @ExcelProperty("表單名稱")
    private String formName;

    @ExcelProperty("提交人")
    private String representativeRealName;

    @ExcelProperty("審核人一")
    private String workflowUserRealName1;

    @ExcelProperty("審核人二")
    private String workflowUserRealName2;

    @ExcelProperty("審核人三")
    private String workflowUserRealName3;

    @ExcelProperty("審核人四")
    private String workflowUserRealName4;

}
