package com.csci.susdev.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "数据字典VO对象")
public class BDictVO {

    @Schema(description = "ID")
    private String id;
    @Schema(description = "编码")
    private String code;
    @Schema(description = "名称")
    private String name;
    @Schema(description = "值")
    private String value;
    @Schema(description = "字典类型")
    private String dictType;
    @Schema(description = "备注")
    private String remarks;
    @Schema(description = "排序")
    private Integer seq;

    @Schema(description = "最后一次更新版本号")
    private Integer lastUpdateVersion;

}
