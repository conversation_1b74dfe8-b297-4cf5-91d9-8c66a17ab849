package com.csci.susdev.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ExcelAccountingManagementVO {
    

    @ExcelProperty("物料名称")
    private String chineseName;
    @ExcelProperty("用量")
    private BigDecimal countOne;
    @ExcelProperty("计算符号")
    private String computeSymbol;
    @ExcelProperty("因子数据")
    private BigDecimal countTwo;
    @ExcelProperty("计算结果")
    private BigDecimal calculationResult;;



    

}
