package com.csci.susdev.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "协议配置VO对象")
public class ProtocolConfigurationVO {

    @Schema(description = "ID")
    private String id;
    @Schema(description = "表单类型")
    private String formType;

    //协议管理
    @Schema(description = "协议ID")
    private String protocolId;
    @Schema(description = "协议名称")
    private String protocolName;
    @Schema(description = "协议名称（简体）")
    private String protocolNameSc;
    @Schema(description = "协议名称（英文）")
    private String protocolNameEn;
    @Schema(description = "大类ID")
    private String categoryId;
    @Schema(description = "大类名称")
    private String categoryName;
    @Schema(description = "大类名称（简体）")
    private String categoryNameSc;
    @Schema(description = "大类名称（英文）")
    private String categoryNameEn;
    @Schema(description = "小类ID")
    private String subCategoryId;
    @Schema(description = "小类名称（繁体）")
    private String subCategoryName;
    @Schema(description = "小类名称（简体）")
    private String subCategoryNameSc;
    @Schema(description = "小类名称（英文）")
    private String subCategoryNameEn;


    // 表单明细
    @Schema(description = "表单明细ID")
    private String formDetailId;
    @Schema(description = "表单编码")
    private String formCode;
    @Schema(description = "表单名称")
    private String formName;
    @Schema(description = "年份")
    private Integer year;
    @Schema(description = "")
    private String typeA;
    @Schema(description = "")
    private String typeB;
    @Schema(description = "")
    private String typeC;
    @Schema(description = "")
    private String typeD;
    @Schema(description = "")
    private String typeE;


    //因子管理
    @Schema(description = "因子类型")
    private String fcFactorType;
    @Schema(description = "因子ID")
    private String fcFactorId;
    @Schema(description = "因子对象")
    private Object fcFactorVO;


    //是否有效
    @Schema(description = "是否有效")
    private Boolean isActive;
    @Schema(description = "最后更新版本")
    private Integer lastUpdateVersion;

}
