package com.csci.susdev.vo;

import lombok.Data;

@Data
public class FactorSelectionVO {

    private String id;

    //因子範圍

    private String factorScopeId;

    private String protocolDetailId;

    private String carbonEmissionLocationId;

    private String carbonEmissionLocationName;

    private String carbonEmissionLocationNameSc;

    private String carbonEmissionLocationNameEn;

    private String protocolId;

    private String protocolName;

    private String protocolNameSc;

    private String protocolNameEn;

    private String categoryId;

    private String categoryName;

    private String categoryNameSc;

    private String categoryNameEn;

    private String subCategoryId;

    private String subCategoryName;

    private String subCategoryNameSc;

    private String subCategoryNameEn;

    private String formDetailId;

    private String formCode;

    private String formName;

    private Integer year;

    private String typeA;

    private String typeB;

    private String typeC;

    private String typeD;

    private String typeE;

    //private String factorType;

    //因子管理

    private String fcCarbonFactorType;

    private String fcCarbonFactorDatasource;

    private String fcCarbonFactorId;

    private Object fcCarbonFactorVO;

    //組織

    private String organizationId;

    private String organizationNo;

    private String organizationCode;

    private String organizationName;

    //繼承關系
    private String inheritedFrom;
    private String inheritedFromId;

    //是否有效

    private Boolean isActive;

    private Integer lastUpdateVersion;

}
