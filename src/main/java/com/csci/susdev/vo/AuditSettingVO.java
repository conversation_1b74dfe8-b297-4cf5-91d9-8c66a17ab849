package com.csci.susdev.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "審核設置")
public class AuditSettingVO {
	
    @Schema(description = "組織id")
	private String organizationId;

    @Schema(description = "表單編號")
	private String formCode;

    @Schema(description = "審核人id 1")
    private String userId1;

    @Schema(description = "審核人id 2")
    private String userId2;

    @Schema(description = "審核人id 3")
    private String userId3;

    @Schema(description = "審核人id 4")
    private String userId4;
}
