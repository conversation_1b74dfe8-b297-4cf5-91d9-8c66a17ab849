package com.csci.susdev.vo;

import com.csci.susdev.model.TzhBsMenu;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema
@Data
public class TzhBsOrgMenuVO {
    private String id;

    @Schema(description="机构")
    private String organizationName;

    /**
    * 协议id
    */
    @Schema(description="协议id")
    private String protocolName;

    /**
    * 模块
    */
    @Schema(description="模块")
    private String module;

    /**
    * 菜单id
    */
    @Schema(description="菜单")
    private String titleEn;

    /**
    * 备注
    */
    @Schema(description="备注")
    private String remark;

    /**
    * 排序
    */
    @Schema(description="排序")
    private Integer seq;

    /**
    * 逻辑删除，1为删除
    */
    @Schema(description="逻辑删除，1为删除")
    private Boolean isDeleted;
    @Schema(description="菜单id")
    private String menuId;
    @Schema(description="菜单")
    private TzhBsMenu menu;
    @Schema(description="机构id")
    private String organizationId;
    @Schema(description="协议id")
    private String protocolId;

    public String getProtocolId() {
        return protocolId;
    }

    public void setProtocolId(String protocolId) {
        this.protocolId = protocolId;
    }

    public String getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    public String getMenuId() {
        return menuId;
    }

    public void setMenuId(String menuId) {
        this.menuId = menuId;
    }
}
