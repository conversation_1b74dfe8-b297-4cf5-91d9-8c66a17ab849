package com.csci.susdev.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "产品管理-核算管理VO对象")
public class AccountingManagementVO {

    @Schema(description = "ID")
    private String id;

    @Schema(description = "协议配置ID")
    private String protocolConfigurationId;
    @Schema(description = "物料名称")
    private String chineseName;
    @Schema(description = "算数一（用量）")
    private BigDecimal countOne;
    @Schema(description = "算数二（因子数据）")
    private BigDecimal countTwo;
    @Schema(description = "计算符号")
    private String computeSymbol;
    @Schema(description = "计算结果")
    private BigDecimal calculationResult;

    @Schema(description = "最后一次更新版本号")
    private Integer lastUpdateVersion;

}
