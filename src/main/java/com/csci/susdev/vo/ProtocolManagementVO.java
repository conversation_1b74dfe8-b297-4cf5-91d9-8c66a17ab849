package com.csci.susdev.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "协议管理VO对象")
public class ProtocolManagementVO {

    @Schema(description = "协议ID")
    private String protocolId;
    @Schema(description = "协议名称（繁体）")
    private String protocolName;
    @Schema(description = "协议名称（简体）")
    private String protocolNameSc;
    @Schema(description = "协议名称（英文）")
    private String protocolNameEn;
    @Schema(description = "协议描述")
    private String protocolDescription;
    @Schema(description = "协议最后一次版本号")
    private Integer protocolLastUpdateVersion;


    @Schema(description = "大类ID")
    private String categoryId;
    @Schema(description = "大类名称（繁体）")
    private String categoryName;
    @Schema(description = "大类名称（简体）")
    private String categoryNameSc;
    @Schema(description = "大类名称（英文）")
    private String categoryNameEn;
    @Schema(description = "大类最后一次版本号")
    private Integer categoryLastUpdateVersion;



    @Schema(description = "小类ID")
    private String subCategoryId;
    @Schema(description = "小类名称（繁体）")
    private String subCategoryName;
    @Schema(description = "小类名称（简体）")
    private String subCategoryNameSc;
    @Schema(description = "小类名称（英文）")
    private String subCategoryNameEn;
    @Schema(description = "小类最后一次版本号")
    private Integer subCategoryLastUpdateVersion;

}
