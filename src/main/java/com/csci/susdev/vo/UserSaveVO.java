package com.csci.susdev.vo;

import lombok.Data;

import java.util.List;

@Data
public class UserSaveVO {
	
	private String id;

    private String name;
    
    private int gender;
    
    private String domainPrefix;

    private String username;
    
    private String password;
    
    private String email;
    
    private String mobile;
    
    private String staffNo;
    
    private String companyName;
    
    private String birthDate;

    private String description;

    private Boolean isEnabled;

    private Boolean isDeleted;

    private Boolean isAdAccount;

    private Integer lastUpdateVersion;

    private List<String> lstRoleId;

    private List<String> lstOrganizationId;

}
