package com.csci.susdev.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class SocialPerformanceHeadVO {

    private String id;

    private String organizationId;

    private Integer year;

    private Integer month;

    private LocalDateTime submitDate;

    private Integer approveStatus;

    private String approveStatusName;

    private Integer lastUpdateVersion;

    private List<SocialPerfOneTableVO> details;

    private Integer workflowControlState;

    private String workflowControlStateName;
}
