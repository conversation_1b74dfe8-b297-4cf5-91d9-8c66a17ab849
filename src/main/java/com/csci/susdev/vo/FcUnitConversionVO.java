package com.csci.susdev.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "产品管理-单位换算VO对象")
public class FcUnitConversionVO {

    @Schema(description = "ID")
    private String id;
    @Schema(description = "单位组")
    private String unitGroup;
    @Schema(description = "单位集")
    private String unitCollect;
    @Schema(description = "单位名称")
    private String unit;
    @Schema(description = "单位符号")
    private String unitSymbol;


    @Schema(description = "最后一次更新版本号")
    private Integer lastUpdateVersion;

}
