package com.csci.susdev.vo;

import com.csci.susdev.model.Menu;
import com.csci.susdev.model.Permission;
import lombok.Data;

import java.util.List;

/**
 * 菜单实体
 * <AUTHOR>
 * @since 2024-03-05
 */
@Data
public class MenuExtVO extends Menu {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.id
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.parent_id
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private String parentId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.path
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private String path;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.description
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private String description;
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.route_icon
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private String routeIcon;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.route_title
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private String routeTitle;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.route_path
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private String routePath;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.route_redirect
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private String routeRedirect;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.route_component
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private String routeComponent;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.seq
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private Integer seq;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.is_active
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private Boolean isActive;
    /**
     * 菜单展示描述
     */
    private String showDesc;

    /**
     * 父菜单
     */
    private String parentDescription;
}
