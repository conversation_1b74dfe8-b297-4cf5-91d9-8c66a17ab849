package com.csci.susdev.model;

import java.time.LocalDateTime;

public class FactorSelection {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_selection.id
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_selection.factor_scope_id
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    private String factorScopeId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_selection.fc_carbon_factor_type
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    private String fcCarbonFactorType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_selection.fc_carbon_factor_datasource
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    private String fcCarbonFactorDatasource;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_selection.fc_carbon_factor_id
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    private String fcCarbonFactorId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_selection.organization_id
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    private String organizationId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_selection.is_active
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    private Boolean isActive;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_selection.creation_time
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_selection.create_username
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_selection.create_user_id
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_selection.last_update_time
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_selection.last_update_username
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_selection.last_update_user_id
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_selection.last_update_version
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    private Integer lastUpdateVersion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_factor_selection.is_deleted
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    private Boolean isDeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_selection.id
     *
     * @return the value of t_factor_selection.id
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_selection.id
     *
     * @param id the value for t_factor_selection.id
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_selection.factor_scope_id
     *
     * @return the value of t_factor_selection.factor_scope_id
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public String getFactorScopeId() {
        return factorScopeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_selection.factor_scope_id
     *
     * @param factorScopeId the value for t_factor_selection.factor_scope_id
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public void setFactorScopeId(String factorScopeId) {
        this.factorScopeId = factorScopeId == null ? null : factorScopeId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_selection.fc_carbon_factor_type
     *
     * @return the value of t_factor_selection.fc_carbon_factor_type
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public String getFcCarbonFactorType() {
        return fcCarbonFactorType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_selection.fc_carbon_factor_type
     *
     * @param fcCarbonFactorType the value for t_factor_selection.fc_carbon_factor_type
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public void setFcCarbonFactorType(String fcCarbonFactorType) {
        this.fcCarbonFactorType = fcCarbonFactorType == null ? null : fcCarbonFactorType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_selection.fc_carbon_factor_datasource
     *
     * @return the value of t_factor_selection.fc_carbon_factor_datasource
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public String getFcCarbonFactorDatasource() {
        return fcCarbonFactorDatasource;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_selection.fc_carbon_factor_datasource
     *
     * @param fcCarbonFactorDatasource the value for t_factor_selection.fc_carbon_factor_datasource
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public void setFcCarbonFactorDatasource(String fcCarbonFactorDatasource) {
        this.fcCarbonFactorDatasource = fcCarbonFactorDatasource == null ? null : fcCarbonFactorDatasource.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_selection.fc_carbon_factor_id
     *
     * @return the value of t_factor_selection.fc_carbon_factor_id
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public String getFcCarbonFactorId() {
        return fcCarbonFactorId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_selection.fc_carbon_factor_id
     *
     * @param fcCarbonFactorId the value for t_factor_selection.fc_carbon_factor_id
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public void setFcCarbonFactorId(String fcCarbonFactorId) {
        this.fcCarbonFactorId = fcCarbonFactorId == null ? null : fcCarbonFactorId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_selection.organization_id
     *
     * @return the value of t_factor_selection.organization_id
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public String getOrganizationId() {
        return organizationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_selection.organization_id
     *
     * @param organizationId the value for t_factor_selection.organization_id
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId == null ? null : organizationId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_selection.is_active
     *
     * @return the value of t_factor_selection.is_active
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public Boolean getIsActive() {
        return isActive;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_selection.is_active
     *
     * @param isActive the value for t_factor_selection.is_active
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_selection.creation_time
     *
     * @return the value of t_factor_selection.creation_time
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_selection.creation_time
     *
     * @param creationTime the value for t_factor_selection.creation_time
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_selection.create_username
     *
     * @return the value of t_factor_selection.create_username
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_selection.create_username
     *
     * @param createUsername the value for t_factor_selection.create_username
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_selection.create_user_id
     *
     * @return the value of t_factor_selection.create_user_id
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_selection.create_user_id
     *
     * @param createUserId the value for t_factor_selection.create_user_id
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_selection.last_update_time
     *
     * @return the value of t_factor_selection.last_update_time
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_selection.last_update_time
     *
     * @param lastUpdateTime the value for t_factor_selection.last_update_time
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_selection.last_update_username
     *
     * @return the value of t_factor_selection.last_update_username
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_selection.last_update_username
     *
     * @param lastUpdateUsername the value for t_factor_selection.last_update_username
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_selection.last_update_user_id
     *
     * @return the value of t_factor_selection.last_update_user_id
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_selection.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_factor_selection.last_update_user_id
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_selection.last_update_version
     *
     * @return the value of t_factor_selection.last_update_version
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_selection.last_update_version
     *
     * @param lastUpdateVersion the value for t_factor_selection.last_update_version
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_factor_selection.is_deleted
     *
     * @return the value of t_factor_selection.is_deleted
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_factor_selection.is_deleted
     *
     * @param isDeleted the value for t_factor_selection.is_deleted
     *
     * @mbg.generated Fri Apr 12 11:57:03 HKT 2024
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }
}