package com.csci.susdev.model;

public class TzhBsUserSite {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Bs_UserSite.Id
     *
     * @mbg.generated Tue Jun 04 12:09:07 HKT 2024
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Bs_UserSite.UserName
     *
     * @mbg.generated Tue Jun 04 12:09:07 HKT 2024
     */
    private String username;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Bs_UserSite.SiteId
     *
     * @mbg.generated Tue Jun 04 12:09:07 HKT 2024
     */
    private String siteid;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Bs_UserSite.Id
     *
     * @return the value of Tzh_Bs_UserSite.Id
     *
     * @mbg.generated Tue Jun 04 12:09:07 HKT 2024
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Bs_UserSite.Id
     *
     * @param id the value for Tzh_Bs_UserSite.Id
     *
     * @mbg.generated Tue Jun 04 12:09:07 HKT 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Bs_UserSite.UserName
     *
     * @return the value of Tzh_Bs_UserSite.UserName
     *
     * @mbg.generated Tue Jun 04 12:09:07 HKT 2024
     */
    public String getUsername() {
        return username;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Bs_UserSite.UserName
     *
     * @param username the value for Tzh_Bs_UserSite.UserName
     *
     * @mbg.generated Tue Jun 04 12:09:07 HKT 2024
     */
    public void setUsername(String username) {
        this.username = username == null ? null : username.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Bs_UserSite.SiteId
     *
     * @return the value of Tzh_Bs_UserSite.SiteId
     *
     * @mbg.generated Tue Jun 04 12:09:07 HKT 2024
     */
    public String getSiteid() {
        return siteid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Bs_UserSite.SiteId
     *
     * @param siteid the value for Tzh_Bs_UserSite.SiteId
     *
     * @mbg.generated Tue Jun 04 12:09:07 HKT 2024
     */
    public void setSiteid(String siteid) {
        this.siteid = siteid == null ? null : siteid.trim();
    }
}