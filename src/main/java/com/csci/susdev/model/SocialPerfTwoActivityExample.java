package com.csci.susdev.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class SocialPerfTwoActivityExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public SocialPerfTwoActivityExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(String value) {
			addCriterion("id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(String value) {
			addCriterion("id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(String value) {
			addCriterion("id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(String value) {
			addCriterion("id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(String value) {
			addCriterion("id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(String value) {
			addCriterion("id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLike(String value) {
			addCriterion("id like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotLike(String value) {
			addCriterion("id not like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<String> values) {
			addCriterion("id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<String> values) {
			addCriterion("id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(String value1, String value2) {
			addCriterion("id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(String value1, String value2) {
			addCriterion("id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andHeadIdIsNull() {
			addCriterion("head_id is null");
			return (Criteria) this;
		}

		public Criteria andHeadIdIsNotNull() {
			addCriterion("head_id is not null");
			return (Criteria) this;
		}

		public Criteria andHeadIdEqualTo(String value) {
			addCriterion("head_id =", value, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdNotEqualTo(String value) {
			addCriterion("head_id <>", value, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdGreaterThan(String value) {
			addCriterion("head_id >", value, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdGreaterThanOrEqualTo(String value) {
			addCriterion("head_id >=", value, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdLessThan(String value) {
			addCriterion("head_id <", value, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdLessThanOrEqualTo(String value) {
			addCriterion("head_id <=", value, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdLike(String value) {
			addCriterion("head_id like", value, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdNotLike(String value) {
			addCriterion("head_id not like", value, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdIn(List<String> values) {
			addCriterion("head_id in", values, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdNotIn(List<String> values) {
			addCriterion("head_id not in", values, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdBetween(String value1, String value2) {
			addCriterion("head_id between", value1, value2, "headId");
			return (Criteria) this;
		}

		public Criteria andHeadIdNotBetween(String value1, String value2) {
			addCriterion("head_id not between", value1, value2, "headId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdIsNull() {
			addCriterion("organization_id is null");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdIsNotNull() {
			addCriterion("organization_id is not null");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdEqualTo(String value) {
			addCriterion("organization_id =", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdNotEqualTo(String value) {
			addCriterion("organization_id <>", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdGreaterThan(String value) {
			addCriterion("organization_id >", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdGreaterThanOrEqualTo(String value) {
			addCriterion("organization_id >=", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdLessThan(String value) {
			addCriterion("organization_id <", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdLessThanOrEqualTo(String value) {
			addCriterion("organization_id <=", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdLike(String value) {
			addCriterion("organization_id like", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdNotLike(String value) {
			addCriterion("organization_id not like", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdIn(List<String> values) {
			addCriterion("organization_id in", values, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdNotIn(List<String> values) {
			addCriterion("organization_id not in", values, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdBetween(String value1, String value2) {
			addCriterion("organization_id between", value1, value2, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdNotBetween(String value1, String value2) {
			addCriterion("organization_id not between", value1, value2, "organizationId");
			return (Criteria) this;
		}

		public Criteria andTypeIsNull() {
			addCriterion("type is null");
			return (Criteria) this;
		}

		public Criteria andTypeIsNotNull() {
			addCriterion("type is not null");
			return (Criteria) this;
		}

		public Criteria andTypeEqualTo(String value) {
			addCriterion("type =", value, "type");
			return (Criteria) this;
		}

		public Criteria andTypeNotEqualTo(String value) {
			addCriterion("type <>", value, "type");
			return (Criteria) this;
		}

		public Criteria andTypeGreaterThan(String value) {
			addCriterion("type >", value, "type");
			return (Criteria) this;
		}

		public Criteria andTypeGreaterThanOrEqualTo(String value) {
			addCriterion("type >=", value, "type");
			return (Criteria) this;
		}

		public Criteria andTypeLessThan(String value) {
			addCriterion("type <", value, "type");
			return (Criteria) this;
		}

		public Criteria andTypeLessThanOrEqualTo(String value) {
			addCriterion("type <=", value, "type");
			return (Criteria) this;
		}

		public Criteria andTypeLike(String value) {
			addCriterion("type like", value, "type");
			return (Criteria) this;
		}

		public Criteria andTypeNotLike(String value) {
			addCriterion("type not like", value, "type");
			return (Criteria) this;
		}

		public Criteria andTypeIn(List<String> values) {
			addCriterion("type in", values, "type");
			return (Criteria) this;
		}

		public Criteria andTypeNotIn(List<String> values) {
			addCriterion("type not in", values, "type");
			return (Criteria) this;
		}

		public Criteria andTypeBetween(String value1, String value2) {
			addCriterion("type between", value1, value2, "type");
			return (Criteria) this;
		}

		public Criteria andTypeNotBetween(String value1, String value2) {
			addCriterion("type not between", value1, value2, "type");
			return (Criteria) this;
		}

		public Criteria andNameIsNull() {
			addCriterion("name is null");
			return (Criteria) this;
		}

		public Criteria andNameIsNotNull() {
			addCriterion("name is not null");
			return (Criteria) this;
		}

		public Criteria andNameEqualTo(String value) {
			addCriterion("name =", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameNotEqualTo(String value) {
			addCriterion("name <>", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameGreaterThan(String value) {
			addCriterion("name >", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameGreaterThanOrEqualTo(String value) {
			addCriterion("name >=", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameLessThan(String value) {
			addCriterion("name <", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameLessThanOrEqualTo(String value) {
			addCriterion("name <=", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameLike(String value) {
			addCriterion("name like", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameNotLike(String value) {
			addCriterion("name not like", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameIn(List<String> values) {
			addCriterion("name in", values, "name");
			return (Criteria) this;
		}

		public Criteria andNameNotIn(List<String> values) {
			addCriterion("name not in", values, "name");
			return (Criteria) this;
		}

		public Criteria andNameBetween(String value1, String value2) {
			addCriterion("name between", value1, value2, "name");
			return (Criteria) this;
		}

		public Criteria andNameNotBetween(String value1, String value2) {
			addCriterion("name not between", value1, value2, "name");
			return (Criteria) this;
		}

		public Criteria andDateIsNull() {
			addCriterion("date is null");
			return (Criteria) this;
		}

		public Criteria andDateIsNotNull() {
			addCriterion("date is not null");
			return (Criteria) this;
		}

		public Criteria andDateEqualTo(LocalDateTime value) {
			addCriterion("date =", value, "date");
			return (Criteria) this;
		}

		public Criteria andDateNotEqualTo(LocalDateTime value) {
			addCriterion("date <>", value, "date");
			return (Criteria) this;
		}

		public Criteria andDateGreaterThan(LocalDateTime value) {
			addCriterion("date >", value, "date");
			return (Criteria) this;
		}

		public Criteria andDateGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("date >=", value, "date");
			return (Criteria) this;
		}

		public Criteria andDateLessThan(LocalDateTime value) {
			addCriterion("date <", value, "date");
			return (Criteria) this;
		}

		public Criteria andDateLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("date <=", value, "date");
			return (Criteria) this;
		}

		public Criteria andDateIn(List<LocalDateTime> values) {
			addCriterion("date in", values, "date");
			return (Criteria) this;
		}

		public Criteria andDateNotIn(List<LocalDateTime> values) {
			addCriterion("date not in", values, "date");
			return (Criteria) this;
		}

		public Criteria andDateBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("date between", value1, value2, "date");
			return (Criteria) this;
		}

		public Criteria andDateNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("date not between", value1, value2, "date");
			return (Criteria) this;
		}

		public Criteria andStaffCountIsNull() {
			addCriterion("staff_count is null");
			return (Criteria) this;
		}

		public Criteria andStaffCountIsNotNull() {
			addCriterion("staff_count is not null");
			return (Criteria) this;
		}

		public Criteria andStaffCountEqualTo(String value) {
			addCriterion("staff_count =", value, "staffCount");
			return (Criteria) this;
		}

		public Criteria andStaffCountNotEqualTo(String value) {
			addCriterion("staff_count <>", value, "staffCount");
			return (Criteria) this;
		}

		public Criteria andStaffCountGreaterThan(String value) {
			addCriterion("staff_count >", value, "staffCount");
			return (Criteria) this;
		}

		public Criteria andStaffCountGreaterThanOrEqualTo(String value) {
			addCriterion("staff_count >=", value, "staffCount");
			return (Criteria) this;
		}

		public Criteria andStaffCountLessThan(String value) {
			addCriterion("staff_count <", value, "staffCount");
			return (Criteria) this;
		}

		public Criteria andStaffCountLessThanOrEqualTo(String value) {
			addCriterion("staff_count <=", value, "staffCount");
			return (Criteria) this;
		}

		public Criteria andStaffCountLike(String value) {
			addCriterion("staff_count like", value, "staffCount");
			return (Criteria) this;
		}

		public Criteria andStaffCountNotLike(String value) {
			addCriterion("staff_count not like", value, "staffCount");
			return (Criteria) this;
		}

		public Criteria andStaffCountIn(List<String> values) {
			addCriterion("staff_count in", values, "staffCount");
			return (Criteria) this;
		}

		public Criteria andStaffCountNotIn(List<String> values) {
			addCriterion("staff_count not in", values, "staffCount");
			return (Criteria) this;
		}

		public Criteria andStaffCountBetween(String value1, String value2) {
			addCriterion("staff_count between", value1, value2, "staffCount");
			return (Criteria) this;
		}

		public Criteria andStaffCountNotBetween(String value1, String value2) {
			addCriterion("staff_count not between", value1, value2, "staffCount");
			return (Criteria) this;
		}

		public Criteria andHourCountIsNull() {
			addCriterion("hour_count is null");
			return (Criteria) this;
		}

		public Criteria andHourCountIsNotNull() {
			addCriterion("hour_count is not null");
			return (Criteria) this;
		}

		public Criteria andHourCountEqualTo(String value) {
			addCriterion("hour_count =", value, "hourCount");
			return (Criteria) this;
		}

		public Criteria andHourCountNotEqualTo(String value) {
			addCriterion("hour_count <>", value, "hourCount");
			return (Criteria) this;
		}

		public Criteria andHourCountGreaterThan(String value) {
			addCriterion("hour_count >", value, "hourCount");
			return (Criteria) this;
		}

		public Criteria andHourCountGreaterThanOrEqualTo(String value) {
			addCriterion("hour_count >=", value, "hourCount");
			return (Criteria) this;
		}

		public Criteria andHourCountLessThan(String value) {
			addCriterion("hour_count <", value, "hourCount");
			return (Criteria) this;
		}

		public Criteria andHourCountLessThanOrEqualTo(String value) {
			addCriterion("hour_count <=", value, "hourCount");
			return (Criteria) this;
		}

		public Criteria andHourCountLike(String value) {
			addCriterion("hour_count like", value, "hourCount");
			return (Criteria) this;
		}

		public Criteria andHourCountNotLike(String value) {
			addCriterion("hour_count not like", value, "hourCount");
			return (Criteria) this;
		}

		public Criteria andHourCountIn(List<String> values) {
			addCriterion("hour_count in", values, "hourCount");
			return (Criteria) this;
		}

		public Criteria andHourCountNotIn(List<String> values) {
			addCriterion("hour_count not in", values, "hourCount");
			return (Criteria) this;
		}

		public Criteria andHourCountBetween(String value1, String value2) {
			addCriterion("hour_count between", value1, value2, "hourCount");
			return (Criteria) this;
		}

		public Criteria andHourCountNotBetween(String value1, String value2) {
			addCriterion("hour_count not between", value1, value2, "hourCount");
			return (Criteria) this;
		}

		public Criteria andServiceTargetIsNull() {
			addCriterion("service_target is null");
			return (Criteria) this;
		}

		public Criteria andServiceTargetIsNotNull() {
			addCriterion("service_target is not null");
			return (Criteria) this;
		}

		public Criteria andServiceTargetEqualTo(String value) {
			addCriterion("service_target =", value, "serviceTarget");
			return (Criteria) this;
		}

		public Criteria andServiceTargetNotEqualTo(String value) {
			addCriterion("service_target <>", value, "serviceTarget");
			return (Criteria) this;
		}

		public Criteria andServiceTargetGreaterThan(String value) {
			addCriterion("service_target >", value, "serviceTarget");
			return (Criteria) this;
		}

		public Criteria andServiceTargetGreaterThanOrEqualTo(String value) {
			addCriterion("service_target >=", value, "serviceTarget");
			return (Criteria) this;
		}

		public Criteria andServiceTargetLessThan(String value) {
			addCriterion("service_target <", value, "serviceTarget");
			return (Criteria) this;
		}

		public Criteria andServiceTargetLessThanOrEqualTo(String value) {
			addCriterion("service_target <=", value, "serviceTarget");
			return (Criteria) this;
		}

		public Criteria andServiceTargetLike(String value) {
			addCriterion("service_target like", value, "serviceTarget");
			return (Criteria) this;
		}

		public Criteria andServiceTargetNotLike(String value) {
			addCriterion("service_target not like", value, "serviceTarget");
			return (Criteria) this;
		}

		public Criteria andServiceTargetIn(List<String> values) {
			addCriterion("service_target in", values, "serviceTarget");
			return (Criteria) this;
		}

		public Criteria andServiceTargetNotIn(List<String> values) {
			addCriterion("service_target not in", values, "serviceTarget");
			return (Criteria) this;
		}

		public Criteria andServiceTargetBetween(String value1, String value2) {
			addCriterion("service_target between", value1, value2, "serviceTarget");
			return (Criteria) this;
		}

		public Criteria andServiceTargetNotBetween(String value1, String value2) {
			addCriterion("service_target not between", value1, value2, "serviceTarget");
			return (Criteria) this;
		}

		public Criteria andBeneficiaryOrganizationIsNull() {
			addCriterion("beneficiary_organization is null");
			return (Criteria) this;
		}

		public Criteria andBeneficiaryOrganizationIsNotNull() {
			addCriterion("beneficiary_organization is not null");
			return (Criteria) this;
		}

		public Criteria andBeneficiaryOrganizationEqualTo(String value) {
			addCriterion("beneficiary_organization =", value, "beneficiaryOrganization");
			return (Criteria) this;
		}

		public Criteria andBeneficiaryOrganizationNotEqualTo(String value) {
			addCriterion("beneficiary_organization <>", value, "beneficiaryOrganization");
			return (Criteria) this;
		}

		public Criteria andBeneficiaryOrganizationGreaterThan(String value) {
			addCriterion("beneficiary_organization >", value, "beneficiaryOrganization");
			return (Criteria) this;
		}

		public Criteria andBeneficiaryOrganizationGreaterThanOrEqualTo(String value) {
			addCriterion("beneficiary_organization >=", value, "beneficiaryOrganization");
			return (Criteria) this;
		}

		public Criteria andBeneficiaryOrganizationLessThan(String value) {
			addCriterion("beneficiary_organization <", value, "beneficiaryOrganization");
			return (Criteria) this;
		}

		public Criteria andBeneficiaryOrganizationLessThanOrEqualTo(String value) {
			addCriterion("beneficiary_organization <=", value, "beneficiaryOrganization");
			return (Criteria) this;
		}

		public Criteria andBeneficiaryOrganizationLike(String value) {
			addCriterion("beneficiary_organization like", value, "beneficiaryOrganization");
			return (Criteria) this;
		}

		public Criteria andBeneficiaryOrganizationNotLike(String value) {
			addCriterion("beneficiary_organization not like", value, "beneficiaryOrganization");
			return (Criteria) this;
		}

		public Criteria andBeneficiaryOrganizationIn(List<String> values) {
			addCriterion("beneficiary_organization in", values, "beneficiaryOrganization");
			return (Criteria) this;
		}

		public Criteria andBeneficiaryOrganizationNotIn(List<String> values) {
			addCriterion("beneficiary_organization not in", values, "beneficiaryOrganization");
			return (Criteria) this;
		}

		public Criteria andBeneficiaryOrganizationBetween(String value1, String value2) {
			addCriterion("beneficiary_organization between", value1, value2, "beneficiaryOrganization");
			return (Criteria) this;
		}

		public Criteria andBeneficiaryOrganizationNotBetween(String value1, String value2) {
			addCriterion("beneficiary_organization not between", value1, value2, "beneficiaryOrganization");
			return (Criteria) this;
		}

		public Criteria andNoOfBeneficiariesIsNull() {
			addCriterion("no_of_beneficiaries is null");
			return (Criteria) this;
		}

		public Criteria andNoOfBeneficiariesIsNotNull() {
			addCriterion("no_of_beneficiaries is not null");
			return (Criteria) this;
		}

		public Criteria andNoOfBeneficiariesEqualTo(String value) {
			addCriterion("no_of_beneficiaries =", value, "noOfBeneficiaries");
			return (Criteria) this;
		}

		public Criteria andNoOfBeneficiariesNotEqualTo(String value) {
			addCriterion("no_of_beneficiaries <>", value, "noOfBeneficiaries");
			return (Criteria) this;
		}

		public Criteria andNoOfBeneficiariesGreaterThan(String value) {
			addCriterion("no_of_beneficiaries >", value, "noOfBeneficiaries");
			return (Criteria) this;
		}

		public Criteria andNoOfBeneficiariesGreaterThanOrEqualTo(String value) {
			addCriterion("no_of_beneficiaries >=", value, "noOfBeneficiaries");
			return (Criteria) this;
		}

		public Criteria andNoOfBeneficiariesLessThan(String value) {
			addCriterion("no_of_beneficiaries <", value, "noOfBeneficiaries");
			return (Criteria) this;
		}

		public Criteria andNoOfBeneficiariesLessThanOrEqualTo(String value) {
			addCriterion("no_of_beneficiaries <=", value, "noOfBeneficiaries");
			return (Criteria) this;
		}

		public Criteria andNoOfBeneficiariesLike(String value) {
			addCriterion("no_of_beneficiaries like", value, "noOfBeneficiaries");
			return (Criteria) this;
		}

		public Criteria andNoOfBeneficiariesNotLike(String value) {
			addCriterion("no_of_beneficiaries not like", value, "noOfBeneficiaries");
			return (Criteria) this;
		}

		public Criteria andNoOfBeneficiariesIn(List<String> values) {
			addCriterion("no_of_beneficiaries in", values, "noOfBeneficiaries");
			return (Criteria) this;
		}

		public Criteria andNoOfBeneficiariesNotIn(List<String> values) {
			addCriterion("no_of_beneficiaries not in", values, "noOfBeneficiaries");
			return (Criteria) this;
		}

		public Criteria andNoOfBeneficiariesBetween(String value1, String value2) {
			addCriterion("no_of_beneficiaries between", value1, value2, "noOfBeneficiaries");
			return (Criteria) this;
		}

		public Criteria andNoOfBeneficiariesNotBetween(String value1, String value2) {
			addCriterion("no_of_beneficiaries not between", value1, value2, "noOfBeneficiaries");
			return (Criteria) this;
		}

		public Criteria andTotalDonationIsNull() {
			addCriterion("total_donation is null");
			return (Criteria) this;
		}

		public Criteria andTotalDonationIsNotNull() {
			addCriterion("total_donation is not null");
			return (Criteria) this;
		}

		public Criteria andTotalDonationEqualTo(String value) {
			addCriterion("total_donation =", value, "totalDonation");
			return (Criteria) this;
		}

		public Criteria andTotalDonationNotEqualTo(String value) {
			addCriterion("total_donation <>", value, "totalDonation");
			return (Criteria) this;
		}

		public Criteria andTotalDonationGreaterThan(String value) {
			addCriterion("total_donation >", value, "totalDonation");
			return (Criteria) this;
		}

		public Criteria andTotalDonationGreaterThanOrEqualTo(String value) {
			addCriterion("total_donation >=", value, "totalDonation");
			return (Criteria) this;
		}

		public Criteria andTotalDonationLessThan(String value) {
			addCriterion("total_donation <", value, "totalDonation");
			return (Criteria) this;
		}

		public Criteria andTotalDonationLessThanOrEqualTo(String value) {
			addCriterion("total_donation <=", value, "totalDonation");
			return (Criteria) this;
		}

		public Criteria andTotalDonationLike(String value) {
			addCriterion("total_donation like", value, "totalDonation");
			return (Criteria) this;
		}

		public Criteria andTotalDonationNotLike(String value) {
			addCriterion("total_donation not like", value, "totalDonation");
			return (Criteria) this;
		}

		public Criteria andTotalDonationIn(List<String> values) {
			addCriterion("total_donation in", values, "totalDonation");
			return (Criteria) this;
		}

		public Criteria andTotalDonationNotIn(List<String> values) {
			addCriterion("total_donation not in", values, "totalDonation");
			return (Criteria) this;
		}

		public Criteria andTotalDonationBetween(String value1, String value2) {
			addCriterion("total_donation between", value1, value2, "totalDonation");
			return (Criteria) this;
		}

		public Criteria andTotalDonationNotBetween(String value1, String value2) {
			addCriterion("total_donation not between", value1, value2, "totalDonation");
			return (Criteria) this;
		}

		public Criteria andDonationTypeIsNull() {
			addCriterion("donation_type is null");
			return (Criteria) this;
		}

		public Criteria andDonationTypeIsNotNull() {
			addCriterion("donation_type is not null");
			return (Criteria) this;
		}

		public Criteria andDonationTypeEqualTo(String value) {
			addCriterion("donation_type =", value, "donationType");
			return (Criteria) this;
		}

		public Criteria andDonationTypeNotEqualTo(String value) {
			addCriterion("donation_type <>", value, "donationType");
			return (Criteria) this;
		}

		public Criteria andDonationTypeGreaterThan(String value) {
			addCriterion("donation_type >", value, "donationType");
			return (Criteria) this;
		}

		public Criteria andDonationTypeGreaterThanOrEqualTo(String value) {
			addCriterion("donation_type >=", value, "donationType");
			return (Criteria) this;
		}

		public Criteria andDonationTypeLessThan(String value) {
			addCriterion("donation_type <", value, "donationType");
			return (Criteria) this;
		}

		public Criteria andDonationTypeLessThanOrEqualTo(String value) {
			addCriterion("donation_type <=", value, "donationType");
			return (Criteria) this;
		}

		public Criteria andDonationTypeLike(String value) {
			addCriterion("donation_type like", value, "donationType");
			return (Criteria) this;
		}

		public Criteria andDonationTypeNotLike(String value) {
			addCriterion("donation_type not like", value, "donationType");
			return (Criteria) this;
		}

		public Criteria andDonationTypeIn(List<String> values) {
			addCriterion("donation_type in", values, "donationType");
			return (Criteria) this;
		}

		public Criteria andDonationTypeNotIn(List<String> values) {
			addCriterion("donation_type not in", values, "donationType");
			return (Criteria) this;
		}

		public Criteria andDonationTypeBetween(String value1, String value2) {
			addCriterion("donation_type between", value1, value2, "donationType");
			return (Criteria) this;
		}

		public Criteria andDonationTypeNotBetween(String value1, String value2) {
			addCriterion("donation_type not between", value1, value2, "donationType");
			return (Criteria) this;
		}

		public Criteria andDonationQtyIsNull() {
			addCriterion("donation_qty is null");
			return (Criteria) this;
		}

		public Criteria andDonationQtyIsNotNull() {
			addCriterion("donation_qty is not null");
			return (Criteria) this;
		}

		public Criteria andDonationQtyEqualTo(String value) {
			addCriterion("donation_qty =", value, "donationQty");
			return (Criteria) this;
		}

		public Criteria andDonationQtyNotEqualTo(String value) {
			addCriterion("donation_qty <>", value, "donationQty");
			return (Criteria) this;
		}

		public Criteria andDonationQtyGreaterThan(String value) {
			addCriterion("donation_qty >", value, "donationQty");
			return (Criteria) this;
		}

		public Criteria andDonationQtyGreaterThanOrEqualTo(String value) {
			addCriterion("donation_qty >=", value, "donationQty");
			return (Criteria) this;
		}

		public Criteria andDonationQtyLessThan(String value) {
			addCriterion("donation_qty <", value, "donationQty");
			return (Criteria) this;
		}

		public Criteria andDonationQtyLessThanOrEqualTo(String value) {
			addCriterion("donation_qty <=", value, "donationQty");
			return (Criteria) this;
		}

		public Criteria andDonationQtyLike(String value) {
			addCriterion("donation_qty like", value, "donationQty");
			return (Criteria) this;
		}

		public Criteria andDonationQtyNotLike(String value) {
			addCriterion("donation_qty not like", value, "donationQty");
			return (Criteria) this;
		}

		public Criteria andDonationQtyIn(List<String> values) {
			addCriterion("donation_qty in", values, "donationQty");
			return (Criteria) this;
		}

		public Criteria andDonationQtyNotIn(List<String> values) {
			addCriterion("donation_qty not in", values, "donationQty");
			return (Criteria) this;
		}

		public Criteria andDonationQtyBetween(String value1, String value2) {
			addCriterion("donation_qty between", value1, value2, "donationQty");
			return (Criteria) this;
		}

		public Criteria andDonationQtyNotBetween(String value1, String value2) {
			addCriterion("donation_qty not between", value1, value2, "donationQty");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIsNull() {
			addCriterion("creation_time is null");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIsNotNull() {
			addCriterion("creation_time is not null");
			return (Criteria) this;
		}

		public Criteria andCreationTimeEqualTo(LocalDateTime value) {
			addCriterion("creation_time =", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotEqualTo(LocalDateTime value) {
			addCriterion("creation_time <>", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeGreaterThan(LocalDateTime value) {
			addCriterion("creation_time >", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("creation_time >=", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeLessThan(LocalDateTime value) {
			addCriterion("creation_time <", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("creation_time <=", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIn(List<LocalDateTime> values) {
			addCriterion("creation_time in", values, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotIn(List<LocalDateTime> values) {
			addCriterion("creation_time not in", values, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("creation_time between", value1, value2, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("creation_time not between", value1, value2, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameIsNull() {
			addCriterion("create_username is null");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameIsNotNull() {
			addCriterion("create_username is not null");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameEqualTo(String value) {
			addCriterion("create_username =", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotEqualTo(String value) {
			addCriterion("create_username <>", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameGreaterThan(String value) {
			addCriterion("create_username >", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameGreaterThanOrEqualTo(String value) {
			addCriterion("create_username >=", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameLessThan(String value) {
			addCriterion("create_username <", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameLessThanOrEqualTo(String value) {
			addCriterion("create_username <=", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameLike(String value) {
			addCriterion("create_username like", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotLike(String value) {
			addCriterion("create_username not like", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameIn(List<String> values) {
			addCriterion("create_username in", values, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotIn(List<String> values) {
			addCriterion("create_username not in", values, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameBetween(String value1, String value2) {
			addCriterion("create_username between", value1, value2, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotBetween(String value1, String value2) {
			addCriterion("create_username not between", value1, value2, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdIsNull() {
			addCriterion("create_user_id is null");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdIsNotNull() {
			addCriterion("create_user_id is not null");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdEqualTo(String value) {
			addCriterion("create_user_id =", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdNotEqualTo(String value) {
			addCriterion("create_user_id <>", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdGreaterThan(String value) {
			addCriterion("create_user_id >", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdGreaterThanOrEqualTo(String value) {
			addCriterion("create_user_id >=", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdLessThan(String value) {
			addCriterion("create_user_id <", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdLessThanOrEqualTo(String value) {
			addCriterion("create_user_id <=", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdLike(String value) {
			addCriterion("create_user_id like", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdNotLike(String value) {
			addCriterion("create_user_id not like", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdIn(List<String> values) {
			addCriterion("create_user_id in", values, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdNotIn(List<String> values) {
			addCriterion("create_user_id not in", values, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdBetween(String value1, String value2) {
			addCriterion("create_user_id between", value1, value2, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdNotBetween(String value1, String value2) {
			addCriterion("create_user_id not between", value1, value2, "createUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIsNull() {
			addCriterion("last_update_time is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIsNotNull() {
			addCriterion("last_update_time is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeEqualTo(LocalDateTime value) {
			addCriterion("last_update_time =", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotEqualTo(LocalDateTime value) {
			addCriterion("last_update_time <>", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeGreaterThan(LocalDateTime value) {
			addCriterion("last_update_time >", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("last_update_time >=", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeLessThan(LocalDateTime value) {
			addCriterion("last_update_time <", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("last_update_time <=", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIn(List<LocalDateTime> values) {
			addCriterion("last_update_time in", values, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotIn(List<LocalDateTime> values) {
			addCriterion("last_update_time not in", values, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("last_update_time between", value1, value2, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("last_update_time not between", value1, value2, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameIsNull() {
			addCriterion("last_update_username is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameIsNotNull() {
			addCriterion("last_update_username is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameEqualTo(String value) {
			addCriterion("last_update_username =", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotEqualTo(String value) {
			addCriterion("last_update_username <>", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameGreaterThan(String value) {
			addCriterion("last_update_username >", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameGreaterThanOrEqualTo(String value) {
			addCriterion("last_update_username >=", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameLessThan(String value) {
			addCriterion("last_update_username <", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameLessThanOrEqualTo(String value) {
			addCriterion("last_update_username <=", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameLike(String value) {
			addCriterion("last_update_username like", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotLike(String value) {
			addCriterion("last_update_username not like", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameIn(List<String> values) {
			addCriterion("last_update_username in", values, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotIn(List<String> values) {
			addCriterion("last_update_username not in", values, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameBetween(String value1, String value2) {
			addCriterion("last_update_username between", value1, value2, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotBetween(String value1, String value2) {
			addCriterion("last_update_username not between", value1, value2, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdIsNull() {
			addCriterion("last_update_user_id is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdIsNotNull() {
			addCriterion("last_update_user_id is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdEqualTo(String value) {
			addCriterion("last_update_user_id =", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdNotEqualTo(String value) {
			addCriterion("last_update_user_id <>", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdGreaterThan(String value) {
			addCriterion("last_update_user_id >", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdGreaterThanOrEqualTo(String value) {
			addCriterion("last_update_user_id >=", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdLessThan(String value) {
			addCriterion("last_update_user_id <", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdLessThanOrEqualTo(String value) {
			addCriterion("last_update_user_id <=", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdLike(String value) {
			addCriterion("last_update_user_id like", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdNotLike(String value) {
			addCriterion("last_update_user_id not like", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdIn(List<String> values) {
			addCriterion("last_update_user_id in", values, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdNotIn(List<String> values) {
			addCriterion("last_update_user_id not in", values, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdBetween(String value1, String value2) {
			addCriterion("last_update_user_id between", value1, value2, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdNotBetween(String value1, String value2) {
			addCriterion("last_update_user_id not between", value1, value2, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionIsNull() {
			addCriterion("last_update_version is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionIsNotNull() {
			addCriterion("last_update_version is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionEqualTo(Integer value) {
			addCriterion("last_update_version =", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionNotEqualTo(Integer value) {
			addCriterion("last_update_version <>", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionGreaterThan(Integer value) {
			addCriterion("last_update_version >", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionGreaterThanOrEqualTo(Integer value) {
			addCriterion("last_update_version >=", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionLessThan(Integer value) {
			addCriterion("last_update_version <", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionLessThanOrEqualTo(Integer value) {
			addCriterion("last_update_version <=", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionIn(List<Integer> values) {
			addCriterion("last_update_version in", values, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionNotIn(List<Integer> values) {
			addCriterion("last_update_version not in", values, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionBetween(Integer value1, Integer value2) {
			addCriterion("last_update_version between", value1, value2, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionNotBetween(Integer value1, Integer value2) {
			addCriterion("last_update_version not between", value1, value2, "lastUpdateVersion");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table t_social_perf_two_activity
	 * @mbg.generated  Tue Dec 19 10:34:43 HKT 2023
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_social_perf_two_activity
     *
     * @mbg.generated do_not_delete_during_merge Fri Sep 29 09:34:15 HKT 2023
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}