package com.csci.susdev.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class FcEnergyFactor {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor.id
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor.chinese_name
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    private String chineseName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor.performance_specifications
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    private String performanceSpecifications;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor.unit
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    private String unit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor.low_heating_value
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    private BigDecimal lowHeatingValue;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor.low_heating_value_unit
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    private String lowHeatingValueUnit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor.unit_calorific_carbon
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    private BigDecimal unitCalorificCarbon;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor.unit_calorific_carbon_unit
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    private String unitCalorificCarbonUnit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor.carbon_oxidation_rate
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    private BigDecimal carbonOxidationRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor.other_carbon_factor
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    private BigDecimal otherCarbonFactor;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor.other_carbon_factor_unit
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    private String otherCarbonFactorUnit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor.description
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor.source_description
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    private String sourceDescription;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor.creation_time
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor.create_username
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor.create_user_id
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor.last_update_time
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor.last_update_username
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor.last_update_user_id
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor.last_update_version
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    private Integer lastUpdateVersion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_fc_energy_factor.is_deleted
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    private Boolean isDeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor.id
     *
     * @return the value of t_fc_energy_factor.id
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor.id
     *
     * @param id the value for t_fc_energy_factor.id
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor.chinese_name
     *
     * @return the value of t_fc_energy_factor.chinese_name
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public String getChineseName() {
        return chineseName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor.chinese_name
     *
     * @param chineseName the value for t_fc_energy_factor.chinese_name
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public void setChineseName(String chineseName) {
        this.chineseName = chineseName == null ? null : chineseName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor.performance_specifications
     *
     * @return the value of t_fc_energy_factor.performance_specifications
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public String getPerformanceSpecifications() {
        return performanceSpecifications;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor.performance_specifications
     *
     * @param performanceSpecifications the value for t_fc_energy_factor.performance_specifications
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public void setPerformanceSpecifications(String performanceSpecifications) {
        this.performanceSpecifications = performanceSpecifications == null ? null : performanceSpecifications.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor.unit
     *
     * @return the value of t_fc_energy_factor.unit
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public String getUnit() {
        return unit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor.unit
     *
     * @param unit the value for t_fc_energy_factor.unit
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor.low_heating_value
     *
     * @return the value of t_fc_energy_factor.low_heating_value
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public BigDecimal getLowHeatingValue() {
        return lowHeatingValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor.low_heating_value
     *
     * @param lowHeatingValue the value for t_fc_energy_factor.low_heating_value
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public void setLowHeatingValue(BigDecimal lowHeatingValue) {
        this.lowHeatingValue = lowHeatingValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor.low_heating_value_unit
     *
     * @return the value of t_fc_energy_factor.low_heating_value_unit
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public String getLowHeatingValueUnit() {
        return lowHeatingValueUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor.low_heating_value_unit
     *
     * @param lowHeatingValueUnit the value for t_fc_energy_factor.low_heating_value_unit
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public void setLowHeatingValueUnit(String lowHeatingValueUnit) {
        this.lowHeatingValueUnit = lowHeatingValueUnit == null ? null : lowHeatingValueUnit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor.unit_calorific_carbon
     *
     * @return the value of t_fc_energy_factor.unit_calorific_carbon
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public BigDecimal getUnitCalorificCarbon() {
        return unitCalorificCarbon;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor.unit_calorific_carbon
     *
     * @param unitCalorificCarbon the value for t_fc_energy_factor.unit_calorific_carbon
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public void setUnitCalorificCarbon(BigDecimal unitCalorificCarbon) {
        this.unitCalorificCarbon = unitCalorificCarbon;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor.unit_calorific_carbon_unit
     *
     * @return the value of t_fc_energy_factor.unit_calorific_carbon_unit
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public String getUnitCalorificCarbonUnit() {
        return unitCalorificCarbonUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor.unit_calorific_carbon_unit
     *
     * @param unitCalorificCarbonUnit the value for t_fc_energy_factor.unit_calorific_carbon_unit
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public void setUnitCalorificCarbonUnit(String unitCalorificCarbonUnit) {
        this.unitCalorificCarbonUnit = unitCalorificCarbonUnit == null ? null : unitCalorificCarbonUnit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor.carbon_oxidation_rate
     *
     * @return the value of t_fc_energy_factor.carbon_oxidation_rate
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public BigDecimal getCarbonOxidationRate() {
        return carbonOxidationRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor.carbon_oxidation_rate
     *
     * @param carbonOxidationRate the value for t_fc_energy_factor.carbon_oxidation_rate
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public void setCarbonOxidationRate(BigDecimal carbonOxidationRate) {
        this.carbonOxidationRate = carbonOxidationRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor.other_carbon_factor
     *
     * @return the value of t_fc_energy_factor.other_carbon_factor
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public BigDecimal getOtherCarbonFactor() {
        return otherCarbonFactor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor.other_carbon_factor
     *
     * @param otherCarbonFactor the value for t_fc_energy_factor.other_carbon_factor
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public void setOtherCarbonFactor(BigDecimal otherCarbonFactor) {
        this.otherCarbonFactor = otherCarbonFactor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor.other_carbon_factor_unit
     *
     * @return the value of t_fc_energy_factor.other_carbon_factor_unit
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public String getOtherCarbonFactorUnit() {
        return otherCarbonFactorUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor.other_carbon_factor_unit
     *
     * @param otherCarbonFactorUnit the value for t_fc_energy_factor.other_carbon_factor_unit
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public void setOtherCarbonFactorUnit(String otherCarbonFactorUnit) {
        this.otherCarbonFactorUnit = otherCarbonFactorUnit == null ? null : otherCarbonFactorUnit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor.description
     *
     * @return the value of t_fc_energy_factor.description
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor.description
     *
     * @param description the value for t_fc_energy_factor.description
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor.source_description
     *
     * @return the value of t_fc_energy_factor.source_description
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public String getSourceDescription() {
        return sourceDescription;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor.source_description
     *
     * @param sourceDescription the value for t_fc_energy_factor.source_description
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public void setSourceDescription(String sourceDescription) {
        this.sourceDescription = sourceDescription == null ? null : sourceDescription.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor.creation_time
     *
     * @return the value of t_fc_energy_factor.creation_time
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor.creation_time
     *
     * @param creationTime the value for t_fc_energy_factor.creation_time
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor.create_username
     *
     * @return the value of t_fc_energy_factor.create_username
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor.create_username
     *
     * @param createUsername the value for t_fc_energy_factor.create_username
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor.create_user_id
     *
     * @return the value of t_fc_energy_factor.create_user_id
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor.create_user_id
     *
     * @param createUserId the value for t_fc_energy_factor.create_user_id
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor.last_update_time
     *
     * @return the value of t_fc_energy_factor.last_update_time
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor.last_update_time
     *
     * @param lastUpdateTime the value for t_fc_energy_factor.last_update_time
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor.last_update_username
     *
     * @return the value of t_fc_energy_factor.last_update_username
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor.last_update_username
     *
     * @param lastUpdateUsername the value for t_fc_energy_factor.last_update_username
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor.last_update_user_id
     *
     * @return the value of t_fc_energy_factor.last_update_user_id
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_fc_energy_factor.last_update_user_id
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor.last_update_version
     *
     * @return the value of t_fc_energy_factor.last_update_version
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor.last_update_version
     *
     * @param lastUpdateVersion the value for t_fc_energy_factor.last_update_version
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_fc_energy_factor.is_deleted
     *
     * @return the value of t_fc_energy_factor.is_deleted
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_fc_energy_factor.is_deleted
     *
     * @param isDeleted the value for t_fc_energy_factor.is_deleted
     *
     * @mbg.generated Mon Mar 17 16:21:10 CST 2025
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }
}