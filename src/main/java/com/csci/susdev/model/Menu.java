package com.csci.susdev.model;

import java.time.LocalDateTime;

public class Menu {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.id
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.parent_id
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private String parentId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.path
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private String path;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.description
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.route_icon
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private String routeIcon;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.route_title
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private String routeTitle;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.route_path
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private String routePath;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.route_redirect
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private String routeRedirect;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.route_component
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private String routeComponent;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.seq
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private Integer seq;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.is_active
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private Boolean isActive;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.creation_time
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.create_username
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.create_user_id
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.last_update_time
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.last_update_username
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_menu.last_update_user_id
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    private String lastUpdateUserId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_menu.id
     *
     * @return the value of t_menu.id
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_menu.id
     *
     * @param id the value for t_menu.id
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_menu.parent_id
     *
     * @return the value of t_menu.parent_id
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public String getParentId() {
        return parentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_menu.parent_id
     *
     * @param parentId the value for t_menu.parent_id
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public void setParentId(String parentId) {
        this.parentId = parentId == null ? null : parentId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_menu.path
     *
     * @return the value of t_menu.path
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public String getPath() {
        return path;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_menu.path
     *
     * @param path the value for t_menu.path
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public void setPath(String path) {
        this.path = path == null ? null : path.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_menu.description
     *
     * @return the value of t_menu.description
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_menu.description
     *
     * @param description the value for t_menu.description
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_menu.route_icon
     *
     * @return the value of t_menu.route_icon
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public String getRouteIcon() {
        return routeIcon;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_menu.route_icon
     *
     * @param routeIcon the value for t_menu.route_icon
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public void setRouteIcon(String routeIcon) {
        this.routeIcon = routeIcon == null ? null : routeIcon.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_menu.route_title
     *
     * @return the value of t_menu.route_title
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public String getRouteTitle() {
        return routeTitle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_menu.route_title
     *
     * @param routeTitle the value for t_menu.route_title
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public void setRouteTitle(String routeTitle) {
        this.routeTitle = routeTitle == null ? null : routeTitle.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_menu.route_path
     *
     * @return the value of t_menu.route_path
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public String getRoutePath() {
        return routePath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_menu.route_path
     *
     * @param routePath the value for t_menu.route_path
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public void setRoutePath(String routePath) {
        this.routePath = routePath == null ? null : routePath.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_menu.route_redirect
     *
     * @return the value of t_menu.route_redirect
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public String getRouteRedirect() {
        return routeRedirect;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_menu.route_redirect
     *
     * @param routeRedirect the value for t_menu.route_redirect
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public void setRouteRedirect(String routeRedirect) {
        this.routeRedirect = routeRedirect == null ? null : routeRedirect.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_menu.route_component
     *
     * @return the value of t_menu.route_component
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public String getRouteComponent() {
        return routeComponent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_menu.route_component
     *
     * @param routeComponent the value for t_menu.route_component
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public void setRouteComponent(String routeComponent) {
        this.routeComponent = routeComponent == null ? null : routeComponent.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_menu.seq
     *
     * @return the value of t_menu.seq
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public Integer getSeq() {
        return seq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_menu.seq
     *
     * @param seq the value for t_menu.seq
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_menu.is_active
     *
     * @return the value of t_menu.is_active
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public Boolean getIsActive() {
        return isActive;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_menu.is_active
     *
     * @param isActive the value for t_menu.is_active
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_menu.creation_time
     *
     * @return the value of t_menu.creation_time
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_menu.creation_time
     *
     * @param creationTime the value for t_menu.creation_time
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_menu.create_username
     *
     * @return the value of t_menu.create_username
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_menu.create_username
     *
     * @param createUsername the value for t_menu.create_username
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_menu.create_user_id
     *
     * @return the value of t_menu.create_user_id
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_menu.create_user_id
     *
     * @param createUserId the value for t_menu.create_user_id
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_menu.last_update_time
     *
     * @return the value of t_menu.last_update_time
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_menu.last_update_time
     *
     * @param lastUpdateTime the value for t_menu.last_update_time
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_menu.last_update_username
     *
     * @return the value of t_menu.last_update_username
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_menu.last_update_username
     *
     * @param lastUpdateUsername the value for t_menu.last_update_username
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_menu.last_update_user_id
     *
     * @return the value of t_menu.last_update_user_id
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_menu.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_menu.last_update_user_id
     *
     * @mbg.generated Fri Apr 26 10:55:29 HKT 2024
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }
}