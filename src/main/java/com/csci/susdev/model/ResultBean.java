package com.csci.susdev.model;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * Created on : 5/6/2018
 *
 * <AUTHOR> Terry<PERSON>ao
 */
@Schema(name = "ResultBean")
public class ResultBean<T> extends ResultBase {

    @Schema(description = "请求访问服务端信息时，请求结果存放在data中")
    private T data;

    public ResultBean() {
        super();
    }

    public ResultBean(T data) {
        this.data = data;
    }

    public ResultBean(int code, String msg) {
        super(code, msg);
    }

    public ResultBean(int code, String msg, T data) {
        super(code, msg);
        this.data = data;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
    public static <T> ResultBean<T> success(T data) {
        return new ResultBean<>(data);
    }
    public static <T> ResultBean<T> ok() {
        return new ResultBean<>(null);
    }
    public static <T> ResultBean<T> ok(T data) {
        return new ResultBean<>(data);
    }
    public static <T> ResultBean<T> fail(String msg) {
        return new ResultBean<>(-1, msg, null);
    }
    public static <T> ResultBean<T> fail(String msg,T data) {
        return new ResultBean<>(-1, msg, data);
    }
}
