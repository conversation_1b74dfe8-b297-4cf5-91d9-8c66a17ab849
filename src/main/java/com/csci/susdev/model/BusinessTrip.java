package com.csci.susdev.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class BusinessTrip {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_business_trip.id
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_business_trip.ambient_head_id
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	private String ambientHeadId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_business_trip.month_value
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	private Integer monthValue;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_business_trip.vehicle_type
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	private String vehicleType;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_business_trip.start_place
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	private String startPlace;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_business_trip.destination
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	private String destination;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_business_trip.route_no
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	private String routeNo;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_business_trip.level
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	private String level;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_business_trip.trip_type
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	private Integer tripType;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_business_trip.ticket_type
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	private Integer ticketType;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_business_trip.person_count
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	private Integer personCount;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_business_trip.flight_distance
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	private BigDecimal flightDistance;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_business_trip.carbon_emission
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	private BigDecimal carbonEmission;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_business_trip.remark
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	private String remark;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_business_trip.creation_time
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	private LocalDateTime creationTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_business_trip.create_username
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	private String createUsername;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_business_trip.create_user_id
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	private String createUserId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_business_trip.last_update_time
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	private LocalDateTime lastUpdateTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_business_trip.last_update_username
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	private String lastUpdateUsername;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_business_trip.last_update_user_id
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	private String lastUpdateUserId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_business_trip.last_update_version
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	private Integer lastUpdateVersion;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_business_trip.id
	 * @return  the value of t_ab_business_trip.id
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_business_trip.id
	 * @param id  the value for t_ab_business_trip.id
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_business_trip.ambient_head_id
	 * @return  the value of t_ab_business_trip.ambient_head_id
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public String getAmbientHeadId() {
		return ambientHeadId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_business_trip.ambient_head_id
	 * @param ambientHeadId  the value for t_ab_business_trip.ambient_head_id
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public void setAmbientHeadId(String ambientHeadId) {
		this.ambientHeadId = ambientHeadId == null ? null : ambientHeadId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_business_trip.month_value
	 * @return  the value of t_ab_business_trip.month_value
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public Integer getMonthValue() {
		return monthValue;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_business_trip.month_value
	 * @param monthValue  the value for t_ab_business_trip.month_value
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public void setMonthValue(Integer monthValue) {
		this.monthValue = monthValue;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_business_trip.vehicle_type
	 * @return  the value of t_ab_business_trip.vehicle_type
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public String getVehicleType() {
		return vehicleType;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_business_trip.vehicle_type
	 * @param vehicleType  the value for t_ab_business_trip.vehicle_type
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public void setVehicleType(String vehicleType) {
		this.vehicleType = vehicleType == null ? null : vehicleType.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_business_trip.start_place
	 * @return  the value of t_ab_business_trip.start_place
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public String getStartPlace() {
		return startPlace;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_business_trip.start_place
	 * @param startPlace  the value for t_ab_business_trip.start_place
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public void setStartPlace(String startPlace) {
		this.startPlace = startPlace == null ? null : startPlace.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_business_trip.destination
	 * @return  the value of t_ab_business_trip.destination
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public String getDestination() {
		return destination;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_business_trip.destination
	 * @param destination  the value for t_ab_business_trip.destination
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public void setDestination(String destination) {
		this.destination = destination == null ? null : destination.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_business_trip.route_no
	 * @return  the value of t_ab_business_trip.route_no
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public String getRouteNo() {
		return routeNo;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_business_trip.route_no
	 * @param routeNo  the value for t_ab_business_trip.route_no
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public void setRouteNo(String routeNo) {
		this.routeNo = routeNo == null ? null : routeNo.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_business_trip.level
	 * @return  the value of t_ab_business_trip.level
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public String getLevel() {
		return level;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_business_trip.level
	 * @param level  the value for t_ab_business_trip.level
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public void setLevel(String level) {
		this.level = level == null ? null : level.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_business_trip.trip_type
	 * @return  the value of t_ab_business_trip.trip_type
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public Integer getTripType() {
		return tripType;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_business_trip.trip_type
	 * @param tripType  the value for t_ab_business_trip.trip_type
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public void setTripType(Integer tripType) {
		this.tripType = tripType;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_business_trip.ticket_type
	 * @return  the value of t_ab_business_trip.ticket_type
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public Integer getTicketType() {
		return ticketType;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_business_trip.ticket_type
	 * @param ticketType  the value for t_ab_business_trip.ticket_type
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public void setTicketType(Integer ticketType) {
		this.ticketType = ticketType;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_business_trip.person_count
	 * @return  the value of t_ab_business_trip.person_count
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public Integer getPersonCount() {
		return personCount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_business_trip.person_count
	 * @param personCount  the value for t_ab_business_trip.person_count
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public void setPersonCount(Integer personCount) {
		this.personCount = personCount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_business_trip.flight_distance
	 * @return  the value of t_ab_business_trip.flight_distance
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public BigDecimal getFlightDistance() {
		return flightDistance;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_business_trip.flight_distance
	 * @param flightDistance  the value for t_ab_business_trip.flight_distance
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public void setFlightDistance(BigDecimal flightDistance) {
		this.flightDistance = flightDistance;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_business_trip.carbon_emission
	 * @return  the value of t_ab_business_trip.carbon_emission
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public BigDecimal getCarbonEmission() {
		return carbonEmission;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_business_trip.carbon_emission
	 * @param carbonEmission  the value for t_ab_business_trip.carbon_emission
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public void setCarbonEmission(BigDecimal carbonEmission) {
		this.carbonEmission = carbonEmission;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_business_trip.remark
	 * @return  the value of t_ab_business_trip.remark
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public String getRemark() {
		return remark;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_business_trip.remark
	 * @param remark  the value for t_ab_business_trip.remark
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public void setRemark(String remark) {
		this.remark = remark == null ? null : remark.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_business_trip.creation_time
	 * @return  the value of t_ab_business_trip.creation_time
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public LocalDateTime getCreationTime() {
		return creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_business_trip.creation_time
	 * @param creationTime  the value for t_ab_business_trip.creation_time
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public void setCreationTime(LocalDateTime creationTime) {
		this.creationTime = creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_business_trip.create_username
	 * @return  the value of t_ab_business_trip.create_username
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public String getCreateUsername() {
		return createUsername;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_business_trip.create_username
	 * @param createUsername  the value for t_ab_business_trip.create_username
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public void setCreateUsername(String createUsername) {
		this.createUsername = createUsername == null ? null : createUsername.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_business_trip.create_user_id
	 * @return  the value of t_ab_business_trip.create_user_id
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public String getCreateUserId() {
		return createUserId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_business_trip.create_user_id
	 * @param createUserId  the value for t_ab_business_trip.create_user_id
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId == null ? null : createUserId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_business_trip.last_update_time
	 * @return  the value of t_ab_business_trip.last_update_time
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public LocalDateTime getLastUpdateTime() {
		return lastUpdateTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_business_trip.last_update_time
	 * @param lastUpdateTime  the value for t_ab_business_trip.last_update_time
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_business_trip.last_update_username
	 * @return  the value of t_ab_business_trip.last_update_username
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public String getLastUpdateUsername() {
		return lastUpdateUsername;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_business_trip.last_update_username
	 * @param lastUpdateUsername  the value for t_ab_business_trip.last_update_username
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public void setLastUpdateUsername(String lastUpdateUsername) {
		this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_business_trip.last_update_user_id
	 * @return  the value of t_ab_business_trip.last_update_user_id
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public String getLastUpdateUserId() {
		return lastUpdateUserId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_business_trip.last_update_user_id
	 * @param lastUpdateUserId  the value for t_ab_business_trip.last_update_user_id
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public void setLastUpdateUserId(String lastUpdateUserId) {
		this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_business_trip.last_update_version
	 * @return  the value of t_ab_business_trip.last_update_version
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public Integer getLastUpdateVersion() {
		return lastUpdateVersion;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_business_trip.last_update_version
	 * @param lastUpdateVersion  the value for t_ab_business_trip.last_update_version
	 * @mbg.generated  Thu Aug 10 09:08:11 HKT 2023
	 */
	public void setLastUpdateVersion(Integer lastUpdateVersion) {
		this.lastUpdateVersion = lastUpdateVersion;
	}
}