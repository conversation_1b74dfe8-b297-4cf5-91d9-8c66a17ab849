package com.csci.susdev.model;

public class TzhBsRole {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Bs_Role.Id
     *
     * @mbg.generated Tue Jun 04 16:19:28 HKT 2024
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Bs_Role.RoleName
     *
     * @mbg.generated Tue Jun 04 16:19:28 HKT 2024
     */
    private String rolename;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Bs_Role.RoleDesc
     *
     * @mbg.generated Tue Jun 04 16:19:28 HKT 2024
     */
    private String roledesc;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Bs_Role.Id
     *
     * @return the value of Tzh_Bs_Role.Id
     *
     * @mbg.generated Tue Jun 04 16:19:28 HKT 2024
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Bs_Role.Id
     *
     * @param id the value for Tzh_Bs_Role.Id
     *
     * @mbg.generated Tue Jun 04 16:19:28 HKT 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Bs_Role.RoleName
     *
     * @return the value of Tzh_Bs_Role.RoleName
     *
     * @mbg.generated Tue Jun 04 16:19:28 HKT 2024
     */
    public String getRolename() {
        return rolename;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Bs_Role.RoleName
     *
     * @param rolename the value for Tzh_Bs_Role.RoleName
     *
     * @mbg.generated Tue Jun 04 16:19:28 HKT 2024
     */
    public void setRolename(String rolename) {
        this.rolename = rolename == null ? null : rolename.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Bs_Role.RoleDesc
     *
     * @return the value of Tzh_Bs_Role.RoleDesc
     *
     * @mbg.generated Tue Jun 04 16:19:28 HKT 2024
     */
    public String getRoledesc() {
        return roledesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Bs_Role.RoleDesc
     *
     * @param roledesc the value for Tzh_Bs_Role.RoleDesc
     *
     * @mbg.generated Tue Jun 04 16:19:28 HKT 2024
     */
    public void setRoledesc(String roledesc) {
        this.roledesc = roledesc == null ? null : roledesc.trim();
    }
}