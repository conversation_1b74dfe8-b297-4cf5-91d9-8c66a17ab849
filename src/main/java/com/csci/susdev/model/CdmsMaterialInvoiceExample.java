package com.csci.susdev.model;

import java.util.ArrayList;
import java.util.List;

public class CdmsMaterialInvoiceExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public CdmsMaterialInvoiceExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(String value) {
			addCriterion("id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(String value) {
			addCriterion("id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(String value) {
			addCriterion("id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(String value) {
			addCriterion("id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(String value) {
			addCriterion("id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(String value) {
			addCriterion("id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLike(String value) {
			addCriterion("id like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotLike(String value) {
			addCriterion("id not like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<String> values) {
			addCriterion("id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<String> values) {
			addCriterion("id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(String value1, String value2) {
			addCriterion("id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(String value1, String value2) {
			addCriterion("id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdIsNull() {
			addCriterion("organization_id is null");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdIsNotNull() {
			addCriterion("organization_id is not null");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdEqualTo(String value) {
			addCriterion("organization_id =", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdNotEqualTo(String value) {
			addCriterion("organization_id <>", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdGreaterThan(String value) {
			addCriterion("organization_id >", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdGreaterThanOrEqualTo(String value) {
			addCriterion("organization_id >=", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdLessThan(String value) {
			addCriterion("organization_id <", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdLessThanOrEqualTo(String value) {
			addCriterion("organization_id <=", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdLike(String value) {
			addCriterion("organization_id like", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdNotLike(String value) {
			addCriterion("organization_id not like", value, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdIn(List<String> values) {
			addCriterion("organization_id in", values, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdNotIn(List<String> values) {
			addCriterion("organization_id not in", values, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdBetween(String value1, String value2) {
			addCriterion("organization_id between", value1, value2, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationIdNotBetween(String value1, String value2) {
			addCriterion("organization_id not between", value1, value2, "organizationId");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameIsNull() {
			addCriterion("organization_name is null");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameIsNotNull() {
			addCriterion("organization_name is not null");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameEqualTo(String value) {
			addCriterion("organization_name =", value, "organizationName");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameNotEqualTo(String value) {
			addCriterion("organization_name <>", value, "organizationName");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameGreaterThan(String value) {
			addCriterion("organization_name >", value, "organizationName");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameGreaterThanOrEqualTo(String value) {
			addCriterion("organization_name >=", value, "organizationName");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameLessThan(String value) {
			addCriterion("organization_name <", value, "organizationName");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameLessThanOrEqualTo(String value) {
			addCriterion("organization_name <=", value, "organizationName");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameLike(String value) {
			addCriterion("organization_name like", value, "organizationName");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameNotLike(String value) {
			addCriterion("organization_name not like", value, "organizationName");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameIn(List<String> values) {
			addCriterion("organization_name in", values, "organizationName");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameNotIn(List<String> values) {
			addCriterion("organization_name not in", values, "organizationName");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameBetween(String value1, String value2) {
			addCriterion("organization_name between", value1, value2, "organizationName");
			return (Criteria) this;
		}

		public Criteria andOrganizationNameNotBetween(String value1, String value2) {
			addCriterion("organization_name not between", value1, value2, "organizationName");
			return (Criteria) this;
		}

		public Criteria andRecordYearMonthIsNull() {
			addCriterion("record_year_month is null");
			return (Criteria) this;
		}

		public Criteria andRecordYearMonthIsNotNull() {
			addCriterion("record_year_month is not null");
			return (Criteria) this;
		}

		public Criteria andRecordYearMonthEqualTo(Integer value) {
			addCriterion("record_year_month =", value, "recordYearMonth");
			return (Criteria) this;
		}

		public Criteria andRecordYearMonthNotEqualTo(Integer value) {
			addCriterion("record_year_month <>", value, "recordYearMonth");
			return (Criteria) this;
		}

		public Criteria andRecordYearMonthGreaterThan(Integer value) {
			addCriterion("record_year_month >", value, "recordYearMonth");
			return (Criteria) this;
		}

		public Criteria andRecordYearMonthGreaterThanOrEqualTo(Integer value) {
			addCriterion("record_year_month >=", value, "recordYearMonth");
			return (Criteria) this;
		}

		public Criteria andRecordYearMonthLessThan(Integer value) {
			addCriterion("record_year_month <", value, "recordYearMonth");
			return (Criteria) this;
		}

		public Criteria andRecordYearMonthLessThanOrEqualTo(Integer value) {
			addCriterion("record_year_month <=", value, "recordYearMonth");
			return (Criteria) this;
		}

		public Criteria andRecordYearMonthIn(List<Integer> values) {
			addCriterion("record_year_month in", values, "recordYearMonth");
			return (Criteria) this;
		}

		public Criteria andRecordYearMonthNotIn(List<Integer> values) {
			addCriterion("record_year_month not in", values, "recordYearMonth");
			return (Criteria) this;
		}

		public Criteria andRecordYearMonthBetween(Integer value1, Integer value2) {
			addCriterion("record_year_month between", value1, value2, "recordYearMonth");
			return (Criteria) this;
		}

		public Criteria andRecordYearMonthNotBetween(Integer value1, Integer value2) {
			addCriterion("record_year_month not between", value1, value2, "recordYearMonth");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionLocationIsNull() {
			addCriterion("carbon_emission_location is null");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionLocationIsNotNull() {
			addCriterion("carbon_emission_location is not null");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionLocationEqualTo(String value) {
			addCriterion("carbon_emission_location =", value, "carbonEmissionLocation");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionLocationNotEqualTo(String value) {
			addCriterion("carbon_emission_location <>", value, "carbonEmissionLocation");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionLocationGreaterThan(String value) {
			addCriterion("carbon_emission_location >", value, "carbonEmissionLocation");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionLocationGreaterThanOrEqualTo(String value) {
			addCriterion("carbon_emission_location >=", value, "carbonEmissionLocation");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionLocationLessThan(String value) {
			addCriterion("carbon_emission_location <", value, "carbonEmissionLocation");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionLocationLessThanOrEqualTo(String value) {
			addCriterion("carbon_emission_location <=", value, "carbonEmissionLocation");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionLocationLike(String value) {
			addCriterion("carbon_emission_location like", value, "carbonEmissionLocation");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionLocationNotLike(String value) {
			addCriterion("carbon_emission_location not like", value, "carbonEmissionLocation");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionLocationIn(List<String> values) {
			addCriterion("carbon_emission_location in", values, "carbonEmissionLocation");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionLocationNotIn(List<String> values) {
			addCriterion("carbon_emission_location not in", values, "carbonEmissionLocation");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionLocationBetween(String value1, String value2) {
			addCriterion("carbon_emission_location between", value1, value2, "carbonEmissionLocation");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionLocationNotBetween(String value1, String value2) {
			addCriterion("carbon_emission_location not between", value1, value2, "carbonEmissionLocation");
			return (Criteria) this;
		}

		public Criteria andMaterialCodeIsNull() {
			addCriterion("material_code is null");
			return (Criteria) this;
		}

		public Criteria andMaterialCodeIsNotNull() {
			addCriterion("material_code is not null");
			return (Criteria) this;
		}

		public Criteria andMaterialCodeEqualTo(String value) {
			addCriterion("material_code =", value, "materialCode");
			return (Criteria) this;
		}

		public Criteria andMaterialCodeNotEqualTo(String value) {
			addCriterion("material_code <>", value, "materialCode");
			return (Criteria) this;
		}

		public Criteria andMaterialCodeGreaterThan(String value) {
			addCriterion("material_code >", value, "materialCode");
			return (Criteria) this;
		}

		public Criteria andMaterialCodeGreaterThanOrEqualTo(String value) {
			addCriterion("material_code >=", value, "materialCode");
			return (Criteria) this;
		}

		public Criteria andMaterialCodeLessThan(String value) {
			addCriterion("material_code <", value, "materialCode");
			return (Criteria) this;
		}

		public Criteria andMaterialCodeLessThanOrEqualTo(String value) {
			addCriterion("material_code <=", value, "materialCode");
			return (Criteria) this;
		}

		public Criteria andMaterialCodeLike(String value) {
			addCriterion("material_code like", value, "materialCode");
			return (Criteria) this;
		}

		public Criteria andMaterialCodeNotLike(String value) {
			addCriterion("material_code not like", value, "materialCode");
			return (Criteria) this;
		}

		public Criteria andMaterialCodeIn(List<String> values) {
			addCriterion("material_code in", values, "materialCode");
			return (Criteria) this;
		}

		public Criteria andMaterialCodeNotIn(List<String> values) {
			addCriterion("material_code not in", values, "materialCode");
			return (Criteria) this;
		}

		public Criteria andMaterialCodeBetween(String value1, String value2) {
			addCriterion("material_code between", value1, value2, "materialCode");
			return (Criteria) this;
		}

		public Criteria andMaterialCodeNotBetween(String value1, String value2) {
			addCriterion("material_code not between", value1, value2, "materialCode");
			return (Criteria) this;
		}

		public Criteria andClassificationIsNull() {
			addCriterion("classification is null");
			return (Criteria) this;
		}

		public Criteria andClassificationIsNotNull() {
			addCriterion("classification is not null");
			return (Criteria) this;
		}

		public Criteria andClassificationEqualTo(String value) {
			addCriterion("classification =", value, "classification");
			return (Criteria) this;
		}

		public Criteria andClassificationNotEqualTo(String value) {
			addCriterion("classification <>", value, "classification");
			return (Criteria) this;
		}

		public Criteria andClassificationGreaterThan(String value) {
			addCriterion("classification >", value, "classification");
			return (Criteria) this;
		}

		public Criteria andClassificationGreaterThanOrEqualTo(String value) {
			addCriterion("classification >=", value, "classification");
			return (Criteria) this;
		}

		public Criteria andClassificationLessThan(String value) {
			addCriterion("classification <", value, "classification");
			return (Criteria) this;
		}

		public Criteria andClassificationLessThanOrEqualTo(String value) {
			addCriterion("classification <=", value, "classification");
			return (Criteria) this;
		}

		public Criteria andClassificationLike(String value) {
			addCriterion("classification like", value, "classification");
			return (Criteria) this;
		}

		public Criteria andClassificationNotLike(String value) {
			addCriterion("classification not like", value, "classification");
			return (Criteria) this;
		}

		public Criteria andClassificationIn(List<String> values) {
			addCriterion("classification in", values, "classification");
			return (Criteria) this;
		}

		public Criteria andClassificationNotIn(List<String> values) {
			addCriterion("classification not in", values, "classification");
			return (Criteria) this;
		}

		public Criteria andClassificationBetween(String value1, String value2) {
			addCriterion("classification between", value1, value2, "classification");
			return (Criteria) this;
		}

		public Criteria andClassificationNotBetween(String value1, String value2) {
			addCriterion("classification not between", value1, value2, "classification");
			return (Criteria) this;
		}

		public Criteria andDescriptionIsNull() {
			addCriterion("description is null");
			return (Criteria) this;
		}

		public Criteria andDescriptionIsNotNull() {
			addCriterion("description is not null");
			return (Criteria) this;
		}

		public Criteria andDescriptionEqualTo(String value) {
			addCriterion("description =", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionNotEqualTo(String value) {
			addCriterion("description <>", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionGreaterThan(String value) {
			addCriterion("description >", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
			addCriterion("description >=", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionLessThan(String value) {
			addCriterion("description <", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionLessThanOrEqualTo(String value) {
			addCriterion("description <=", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionLike(String value) {
			addCriterion("description like", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionNotLike(String value) {
			addCriterion("description not like", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionIn(List<String> values) {
			addCriterion("description in", values, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionNotIn(List<String> values) {
			addCriterion("description not in", values, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionBetween(String value1, String value2) {
			addCriterion("description between", value1, value2, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionNotBetween(String value1, String value2) {
			addCriterion("description not between", value1, value2, "description");
			return (Criteria) this;
		}

		public Criteria andPurInvoiceIdIsNull() {
			addCriterion("pur_invoice_id is null");
			return (Criteria) this;
		}

		public Criteria andPurInvoiceIdIsNotNull() {
			addCriterion("pur_invoice_id is not null");
			return (Criteria) this;
		}

		public Criteria andPurInvoiceIdEqualTo(String value) {
			addCriterion("pur_invoice_id =", value, "purInvoiceId");
			return (Criteria) this;
		}

		public Criteria andPurInvoiceIdNotEqualTo(String value) {
			addCriterion("pur_invoice_id <>", value, "purInvoiceId");
			return (Criteria) this;
		}

		public Criteria andPurInvoiceIdGreaterThan(String value) {
			addCriterion("pur_invoice_id >", value, "purInvoiceId");
			return (Criteria) this;
		}

		public Criteria andPurInvoiceIdGreaterThanOrEqualTo(String value) {
			addCriterion("pur_invoice_id >=", value, "purInvoiceId");
			return (Criteria) this;
		}

		public Criteria andPurInvoiceIdLessThan(String value) {
			addCriterion("pur_invoice_id <", value, "purInvoiceId");
			return (Criteria) this;
		}

		public Criteria andPurInvoiceIdLessThanOrEqualTo(String value) {
			addCriterion("pur_invoice_id <=", value, "purInvoiceId");
			return (Criteria) this;
		}

		public Criteria andPurInvoiceIdLike(String value) {
			addCriterion("pur_invoice_id like", value, "purInvoiceId");
			return (Criteria) this;
		}

		public Criteria andPurInvoiceIdNotLike(String value) {
			addCriterion("pur_invoice_id not like", value, "purInvoiceId");
			return (Criteria) this;
		}

		public Criteria andPurInvoiceIdIn(List<String> values) {
			addCriterion("pur_invoice_id in", values, "purInvoiceId");
			return (Criteria) this;
		}

		public Criteria andPurInvoiceIdNotIn(List<String> values) {
			addCriterion("pur_invoice_id not in", values, "purInvoiceId");
			return (Criteria) this;
		}

		public Criteria andPurInvoiceIdBetween(String value1, String value2) {
			addCriterion("pur_invoice_id between", value1, value2, "purInvoiceId");
			return (Criteria) this;
		}

		public Criteria andPurInvoiceIdNotBetween(String value1, String value2) {
			addCriterion("pur_invoice_id not between", value1, value2, "purInvoiceId");
			return (Criteria) this;
		}

		public Criteria andInvoiceFileIsNull() {
			addCriterion("invoice_file is null");
			return (Criteria) this;
		}

		public Criteria andInvoiceFileIsNotNull() {
			addCriterion("invoice_file is not null");
			return (Criteria) this;
		}

		public Criteria andInvoiceFileEqualTo(String value) {
			addCriterion("invoice_file =", value, "invoiceFile");
			return (Criteria) this;
		}

		public Criteria andInvoiceFileNotEqualTo(String value) {
			addCriterion("invoice_file <>", value, "invoiceFile");
			return (Criteria) this;
		}

		public Criteria andInvoiceFileGreaterThan(String value) {
			addCriterion("invoice_file >", value, "invoiceFile");
			return (Criteria) this;
		}

		public Criteria andInvoiceFileGreaterThanOrEqualTo(String value) {
			addCriterion("invoice_file >=", value, "invoiceFile");
			return (Criteria) this;
		}

		public Criteria andInvoiceFileLessThan(String value) {
			addCriterion("invoice_file <", value, "invoiceFile");
			return (Criteria) this;
		}

		public Criteria andInvoiceFileLessThanOrEqualTo(String value) {
			addCriterion("invoice_file <=", value, "invoiceFile");
			return (Criteria) this;
		}

		public Criteria andInvoiceFileLike(String value) {
			addCriterion("invoice_file like", value, "invoiceFile");
			return (Criteria) this;
		}

		public Criteria andInvoiceFileNotLike(String value) {
			addCriterion("invoice_file not like", value, "invoiceFile");
			return (Criteria) this;
		}

		public Criteria andInvoiceFileIn(List<String> values) {
			addCriterion("invoice_file in", values, "invoiceFile");
			return (Criteria) this;
		}

		public Criteria andInvoiceFileNotIn(List<String> values) {
			addCriterion("invoice_file not in", values, "invoiceFile");
			return (Criteria) this;
		}

		public Criteria andInvoiceFileBetween(String value1, String value2) {
			addCriterion("invoice_file between", value1, value2, "invoiceFile");
			return (Criteria) this;
		}

		public Criteria andInvoiceFileNotBetween(String value1, String value2) {
			addCriterion("invoice_file not between", value1, value2, "invoiceFile");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table t_cdms_material_invoice
	 * @mbg.generated  Fri Jan 05 15:59:13 HKT 2024
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_cdms_material_invoice
     *
     * @mbg.generated do_not_delete_during_merge Fri Jan 05 15:33:28 HKT 2024
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}