package com.csci.susdev.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class AccountingManagementExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public AccountingManagementExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProtocolConfigurationIdIsNull() {
            addCriterion("protocol_configuration_id is null");
            return (Criteria) this;
        }

        public Criteria andProtocolConfigurationIdIsNotNull() {
            addCriterion("protocol_configuration_id is not null");
            return (Criteria) this;
        }

        public Criteria andProtocolConfigurationIdEqualTo(String value) {
            addCriterion("protocol_configuration_id =", value, "protocolConfigurationId");
            return (Criteria) this;
        }

        public Criteria andProtocolConfigurationIdNotEqualTo(String value) {
            addCriterion("protocol_configuration_id <>", value, "protocolConfigurationId");
            return (Criteria) this;
        }

        public Criteria andProtocolConfigurationIdGreaterThan(String value) {
            addCriterion("protocol_configuration_id >", value, "protocolConfigurationId");
            return (Criteria) this;
        }

        public Criteria andProtocolConfigurationIdGreaterThanOrEqualTo(String value) {
            addCriterion("protocol_configuration_id >=", value, "protocolConfigurationId");
            return (Criteria) this;
        }

        public Criteria andProtocolConfigurationIdLessThan(String value) {
            addCriterion("protocol_configuration_id <", value, "protocolConfigurationId");
            return (Criteria) this;
        }

        public Criteria andProtocolConfigurationIdLessThanOrEqualTo(String value) {
            addCriterion("protocol_configuration_id <=", value, "protocolConfigurationId");
            return (Criteria) this;
        }

        public Criteria andProtocolConfigurationIdLike(String value) {
            addCriterion("protocol_configuration_id like", value, "protocolConfigurationId");
            return (Criteria) this;
        }

        public Criteria andProtocolConfigurationIdNotLike(String value) {
            addCriterion("protocol_configuration_id not like", value, "protocolConfigurationId");
            return (Criteria) this;
        }

        public Criteria andProtocolConfigurationIdIn(List<String> values) {
            addCriterion("protocol_configuration_id in", values, "protocolConfigurationId");
            return (Criteria) this;
        }

        public Criteria andProtocolConfigurationIdNotIn(List<String> values) {
            addCriterion("protocol_configuration_id not in", values, "protocolConfigurationId");
            return (Criteria) this;
        }

        public Criteria andProtocolConfigurationIdBetween(String value1, String value2) {
            addCriterion("protocol_configuration_id between", value1, value2, "protocolConfigurationId");
            return (Criteria) this;
        }

        public Criteria andProtocolConfigurationIdNotBetween(String value1, String value2) {
            addCriterion("protocol_configuration_id not between", value1, value2, "protocolConfigurationId");
            return (Criteria) this;
        }

        public Criteria andChineseNameIsNull() {
            addCriterion("chinese_name is null");
            return (Criteria) this;
        }

        public Criteria andChineseNameIsNotNull() {
            addCriterion("chinese_name is not null");
            return (Criteria) this;
        }

        public Criteria andChineseNameEqualTo(String value) {
            addCriterion("chinese_name =", value, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameNotEqualTo(String value) {
            addCriterion("chinese_name <>", value, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameGreaterThan(String value) {
            addCriterion("chinese_name >", value, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameGreaterThanOrEqualTo(String value) {
            addCriterion("chinese_name >=", value, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameLessThan(String value) {
            addCriterion("chinese_name <", value, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameLessThanOrEqualTo(String value) {
            addCriterion("chinese_name <=", value, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameLike(String value) {
            addCriterion("chinese_name like", value, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameNotLike(String value) {
            addCriterion("chinese_name not like", value, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameIn(List<String> values) {
            addCriterion("chinese_name in", values, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameNotIn(List<String> values) {
            addCriterion("chinese_name not in", values, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameBetween(String value1, String value2) {
            addCriterion("chinese_name between", value1, value2, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameNotBetween(String value1, String value2) {
            addCriterion("chinese_name not between", value1, value2, "chineseName");
            return (Criteria) this;
        }

        public Criteria andCountOneIsNull() {
            addCriterion("count_one is null");
            return (Criteria) this;
        }

        public Criteria andCountOneIsNotNull() {
            addCriterion("count_one is not null");
            return (Criteria) this;
        }

        public Criteria andCountOneEqualTo(BigDecimal value) {
            addCriterion("count_one =", value, "countOne");
            return (Criteria) this;
        }

        public Criteria andCountOneNotEqualTo(BigDecimal value) {
            addCriterion("count_one <>", value, "countOne");
            return (Criteria) this;
        }

        public Criteria andCountOneGreaterThan(BigDecimal value) {
            addCriterion("count_one >", value, "countOne");
            return (Criteria) this;
        }

        public Criteria andCountOneGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("count_one >=", value, "countOne");
            return (Criteria) this;
        }

        public Criteria andCountOneLessThan(BigDecimal value) {
            addCriterion("count_one <", value, "countOne");
            return (Criteria) this;
        }

        public Criteria andCountOneLessThanOrEqualTo(BigDecimal value) {
            addCriterion("count_one <=", value, "countOne");
            return (Criteria) this;
        }

        public Criteria andCountOneIn(List<BigDecimal> values) {
            addCriterion("count_one in", values, "countOne");
            return (Criteria) this;
        }

        public Criteria andCountOneNotIn(List<BigDecimal> values) {
            addCriterion("count_one not in", values, "countOne");
            return (Criteria) this;
        }

        public Criteria andCountOneBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("count_one between", value1, value2, "countOne");
            return (Criteria) this;
        }

        public Criteria andCountOneNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("count_one not between", value1, value2, "countOne");
            return (Criteria) this;
        }

        public Criteria andCountTwoIsNull() {
            addCriterion("count_two is null");
            return (Criteria) this;
        }

        public Criteria andCountTwoIsNotNull() {
            addCriterion("count_two is not null");
            return (Criteria) this;
        }

        public Criteria andCountTwoEqualTo(BigDecimal value) {
            addCriterion("count_two =", value, "countTwo");
            return (Criteria) this;
        }

        public Criteria andCountTwoNotEqualTo(BigDecimal value) {
            addCriterion("count_two <>", value, "countTwo");
            return (Criteria) this;
        }

        public Criteria andCountTwoGreaterThan(BigDecimal value) {
            addCriterion("count_two >", value, "countTwo");
            return (Criteria) this;
        }

        public Criteria andCountTwoGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("count_two >=", value, "countTwo");
            return (Criteria) this;
        }

        public Criteria andCountTwoLessThan(BigDecimal value) {
            addCriterion("count_two <", value, "countTwo");
            return (Criteria) this;
        }

        public Criteria andCountTwoLessThanOrEqualTo(BigDecimal value) {
            addCriterion("count_two <=", value, "countTwo");
            return (Criteria) this;
        }

        public Criteria andCountTwoIn(List<BigDecimal> values) {
            addCriterion("count_two in", values, "countTwo");
            return (Criteria) this;
        }

        public Criteria andCountTwoNotIn(List<BigDecimal> values) {
            addCriterion("count_two not in", values, "countTwo");
            return (Criteria) this;
        }

        public Criteria andCountTwoBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("count_two between", value1, value2, "countTwo");
            return (Criteria) this;
        }

        public Criteria andCountTwoNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("count_two not between", value1, value2, "countTwo");
            return (Criteria) this;
        }

        public Criteria andComputeSymbolIsNull() {
            addCriterion("compute_symbol is null");
            return (Criteria) this;
        }

        public Criteria andComputeSymbolIsNotNull() {
            addCriterion("compute_symbol is not null");
            return (Criteria) this;
        }

        public Criteria andComputeSymbolEqualTo(String value) {
            addCriterion("compute_symbol =", value, "computeSymbol");
            return (Criteria) this;
        }

        public Criteria andComputeSymbolNotEqualTo(String value) {
            addCriterion("compute_symbol <>", value, "computeSymbol");
            return (Criteria) this;
        }

        public Criteria andComputeSymbolGreaterThan(String value) {
            addCriterion("compute_symbol >", value, "computeSymbol");
            return (Criteria) this;
        }

        public Criteria andComputeSymbolGreaterThanOrEqualTo(String value) {
            addCriterion("compute_symbol >=", value, "computeSymbol");
            return (Criteria) this;
        }

        public Criteria andComputeSymbolLessThan(String value) {
            addCriterion("compute_symbol <", value, "computeSymbol");
            return (Criteria) this;
        }

        public Criteria andComputeSymbolLessThanOrEqualTo(String value) {
            addCriterion("compute_symbol <=", value, "computeSymbol");
            return (Criteria) this;
        }

        public Criteria andComputeSymbolLike(String value) {
            addCriterion("compute_symbol like", value, "computeSymbol");
            return (Criteria) this;
        }

        public Criteria andComputeSymbolNotLike(String value) {
            addCriterion("compute_symbol not like", value, "computeSymbol");
            return (Criteria) this;
        }

        public Criteria andComputeSymbolIn(List<String> values) {
            addCriterion("compute_symbol in", values, "computeSymbol");
            return (Criteria) this;
        }

        public Criteria andComputeSymbolNotIn(List<String> values) {
            addCriterion("compute_symbol not in", values, "computeSymbol");
            return (Criteria) this;
        }

        public Criteria andComputeSymbolBetween(String value1, String value2) {
            addCriterion("compute_symbol between", value1, value2, "computeSymbol");
            return (Criteria) this;
        }

        public Criteria andComputeSymbolNotBetween(String value1, String value2) {
            addCriterion("compute_symbol not between", value1, value2, "computeSymbol");
            return (Criteria) this;
        }

        public Criteria andCalculationResultIsNull() {
            addCriterion("calculation_result is null");
            return (Criteria) this;
        }

        public Criteria andCalculationResultIsNotNull() {
            addCriterion("calculation_result is not null");
            return (Criteria) this;
        }

        public Criteria andCalculationResultEqualTo(BigDecimal value) {
            addCriterion("calculation_result =", value, "calculationResult");
            return (Criteria) this;
        }

        public Criteria andCalculationResultNotEqualTo(BigDecimal value) {
            addCriterion("calculation_result <>", value, "calculationResult");
            return (Criteria) this;
        }

        public Criteria andCalculationResultGreaterThan(BigDecimal value) {
            addCriterion("calculation_result >", value, "calculationResult");
            return (Criteria) this;
        }

        public Criteria andCalculationResultGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("calculation_result >=", value, "calculationResult");
            return (Criteria) this;
        }

        public Criteria andCalculationResultLessThan(BigDecimal value) {
            addCriterion("calculation_result <", value, "calculationResult");
            return (Criteria) this;
        }

        public Criteria andCalculationResultLessThanOrEqualTo(BigDecimal value) {
            addCriterion("calculation_result <=", value, "calculationResult");
            return (Criteria) this;
        }

        public Criteria andCalculationResultIn(List<BigDecimal> values) {
            addCriterion("calculation_result in", values, "calculationResult");
            return (Criteria) this;
        }

        public Criteria andCalculationResultNotIn(List<BigDecimal> values) {
            addCriterion("calculation_result not in", values, "calculationResult");
            return (Criteria) this;
        }

        public Criteria andCalculationResultBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("calculation_result between", value1, value2, "calculationResult");
            return (Criteria) this;
        }

        public Criteria andCalculationResultNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("calculation_result not between", value1, value2, "calculationResult");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNull() {
            addCriterion("creation_time is null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNotNull() {
            addCriterion("creation_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeEqualTo(LocalDateTime value) {
            addCriterion("creation_time =", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotEqualTo(LocalDateTime value) {
            addCriterion("creation_time <>", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThan(LocalDateTime value) {
            addCriterion("creation_time >", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time >=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThan(LocalDateTime value) {
            addCriterion("creation_time <", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time <=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIn(List<LocalDateTime> values) {
            addCriterion("creation_time in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotIn(List<LocalDateTime> values) {
            addCriterion("creation_time not in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time not between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIsNull() {
            addCriterion("create_username is null");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIsNotNull() {
            addCriterion("create_username is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameEqualTo(String value) {
            addCriterion("create_username =", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotEqualTo(String value) {
            addCriterion("create_username <>", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameGreaterThan(String value) {
            addCriterion("create_username >", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("create_username >=", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLessThan(String value) {
            addCriterion("create_username <", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLessThanOrEqualTo(String value) {
            addCriterion("create_username <=", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLike(String value) {
            addCriterion("create_username like", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotLike(String value) {
            addCriterion("create_username not like", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIn(List<String> values) {
            addCriterion("create_username in", values, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotIn(List<String> values) {
            addCriterion("create_username not in", values, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameBetween(String value1, String value2) {
            addCriterion("create_username between", value1, value2, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotBetween(String value1, String value2) {
            addCriterion("create_username not between", value1, value2, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(String value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(String value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(String value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(String value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(String value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLike(String value) {
            addCriterion("create_user_id like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotLike(String value) {
            addCriterion("create_user_id not like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<String> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<String> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(String value1, String value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(String value1, String value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIsNull() {
            addCriterion("last_update_time is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIsNotNull() {
            addCriterion("last_update_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("last_update_time =", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("last_update_time <>", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("last_update_time >", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_update_time >=", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("last_update_time <", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_update_time <=", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("last_update_time in", values, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("last_update_time not in", values, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_update_time between", value1, value2, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_update_time not between", value1, value2, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIsNull() {
            addCriterion("last_update_username is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIsNotNull() {
            addCriterion("last_update_username is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameEqualTo(String value) {
            addCriterion("last_update_username =", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotEqualTo(String value) {
            addCriterion("last_update_username <>", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameGreaterThan(String value) {
            addCriterion("last_update_username >", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("last_update_username >=", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLessThan(String value) {
            addCriterion("last_update_username <", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLessThanOrEqualTo(String value) {
            addCriterion("last_update_username <=", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLike(String value) {
            addCriterion("last_update_username like", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotLike(String value) {
            addCriterion("last_update_username not like", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIn(List<String> values) {
            addCriterion("last_update_username in", values, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotIn(List<String> values) {
            addCriterion("last_update_username not in", values, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameBetween(String value1, String value2) {
            addCriterion("last_update_username between", value1, value2, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotBetween(String value1, String value2) {
            addCriterion("last_update_username not between", value1, value2, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIsNull() {
            addCriterion("last_update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIsNotNull() {
            addCriterion("last_update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdEqualTo(String value) {
            addCriterion("last_update_user_id =", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotEqualTo(String value) {
            addCriterion("last_update_user_id <>", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdGreaterThan(String value) {
            addCriterion("last_update_user_id >", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("last_update_user_id >=", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLessThan(String value) {
            addCriterion("last_update_user_id <", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLessThanOrEqualTo(String value) {
            addCriterion("last_update_user_id <=", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLike(String value) {
            addCriterion("last_update_user_id like", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotLike(String value) {
            addCriterion("last_update_user_id not like", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIn(List<String> values) {
            addCriterion("last_update_user_id in", values, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotIn(List<String> values) {
            addCriterion("last_update_user_id not in", values, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdBetween(String value1, String value2) {
            addCriterion("last_update_user_id between", value1, value2, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotBetween(String value1, String value2) {
            addCriterion("last_update_user_id not between", value1, value2, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIsNull() {
            addCriterion("last_update_version is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIsNotNull() {
            addCriterion("last_update_version is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionEqualTo(Integer value) {
            addCriterion("last_update_version =", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotEqualTo(Integer value) {
            addCriterion("last_update_version <>", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionGreaterThan(Integer value) {
            addCriterion("last_update_version >", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("last_update_version >=", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionLessThan(Integer value) {
            addCriterion("last_update_version <", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionLessThanOrEqualTo(Integer value) {
            addCriterion("last_update_version <=", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIn(List<Integer> values) {
            addCriterion("last_update_version in", values, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotIn(List<Integer> values) {
            addCriterion("last_update_version not in", values, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionBetween(Integer value1, Integer value2) {
            addCriterion("last_update_version between", value1, value2, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("last_update_version not between", value1, value2, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Boolean value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Boolean value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Boolean value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Boolean value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Boolean> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Boolean> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_accounting_management
     *
     * @mbg.generated do_not_delete_during_merge Mon Mar 17 15:11:03 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_accounting_management
     *
     * @mbg.generated Mon Mar 17 15:11:03 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}