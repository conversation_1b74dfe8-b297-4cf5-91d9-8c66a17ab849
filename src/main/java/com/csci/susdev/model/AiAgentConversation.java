package com.csci.susdev.model;

import java.time.LocalDateTime;

public class AiAgentConversation {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ai_agent_conversation.id
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ai_agent_conversation.username
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    private String username;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ai_agent_conversation.app_conversation_id
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    private String appConversationId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ai_agent_conversation.conversation_name
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    private String conversationName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ai_agent_conversation.creation_time
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ai_agent_conversation.create_username
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ai_agent_conversation.create_user_id
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ai_agent_conversation.last_update_time
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ai_agent_conversation.last_update_username
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ai_agent_conversation.last_update_user_id
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ai_agent_conversation.last_update_version
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    private Integer lastUpdateVersion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ai_agent_conversation.is_deleted
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    private Boolean isDeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_ai_agent_conversation.is_manual_update
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    private Boolean isManualUpdate;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ai_agent_conversation.id
     *
     * @return the value of t_ai_agent_conversation.id
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ai_agent_conversation.id
     *
     * @param id the value for t_ai_agent_conversation.id
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ai_agent_conversation.username
     *
     * @return the value of t_ai_agent_conversation.username
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public String getUsername() {
        return username;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ai_agent_conversation.username
     *
     * @param username the value for t_ai_agent_conversation.username
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public void setUsername(String username) {
        this.username = username == null ? null : username.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ai_agent_conversation.app_conversation_id
     *
     * @return the value of t_ai_agent_conversation.app_conversation_id
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public String getAppConversationId() {
        return appConversationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ai_agent_conversation.app_conversation_id
     *
     * @param appConversationId the value for t_ai_agent_conversation.app_conversation_id
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public void setAppConversationId(String appConversationId) {
        this.appConversationId = appConversationId == null ? null : appConversationId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ai_agent_conversation.conversation_name
     *
     * @return the value of t_ai_agent_conversation.conversation_name
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public String getConversationName() {
        return conversationName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ai_agent_conversation.conversation_name
     *
     * @param conversationName the value for t_ai_agent_conversation.conversation_name
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public void setConversationName(String conversationName) {
        this.conversationName = conversationName == null ? null : conversationName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ai_agent_conversation.creation_time
     *
     * @return the value of t_ai_agent_conversation.creation_time
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ai_agent_conversation.creation_time
     *
     * @param creationTime the value for t_ai_agent_conversation.creation_time
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ai_agent_conversation.create_username
     *
     * @return the value of t_ai_agent_conversation.create_username
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ai_agent_conversation.create_username
     *
     * @param createUsername the value for t_ai_agent_conversation.create_username
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ai_agent_conversation.create_user_id
     *
     * @return the value of t_ai_agent_conversation.create_user_id
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ai_agent_conversation.create_user_id
     *
     * @param createUserId the value for t_ai_agent_conversation.create_user_id
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ai_agent_conversation.last_update_time
     *
     * @return the value of t_ai_agent_conversation.last_update_time
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ai_agent_conversation.last_update_time
     *
     * @param lastUpdateTime the value for t_ai_agent_conversation.last_update_time
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ai_agent_conversation.last_update_username
     *
     * @return the value of t_ai_agent_conversation.last_update_username
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ai_agent_conversation.last_update_username
     *
     * @param lastUpdateUsername the value for t_ai_agent_conversation.last_update_username
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ai_agent_conversation.last_update_user_id
     *
     * @return the value of t_ai_agent_conversation.last_update_user_id
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ai_agent_conversation.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_ai_agent_conversation.last_update_user_id
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ai_agent_conversation.last_update_version
     *
     * @return the value of t_ai_agent_conversation.last_update_version
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ai_agent_conversation.last_update_version
     *
     * @param lastUpdateVersion the value for t_ai_agent_conversation.last_update_version
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ai_agent_conversation.is_deleted
     *
     * @return the value of t_ai_agent_conversation.is_deleted
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ai_agent_conversation.is_deleted
     *
     * @param isDeleted the value for t_ai_agent_conversation.is_deleted
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_ai_agent_conversation.is_manual_update
     *
     * @return the value of t_ai_agent_conversation.is_manual_update
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public Boolean getIsManualUpdate() {
        return isManualUpdate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_ai_agent_conversation.is_manual_update
     *
     * @param isManualUpdate the value for t_ai_agent_conversation.is_manual_update
     *
     * @mbg.generated Mon Apr 14 14:12:30 CST 2025
     */
    public void setIsManualUpdate(Boolean isManualUpdate) {
        this.isManualUpdate = isManualUpdate;
    }
}