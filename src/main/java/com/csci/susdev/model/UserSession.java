package com.csci.susdev.model;

import java.time.LocalDateTime;

public class UserSession {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_user_session.id
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_user_session.user_id
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	private String userId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_user_session.username
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	private String username;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_user_session.name
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	private String name;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_user_session.token
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	private String token;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_user_session.access_token
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	private String accessToken;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_user_session.refresh_token
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	private String refreshToken;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_user_session.creation_time
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	private LocalDateTime creationTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_user_session.create_username
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	private String createUsername;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_user_session.create_user_id
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	private String createUserId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_user_session.last_update_time
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	private LocalDateTime lastUpdateTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_user_session.last_update_username
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	private String lastUpdateUsername;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_user_session.last_update_user_id
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	private String lastUpdateUserId;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_user_session.id
	 * @return  the value of t_user_session.id
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_user_session.id
	 * @param id  the value for t_user_session.id
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_user_session.user_id
	 * @return  the value of t_user_session.user_id
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public String getUserId() {
		return userId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_user_session.user_id
	 * @param userId  the value for t_user_session.user_id
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public void setUserId(String userId) {
		this.userId = userId == null ? null : userId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_user_session.username
	 * @return  the value of t_user_session.username
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public String getUsername() {
		return username;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_user_session.username
	 * @param username  the value for t_user_session.username
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public void setUsername(String username) {
		this.username = username == null ? null : username.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_user_session.name
	 * @return  the value of t_user_session.name
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public String getName() {
		return name;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_user_session.name
	 * @param name  the value for t_user_session.name
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public void setName(String name) {
		this.name = name == null ? null : name.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_user_session.token
	 * @return  the value of t_user_session.token
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public String getToken() {
		return token;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_user_session.token
	 * @param token  the value for t_user_session.token
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public void setToken(String token) {
		this.token = token == null ? null : token.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_user_session.access_token
	 * @return  the value of t_user_session.access_token
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public String getAccessToken() {
		return accessToken;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_user_session.access_token
	 * @param accessToken  the value for t_user_session.access_token
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken == null ? null : accessToken.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_user_session.refresh_token
	 * @return  the value of t_user_session.refresh_token
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public String getRefreshToken() {
		return refreshToken;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_user_session.refresh_token
	 * @param refreshToken  the value for t_user_session.refresh_token
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public void setRefreshToken(String refreshToken) {
		this.refreshToken = refreshToken == null ? null : refreshToken.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_user_session.creation_time
	 * @return  the value of t_user_session.creation_time
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public LocalDateTime getCreationTime() {
		return creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_user_session.creation_time
	 * @param creationTime  the value for t_user_session.creation_time
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public void setCreationTime(LocalDateTime creationTime) {
		this.creationTime = creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_user_session.create_username
	 * @return  the value of t_user_session.create_username
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public String getCreateUsername() {
		return createUsername;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_user_session.create_username
	 * @param createUsername  the value for t_user_session.create_username
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public void setCreateUsername(String createUsername) {
		this.createUsername = createUsername == null ? null : createUsername.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_user_session.create_user_id
	 * @return  the value of t_user_session.create_user_id
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public String getCreateUserId() {
		return createUserId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_user_session.create_user_id
	 * @param createUserId  the value for t_user_session.create_user_id
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId == null ? null : createUserId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_user_session.last_update_time
	 * @return  the value of t_user_session.last_update_time
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public LocalDateTime getLastUpdateTime() {
		return lastUpdateTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_user_session.last_update_time
	 * @param lastUpdateTime  the value for t_user_session.last_update_time
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_user_session.last_update_username
	 * @return  the value of t_user_session.last_update_username
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public String getLastUpdateUsername() {
		return lastUpdateUsername;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_user_session.last_update_username
	 * @param lastUpdateUsername  the value for t_user_session.last_update_username
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public void setLastUpdateUsername(String lastUpdateUsername) {
		this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_user_session.last_update_user_id
	 * @return  the value of t_user_session.last_update_user_id
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public String getLastUpdateUserId() {
		return lastUpdateUserId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_user_session.last_update_user_id
	 * @param lastUpdateUserId  the value for t_user_session.last_update_user_id
	 * @mbg.generated  Thu Jun 09 17:07:08 HKT 2022
	 */
	public void setLastUpdateUserId(String lastUpdateUserId) {
		this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
	}
}