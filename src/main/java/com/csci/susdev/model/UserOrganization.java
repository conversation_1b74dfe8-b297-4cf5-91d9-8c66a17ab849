package com.csci.susdev.model;

import java.time.LocalDateTime;

public class UserOrganization {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_user_organization.id
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_user_organization.user_id
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	private String userId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_user_organization.organization_id
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	private String organizationId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_user_organization.creation_time
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	private LocalDateTime creationTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_user_organization.create_username
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	private String createUsername;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_user_organization.create_user_id
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	private String createUserId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_user_organization.last_update_time
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	private LocalDateTime lastUpdateTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_user_organization.last_update_username
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	private String lastUpdateUsername;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_user_organization.last_update_user_id
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	private String lastUpdateUserId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_user_organization.is_deleted
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	private Boolean isDeleted;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_user_organization.id
	 * @return  the value of t_user_organization.id
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_user_organization.id
	 * @param id  the value for t_user_organization.id
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_user_organization.user_id
	 * @return  the value of t_user_organization.user_id
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	public String getUserId() {
		return userId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_user_organization.user_id
	 * @param userId  the value for t_user_organization.user_id
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	public void setUserId(String userId) {
		this.userId = userId == null ? null : userId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_user_organization.organization_id
	 * @return  the value of t_user_organization.organization_id
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	public String getOrganizationId() {
		return organizationId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_user_organization.organization_id
	 * @param organizationId  the value for t_user_organization.organization_id
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	public void setOrganizationId(String organizationId) {
		this.organizationId = organizationId == null ? null : organizationId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_user_organization.creation_time
	 * @return  the value of t_user_organization.creation_time
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	public LocalDateTime getCreationTime() {
		return creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_user_organization.creation_time
	 * @param creationTime  the value for t_user_organization.creation_time
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	public void setCreationTime(LocalDateTime creationTime) {
		this.creationTime = creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_user_organization.create_username
	 * @return  the value of t_user_organization.create_username
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	public String getCreateUsername() {
		return createUsername;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_user_organization.create_username
	 * @param createUsername  the value for t_user_organization.create_username
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	public void setCreateUsername(String createUsername) {
		this.createUsername = createUsername == null ? null : createUsername.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_user_organization.create_user_id
	 * @return  the value of t_user_organization.create_user_id
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	public String getCreateUserId() {
		return createUserId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_user_organization.create_user_id
	 * @param createUserId  the value for t_user_organization.create_user_id
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId == null ? null : createUserId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_user_organization.last_update_time
	 * @return  the value of t_user_organization.last_update_time
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	public LocalDateTime getLastUpdateTime() {
		return lastUpdateTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_user_organization.last_update_time
	 * @param lastUpdateTime  the value for t_user_organization.last_update_time
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_user_organization.last_update_username
	 * @return  the value of t_user_organization.last_update_username
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	public String getLastUpdateUsername() {
		return lastUpdateUsername;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_user_organization.last_update_username
	 * @param lastUpdateUsername  the value for t_user_organization.last_update_username
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	public void setLastUpdateUsername(String lastUpdateUsername) {
		this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_user_organization.last_update_user_id
	 * @return  the value of t_user_organization.last_update_user_id
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	public String getLastUpdateUserId() {
		return lastUpdateUserId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_user_organization.last_update_user_id
	 * @param lastUpdateUserId  the value for t_user_organization.last_update_user_id
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	public void setLastUpdateUserId(String lastUpdateUserId) {
		this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_user_organization.is_deleted
	 * @return  the value of t_user_organization.is_deleted
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	public Boolean getIsDeleted() {
		return isDeleted;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_user_organization.is_deleted
	 * @param isDeleted  the value for t_user_organization.is_deleted
	 * @mbg.generated  Thu Jun 09 17:06:46 HKT 2022
	 */
	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}
}