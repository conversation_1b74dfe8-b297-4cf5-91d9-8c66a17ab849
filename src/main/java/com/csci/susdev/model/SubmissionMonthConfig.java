package com.csci.susdev.model;

import java.time.LocalDateTime;

public class SubmissionMonthConfig {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submission_month_config.id
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submission_month_config.year
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    private Integer year;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submission_month_config.month1
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    private Boolean month1;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submission_month_config.month2
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    private Boolean month2;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submission_month_config.month3
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    private Boolean month3;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submission_month_config.month4
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    private Boolean month4;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submission_month_config.month5
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    private Boolean month5;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submission_month_config.month6
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    private Boolean month6;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submission_month_config.month7
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    private Boolean month7;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submission_month_config.month8
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    private Boolean month8;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submission_month_config.month9
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    private Boolean month9;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submission_month_config.month10
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    private Boolean month10;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submission_month_config.month11
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    private Boolean month11;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submission_month_config.month12
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    private Boolean month12;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submission_month_config.creation_time
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submission_month_config.create_username
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submission_month_config.create_user_id
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submission_month_config.last_update_time
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submission_month_config.last_update_username
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submission_month_config.last_update_user_id
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submission_month_config.last_update_version
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    private Integer lastUpdateVersion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submission_month_config.is_deleted
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    private Boolean isDeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_submission_month_config.id
     *
     * @return the value of t_submission_month_config.id
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_submission_month_config.id
     *
     * @param id the value for t_submission_month_config.id
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_submission_month_config.year
     *
     * @return the value of t_submission_month_config.year
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public Integer getYear() {
        return year;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_submission_month_config.year
     *
     * @param year the value for t_submission_month_config.year
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void setYear(Integer year) {
        this.year = year;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_submission_month_config.month1
     *
     * @return the value of t_submission_month_config.month1
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public Boolean getMonth1() {
        return month1;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_submission_month_config.month1
     *
     * @param month1 the value for t_submission_month_config.month1
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void setMonth1(Boolean month1) {
        this.month1 = month1;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_submission_month_config.month2
     *
     * @return the value of t_submission_month_config.month2
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public Boolean getMonth2() {
        return month2;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_submission_month_config.month2
     *
     * @param month2 the value for t_submission_month_config.month2
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void setMonth2(Boolean month2) {
        this.month2 = month2;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_submission_month_config.month3
     *
     * @return the value of t_submission_month_config.month3
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public Boolean getMonth3() {
        return month3;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_submission_month_config.month3
     *
     * @param month3 the value for t_submission_month_config.month3
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void setMonth3(Boolean month3) {
        this.month3 = month3;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_submission_month_config.month4
     *
     * @return the value of t_submission_month_config.month4
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public Boolean getMonth4() {
        return month4;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_submission_month_config.month4
     *
     * @param month4 the value for t_submission_month_config.month4
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void setMonth4(Boolean month4) {
        this.month4 = month4;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_submission_month_config.month5
     *
     * @return the value of t_submission_month_config.month5
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public Boolean getMonth5() {
        return month5;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_submission_month_config.month5
     *
     * @param month5 the value for t_submission_month_config.month5
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void setMonth5(Boolean month5) {
        this.month5 = month5;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_submission_month_config.month6
     *
     * @return the value of t_submission_month_config.month6
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public Boolean getMonth6() {
        return month6;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_submission_month_config.month6
     *
     * @param month6 the value for t_submission_month_config.month6
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void setMonth6(Boolean month6) {
        this.month6 = month6;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_submission_month_config.month7
     *
     * @return the value of t_submission_month_config.month7
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public Boolean getMonth7() {
        return month7;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_submission_month_config.month7
     *
     * @param month7 the value for t_submission_month_config.month7
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void setMonth7(Boolean month7) {
        this.month7 = month7;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_submission_month_config.month8
     *
     * @return the value of t_submission_month_config.month8
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public Boolean getMonth8() {
        return month8;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_submission_month_config.month8
     *
     * @param month8 the value for t_submission_month_config.month8
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void setMonth8(Boolean month8) {
        this.month8 = month8;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_submission_month_config.month9
     *
     * @return the value of t_submission_month_config.month9
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public Boolean getMonth9() {
        return month9;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_submission_month_config.month9
     *
     * @param month9 the value for t_submission_month_config.month9
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void setMonth9(Boolean month9) {
        this.month9 = month9;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_submission_month_config.month10
     *
     * @return the value of t_submission_month_config.month10
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public Boolean getMonth10() {
        return month10;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_submission_month_config.month10
     *
     * @param month10 the value for t_submission_month_config.month10
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void setMonth10(Boolean month10) {
        this.month10 = month10;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_submission_month_config.month11
     *
     * @return the value of t_submission_month_config.month11
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public Boolean getMonth11() {
        return month11;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_submission_month_config.month11
     *
     * @param month11 the value for t_submission_month_config.month11
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void setMonth11(Boolean month11) {
        this.month11 = month11;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_submission_month_config.month12
     *
     * @return the value of t_submission_month_config.month12
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public Boolean getMonth12() {
        return month12;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_submission_month_config.month12
     *
     * @param month12 the value for t_submission_month_config.month12
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void setMonth12(Boolean month12) {
        this.month12 = month12;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_submission_month_config.creation_time
     *
     * @return the value of t_submission_month_config.creation_time
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_submission_month_config.creation_time
     *
     * @param creationTime the value for t_submission_month_config.creation_time
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_submission_month_config.create_username
     *
     * @return the value of t_submission_month_config.create_username
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_submission_month_config.create_username
     *
     * @param createUsername the value for t_submission_month_config.create_username
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_submission_month_config.create_user_id
     *
     * @return the value of t_submission_month_config.create_user_id
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_submission_month_config.create_user_id
     *
     * @param createUserId the value for t_submission_month_config.create_user_id
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_submission_month_config.last_update_time
     *
     * @return the value of t_submission_month_config.last_update_time
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_submission_month_config.last_update_time
     *
     * @param lastUpdateTime the value for t_submission_month_config.last_update_time
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_submission_month_config.last_update_username
     *
     * @return the value of t_submission_month_config.last_update_username
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_submission_month_config.last_update_username
     *
     * @param lastUpdateUsername the value for t_submission_month_config.last_update_username
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_submission_month_config.last_update_user_id
     *
     * @return the value of t_submission_month_config.last_update_user_id
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_submission_month_config.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_submission_month_config.last_update_user_id
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_submission_month_config.last_update_version
     *
     * @return the value of t_submission_month_config.last_update_version
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_submission_month_config.last_update_version
     *
     * @param lastUpdateVersion the value for t_submission_month_config.last_update_version
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_submission_month_config.is_deleted
     *
     * @return the value of t_submission_month_config.is_deleted
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_submission_month_config.is_deleted
     *
     * @param isDeleted the value for t_submission_month_config.is_deleted
     *
     * @mbg.generated Thu Apr 18 11:23:09 HKT 2024
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }
}