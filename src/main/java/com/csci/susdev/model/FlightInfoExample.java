package com.csci.susdev.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class FlightInfoExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	public FlightInfoExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(String value) {
			addCriterion("id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(String value) {
			addCriterion("id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(String value) {
			addCriterion("id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(String value) {
			addCriterion("id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(String value) {
			addCriterion("id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(String value) {
			addCriterion("id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLike(String value) {
			addCriterion("id like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotLike(String value) {
			addCriterion("id not like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<String> values) {
			addCriterion("id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<String> values) {
			addCriterion("id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(String value1, String value2) {
			addCriterion("id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(String value1, String value2) {
			addCriterion("id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andRecordYearIsNull() {
			addCriterion("record_year is null");
			return (Criteria) this;
		}

		public Criteria andRecordYearIsNotNull() {
			addCriterion("record_year is not null");
			return (Criteria) this;
		}

		public Criteria andRecordYearEqualTo(Integer value) {
			addCriterion("record_year =", value, "recordYear");
			return (Criteria) this;
		}

		public Criteria andRecordYearNotEqualTo(Integer value) {
			addCriterion("record_year <>", value, "recordYear");
			return (Criteria) this;
		}

		public Criteria andRecordYearGreaterThan(Integer value) {
			addCriterion("record_year >", value, "recordYear");
			return (Criteria) this;
		}

		public Criteria andRecordYearGreaterThanOrEqualTo(Integer value) {
			addCriterion("record_year >=", value, "recordYear");
			return (Criteria) this;
		}

		public Criteria andRecordYearLessThan(Integer value) {
			addCriterion("record_year <", value, "recordYear");
			return (Criteria) this;
		}

		public Criteria andRecordYearLessThanOrEqualTo(Integer value) {
			addCriterion("record_year <=", value, "recordYear");
			return (Criteria) this;
		}

		public Criteria andRecordYearIn(List<Integer> values) {
			addCriterion("record_year in", values, "recordYear");
			return (Criteria) this;
		}

		public Criteria andRecordYearNotIn(List<Integer> values) {
			addCriterion("record_year not in", values, "recordYear");
			return (Criteria) this;
		}

		public Criteria andRecordYearBetween(Integer value1, Integer value2) {
			addCriterion("record_year between", value1, value2, "recordYear");
			return (Criteria) this;
		}

		public Criteria andRecordYearNotBetween(Integer value1, Integer value2) {
			addCriterion("record_year not between", value1, value2, "recordYear");
			return (Criteria) this;
		}

		public Criteria andStartPlaceIsNull() {
			addCriterion("start_place is null");
			return (Criteria) this;
		}

		public Criteria andStartPlaceIsNotNull() {
			addCriterion("start_place is not null");
			return (Criteria) this;
		}

		public Criteria andStartPlaceEqualTo(String value) {
			addCriterion("start_place =", value, "startPlace");
			return (Criteria) this;
		}

		public Criteria andStartPlaceNotEqualTo(String value) {
			addCriterion("start_place <>", value, "startPlace");
			return (Criteria) this;
		}

		public Criteria andStartPlaceGreaterThan(String value) {
			addCriterion("start_place >", value, "startPlace");
			return (Criteria) this;
		}

		public Criteria andStartPlaceGreaterThanOrEqualTo(String value) {
			addCriterion("start_place >=", value, "startPlace");
			return (Criteria) this;
		}

		public Criteria andStartPlaceLessThan(String value) {
			addCriterion("start_place <", value, "startPlace");
			return (Criteria) this;
		}

		public Criteria andStartPlaceLessThanOrEqualTo(String value) {
			addCriterion("start_place <=", value, "startPlace");
			return (Criteria) this;
		}

		public Criteria andStartPlaceLike(String value) {
			addCriterion("start_place like", value, "startPlace");
			return (Criteria) this;
		}

		public Criteria andStartPlaceNotLike(String value) {
			addCriterion("start_place not like", value, "startPlace");
			return (Criteria) this;
		}

		public Criteria andStartPlaceIn(List<String> values) {
			addCriterion("start_place in", values, "startPlace");
			return (Criteria) this;
		}

		public Criteria andStartPlaceNotIn(List<String> values) {
			addCriterion("start_place not in", values, "startPlace");
			return (Criteria) this;
		}

		public Criteria andStartPlaceBetween(String value1, String value2) {
			addCriterion("start_place between", value1, value2, "startPlace");
			return (Criteria) this;
		}

		public Criteria andStartPlaceNotBetween(String value1, String value2) {
			addCriterion("start_place not between", value1, value2, "startPlace");
			return (Criteria) this;
		}

		public Criteria andDestinationIsNull() {
			addCriterion("destination is null");
			return (Criteria) this;
		}

		public Criteria andDestinationIsNotNull() {
			addCriterion("destination is not null");
			return (Criteria) this;
		}

		public Criteria andDestinationEqualTo(String value) {
			addCriterion("destination =", value, "destination");
			return (Criteria) this;
		}

		public Criteria andDestinationNotEqualTo(String value) {
			addCriterion("destination <>", value, "destination");
			return (Criteria) this;
		}

		public Criteria andDestinationGreaterThan(String value) {
			addCriterion("destination >", value, "destination");
			return (Criteria) this;
		}

		public Criteria andDestinationGreaterThanOrEqualTo(String value) {
			addCriterion("destination >=", value, "destination");
			return (Criteria) this;
		}

		public Criteria andDestinationLessThan(String value) {
			addCriterion("destination <", value, "destination");
			return (Criteria) this;
		}

		public Criteria andDestinationLessThanOrEqualTo(String value) {
			addCriterion("destination <=", value, "destination");
			return (Criteria) this;
		}

		public Criteria andDestinationLike(String value) {
			addCriterion("destination like", value, "destination");
			return (Criteria) this;
		}

		public Criteria andDestinationNotLike(String value) {
			addCriterion("destination not like", value, "destination");
			return (Criteria) this;
		}

		public Criteria andDestinationIn(List<String> values) {
			addCriterion("destination in", values, "destination");
			return (Criteria) this;
		}

		public Criteria andDestinationNotIn(List<String> values) {
			addCriterion("destination not in", values, "destination");
			return (Criteria) this;
		}

		public Criteria andDestinationBetween(String value1, String value2) {
			addCriterion("destination between", value1, value2, "destination");
			return (Criteria) this;
		}

		public Criteria andDestinationNotBetween(String value1, String value2) {
			addCriterion("destination not between", value1, value2, "destination");
			return (Criteria) this;
		}

		public Criteria andLevelIsNull() {
			addCriterion("level is null");
			return (Criteria) this;
		}

		public Criteria andLevelIsNotNull() {
			addCriterion("level is not null");
			return (Criteria) this;
		}

		public Criteria andLevelEqualTo(String value) {
			addCriterion("level =", value, "level");
			return (Criteria) this;
		}

		public Criteria andLevelNotEqualTo(String value) {
			addCriterion("level <>", value, "level");
			return (Criteria) this;
		}

		public Criteria andLevelGreaterThan(String value) {
			addCriterion("level >", value, "level");
			return (Criteria) this;
		}

		public Criteria andLevelGreaterThanOrEqualTo(String value) {
			addCriterion("level >=", value, "level");
			return (Criteria) this;
		}

		public Criteria andLevelLessThan(String value) {
			addCriterion("level <", value, "level");
			return (Criteria) this;
		}

		public Criteria andLevelLessThanOrEqualTo(String value) {
			addCriterion("level <=", value, "level");
			return (Criteria) this;
		}

		public Criteria andLevelLike(String value) {
			addCriterion("level like", value, "level");
			return (Criteria) this;
		}

		public Criteria andLevelNotLike(String value) {
			addCriterion("level not like", value, "level");
			return (Criteria) this;
		}

		public Criteria andLevelIn(List<String> values) {
			addCriterion("level in", values, "level");
			return (Criteria) this;
		}

		public Criteria andLevelNotIn(List<String> values) {
			addCriterion("level not in", values, "level");
			return (Criteria) this;
		}

		public Criteria andLevelBetween(String value1, String value2) {
			addCriterion("level between", value1, value2, "level");
			return (Criteria) this;
		}

		public Criteria andLevelNotBetween(String value1, String value2) {
			addCriterion("level not between", value1, value2, "level");
			return (Criteria) this;
		}

		public Criteria andTicketTypeIsNull() {
			addCriterion("ticket_type is null");
			return (Criteria) this;
		}

		public Criteria andTicketTypeIsNotNull() {
			addCriterion("ticket_type is not null");
			return (Criteria) this;
		}

		public Criteria andTicketTypeEqualTo(Integer value) {
			addCriterion("ticket_type =", value, "ticketType");
			return (Criteria) this;
		}

		public Criteria andTicketTypeNotEqualTo(Integer value) {
			addCriterion("ticket_type <>", value, "ticketType");
			return (Criteria) this;
		}

		public Criteria andTicketTypeGreaterThan(Integer value) {
			addCriterion("ticket_type >", value, "ticketType");
			return (Criteria) this;
		}

		public Criteria andTicketTypeGreaterThanOrEqualTo(Integer value) {
			addCriterion("ticket_type >=", value, "ticketType");
			return (Criteria) this;
		}

		public Criteria andTicketTypeLessThan(Integer value) {
			addCriterion("ticket_type <", value, "ticketType");
			return (Criteria) this;
		}

		public Criteria andTicketTypeLessThanOrEqualTo(Integer value) {
			addCriterion("ticket_type <=", value, "ticketType");
			return (Criteria) this;
		}

		public Criteria andTicketTypeIn(List<Integer> values) {
			addCriterion("ticket_type in", values, "ticketType");
			return (Criteria) this;
		}

		public Criteria andTicketTypeNotIn(List<Integer> values) {
			addCriterion("ticket_type not in", values, "ticketType");
			return (Criteria) this;
		}

		public Criteria andTicketTypeBetween(Integer value1, Integer value2) {
			addCriterion("ticket_type between", value1, value2, "ticketType");
			return (Criteria) this;
		}

		public Criteria andTicketTypeNotBetween(Integer value1, Integer value2) {
			addCriterion("ticket_type not between", value1, value2, "ticketType");
			return (Criteria) this;
		}

		public Criteria andPersonCountIsNull() {
			addCriterion("person_count is null");
			return (Criteria) this;
		}

		public Criteria andPersonCountIsNotNull() {
			addCriterion("person_count is not null");
			return (Criteria) this;
		}

		public Criteria andPersonCountEqualTo(Integer value) {
			addCriterion("person_count =", value, "personCount");
			return (Criteria) this;
		}

		public Criteria andPersonCountNotEqualTo(Integer value) {
			addCriterion("person_count <>", value, "personCount");
			return (Criteria) this;
		}

		public Criteria andPersonCountGreaterThan(Integer value) {
			addCriterion("person_count >", value, "personCount");
			return (Criteria) this;
		}

		public Criteria andPersonCountGreaterThanOrEqualTo(Integer value) {
			addCriterion("person_count >=", value, "personCount");
			return (Criteria) this;
		}

		public Criteria andPersonCountLessThan(Integer value) {
			addCriterion("person_count <", value, "personCount");
			return (Criteria) this;
		}

		public Criteria andPersonCountLessThanOrEqualTo(Integer value) {
			addCriterion("person_count <=", value, "personCount");
			return (Criteria) this;
		}

		public Criteria andPersonCountIn(List<Integer> values) {
			addCriterion("person_count in", values, "personCount");
			return (Criteria) this;
		}

		public Criteria andPersonCountNotIn(List<Integer> values) {
			addCriterion("person_count not in", values, "personCount");
			return (Criteria) this;
		}

		public Criteria andPersonCountBetween(Integer value1, Integer value2) {
			addCriterion("person_count between", value1, value2, "personCount");
			return (Criteria) this;
		}

		public Criteria andPersonCountNotBetween(Integer value1, Integer value2) {
			addCriterion("person_count not between", value1, value2, "personCount");
			return (Criteria) this;
		}

		public Criteria andFlightDistanceIsNull() {
			addCriterion("flight_distance is null");
			return (Criteria) this;
		}

		public Criteria andFlightDistanceIsNotNull() {
			addCriterion("flight_distance is not null");
			return (Criteria) this;
		}

		public Criteria andFlightDistanceEqualTo(BigDecimal value) {
			addCriterion("flight_distance =", value, "flightDistance");
			return (Criteria) this;
		}

		public Criteria andFlightDistanceNotEqualTo(BigDecimal value) {
			addCriterion("flight_distance <>", value, "flightDistance");
			return (Criteria) this;
		}

		public Criteria andFlightDistanceGreaterThan(BigDecimal value) {
			addCriterion("flight_distance >", value, "flightDistance");
			return (Criteria) this;
		}

		public Criteria andFlightDistanceGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("flight_distance >=", value, "flightDistance");
			return (Criteria) this;
		}

		public Criteria andFlightDistanceLessThan(BigDecimal value) {
			addCriterion("flight_distance <", value, "flightDistance");
			return (Criteria) this;
		}

		public Criteria andFlightDistanceLessThanOrEqualTo(BigDecimal value) {
			addCriterion("flight_distance <=", value, "flightDistance");
			return (Criteria) this;
		}

		public Criteria andFlightDistanceIn(List<BigDecimal> values) {
			addCriterion("flight_distance in", values, "flightDistance");
			return (Criteria) this;
		}

		public Criteria andFlightDistanceNotIn(List<BigDecimal> values) {
			addCriterion("flight_distance not in", values, "flightDistance");
			return (Criteria) this;
		}

		public Criteria andFlightDistanceBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("flight_distance between", value1, value2, "flightDistance");
			return (Criteria) this;
		}

		public Criteria andFlightDistanceNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("flight_distance not between", value1, value2, "flightDistance");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionIsNull() {
			addCriterion("carbon_emission is null");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionIsNotNull() {
			addCriterion("carbon_emission is not null");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionEqualTo(BigDecimal value) {
			addCriterion("carbon_emission =", value, "carbonEmission");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionNotEqualTo(BigDecimal value) {
			addCriterion("carbon_emission <>", value, "carbonEmission");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionGreaterThan(BigDecimal value) {
			addCriterion("carbon_emission >", value, "carbonEmission");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("carbon_emission >=", value, "carbonEmission");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionLessThan(BigDecimal value) {
			addCriterion("carbon_emission <", value, "carbonEmission");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionLessThanOrEqualTo(BigDecimal value) {
			addCriterion("carbon_emission <=", value, "carbonEmission");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionIn(List<BigDecimal> values) {
			addCriterion("carbon_emission in", values, "carbonEmission");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionNotIn(List<BigDecimal> values) {
			addCriterion("carbon_emission not in", values, "carbonEmission");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("carbon_emission between", value1, value2, "carbonEmission");
			return (Criteria) this;
		}

		public Criteria andCarbonEmissionNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("carbon_emission not between", value1, value2, "carbonEmission");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIsNull() {
			addCriterion("creation_time is null");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIsNotNull() {
			addCriterion("creation_time is not null");
			return (Criteria) this;
		}

		public Criteria andCreationTimeEqualTo(LocalDateTime value) {
			addCriterion("creation_time =", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotEqualTo(LocalDateTime value) {
			addCriterion("creation_time <>", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeGreaterThan(LocalDateTime value) {
			addCriterion("creation_time >", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("creation_time >=", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeLessThan(LocalDateTime value) {
			addCriterion("creation_time <", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("creation_time <=", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIn(List<LocalDateTime> values) {
			addCriterion("creation_time in", values, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotIn(List<LocalDateTime> values) {
			addCriterion("creation_time not in", values, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("creation_time between", value1, value2, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("creation_time not between", value1, value2, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameIsNull() {
			addCriterion("create_username is null");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameIsNotNull() {
			addCriterion("create_username is not null");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameEqualTo(String value) {
			addCriterion("create_username =", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotEqualTo(String value) {
			addCriterion("create_username <>", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameGreaterThan(String value) {
			addCriterion("create_username >", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameGreaterThanOrEqualTo(String value) {
			addCriterion("create_username >=", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameLessThan(String value) {
			addCriterion("create_username <", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameLessThanOrEqualTo(String value) {
			addCriterion("create_username <=", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameLike(String value) {
			addCriterion("create_username like", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotLike(String value) {
			addCriterion("create_username not like", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameIn(List<String> values) {
			addCriterion("create_username in", values, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotIn(List<String> values) {
			addCriterion("create_username not in", values, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameBetween(String value1, String value2) {
			addCriterion("create_username between", value1, value2, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotBetween(String value1, String value2) {
			addCriterion("create_username not between", value1, value2, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdIsNull() {
			addCriterion("create_user_id is null");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdIsNotNull() {
			addCriterion("create_user_id is not null");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdEqualTo(String value) {
			addCriterion("create_user_id =", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdNotEqualTo(String value) {
			addCriterion("create_user_id <>", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdGreaterThan(String value) {
			addCriterion("create_user_id >", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdGreaterThanOrEqualTo(String value) {
			addCriterion("create_user_id >=", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdLessThan(String value) {
			addCriterion("create_user_id <", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdLessThanOrEqualTo(String value) {
			addCriterion("create_user_id <=", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdLike(String value) {
			addCriterion("create_user_id like", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdNotLike(String value) {
			addCriterion("create_user_id not like", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdIn(List<String> values) {
			addCriterion("create_user_id in", values, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdNotIn(List<String> values) {
			addCriterion("create_user_id not in", values, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdBetween(String value1, String value2) {
			addCriterion("create_user_id between", value1, value2, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdNotBetween(String value1, String value2) {
			addCriterion("create_user_id not between", value1, value2, "createUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIsNull() {
			addCriterion("last_update_time is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIsNotNull() {
			addCriterion("last_update_time is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeEqualTo(LocalDateTime value) {
			addCriterion("last_update_time =", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotEqualTo(LocalDateTime value) {
			addCriterion("last_update_time <>", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeGreaterThan(LocalDateTime value) {
			addCriterion("last_update_time >", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("last_update_time >=", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeLessThan(LocalDateTime value) {
			addCriterion("last_update_time <", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("last_update_time <=", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIn(List<LocalDateTime> values) {
			addCriterion("last_update_time in", values, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotIn(List<LocalDateTime> values) {
			addCriterion("last_update_time not in", values, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("last_update_time between", value1, value2, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("last_update_time not between", value1, value2, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameIsNull() {
			addCriterion("last_update_username is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameIsNotNull() {
			addCriterion("last_update_username is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameEqualTo(String value) {
			addCriterion("last_update_username =", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotEqualTo(String value) {
			addCriterion("last_update_username <>", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameGreaterThan(String value) {
			addCriterion("last_update_username >", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameGreaterThanOrEqualTo(String value) {
			addCriterion("last_update_username >=", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameLessThan(String value) {
			addCriterion("last_update_username <", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameLessThanOrEqualTo(String value) {
			addCriterion("last_update_username <=", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameLike(String value) {
			addCriterion("last_update_username like", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotLike(String value) {
			addCriterion("last_update_username not like", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameIn(List<String> values) {
			addCriterion("last_update_username in", values, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotIn(List<String> values) {
			addCriterion("last_update_username not in", values, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameBetween(String value1, String value2) {
			addCriterion("last_update_username between", value1, value2, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotBetween(String value1, String value2) {
			addCriterion("last_update_username not between", value1, value2, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdIsNull() {
			addCriterion("last_update_user_id is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdIsNotNull() {
			addCriterion("last_update_user_id is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdEqualTo(String value) {
			addCriterion("last_update_user_id =", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdNotEqualTo(String value) {
			addCriterion("last_update_user_id <>", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdGreaterThan(String value) {
			addCriterion("last_update_user_id >", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdGreaterThanOrEqualTo(String value) {
			addCriterion("last_update_user_id >=", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdLessThan(String value) {
			addCriterion("last_update_user_id <", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdLessThanOrEqualTo(String value) {
			addCriterion("last_update_user_id <=", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdLike(String value) {
			addCriterion("last_update_user_id like", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdNotLike(String value) {
			addCriterion("last_update_user_id not like", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdIn(List<String> values) {
			addCriterion("last_update_user_id in", values, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdNotIn(List<String> values) {
			addCriterion("last_update_user_id not in", values, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdBetween(String value1, String value2) {
			addCriterion("last_update_user_id between", value1, value2, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdNotBetween(String value1, String value2) {
			addCriterion("last_update_user_id not between", value1, value2, "lastUpdateUserId");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table t_flight_info
	 * @mbg.generated  Thu Jun 08 11:34:14 HKT 2023
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_flight_info
     *
     * @mbg.generated do_not_delete_during_merge Wed Jun 07 14:32:18 HKT 2023
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}