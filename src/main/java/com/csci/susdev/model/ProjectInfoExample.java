package com.csci.susdev.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProjectInfoExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_project_info
	 * @mbg.generated  Wed Apr 13 18:03:43 HKT 2022
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_project_info
	 * @mbg.generated  Wed Apr 13 18:03:43 HKT 2022
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_project_info
	 * @mbg.generated  Wed Apr 13 18:03:43 HKT 2022
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_project_info
	 * @mbg.generated  Wed Apr 13 18:03:43 HKT 2022
	 */
	public ProjectInfoExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_project_info
	 * @mbg.generated  Wed Apr 13 18:03:43 HKT 2022
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_project_info
	 * @mbg.generated  Wed Apr 13 18:03:43 HKT 2022
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_project_info
	 * @mbg.generated  Wed Apr 13 18:03:43 HKT 2022
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_project_info
	 * @mbg.generated  Wed Apr 13 18:03:43 HKT 2022
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_project_info
	 * @mbg.generated  Wed Apr 13 18:03:43 HKT 2022
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_project_info
	 * @mbg.generated  Wed Apr 13 18:03:43 HKT 2022
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_project_info
	 * @mbg.generated  Wed Apr 13 18:03:43 HKT 2022
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_project_info
	 * @mbg.generated  Wed Apr 13 18:03:43 HKT 2022
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_project_info
	 * @mbg.generated  Wed Apr 13 18:03:43 HKT 2022
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_project_info
	 * @mbg.generated  Wed Apr 13 18:03:43 HKT 2022
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table t_project_info
	 * @mbg.generated  Wed Apr 13 18:03:43 HKT 2022
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(String value) {
			addCriterion("id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(String value) {
			addCriterion("id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(String value) {
			addCriterion("id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(String value) {
			addCriterion("id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(String value) {
			addCriterion("id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(String value) {
			addCriterion("id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLike(String value) {
			addCriterion("id like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotLike(String value) {
			addCriterion("id not like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<String> values) {
			addCriterion("id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<String> values) {
			addCriterion("id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(String value1, String value2) {
			addCriterion("id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(String value1, String value2) {
			addCriterion("id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andCodeIsNull() {
			addCriterion("code is null");
			return (Criteria) this;
		}

		public Criteria andCodeIsNotNull() {
			addCriterion("code is not null");
			return (Criteria) this;
		}

		public Criteria andCodeEqualTo(String value) {
			addCriterion("code =", value, "code");
			return (Criteria) this;
		}

		public Criteria andCodeNotEqualTo(String value) {
			addCriterion("code <>", value, "code");
			return (Criteria) this;
		}

		public Criteria andCodeGreaterThan(String value) {
			addCriterion("code >", value, "code");
			return (Criteria) this;
		}

		public Criteria andCodeGreaterThanOrEqualTo(String value) {
			addCriterion("code >=", value, "code");
			return (Criteria) this;
		}

		public Criteria andCodeLessThan(String value) {
			addCriterion("code <", value, "code");
			return (Criteria) this;
		}

		public Criteria andCodeLessThanOrEqualTo(String value) {
			addCriterion("code <=", value, "code");
			return (Criteria) this;
		}

		public Criteria andCodeLike(String value) {
			addCriterion("code like", value, "code");
			return (Criteria) this;
		}

		public Criteria andCodeNotLike(String value) {
			addCriterion("code not like", value, "code");
			return (Criteria) this;
		}

		public Criteria andCodeIn(List<String> values) {
			addCriterion("code in", values, "code");
			return (Criteria) this;
		}

		public Criteria andCodeNotIn(List<String> values) {
			addCriterion("code not in", values, "code");
			return (Criteria) this;
		}

		public Criteria andCodeBetween(String value1, String value2) {
			addCriterion("code between", value1, value2, "code");
			return (Criteria) this;
		}

		public Criteria andCodeNotBetween(String value1, String value2) {
			addCriterion("code not between", value1, value2, "code");
			return (Criteria) this;
		}

		public Criteria andTitleIsNull() {
			addCriterion("title is null");
			return (Criteria) this;
		}

		public Criteria andTitleIsNotNull() {
			addCriterion("title is not null");
			return (Criteria) this;
		}

		public Criteria andTitleEqualTo(String value) {
			addCriterion("title =", value, "title");
			return (Criteria) this;
		}

		public Criteria andTitleNotEqualTo(String value) {
			addCriterion("title <>", value, "title");
			return (Criteria) this;
		}

		public Criteria andTitleGreaterThan(String value) {
			addCriterion("title >", value, "title");
			return (Criteria) this;
		}

		public Criteria andTitleGreaterThanOrEqualTo(String value) {
			addCriterion("title >=", value, "title");
			return (Criteria) this;
		}

		public Criteria andTitleLessThan(String value) {
			addCriterion("title <", value, "title");
			return (Criteria) this;
		}

		public Criteria andTitleLessThanOrEqualTo(String value) {
			addCriterion("title <=", value, "title");
			return (Criteria) this;
		}

		public Criteria andTitleLike(String value) {
			addCriterion("title like", value, "title");
			return (Criteria) this;
		}

		public Criteria andTitleNotLike(String value) {
			addCriterion("title not like", value, "title");
			return (Criteria) this;
		}

		public Criteria andTitleIn(List<String> values) {
			addCriterion("title in", values, "title");
			return (Criteria) this;
		}

		public Criteria andTitleNotIn(List<String> values) {
			addCriterion("title not in", values, "title");
			return (Criteria) this;
		}

		public Criteria andTitleBetween(String value1, String value2) {
			addCriterion("title between", value1, value2, "title");
			return (Criteria) this;
		}

		public Criteria andTitleNotBetween(String value1, String value2) {
			addCriterion("title not between", value1, value2, "title");
			return (Criteria) this;
		}

		public Criteria andAddressIsNull() {
			addCriterion("address is null");
			return (Criteria) this;
		}

		public Criteria andAddressIsNotNull() {
			addCriterion("address is not null");
			return (Criteria) this;
		}

		public Criteria andAddressEqualTo(String value) {
			addCriterion("address =", value, "address");
			return (Criteria) this;
		}

		public Criteria andAddressNotEqualTo(String value) {
			addCriterion("address <>", value, "address");
			return (Criteria) this;
		}

		public Criteria andAddressGreaterThan(String value) {
			addCriterion("address >", value, "address");
			return (Criteria) this;
		}

		public Criteria andAddressGreaterThanOrEqualTo(String value) {
			addCriterion("address >=", value, "address");
			return (Criteria) this;
		}

		public Criteria andAddressLessThan(String value) {
			addCriterion("address <", value, "address");
			return (Criteria) this;
		}

		public Criteria andAddressLessThanOrEqualTo(String value) {
			addCriterion("address <=", value, "address");
			return (Criteria) this;
		}

		public Criteria andAddressLike(String value) {
			addCriterion("address like", value, "address");
			return (Criteria) this;
		}

		public Criteria andAddressNotLike(String value) {
			addCriterion("address not like", value, "address");
			return (Criteria) this;
		}

		public Criteria andAddressIn(List<String> values) {
			addCriterion("address in", values, "address");
			return (Criteria) this;
		}

		public Criteria andAddressNotIn(List<String> values) {
			addCriterion("address not in", values, "address");
			return (Criteria) this;
		}

		public Criteria andAddressBetween(String value1, String value2) {
			addCriterion("address between", value1, value2, "address");
			return (Criteria) this;
		}

		public Criteria andAddressNotBetween(String value1, String value2) {
			addCriterion("address not between", value1, value2, "address");
			return (Criteria) this;
		}

		public Criteria andLatitudeIsNull() {
			addCriterion("latitude is null");
			return (Criteria) this;
		}

		public Criteria andLatitudeIsNotNull() {
			addCriterion("latitude is not null");
			return (Criteria) this;
		}

		public Criteria andLatitudeEqualTo(String value) {
			addCriterion("latitude =", value, "latitude");
			return (Criteria) this;
		}

		public Criteria andLatitudeNotEqualTo(String value) {
			addCriterion("latitude <>", value, "latitude");
			return (Criteria) this;
		}

		public Criteria andLatitudeGreaterThan(String value) {
			addCriterion("latitude >", value, "latitude");
			return (Criteria) this;
		}

		public Criteria andLatitudeGreaterThanOrEqualTo(String value) {
			addCriterion("latitude >=", value, "latitude");
			return (Criteria) this;
		}

		public Criteria andLatitudeLessThan(String value) {
			addCriterion("latitude <", value, "latitude");
			return (Criteria) this;
		}

		public Criteria andLatitudeLessThanOrEqualTo(String value) {
			addCriterion("latitude <=", value, "latitude");
			return (Criteria) this;
		}

		public Criteria andLatitudeLike(String value) {
			addCriterion("latitude like", value, "latitude");
			return (Criteria) this;
		}

		public Criteria andLatitudeNotLike(String value) {
			addCriterion("latitude not like", value, "latitude");
			return (Criteria) this;
		}

		public Criteria andLatitudeIn(List<String> values) {
			addCriterion("latitude in", values, "latitude");
			return (Criteria) this;
		}

		public Criteria andLatitudeNotIn(List<String> values) {
			addCriterion("latitude not in", values, "latitude");
			return (Criteria) this;
		}

		public Criteria andLatitudeBetween(String value1, String value2) {
			addCriterion("latitude between", value1, value2, "latitude");
			return (Criteria) this;
		}

		public Criteria andLatitudeNotBetween(String value1, String value2) {
			addCriterion("latitude not between", value1, value2, "latitude");
			return (Criteria) this;
		}

		public Criteria andLongiduteIsNull() {
			addCriterion("longidute is null");
			return (Criteria) this;
		}

		public Criteria andLongiduteIsNotNull() {
			addCriterion("longidute is not null");
			return (Criteria) this;
		}

		public Criteria andLongiduteEqualTo(String value) {
			addCriterion("longidute =", value, "longidute");
			return (Criteria) this;
		}

		public Criteria andLongiduteNotEqualTo(String value) {
			addCriterion("longidute <>", value, "longidute");
			return (Criteria) this;
		}

		public Criteria andLongiduteGreaterThan(String value) {
			addCriterion("longidute >", value, "longidute");
			return (Criteria) this;
		}

		public Criteria andLongiduteGreaterThanOrEqualTo(String value) {
			addCriterion("longidute >=", value, "longidute");
			return (Criteria) this;
		}

		public Criteria andLongiduteLessThan(String value) {
			addCriterion("longidute <", value, "longidute");
			return (Criteria) this;
		}

		public Criteria andLongiduteLessThanOrEqualTo(String value) {
			addCriterion("longidute <=", value, "longidute");
			return (Criteria) this;
		}

		public Criteria andLongiduteLike(String value) {
			addCriterion("longidute like", value, "longidute");
			return (Criteria) this;
		}

		public Criteria andLongiduteNotLike(String value) {
			addCriterion("longidute not like", value, "longidute");
			return (Criteria) this;
		}

		public Criteria andLongiduteIn(List<String> values) {
			addCriterion("longidute in", values, "longidute");
			return (Criteria) this;
		}

		public Criteria andLongiduteNotIn(List<String> values) {
			addCriterion("longidute not in", values, "longidute");
			return (Criteria) this;
		}

		public Criteria andLongiduteBetween(String value1, String value2) {
			addCriterion("longidute between", value1, value2, "longidute");
			return (Criteria) this;
		}

		public Criteria andLongiduteNotBetween(String value1, String value2) {
			addCriterion("longidute not between", value1, value2, "longidute");
			return (Criteria) this;
		}

		public Criteria andCurrencyIsNull() {
			addCriterion("currency is null");
			return (Criteria) this;
		}

		public Criteria andCurrencyIsNotNull() {
			addCriterion("currency is not null");
			return (Criteria) this;
		}

		public Criteria andCurrencyEqualTo(String value) {
			addCriterion("currency =", value, "currency");
			return (Criteria) this;
		}

		public Criteria andCurrencyNotEqualTo(String value) {
			addCriterion("currency <>", value, "currency");
			return (Criteria) this;
		}

		public Criteria andCurrencyGreaterThan(String value) {
			addCriterion("currency >", value, "currency");
			return (Criteria) this;
		}

		public Criteria andCurrencyGreaterThanOrEqualTo(String value) {
			addCriterion("currency >=", value, "currency");
			return (Criteria) this;
		}

		public Criteria andCurrencyLessThan(String value) {
			addCriterion("currency <", value, "currency");
			return (Criteria) this;
		}

		public Criteria andCurrencyLessThanOrEqualTo(String value) {
			addCriterion("currency <=", value, "currency");
			return (Criteria) this;
		}

		public Criteria andCurrencyLike(String value) {
			addCriterion("currency like", value, "currency");
			return (Criteria) this;
		}

		public Criteria andCurrencyNotLike(String value) {
			addCriterion("currency not like", value, "currency");
			return (Criteria) this;
		}

		public Criteria andCurrencyIn(List<String> values) {
			addCriterion("currency in", values, "currency");
			return (Criteria) this;
		}

		public Criteria andCurrencyNotIn(List<String> values) {
			addCriterion("currency not in", values, "currency");
			return (Criteria) this;
		}

		public Criteria andCurrencyBetween(String value1, String value2) {
			addCriterion("currency between", value1, value2, "currency");
			return (Criteria) this;
		}

		public Criteria andCurrencyNotBetween(String value1, String value2) {
			addCriterion("currency not between", value1, value2, "currency");
			return (Criteria) this;
		}

		public Criteria andDivisionIsNull() {
			addCriterion("division is null");
			return (Criteria) this;
		}

		public Criteria andDivisionIsNotNull() {
			addCriterion("division is not null");
			return (Criteria) this;
		}

		public Criteria andDivisionEqualTo(String value) {
			addCriterion("division =", value, "division");
			return (Criteria) this;
		}

		public Criteria andDivisionNotEqualTo(String value) {
			addCriterion("division <>", value, "division");
			return (Criteria) this;
		}

		public Criteria andDivisionGreaterThan(String value) {
			addCriterion("division >", value, "division");
			return (Criteria) this;
		}

		public Criteria andDivisionGreaterThanOrEqualTo(String value) {
			addCriterion("division >=", value, "division");
			return (Criteria) this;
		}

		public Criteria andDivisionLessThan(String value) {
			addCriterion("division <", value, "division");
			return (Criteria) this;
		}

		public Criteria andDivisionLessThanOrEqualTo(String value) {
			addCriterion("division <=", value, "division");
			return (Criteria) this;
		}

		public Criteria andDivisionLike(String value) {
			addCriterion("division like", value, "division");
			return (Criteria) this;
		}

		public Criteria andDivisionNotLike(String value) {
			addCriterion("division not like", value, "division");
			return (Criteria) this;
		}

		public Criteria andDivisionIn(List<String> values) {
			addCriterion("division in", values, "division");
			return (Criteria) this;
		}

		public Criteria andDivisionNotIn(List<String> values) {
			addCriterion("division not in", values, "division");
			return (Criteria) this;
		}

		public Criteria andDivisionBetween(String value1, String value2) {
			addCriterion("division between", value1, value2, "division");
			return (Criteria) this;
		}

		public Criteria andDivisionNotBetween(String value1, String value2) {
			addCriterion("division not between", value1, value2, "division");
			return (Criteria) this;
		}

		public Criteria andRegionIsNull() {
			addCriterion("region is null");
			return (Criteria) this;
		}

		public Criteria andRegionIsNotNull() {
			addCriterion("region is not null");
			return (Criteria) this;
		}

		public Criteria andRegionEqualTo(String value) {
			addCriterion("region =", value, "region");
			return (Criteria) this;
		}

		public Criteria andRegionNotEqualTo(String value) {
			addCriterion("region <>", value, "region");
			return (Criteria) this;
		}

		public Criteria andRegionGreaterThan(String value) {
			addCriterion("region >", value, "region");
			return (Criteria) this;
		}

		public Criteria andRegionGreaterThanOrEqualTo(String value) {
			addCriterion("region >=", value, "region");
			return (Criteria) this;
		}

		public Criteria andRegionLessThan(String value) {
			addCriterion("region <", value, "region");
			return (Criteria) this;
		}

		public Criteria andRegionLessThanOrEqualTo(String value) {
			addCriterion("region <=", value, "region");
			return (Criteria) this;
		}

		public Criteria andRegionLike(String value) {
			addCriterion("region like", value, "region");
			return (Criteria) this;
		}

		public Criteria andRegionNotLike(String value) {
			addCriterion("region not like", value, "region");
			return (Criteria) this;
		}

		public Criteria andRegionIn(List<String> values) {
			addCriterion("region in", values, "region");
			return (Criteria) this;
		}

		public Criteria andRegionNotIn(List<String> values) {
			addCriterion("region not in", values, "region");
			return (Criteria) this;
		}

		public Criteria andRegionBetween(String value1, String value2) {
			addCriterion("region between", value1, value2, "region");
			return (Criteria) this;
		}

		public Criteria andRegionNotBetween(String value1, String value2) {
			addCriterion("region not between", value1, value2, "region");
			return (Criteria) this;
		}

		public Criteria andPowerSupplyIsNull() {
			addCriterion("power_supply is null");
			return (Criteria) this;
		}

		public Criteria andPowerSupplyIsNotNull() {
			addCriterion("power_supply is not null");
			return (Criteria) this;
		}

		public Criteria andPowerSupplyEqualTo(String value) {
			addCriterion("power_supply =", value, "powerSupply");
			return (Criteria) this;
		}

		public Criteria andPowerSupplyNotEqualTo(String value) {
			addCriterion("power_supply <>", value, "powerSupply");
			return (Criteria) this;
		}

		public Criteria andPowerSupplyGreaterThan(String value) {
			addCriterion("power_supply >", value, "powerSupply");
			return (Criteria) this;
		}

		public Criteria andPowerSupplyGreaterThanOrEqualTo(String value) {
			addCriterion("power_supply >=", value, "powerSupply");
			return (Criteria) this;
		}

		public Criteria andPowerSupplyLessThan(String value) {
			addCriterion("power_supply <", value, "powerSupply");
			return (Criteria) this;
		}

		public Criteria andPowerSupplyLessThanOrEqualTo(String value) {
			addCriterion("power_supply <=", value, "powerSupply");
			return (Criteria) this;
		}

		public Criteria andPowerSupplyLike(String value) {
			addCriterion("power_supply like", value, "powerSupply");
			return (Criteria) this;
		}

		public Criteria andPowerSupplyNotLike(String value) {
			addCriterion("power_supply not like", value, "powerSupply");
			return (Criteria) this;
		}

		public Criteria andPowerSupplyIn(List<String> values) {
			addCriterion("power_supply in", values, "powerSupply");
			return (Criteria) this;
		}

		public Criteria andPowerSupplyNotIn(List<String> values) {
			addCriterion("power_supply not in", values, "powerSupply");
			return (Criteria) this;
		}

		public Criteria andPowerSupplyBetween(String value1, String value2) {
			addCriterion("power_supply between", value1, value2, "powerSupply");
			return (Criteria) this;
		}

		public Criteria andPowerSupplyNotBetween(String value1, String value2) {
			addCriterion("power_supply not between", value1, value2, "powerSupply");
			return (Criteria) this;
		}

		public Criteria andStartDateIsNull() {
			addCriterion("start_date is null");
			return (Criteria) this;
		}

		public Criteria andStartDateIsNotNull() {
			addCriterion("start_date is not null");
			return (Criteria) this;
		}

		public Criteria andStartDateEqualTo(Date value) {
			addCriterion("start_date =", value, "startDate");
			return (Criteria) this;
		}

		public Criteria andStartDateNotEqualTo(Date value) {
			addCriterion("start_date <>", value, "startDate");
			return (Criteria) this;
		}

		public Criteria andStartDateGreaterThan(Date value) {
			addCriterion("start_date >", value, "startDate");
			return (Criteria) this;
		}

		public Criteria andStartDateGreaterThanOrEqualTo(Date value) {
			addCriterion("start_date >=", value, "startDate");
			return (Criteria) this;
		}

		public Criteria andStartDateLessThan(Date value) {
			addCriterion("start_date <", value, "startDate");
			return (Criteria) this;
		}

		public Criteria andStartDateLessThanOrEqualTo(Date value) {
			addCriterion("start_date <=", value, "startDate");
			return (Criteria) this;
		}

		public Criteria andStartDateIn(List<Date> values) {
			addCriterion("start_date in", values, "startDate");
			return (Criteria) this;
		}

		public Criteria andStartDateNotIn(List<Date> values) {
			addCriterion("start_date not in", values, "startDate");
			return (Criteria) this;
		}

		public Criteria andStartDateBetween(Date value1, Date value2) {
			addCriterion("start_date between", value1, value2, "startDate");
			return (Criteria) this;
		}

		public Criteria andStartDateNotBetween(Date value1, Date value2) {
			addCriterion("start_date not between", value1, value2, "startDate");
			return (Criteria) this;
		}

		public Criteria andEndDateIsNull() {
			addCriterion("end_date is null");
			return (Criteria) this;
		}

		public Criteria andEndDateIsNotNull() {
			addCriterion("end_date is not null");
			return (Criteria) this;
		}

		public Criteria andEndDateEqualTo(Date value) {
			addCriterion("end_date =", value, "endDate");
			return (Criteria) this;
		}

		public Criteria andEndDateNotEqualTo(Date value) {
			addCriterion("end_date <>", value, "endDate");
			return (Criteria) this;
		}

		public Criteria andEndDateGreaterThan(Date value) {
			addCriterion("end_date >", value, "endDate");
			return (Criteria) this;
		}

		public Criteria andEndDateGreaterThanOrEqualTo(Date value) {
			addCriterion("end_date >=", value, "endDate");
			return (Criteria) this;
		}

		public Criteria andEndDateLessThan(Date value) {
			addCriterion("end_date <", value, "endDate");
			return (Criteria) this;
		}

		public Criteria andEndDateLessThanOrEqualTo(Date value) {
			addCriterion("end_date <=", value, "endDate");
			return (Criteria) this;
		}

		public Criteria andEndDateIn(List<Date> values) {
			addCriterion("end_date in", values, "endDate");
			return (Criteria) this;
		}

		public Criteria andEndDateNotIn(List<Date> values) {
			addCriterion("end_date not in", values, "endDate");
			return (Criteria) this;
		}

		public Criteria andEndDateBetween(Date value1, Date value2) {
			addCriterion("end_date between", value1, value2, "endDate");
			return (Criteria) this;
		}

		public Criteria andEndDateNotBetween(Date value1, Date value2) {
			addCriterion("end_date not between", value1, value2, "endDate");
			return (Criteria) this;
		}

		public Criteria andIsCompletedIsNull() {
			addCriterion("is_completed is null");
			return (Criteria) this;
		}

		public Criteria andIsCompletedIsNotNull() {
			addCriterion("is_completed is not null");
			return (Criteria) this;
		}

		public Criteria andIsCompletedEqualTo(Boolean value) {
			addCriterion("is_completed =", value, "isCompleted");
			return (Criteria) this;
		}

		public Criteria andIsCompletedNotEqualTo(Boolean value) {
			addCriterion("is_completed <>", value, "isCompleted");
			return (Criteria) this;
		}

		public Criteria andIsCompletedGreaterThan(Boolean value) {
			addCriterion("is_completed >", value, "isCompleted");
			return (Criteria) this;
		}

		public Criteria andIsCompletedGreaterThanOrEqualTo(Boolean value) {
			addCriterion("is_completed >=", value, "isCompleted");
			return (Criteria) this;
		}

		public Criteria andIsCompletedLessThan(Boolean value) {
			addCriterion("is_completed <", value, "isCompleted");
			return (Criteria) this;
		}

		public Criteria andIsCompletedLessThanOrEqualTo(Boolean value) {
			addCriterion("is_completed <=", value, "isCompleted");
			return (Criteria) this;
		}

		public Criteria andIsCompletedIn(List<Boolean> values) {
			addCriterion("is_completed in", values, "isCompleted");
			return (Criteria) this;
		}

		public Criteria andIsCompletedNotIn(List<Boolean> values) {
			addCriterion("is_completed not in", values, "isCompleted");
			return (Criteria) this;
		}

		public Criteria andIsCompletedBetween(Boolean value1, Boolean value2) {
			addCriterion("is_completed between", value1, value2, "isCompleted");
			return (Criteria) this;
		}

		public Criteria andIsCompletedNotBetween(Boolean value1, Boolean value2) {
			addCriterion("is_completed not between", value1, value2, "isCompleted");
			return (Criteria) this;
		}

		public Criteria andJvIsNull() {
			addCriterion("jv is null");
			return (Criteria) this;
		}

		public Criteria andJvIsNotNull() {
			addCriterion("jv is not null");
			return (Criteria) this;
		}

		public Criteria andJvEqualTo(String value) {
			addCriterion("jv =", value, "jv");
			return (Criteria) this;
		}

		public Criteria andJvNotEqualTo(String value) {
			addCriterion("jv <>", value, "jv");
			return (Criteria) this;
		}

		public Criteria andJvGreaterThan(String value) {
			addCriterion("jv >", value, "jv");
			return (Criteria) this;
		}

		public Criteria andJvGreaterThanOrEqualTo(String value) {
			addCriterion("jv >=", value, "jv");
			return (Criteria) this;
		}

		public Criteria andJvLessThan(String value) {
			addCriterion("jv <", value, "jv");
			return (Criteria) this;
		}

		public Criteria andJvLessThanOrEqualTo(String value) {
			addCriterion("jv <=", value, "jv");
			return (Criteria) this;
		}

		public Criteria andJvLike(String value) {
			addCriterion("jv like", value, "jv");
			return (Criteria) this;
		}

		public Criteria andJvNotLike(String value) {
			addCriterion("jv not like", value, "jv");
			return (Criteria) this;
		}

		public Criteria andJvIn(List<String> values) {
			addCriterion("jv in", values, "jv");
			return (Criteria) this;
		}

		public Criteria andJvNotIn(List<String> values) {
			addCriterion("jv not in", values, "jv");
			return (Criteria) this;
		}

		public Criteria andJvBetween(String value1, String value2) {
			addCriterion("jv between", value1, value2, "jv");
			return (Criteria) this;
		}

		public Criteria andJvNotBetween(String value1, String value2) {
			addCriterion("jv not between", value1, value2, "jv");
			return (Criteria) this;
		}

		public Criteria andTotalCfaIsNull() {
			addCriterion("total_cfa is null");
			return (Criteria) this;
		}

		public Criteria andTotalCfaIsNotNull() {
			addCriterion("total_cfa is not null");
			return (Criteria) this;
		}

		public Criteria andTotalCfaEqualTo(BigDecimal value) {
			addCriterion("total_cfa =", value, "totalCfa");
			return (Criteria) this;
		}

		public Criteria andTotalCfaNotEqualTo(BigDecimal value) {
			addCriterion("total_cfa <>", value, "totalCfa");
			return (Criteria) this;
		}

		public Criteria andTotalCfaGreaterThan(BigDecimal value) {
			addCriterion("total_cfa >", value, "totalCfa");
			return (Criteria) this;
		}

		public Criteria andTotalCfaGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("total_cfa >=", value, "totalCfa");
			return (Criteria) this;
		}

		public Criteria andTotalCfaLessThan(BigDecimal value) {
			addCriterion("total_cfa <", value, "totalCfa");
			return (Criteria) this;
		}

		public Criteria andTotalCfaLessThanOrEqualTo(BigDecimal value) {
			addCriterion("total_cfa <=", value, "totalCfa");
			return (Criteria) this;
		}

		public Criteria andTotalCfaIn(List<BigDecimal> values) {
			addCriterion("total_cfa in", values, "totalCfa");
			return (Criteria) this;
		}

		public Criteria andTotalCfaNotIn(List<BigDecimal> values) {
			addCriterion("total_cfa not in", values, "totalCfa");
			return (Criteria) this;
		}

		public Criteria andTotalCfaBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("total_cfa between", value1, value2, "totalCfa");
			return (Criteria) this;
		}

		public Criteria andTotalCfaNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("total_cfa not between", value1, value2, "totalCfa");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIsNull() {
			addCriterion("creation_time is null");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIsNotNull() {
			addCriterion("creation_time is not null");
			return (Criteria) this;
		}

		public Criteria andCreationTimeEqualTo(Date value) {
			addCriterion("creation_time =", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotEqualTo(Date value) {
			addCriterion("creation_time <>", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeGreaterThan(Date value) {
			addCriterion("creation_time >", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeGreaterThanOrEqualTo(Date value) {
			addCriterion("creation_time >=", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeLessThan(Date value) {
			addCriterion("creation_time <", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeLessThanOrEqualTo(Date value) {
			addCriterion("creation_time <=", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIn(List<Date> values) {
			addCriterion("creation_time in", values, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotIn(List<Date> values) {
			addCriterion("creation_time not in", values, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeBetween(Date value1, Date value2) {
			addCriterion("creation_time between", value1, value2, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotBetween(Date value1, Date value2) {
			addCriterion("creation_time not between", value1, value2, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameIsNull() {
			addCriterion("create_username is null");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameIsNotNull() {
			addCriterion("create_username is not null");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameEqualTo(String value) {
			addCriterion("create_username =", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotEqualTo(String value) {
			addCriterion("create_username <>", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameGreaterThan(String value) {
			addCriterion("create_username >", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameGreaterThanOrEqualTo(String value) {
			addCriterion("create_username >=", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameLessThan(String value) {
			addCriterion("create_username <", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameLessThanOrEqualTo(String value) {
			addCriterion("create_username <=", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameLike(String value) {
			addCriterion("create_username like", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotLike(String value) {
			addCriterion("create_username not like", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameIn(List<String> values) {
			addCriterion("create_username in", values, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotIn(List<String> values) {
			addCriterion("create_username not in", values, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameBetween(String value1, String value2) {
			addCriterion("create_username between", value1, value2, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotBetween(String value1, String value2) {
			addCriterion("create_username not between", value1, value2, "createUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIsNull() {
			addCriterion("last_update_time is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIsNotNull() {
			addCriterion("last_update_time is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeEqualTo(Date value) {
			addCriterion("last_update_time =", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotEqualTo(Date value) {
			addCriterion("last_update_time <>", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeGreaterThan(Date value) {
			addCriterion("last_update_time >", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeGreaterThanOrEqualTo(Date value) {
			addCriterion("last_update_time >=", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeLessThan(Date value) {
			addCriterion("last_update_time <", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeLessThanOrEqualTo(Date value) {
			addCriterion("last_update_time <=", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIn(List<Date> values) {
			addCriterion("last_update_time in", values, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotIn(List<Date> values) {
			addCriterion("last_update_time not in", values, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeBetween(Date value1, Date value2) {
			addCriterion("last_update_time between", value1, value2, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotBetween(Date value1, Date value2) {
			addCriterion("last_update_time not between", value1, value2, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameIsNull() {
			addCriterion("last_update_username is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameIsNotNull() {
			addCriterion("last_update_username is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameEqualTo(String value) {
			addCriterion("last_update_username =", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotEqualTo(String value) {
			addCriterion("last_update_username <>", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameGreaterThan(String value) {
			addCriterion("last_update_username >", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameGreaterThanOrEqualTo(String value) {
			addCriterion("last_update_username >=", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameLessThan(String value) {
			addCriterion("last_update_username <", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameLessThanOrEqualTo(String value) {
			addCriterion("last_update_username <=", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameLike(String value) {
			addCriterion("last_update_username like", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotLike(String value) {
			addCriterion("last_update_username not like", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameIn(List<String> values) {
			addCriterion("last_update_username in", values, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotIn(List<String> values) {
			addCriterion("last_update_username not in", values, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameBetween(String value1, String value2) {
			addCriterion("last_update_username between", value1, value2, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotBetween(String value1, String value2) {
			addCriterion("last_update_username not between", value1, value2, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionIsNull() {
			addCriterion("last_update_version is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionIsNotNull() {
			addCriterion("last_update_version is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionEqualTo(Integer value) {
			addCriterion("last_update_version =", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionNotEqualTo(Integer value) {
			addCriterion("last_update_version <>", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionGreaterThan(Integer value) {
			addCriterion("last_update_version >", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionGreaterThanOrEqualTo(Integer value) {
			addCriterion("last_update_version >=", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionLessThan(Integer value) {
			addCriterion("last_update_version <", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionLessThanOrEqualTo(Integer value) {
			addCriterion("last_update_version <=", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionIn(List<Integer> values) {
			addCriterion("last_update_version in", values, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionNotIn(List<Integer> values) {
			addCriterion("last_update_version not in", values, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionBetween(Integer value1, Integer value2) {
			addCriterion("last_update_version between", value1, value2, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionNotBetween(Integer value1, Integer value2) {
			addCriterion("last_update_version not between", value1, value2, "lastUpdateVersion");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table t_project_info
	 * @mbg.generated  Wed Apr 13 18:03:43 HKT 2022
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_project_info
     *
     * @mbg.generated do_not_delete_during_merge Wed Apr 13 17:51:29 HKT 2022
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}