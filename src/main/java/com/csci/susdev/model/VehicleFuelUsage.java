package com.csci.susdev.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class VehicleFuelUsage {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_vehicle_fuel_usage.id
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_vehicle_fuel_usage.ambient_head_id
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	private String ambientHeadId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_vehicle_fuel_usage.month_value
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	private Integer monthValue;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_vehicle_fuel_usage.vehicle_type
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	private String vehicleType;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_vehicle_fuel_usage.vehicle_emission_standard
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	private String vehicleEmissionStandard;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_vehicle_fuel_usage.vehicle_license
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	private String vehicleLicense;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_vehicle_fuel_usage.mileage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	private BigDecimal mileage;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_vehicle_fuel_usage.cylinder_capacity
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	private BigDecimal cylinderCapacity;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_vehicle_fuel_usage.weight
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	private BigDecimal weight;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_vehicle_fuel_usage.rated_power
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	private BigDecimal ratedPower;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_vehicle_fuel_usage.fuel_type
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	private String fuelType;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_vehicle_fuel_usage.fuel_use_amount
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	private BigDecimal fuelUseAmount;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_vehicle_fuel_usage.remark
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	private String remark;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_vehicle_fuel_usage.creation_time
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	private LocalDateTime creationTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_vehicle_fuel_usage.create_username
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	private String createUsername;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_vehicle_fuel_usage.create_user_id
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	private String createUserId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_vehicle_fuel_usage.last_update_time
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	private LocalDateTime lastUpdateTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_vehicle_fuel_usage.last_update_username
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	private String lastUpdateUsername;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_vehicle_fuel_usage.last_update_user_id
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	private String lastUpdateUserId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column t_ab_vehicle_fuel_usage.last_update_version
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	private Integer lastUpdateVersion;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_vehicle_fuel_usage.id
	 * @return  the value of t_ab_vehicle_fuel_usage.id
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_vehicle_fuel_usage.id
	 * @param id  the value for t_ab_vehicle_fuel_usage.id
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_vehicle_fuel_usage.ambient_head_id
	 * @return  the value of t_ab_vehicle_fuel_usage.ambient_head_id
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public String getAmbientHeadId() {
		return ambientHeadId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_vehicle_fuel_usage.ambient_head_id
	 * @param ambientHeadId  the value for t_ab_vehicle_fuel_usage.ambient_head_id
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public void setAmbientHeadId(String ambientHeadId) {
		this.ambientHeadId = ambientHeadId == null ? null : ambientHeadId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_vehicle_fuel_usage.month_value
	 * @return  the value of t_ab_vehicle_fuel_usage.month_value
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public Integer getMonthValue() {
		return monthValue;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_vehicle_fuel_usage.month_value
	 * @param monthValue  the value for t_ab_vehicle_fuel_usage.month_value
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public void setMonthValue(Integer monthValue) {
		this.monthValue = monthValue;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_vehicle_fuel_usage.vehicle_type
	 * @return  the value of t_ab_vehicle_fuel_usage.vehicle_type
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public String getVehicleType() {
		return vehicleType;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_vehicle_fuel_usage.vehicle_type
	 * @param vehicleType  the value for t_ab_vehicle_fuel_usage.vehicle_type
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public void setVehicleType(String vehicleType) {
		this.vehicleType = vehicleType == null ? null : vehicleType.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_vehicle_fuel_usage.vehicle_emission_standard
	 * @return  the value of t_ab_vehicle_fuel_usage.vehicle_emission_standard
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public String getVehicleEmissionStandard() {
		return vehicleEmissionStandard;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_vehicle_fuel_usage.vehicle_emission_standard
	 * @param vehicleEmissionStandard  the value for t_ab_vehicle_fuel_usage.vehicle_emission_standard
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public void setVehicleEmissionStandard(String vehicleEmissionStandard) {
		this.vehicleEmissionStandard = vehicleEmissionStandard == null ? null : vehicleEmissionStandard.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_vehicle_fuel_usage.vehicle_license
	 * @return  the value of t_ab_vehicle_fuel_usage.vehicle_license
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public String getVehicleLicense() {
		return vehicleLicense;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_vehicle_fuel_usage.vehicle_license
	 * @param vehicleLicense  the value for t_ab_vehicle_fuel_usage.vehicle_license
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public void setVehicleLicense(String vehicleLicense) {
		this.vehicleLicense = vehicleLicense == null ? null : vehicleLicense.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_vehicle_fuel_usage.mileage
	 * @return  the value of t_ab_vehicle_fuel_usage.mileage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public BigDecimal getMileage() {
		return mileage;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_vehicle_fuel_usage.mileage
	 * @param mileage  the value for t_ab_vehicle_fuel_usage.mileage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public void setMileage(BigDecimal mileage) {
		this.mileage = mileage;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_vehicle_fuel_usage.cylinder_capacity
	 * @return  the value of t_ab_vehicle_fuel_usage.cylinder_capacity
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public BigDecimal getCylinderCapacity() {
		return cylinderCapacity;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_vehicle_fuel_usage.cylinder_capacity
	 * @param cylinderCapacity  the value for t_ab_vehicle_fuel_usage.cylinder_capacity
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public void setCylinderCapacity(BigDecimal cylinderCapacity) {
		this.cylinderCapacity = cylinderCapacity;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_vehicle_fuel_usage.weight
	 * @return  the value of t_ab_vehicle_fuel_usage.weight
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public BigDecimal getWeight() {
		return weight;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_vehicle_fuel_usage.weight
	 * @param weight  the value for t_ab_vehicle_fuel_usage.weight
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public void setWeight(BigDecimal weight) {
		this.weight = weight;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_vehicle_fuel_usage.rated_power
	 * @return  the value of t_ab_vehicle_fuel_usage.rated_power
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public BigDecimal getRatedPower() {
		return ratedPower;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_vehicle_fuel_usage.rated_power
	 * @param ratedPower  the value for t_ab_vehicle_fuel_usage.rated_power
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public void setRatedPower(BigDecimal ratedPower) {
		this.ratedPower = ratedPower;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_vehicle_fuel_usage.fuel_type
	 * @return  the value of t_ab_vehicle_fuel_usage.fuel_type
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public String getFuelType() {
		return fuelType;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_vehicle_fuel_usage.fuel_type
	 * @param fuelType  the value for t_ab_vehicle_fuel_usage.fuel_type
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public void setFuelType(String fuelType) {
		this.fuelType = fuelType == null ? null : fuelType.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_vehicle_fuel_usage.fuel_use_amount
	 * @return  the value of t_ab_vehicle_fuel_usage.fuel_use_amount
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public BigDecimal getFuelUseAmount() {
		return fuelUseAmount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_vehicle_fuel_usage.fuel_use_amount
	 * @param fuelUseAmount  the value for t_ab_vehicle_fuel_usage.fuel_use_amount
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public void setFuelUseAmount(BigDecimal fuelUseAmount) {
		this.fuelUseAmount = fuelUseAmount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_vehicle_fuel_usage.remark
	 * @return  the value of t_ab_vehicle_fuel_usage.remark
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public String getRemark() {
		return remark;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_vehicle_fuel_usage.remark
	 * @param remark  the value for t_ab_vehicle_fuel_usage.remark
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public void setRemark(String remark) {
		this.remark = remark == null ? null : remark.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_vehicle_fuel_usage.creation_time
	 * @return  the value of t_ab_vehicle_fuel_usage.creation_time
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public LocalDateTime getCreationTime() {
		return creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_vehicle_fuel_usage.creation_time
	 * @param creationTime  the value for t_ab_vehicle_fuel_usage.creation_time
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public void setCreationTime(LocalDateTime creationTime) {
		this.creationTime = creationTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_vehicle_fuel_usage.create_username
	 * @return  the value of t_ab_vehicle_fuel_usage.create_username
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public String getCreateUsername() {
		return createUsername;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_vehicle_fuel_usage.create_username
	 * @param createUsername  the value for t_ab_vehicle_fuel_usage.create_username
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public void setCreateUsername(String createUsername) {
		this.createUsername = createUsername == null ? null : createUsername.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_vehicle_fuel_usage.create_user_id
	 * @return  the value of t_ab_vehicle_fuel_usage.create_user_id
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public String getCreateUserId() {
		return createUserId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_vehicle_fuel_usage.create_user_id
	 * @param createUserId  the value for t_ab_vehicle_fuel_usage.create_user_id
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId == null ? null : createUserId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_vehicle_fuel_usage.last_update_time
	 * @return  the value of t_ab_vehicle_fuel_usage.last_update_time
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public LocalDateTime getLastUpdateTime() {
		return lastUpdateTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_vehicle_fuel_usage.last_update_time
	 * @param lastUpdateTime  the value for t_ab_vehicle_fuel_usage.last_update_time
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_vehicle_fuel_usage.last_update_username
	 * @return  the value of t_ab_vehicle_fuel_usage.last_update_username
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public String getLastUpdateUsername() {
		return lastUpdateUsername;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_vehicle_fuel_usage.last_update_username
	 * @param lastUpdateUsername  the value for t_ab_vehicle_fuel_usage.last_update_username
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public void setLastUpdateUsername(String lastUpdateUsername) {
		this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_vehicle_fuel_usage.last_update_user_id
	 * @return  the value of t_ab_vehicle_fuel_usage.last_update_user_id
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public String getLastUpdateUserId() {
		return lastUpdateUserId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_vehicle_fuel_usage.last_update_user_id
	 * @param lastUpdateUserId  the value for t_ab_vehicle_fuel_usage.last_update_user_id
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public void setLastUpdateUserId(String lastUpdateUserId) {
		this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column t_ab_vehicle_fuel_usage.last_update_version
	 * @return  the value of t_ab_vehicle_fuel_usage.last_update_version
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public Integer getLastUpdateVersion() {
		return lastUpdateVersion;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column t_ab_vehicle_fuel_usage.last_update_version
	 * @param lastUpdateVersion  the value for t_ab_vehicle_fuel_usage.last_update_version
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public void setLastUpdateVersion(Integer lastUpdateVersion) {
		this.lastUpdateVersion = lastUpdateVersion;
	}
}