package com.csci.susdev.model;

public class TzhCarbonPredictionDetail1 {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tzh_carbon_prediction_detail_1.id
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tzh_carbon_prediction_detail_1.head_id
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    private String headId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tzh_carbon_prediction_detail_1.col_1
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    private String col1;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tzh_carbon_prediction_detail_1.col_2
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    private String col2;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tzh_carbon_prediction_detail_1.col_3
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    private String col3;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tzh_carbon_prediction_detail_1.col_4
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    private String col4;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tzh_carbon_prediction_detail_1.col_5
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    private String col5;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tzh_carbon_prediction_detail_1.col_6
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    private String col6;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tzh_carbon_prediction_detail_1.seq
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    private Integer seq;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_tzh_carbon_prediction_detail_1.id
     *
     * @return the value of t_tzh_carbon_prediction_detail_1.id
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_tzh_carbon_prediction_detail_1.id
     *
     * @param id the value for t_tzh_carbon_prediction_detail_1.id
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_tzh_carbon_prediction_detail_1.head_id
     *
     * @return the value of t_tzh_carbon_prediction_detail_1.head_id
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    public String getHeadId() {
        return headId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_tzh_carbon_prediction_detail_1.head_id
     *
     * @param headId the value for t_tzh_carbon_prediction_detail_1.head_id
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    public void setHeadId(String headId) {
        this.headId = headId == null ? null : headId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_tzh_carbon_prediction_detail_1.col_1
     *
     * @return the value of t_tzh_carbon_prediction_detail_1.col_1
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    public String getCol1() {
        return col1;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_tzh_carbon_prediction_detail_1.col_1
     *
     * @param col1 the value for t_tzh_carbon_prediction_detail_1.col_1
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    public void setCol1(String col1) {
        this.col1 = col1 == null ? null : col1.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_tzh_carbon_prediction_detail_1.col_2
     *
     * @return the value of t_tzh_carbon_prediction_detail_1.col_2
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    public String getCol2() {
        return col2;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_tzh_carbon_prediction_detail_1.col_2
     *
     * @param col2 the value for t_tzh_carbon_prediction_detail_1.col_2
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    public void setCol2(String col2) {
        this.col2 = col2 == null ? null : col2.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_tzh_carbon_prediction_detail_1.col_3
     *
     * @return the value of t_tzh_carbon_prediction_detail_1.col_3
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    public String getCol3() {
        return col3;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_tzh_carbon_prediction_detail_1.col_3
     *
     * @param col3 the value for t_tzh_carbon_prediction_detail_1.col_3
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    public void setCol3(String col3) {
        this.col3 = col3 == null ? null : col3.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_tzh_carbon_prediction_detail_1.col_4
     *
     * @return the value of t_tzh_carbon_prediction_detail_1.col_4
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    public String getCol4() {
        return col4;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_tzh_carbon_prediction_detail_1.col_4
     *
     * @param col4 the value for t_tzh_carbon_prediction_detail_1.col_4
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    public void setCol4(String col4) {
        this.col4 = col4 == null ? null : col4.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_tzh_carbon_prediction_detail_1.col_5
     *
     * @return the value of t_tzh_carbon_prediction_detail_1.col_5
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    public String getCol5() {
        return col5;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_tzh_carbon_prediction_detail_1.col_5
     *
     * @param col5 the value for t_tzh_carbon_prediction_detail_1.col_5
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    public void setCol5(String col5) {
        this.col5 = col5 == null ? null : col5.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_tzh_carbon_prediction_detail_1.col_6
     *
     * @return the value of t_tzh_carbon_prediction_detail_1.col_6
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    public String getCol6() {
        return col6;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_tzh_carbon_prediction_detail_1.col_6
     *
     * @param col6 the value for t_tzh_carbon_prediction_detail_1.col_6
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    public void setCol6(String col6) {
        this.col6 = col6 == null ? null : col6.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_tzh_carbon_prediction_detail_1.seq
     *
     * @return the value of t_tzh_carbon_prediction_detail_1.seq
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    public Integer getSeq() {
        return seq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_tzh_carbon_prediction_detail_1.seq
     *
     * @param seq the value for t_tzh_carbon_prediction_detail_1.seq
     *
     * @mbg.generated Tue Jul 26 12:35:09 HKT 2022
     */
    public void setSeq(Integer seq) {
        this.seq = seq;
    }
}