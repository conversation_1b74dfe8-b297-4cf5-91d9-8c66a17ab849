package com.csci.susdev.model;

import java.time.LocalDateTime;

public class AirPollutants {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_air_pollutants.id
     *
     * @mbg.generated
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_air_pollutants.batch_id
     *
     * @mbg.generated
     */
    private String batchId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_air_pollutants.category
     *
     * @mbg.generated
     */
    private String category;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_air_pollutants.emission_source
     *
     * @mbg.generated
     */
    private String emissionSource;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_air_pollutants.type
     *
     * @mbg.generated
     */
    private String type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_air_pollutants.unit
     *
     * @mbg.generated
     */
    private String unit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_air_pollutants.air_pollution_type
     *
     * @mbg.generated
     */
    private String airPollutionType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_air_pollutants.vehicle_type
     *
     * @mbg.generated
     */
    private String vehicleType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_air_pollutants.vehicle_emission_standard
     *
     * @mbg.generated
     */
    private String vehicleEmissionStandard;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_air_pollutants.air_pol_emi_factor
     *
     * @mbg.generated
     */
    private String airPolEmiFactor;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_air_pollutants.air_pol_emi_unit
     *
     * @mbg.generated
     */
    private String airPolEmiUnit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_air_pollutants.creation_time
     *
     * @mbg.generated
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_air_pollutants.create_username
     *
     * @mbg.generated
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_air_pollutants.create_user_id
     *
     * @mbg.generated
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_air_pollutants.last_update_time
     *
     * @mbg.generated
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_air_pollutants.last_update_username
     *
     * @mbg.generated
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_air_pollutants.last_update_user_id
     *
     * @mbg.generated
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_cf_air_pollutants.last_update_version
     *
     * @mbg.generated
     */
    private Integer lastUpdateVersion;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_air_pollutants.id
     *
     * @return the value of t_cf_air_pollutants.id
     *
     * @mbg.generated
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_air_pollutants.id
     *
     * @param id the value for t_cf_air_pollutants.id
     *
     * @mbg.generated
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_air_pollutants.batch_id
     *
     * @return the value of t_cf_air_pollutants.batch_id
     *
     * @mbg.generated
     */
    public String getBatchId() {
        return batchId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_air_pollutants.batch_id
     *
     * @param batchId the value for t_cf_air_pollutants.batch_id
     *
     * @mbg.generated
     */
    public void setBatchId(String batchId) {
        this.batchId = batchId == null ? null : batchId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_air_pollutants.category
     *
     * @return the value of t_cf_air_pollutants.category
     *
     * @mbg.generated
     */
    public String getCategory() {
        return category;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_air_pollutants.category
     *
     * @param category the value for t_cf_air_pollutants.category
     *
     * @mbg.generated
     */
    public void setCategory(String category) {
        this.category = category == null ? null : category.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_air_pollutants.emission_source
     *
     * @return the value of t_cf_air_pollutants.emission_source
     *
     * @mbg.generated
     */
    public String getEmissionSource() {
        return emissionSource;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_air_pollutants.emission_source
     *
     * @param emissionSource the value for t_cf_air_pollutants.emission_source
     *
     * @mbg.generated
     */
    public void setEmissionSource(String emissionSource) {
        this.emissionSource = emissionSource == null ? null : emissionSource.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_air_pollutants.type
     *
     * @return the value of t_cf_air_pollutants.type
     *
     * @mbg.generated
     */
    public String getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_air_pollutants.type
     *
     * @param type the value for t_cf_air_pollutants.type
     *
     * @mbg.generated
     */
    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_air_pollutants.unit
     *
     * @return the value of t_cf_air_pollutants.unit
     *
     * @mbg.generated
     */
    public String getUnit() {
        return unit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_air_pollutants.unit
     *
     * @param unit the value for t_cf_air_pollutants.unit
     *
     * @mbg.generated
     */
    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_air_pollutants.air_pollution_type
     *
     * @return the value of t_cf_air_pollutants.air_pollution_type
     *
     * @mbg.generated
     */
    public String getAirPollutionType() {
        return airPollutionType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_air_pollutants.air_pollution_type
     *
     * @param airPollutionType the value for t_cf_air_pollutants.air_pollution_type
     *
     * @mbg.generated
     */
    public void setAirPollutionType(String airPollutionType) {
        this.airPollutionType = airPollutionType == null ? null : airPollutionType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_air_pollutants.vehicle_type
     *
     * @return the value of t_cf_air_pollutants.vehicle_type
     *
     * @mbg.generated
     */
    public String getVehicleType() {
        return vehicleType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_air_pollutants.vehicle_type
     *
     * @param vehicleType the value for t_cf_air_pollutants.vehicle_type
     *
     * @mbg.generated
     */
    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType == null ? null : vehicleType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_air_pollutants.vehicle_emission_standard
     *
     * @return the value of t_cf_air_pollutants.vehicle_emission_standard
     *
     * @mbg.generated
     */
    public String getVehicleEmissionStandard() {
        return vehicleEmissionStandard;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_air_pollutants.vehicle_emission_standard
     *
     * @param vehicleEmissionStandard the value for t_cf_air_pollutants.vehicle_emission_standard
     *
     * @mbg.generated
     */
    public void setVehicleEmissionStandard(String vehicleEmissionStandard) {
        this.vehicleEmissionStandard = vehicleEmissionStandard == null ? null : vehicleEmissionStandard.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_air_pollutants.air_pol_emi_factor
     *
     * @return the value of t_cf_air_pollutants.air_pol_emi_factor
     *
     * @mbg.generated
     */
    public String getAirPolEmiFactor() {
        return airPolEmiFactor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_air_pollutants.air_pol_emi_factor
     *
     * @param airPolEmiFactor the value for t_cf_air_pollutants.air_pol_emi_factor
     *
     * @mbg.generated
     */
    public void setAirPolEmiFactor(String airPolEmiFactor) {
        this.airPolEmiFactor = airPolEmiFactor == null ? null : airPolEmiFactor.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_air_pollutants.air_pol_emi_unit
     *
     * @return the value of t_cf_air_pollutants.air_pol_emi_unit
     *
     * @mbg.generated
     */
    public String getAirPolEmiUnit() {
        return airPolEmiUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_air_pollutants.air_pol_emi_unit
     *
     * @param airPolEmiUnit the value for t_cf_air_pollutants.air_pol_emi_unit
     *
     * @mbg.generated
     */
    public void setAirPolEmiUnit(String airPolEmiUnit) {
        this.airPolEmiUnit = airPolEmiUnit == null ? null : airPolEmiUnit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_air_pollutants.creation_time
     *
     * @return the value of t_cf_air_pollutants.creation_time
     *
     * @mbg.generated
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_air_pollutants.creation_time
     *
     * @param creationTime the value for t_cf_air_pollutants.creation_time
     *
     * @mbg.generated
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_air_pollutants.create_username
     *
     * @return the value of t_cf_air_pollutants.create_username
     *
     * @mbg.generated
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_air_pollutants.create_username
     *
     * @param createUsername the value for t_cf_air_pollutants.create_username
     *
     * @mbg.generated
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_air_pollutants.create_user_id
     *
     * @return the value of t_cf_air_pollutants.create_user_id
     *
     * @mbg.generated
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_air_pollutants.create_user_id
     *
     * @param createUserId the value for t_cf_air_pollutants.create_user_id
     *
     * @mbg.generated
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_air_pollutants.last_update_time
     *
     * @return the value of t_cf_air_pollutants.last_update_time
     *
     * @mbg.generated
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_air_pollutants.last_update_time
     *
     * @param lastUpdateTime the value for t_cf_air_pollutants.last_update_time
     *
     * @mbg.generated
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_air_pollutants.last_update_username
     *
     * @return the value of t_cf_air_pollutants.last_update_username
     *
     * @mbg.generated
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_air_pollutants.last_update_username
     *
     * @param lastUpdateUsername the value for t_cf_air_pollutants.last_update_username
     *
     * @mbg.generated
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_air_pollutants.last_update_user_id
     *
     * @return the value of t_cf_air_pollutants.last_update_user_id
     *
     * @mbg.generated
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_air_pollutants.last_update_user_id
     *
     * @param lastUpdateUserId the value for t_cf_air_pollutants.last_update_user_id
     *
     * @mbg.generated
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_cf_air_pollutants.last_update_version
     *
     * @return the value of t_cf_air_pollutants.last_update_version
     *
     * @mbg.generated
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_cf_air_pollutants.last_update_version
     *
     * @param lastUpdateVersion the value for t_cf_air_pollutants.last_update_version
     *
     * @mbg.generated
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }
}