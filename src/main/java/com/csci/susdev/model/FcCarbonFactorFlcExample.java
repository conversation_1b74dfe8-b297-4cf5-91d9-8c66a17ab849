package com.csci.susdev.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class FcCarbonFactorFlcExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public FcCarbonFactorFlcExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andChineseNameIsNull() {
            addCriterion("chinese_name is null");
            return (Criteria) this;
        }

        public Criteria andChineseNameIsNotNull() {
            addCriterion("chinese_name is not null");
            return (Criteria) this;
        }

        public Criteria andChineseNameEqualTo(String value) {
            addCriterion("chinese_name =", value, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameNotEqualTo(String value) {
            addCriterion("chinese_name <>", value, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameGreaterThan(String value) {
            addCriterion("chinese_name >", value, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameGreaterThanOrEqualTo(String value) {
            addCriterion("chinese_name >=", value, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameLessThan(String value) {
            addCriterion("chinese_name <", value, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameLessThanOrEqualTo(String value) {
            addCriterion("chinese_name <=", value, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameLike(String value) {
            addCriterion("chinese_name like", value, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameNotLike(String value) {
            addCriterion("chinese_name not like", value, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameIn(List<String> values) {
            addCriterion("chinese_name in", values, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameNotIn(List<String> values) {
            addCriterion("chinese_name not in", values, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameBetween(String value1, String value2) {
            addCriterion("chinese_name between", value1, value2, "chineseName");
            return (Criteria) this;
        }

        public Criteria andChineseNameNotBetween(String value1, String value2) {
            addCriterion("chinese_name not between", value1, value2, "chineseName");
            return (Criteria) this;
        }

        public Criteria andUpstreamEmissionIsNull() {
            addCriterion("upstream_emission is null");
            return (Criteria) this;
        }

        public Criteria andUpstreamEmissionIsNotNull() {
            addCriterion("upstream_emission is not null");
            return (Criteria) this;
        }

        public Criteria andUpstreamEmissionEqualTo(BigDecimal value) {
            addCriterion("upstream_emission =", value, "upstreamEmission");
            return (Criteria) this;
        }

        public Criteria andUpstreamEmissionNotEqualTo(BigDecimal value) {
            addCriterion("upstream_emission <>", value, "upstreamEmission");
            return (Criteria) this;
        }

        public Criteria andUpstreamEmissionGreaterThan(BigDecimal value) {
            addCriterion("upstream_emission >", value, "upstreamEmission");
            return (Criteria) this;
        }

        public Criteria andUpstreamEmissionGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("upstream_emission >=", value, "upstreamEmission");
            return (Criteria) this;
        }

        public Criteria andUpstreamEmissionLessThan(BigDecimal value) {
            addCriterion("upstream_emission <", value, "upstreamEmission");
            return (Criteria) this;
        }

        public Criteria andUpstreamEmissionLessThanOrEqualTo(BigDecimal value) {
            addCriterion("upstream_emission <=", value, "upstreamEmission");
            return (Criteria) this;
        }

        public Criteria andUpstreamEmissionIn(List<BigDecimal> values) {
            addCriterion("upstream_emission in", values, "upstreamEmission");
            return (Criteria) this;
        }

        public Criteria andUpstreamEmissionNotIn(List<BigDecimal> values) {
            addCriterion("upstream_emission not in", values, "upstreamEmission");
            return (Criteria) this;
        }

        public Criteria andUpstreamEmissionBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("upstream_emission between", value1, value2, "upstreamEmission");
            return (Criteria) this;
        }

        public Criteria andUpstreamEmissionNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("upstream_emission not between", value1, value2, "upstreamEmission");
            return (Criteria) this;
        }

        public Criteria andDownstreamEmissionIsNull() {
            addCriterion("downstream_emission is null");
            return (Criteria) this;
        }

        public Criteria andDownstreamEmissionIsNotNull() {
            addCriterion("downstream_emission is not null");
            return (Criteria) this;
        }

        public Criteria andDownstreamEmissionEqualTo(BigDecimal value) {
            addCriterion("downstream_emission =", value, "downstreamEmission");
            return (Criteria) this;
        }

        public Criteria andDownstreamEmissionNotEqualTo(BigDecimal value) {
            addCriterion("downstream_emission <>", value, "downstreamEmission");
            return (Criteria) this;
        }

        public Criteria andDownstreamEmissionGreaterThan(BigDecimal value) {
            addCriterion("downstream_emission >", value, "downstreamEmission");
            return (Criteria) this;
        }

        public Criteria andDownstreamEmissionGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("downstream_emission >=", value, "downstreamEmission");
            return (Criteria) this;
        }

        public Criteria andDownstreamEmissionLessThan(BigDecimal value) {
            addCriterion("downstream_emission <", value, "downstreamEmission");
            return (Criteria) this;
        }

        public Criteria andDownstreamEmissionLessThanOrEqualTo(BigDecimal value) {
            addCriterion("downstream_emission <=", value, "downstreamEmission");
            return (Criteria) this;
        }

        public Criteria andDownstreamEmissionIn(List<BigDecimal> values) {
            addCriterion("downstream_emission in", values, "downstreamEmission");
            return (Criteria) this;
        }

        public Criteria andDownstreamEmissionNotIn(List<BigDecimal> values) {
            addCriterion("downstream_emission not in", values, "downstreamEmission");
            return (Criteria) this;
        }

        public Criteria andDownstreamEmissionBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("downstream_emission between", value1, value2, "downstreamEmission");
            return (Criteria) this;
        }

        public Criteria andDownstreamEmissionNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("downstream_emission not between", value1, value2, "downstreamEmission");
            return (Criteria) this;
        }

        public Criteria andEmissionUnitIsNull() {
            addCriterion("emission_unit is null");
            return (Criteria) this;
        }

        public Criteria andEmissionUnitIsNotNull() {
            addCriterion("emission_unit is not null");
            return (Criteria) this;
        }

        public Criteria andEmissionUnitEqualTo(String value) {
            addCriterion("emission_unit =", value, "emissionUnit");
            return (Criteria) this;
        }

        public Criteria andEmissionUnitNotEqualTo(String value) {
            addCriterion("emission_unit <>", value, "emissionUnit");
            return (Criteria) this;
        }

        public Criteria andEmissionUnitGreaterThan(String value) {
            addCriterion("emission_unit >", value, "emissionUnit");
            return (Criteria) this;
        }

        public Criteria andEmissionUnitGreaterThanOrEqualTo(String value) {
            addCriterion("emission_unit >=", value, "emissionUnit");
            return (Criteria) this;
        }

        public Criteria andEmissionUnitLessThan(String value) {
            addCriterion("emission_unit <", value, "emissionUnit");
            return (Criteria) this;
        }

        public Criteria andEmissionUnitLessThanOrEqualTo(String value) {
            addCriterion("emission_unit <=", value, "emissionUnit");
            return (Criteria) this;
        }

        public Criteria andEmissionUnitLike(String value) {
            addCriterion("emission_unit like", value, "emissionUnit");
            return (Criteria) this;
        }

        public Criteria andEmissionUnitNotLike(String value) {
            addCriterion("emission_unit not like", value, "emissionUnit");
            return (Criteria) this;
        }

        public Criteria andEmissionUnitIn(List<String> values) {
            addCriterion("emission_unit in", values, "emissionUnit");
            return (Criteria) this;
        }

        public Criteria andEmissionUnitNotIn(List<String> values) {
            addCriterion("emission_unit not in", values, "emissionUnit");
            return (Criteria) this;
        }

        public Criteria andEmissionUnitBetween(String value1, String value2) {
            addCriterion("emission_unit between", value1, value2, "emissionUnit");
            return (Criteria) this;
        }

        public Criteria andEmissionUnitNotBetween(String value1, String value2) {
            addCriterion("emission_unit not between", value1, value2, "emissionUnit");
            return (Criteria) this;
        }

        public Criteria andEmissionStageIsNull() {
            addCriterion("emission_stage is null");
            return (Criteria) this;
        }

        public Criteria andEmissionStageIsNotNull() {
            addCriterion("emission_stage is not null");
            return (Criteria) this;
        }

        public Criteria andEmissionStageEqualTo(String value) {
            addCriterion("emission_stage =", value, "emissionStage");
            return (Criteria) this;
        }

        public Criteria andEmissionStageNotEqualTo(String value) {
            addCriterion("emission_stage <>", value, "emissionStage");
            return (Criteria) this;
        }

        public Criteria andEmissionStageGreaterThan(String value) {
            addCriterion("emission_stage >", value, "emissionStage");
            return (Criteria) this;
        }

        public Criteria andEmissionStageGreaterThanOrEqualTo(String value) {
            addCriterion("emission_stage >=", value, "emissionStage");
            return (Criteria) this;
        }

        public Criteria andEmissionStageLessThan(String value) {
            addCriterion("emission_stage <", value, "emissionStage");
            return (Criteria) this;
        }

        public Criteria andEmissionStageLessThanOrEqualTo(String value) {
            addCriterion("emission_stage <=", value, "emissionStage");
            return (Criteria) this;
        }

        public Criteria andEmissionStageLike(String value) {
            addCriterion("emission_stage like", value, "emissionStage");
            return (Criteria) this;
        }

        public Criteria andEmissionStageNotLike(String value) {
            addCriterion("emission_stage not like", value, "emissionStage");
            return (Criteria) this;
        }

        public Criteria andEmissionStageIn(List<String> values) {
            addCriterion("emission_stage in", values, "emissionStage");
            return (Criteria) this;
        }

        public Criteria andEmissionStageNotIn(List<String> values) {
            addCriterion("emission_stage not in", values, "emissionStage");
            return (Criteria) this;
        }

        public Criteria andEmissionStageBetween(String value1, String value2) {
            addCriterion("emission_stage between", value1, value2, "emissionStage");
            return (Criteria) this;
        }

        public Criteria andEmissionStageNotBetween(String value1, String value2) {
            addCriterion("emission_stage not between", value1, value2, "emissionStage");
            return (Criteria) this;
        }

        public Criteria andUncertaintyIsNull() {
            addCriterion("uncertainty is null");
            return (Criteria) this;
        }

        public Criteria andUncertaintyIsNotNull() {
            addCriterion("uncertainty is not null");
            return (Criteria) this;
        }

        public Criteria andUncertaintyEqualTo(String value) {
            addCriterion("uncertainty =", value, "uncertainty");
            return (Criteria) this;
        }

        public Criteria andUncertaintyNotEqualTo(String value) {
            addCriterion("uncertainty <>", value, "uncertainty");
            return (Criteria) this;
        }

        public Criteria andUncertaintyGreaterThan(String value) {
            addCriterion("uncertainty >", value, "uncertainty");
            return (Criteria) this;
        }

        public Criteria andUncertaintyGreaterThanOrEqualTo(String value) {
            addCriterion("uncertainty >=", value, "uncertainty");
            return (Criteria) this;
        }

        public Criteria andUncertaintyLessThan(String value) {
            addCriterion("uncertainty <", value, "uncertainty");
            return (Criteria) this;
        }

        public Criteria andUncertaintyLessThanOrEqualTo(String value) {
            addCriterion("uncertainty <=", value, "uncertainty");
            return (Criteria) this;
        }

        public Criteria andUncertaintyLike(String value) {
            addCriterion("uncertainty like", value, "uncertainty");
            return (Criteria) this;
        }

        public Criteria andUncertaintyNotLike(String value) {
            addCriterion("uncertainty not like", value, "uncertainty");
            return (Criteria) this;
        }

        public Criteria andUncertaintyIn(List<String> values) {
            addCriterion("uncertainty in", values, "uncertainty");
            return (Criteria) this;
        }

        public Criteria andUncertaintyNotIn(List<String> values) {
            addCriterion("uncertainty not in", values, "uncertainty");
            return (Criteria) this;
        }

        public Criteria andUncertaintyBetween(String value1, String value2) {
            addCriterion("uncertainty between", value1, value2, "uncertainty");
            return (Criteria) this;
        }

        public Criteria andUncertaintyNotBetween(String value1, String value2) {
            addCriterion("uncertainty not between", value1, value2, "uncertainty");
            return (Criteria) this;
        }

        public Criteria andCaptionIsNull() {
            addCriterion("caption is null");
            return (Criteria) this;
        }

        public Criteria andCaptionIsNotNull() {
            addCriterion("caption is not null");
            return (Criteria) this;
        }

        public Criteria andCaptionEqualTo(String value) {
            addCriterion("caption =", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionNotEqualTo(String value) {
            addCriterion("caption <>", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionGreaterThan(String value) {
            addCriterion("caption >", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionGreaterThanOrEqualTo(String value) {
            addCriterion("caption >=", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionLessThan(String value) {
            addCriterion("caption <", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionLessThanOrEqualTo(String value) {
            addCriterion("caption <=", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionLike(String value) {
            addCriterion("caption like", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionNotLike(String value) {
            addCriterion("caption not like", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionIn(List<String> values) {
            addCriterion("caption in", values, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionNotIn(List<String> values) {
            addCriterion("caption not in", values, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionBetween(String value1, String value2) {
            addCriterion("caption between", value1, value2, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionNotBetween(String value1, String value2) {
            addCriterion("caption not between", value1, value2, "caption");
            return (Criteria) this;
        }

        public Criteria andDataTimeIsNull() {
            addCriterion("data_time is null");
            return (Criteria) this;
        }

        public Criteria andDataTimeIsNotNull() {
            addCriterion("data_time is not null");
            return (Criteria) this;
        }

        public Criteria andDataTimeEqualTo(String value) {
            addCriterion("data_time =", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeNotEqualTo(String value) {
            addCriterion("data_time <>", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeGreaterThan(String value) {
            addCriterion("data_time >", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeGreaterThanOrEqualTo(String value) {
            addCriterion("data_time >=", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeLessThan(String value) {
            addCriterion("data_time <", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeLessThanOrEqualTo(String value) {
            addCriterion("data_time <=", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeLike(String value) {
            addCriterion("data_time like", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeNotLike(String value) {
            addCriterion("data_time not like", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeIn(List<String> values) {
            addCriterion("data_time in", values, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeNotIn(List<String> values) {
            addCriterion("data_time not in", values, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeBetween(String value1, String value2) {
            addCriterion("data_time between", value1, value2, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeNotBetween(String value1, String value2) {
            addCriterion("data_time not between", value1, value2, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorIsNull() {
            addCriterion("carbon_factor is null");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorIsNotNull() {
            addCriterion("carbon_factor is not null");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorEqualTo(BigDecimal value) {
            addCriterion("carbon_factor =", value, "carbonFactor");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorNotEqualTo(BigDecimal value) {
            addCriterion("carbon_factor <>", value, "carbonFactor");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorGreaterThan(BigDecimal value) {
            addCriterion("carbon_factor >", value, "carbonFactor");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("carbon_factor >=", value, "carbonFactor");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorLessThan(BigDecimal value) {
            addCriterion("carbon_factor <", value, "carbonFactor");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorLessThanOrEqualTo(BigDecimal value) {
            addCriterion("carbon_factor <=", value, "carbonFactor");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorIn(List<BigDecimal> values) {
            addCriterion("carbon_factor in", values, "carbonFactor");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorNotIn(List<BigDecimal> values) {
            addCriterion("carbon_factor not in", values, "carbonFactor");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("carbon_factor between", value1, value2, "carbonFactor");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("carbon_factor not between", value1, value2, "carbonFactor");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitIsNull() {
            addCriterion("carbon_factor_unit is null");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitIsNotNull() {
            addCriterion("carbon_factor_unit is not null");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitEqualTo(String value) {
            addCriterion("carbon_factor_unit =", value, "carbonFactorUnit");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitNotEqualTo(String value) {
            addCriterion("carbon_factor_unit <>", value, "carbonFactorUnit");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitGreaterThan(String value) {
            addCriterion("carbon_factor_unit >", value, "carbonFactorUnit");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitGreaterThanOrEqualTo(String value) {
            addCriterion("carbon_factor_unit >=", value, "carbonFactorUnit");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitLessThan(String value) {
            addCriterion("carbon_factor_unit <", value, "carbonFactorUnit");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitLessThanOrEqualTo(String value) {
            addCriterion("carbon_factor_unit <=", value, "carbonFactorUnit");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitLike(String value) {
            addCriterion("carbon_factor_unit like", value, "carbonFactorUnit");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitNotLike(String value) {
            addCriterion("carbon_factor_unit not like", value, "carbonFactorUnit");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitIn(List<String> values) {
            addCriterion("carbon_factor_unit in", values, "carbonFactorUnit");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitNotIn(List<String> values) {
            addCriterion("carbon_factor_unit not in", values, "carbonFactorUnit");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitBetween(String value1, String value2) {
            addCriterion("carbon_factor_unit between", value1, value2, "carbonFactorUnit");
            return (Criteria) this;
        }

        public Criteria andCarbonFactorUnitNotBetween(String value1, String value2) {
            addCriterion("carbon_factor_unit not between", value1, value2, "carbonFactorUnit");
            return (Criteria) this;
        }

        public Criteria andDatasourceIsNull() {
            addCriterion("datasource is null");
            return (Criteria) this;
        }

        public Criteria andDatasourceIsNotNull() {
            addCriterion("datasource is not null");
            return (Criteria) this;
        }

        public Criteria andDatasourceEqualTo(String value) {
            addCriterion("datasource =", value, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceNotEqualTo(String value) {
            addCriterion("datasource <>", value, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceGreaterThan(String value) {
            addCriterion("datasource >", value, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceGreaterThanOrEqualTo(String value) {
            addCriterion("datasource >=", value, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceLessThan(String value) {
            addCriterion("datasource <", value, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceLessThanOrEqualTo(String value) {
            addCriterion("datasource <=", value, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceLike(String value) {
            addCriterion("datasource like", value, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceNotLike(String value) {
            addCriterion("datasource not like", value, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceIn(List<String> values) {
            addCriterion("datasource in", values, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceNotIn(List<String> values) {
            addCriterion("datasource not in", values, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceBetween(String value1, String value2) {
            addCriterion("datasource between", value1, value2, "datasource");
            return (Criteria) this;
        }

        public Criteria andDatasourceNotBetween(String value1, String value2) {
            addCriterion("datasource not between", value1, value2, "datasource");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNull() {
            addCriterion("creation_time is null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIsNotNull() {
            addCriterion("creation_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreationTimeEqualTo(LocalDateTime value) {
            addCriterion("creation_time =", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotEqualTo(LocalDateTime value) {
            addCriterion("creation_time <>", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThan(LocalDateTime value) {
            addCriterion("creation_time >", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time >=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThan(LocalDateTime value) {
            addCriterion("creation_time <", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("creation_time <=", value, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeIn(List<LocalDateTime> values) {
            addCriterion("creation_time in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotIn(List<LocalDateTime> values) {
            addCriterion("creation_time not in", values, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreationTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("creation_time not between", value1, value2, "creationTime");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIsNull() {
            addCriterion("create_username is null");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIsNotNull() {
            addCriterion("create_username is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameEqualTo(String value) {
            addCriterion("create_username =", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotEqualTo(String value) {
            addCriterion("create_username <>", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameGreaterThan(String value) {
            addCriterion("create_username >", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("create_username >=", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLessThan(String value) {
            addCriterion("create_username <", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLessThanOrEqualTo(String value) {
            addCriterion("create_username <=", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameLike(String value) {
            addCriterion("create_username like", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotLike(String value) {
            addCriterion("create_username not like", value, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameIn(List<String> values) {
            addCriterion("create_username in", values, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotIn(List<String> values) {
            addCriterion("create_username not in", values, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameBetween(String value1, String value2) {
            addCriterion("create_username between", value1, value2, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUsernameNotBetween(String value1, String value2) {
            addCriterion("create_username not between", value1, value2, "createUsername");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(String value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(String value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(String value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(String value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(String value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLike(String value) {
            addCriterion("create_user_id like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotLike(String value) {
            addCriterion("create_user_id not like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<String> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<String> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(String value1, String value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(String value1, String value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIsNull() {
            addCriterion("last_update_time is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIsNotNull() {
            addCriterion("last_update_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("last_update_time =", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("last_update_time <>", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("last_update_time >", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_update_time >=", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("last_update_time <", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_update_time <=", value, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("last_update_time in", values, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("last_update_time not in", values, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_update_time between", value1, value2, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_update_time not between", value1, value2, "lastUpdateTime");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIsNull() {
            addCriterion("last_update_username is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIsNotNull() {
            addCriterion("last_update_username is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameEqualTo(String value) {
            addCriterion("last_update_username =", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotEqualTo(String value) {
            addCriterion("last_update_username <>", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameGreaterThan(String value) {
            addCriterion("last_update_username >", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("last_update_username >=", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLessThan(String value) {
            addCriterion("last_update_username <", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLessThanOrEqualTo(String value) {
            addCriterion("last_update_username <=", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameLike(String value) {
            addCriterion("last_update_username like", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotLike(String value) {
            addCriterion("last_update_username not like", value, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameIn(List<String> values) {
            addCriterion("last_update_username in", values, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotIn(List<String> values) {
            addCriterion("last_update_username not in", values, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameBetween(String value1, String value2) {
            addCriterion("last_update_username between", value1, value2, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUsernameNotBetween(String value1, String value2) {
            addCriterion("last_update_username not between", value1, value2, "lastUpdateUsername");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIsNull() {
            addCriterion("last_update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIsNotNull() {
            addCriterion("last_update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdEqualTo(String value) {
            addCriterion("last_update_user_id =", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotEqualTo(String value) {
            addCriterion("last_update_user_id <>", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdGreaterThan(String value) {
            addCriterion("last_update_user_id >", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("last_update_user_id >=", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLessThan(String value) {
            addCriterion("last_update_user_id <", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLessThanOrEqualTo(String value) {
            addCriterion("last_update_user_id <=", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdLike(String value) {
            addCriterion("last_update_user_id like", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotLike(String value) {
            addCriterion("last_update_user_id not like", value, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdIn(List<String> values) {
            addCriterion("last_update_user_id in", values, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotIn(List<String> values) {
            addCriterion("last_update_user_id not in", values, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdBetween(String value1, String value2) {
            addCriterion("last_update_user_id between", value1, value2, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateUserIdNotBetween(String value1, String value2) {
            addCriterion("last_update_user_id not between", value1, value2, "lastUpdateUserId");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIsNull() {
            addCriterion("last_update_version is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIsNotNull() {
            addCriterion("last_update_version is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionEqualTo(Integer value) {
            addCriterion("last_update_version =", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotEqualTo(Integer value) {
            addCriterion("last_update_version <>", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionGreaterThan(Integer value) {
            addCriterion("last_update_version >", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("last_update_version >=", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionLessThan(Integer value) {
            addCriterion("last_update_version <", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionLessThanOrEqualTo(Integer value) {
            addCriterion("last_update_version <=", value, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionIn(List<Integer> values) {
            addCriterion("last_update_version in", values, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotIn(List<Integer> values) {
            addCriterion("last_update_version not in", values, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionBetween(Integer value1, Integer value2) {
            addCriterion("last_update_version between", value1, value2, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andLastUpdateVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("last_update_version not between", value1, value2, "lastUpdateVersion");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Boolean value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Boolean value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Boolean value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Boolean value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Boolean> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Boolean> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andRecordYearIsNull() {
            addCriterion("record_year is null");
            return (Criteria) this;
        }

        public Criteria andRecordYearIsNotNull() {
            addCriterion("record_year is not null");
            return (Criteria) this;
        }

        public Criteria andRecordYearEqualTo(Integer value) {
            addCriterion("record_year =", value, "recordYear");
            return (Criteria) this;
        }

        public Criteria andRecordYearNotEqualTo(Integer value) {
            addCriterion("record_year <>", value, "recordYear");
            return (Criteria) this;
        }

        public Criteria andRecordYearGreaterThan(Integer value) {
            addCriterion("record_year >", value, "recordYear");
            return (Criteria) this;
        }

        public Criteria andRecordYearGreaterThanOrEqualTo(Integer value) {
            addCriterion("record_year >=", value, "recordYear");
            return (Criteria) this;
        }

        public Criteria andRecordYearLessThan(Integer value) {
            addCriterion("record_year <", value, "recordYear");
            return (Criteria) this;
        }

        public Criteria andRecordYearLessThanOrEqualTo(Integer value) {
            addCriterion("record_year <=", value, "recordYear");
            return (Criteria) this;
        }

        public Criteria andRecordYearIn(List<Integer> values) {
            addCriterion("record_year in", values, "recordYear");
            return (Criteria) this;
        }

        public Criteria andRecordYearNotIn(List<Integer> values) {
            addCriterion("record_year not in", values, "recordYear");
            return (Criteria) this;
        }

        public Criteria andRecordYearBetween(Integer value1, Integer value2) {
            addCriterion("record_year between", value1, value2, "recordYear");
            return (Criteria) this;
        }

        public Criteria andRecordYearNotBetween(Integer value1, Integer value2) {
            addCriterion("record_year not between", value1, value2, "recordYear");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated do_not_delete_during_merge Wed Feb 05 09:43:33 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_fc_carbon_factor_flc
     *
     * @mbg.generated Wed Feb 05 09:43:33 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}