package com.csci.susdev.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class VehicleFuelUsageExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public VehicleFuelUsageExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(String value) {
			addCriterion("id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(String value) {
			addCriterion("id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(String value) {
			addCriterion("id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(String value) {
			addCriterion("id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(String value) {
			addCriterion("id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(String value) {
			addCriterion("id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLike(String value) {
			addCriterion("id like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotLike(String value) {
			addCriterion("id not like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<String> values) {
			addCriterion("id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<String> values) {
			addCriterion("id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(String value1, String value2) {
			addCriterion("id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(String value1, String value2) {
			addCriterion("id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andAmbientHeadIdIsNull() {
			addCriterion("ambient_head_id is null");
			return (Criteria) this;
		}

		public Criteria andAmbientHeadIdIsNotNull() {
			addCriterion("ambient_head_id is not null");
			return (Criteria) this;
		}

		public Criteria andAmbientHeadIdEqualTo(String value) {
			addCriterion("ambient_head_id =", value, "ambientHeadId");
			return (Criteria) this;
		}

		public Criteria andAmbientHeadIdNotEqualTo(String value) {
			addCriterion("ambient_head_id <>", value, "ambientHeadId");
			return (Criteria) this;
		}

		public Criteria andAmbientHeadIdGreaterThan(String value) {
			addCriterion("ambient_head_id >", value, "ambientHeadId");
			return (Criteria) this;
		}

		public Criteria andAmbientHeadIdGreaterThanOrEqualTo(String value) {
			addCriterion("ambient_head_id >=", value, "ambientHeadId");
			return (Criteria) this;
		}

		public Criteria andAmbientHeadIdLessThan(String value) {
			addCriterion("ambient_head_id <", value, "ambientHeadId");
			return (Criteria) this;
		}

		public Criteria andAmbientHeadIdLessThanOrEqualTo(String value) {
			addCriterion("ambient_head_id <=", value, "ambientHeadId");
			return (Criteria) this;
		}

		public Criteria andAmbientHeadIdLike(String value) {
			addCriterion("ambient_head_id like", value, "ambientHeadId");
			return (Criteria) this;
		}

		public Criteria andAmbientHeadIdNotLike(String value) {
			addCriterion("ambient_head_id not like", value, "ambientHeadId");
			return (Criteria) this;
		}

		public Criteria andAmbientHeadIdIn(List<String> values) {
			addCriterion("ambient_head_id in", values, "ambientHeadId");
			return (Criteria) this;
		}

		public Criteria andAmbientHeadIdNotIn(List<String> values) {
			addCriterion("ambient_head_id not in", values, "ambientHeadId");
			return (Criteria) this;
		}

		public Criteria andAmbientHeadIdBetween(String value1, String value2) {
			addCriterion("ambient_head_id between", value1, value2, "ambientHeadId");
			return (Criteria) this;
		}

		public Criteria andAmbientHeadIdNotBetween(String value1, String value2) {
			addCriterion("ambient_head_id not between", value1, value2, "ambientHeadId");
			return (Criteria) this;
		}

		public Criteria andMonthValueIsNull() {
			addCriterion("month_value is null");
			return (Criteria) this;
		}

		public Criteria andMonthValueIsNotNull() {
			addCriterion("month_value is not null");
			return (Criteria) this;
		}

		public Criteria andMonthValueEqualTo(Integer value) {
			addCriterion("month_value =", value, "monthValue");
			return (Criteria) this;
		}

		public Criteria andMonthValueNotEqualTo(Integer value) {
			addCriterion("month_value <>", value, "monthValue");
			return (Criteria) this;
		}

		public Criteria andMonthValueGreaterThan(Integer value) {
			addCriterion("month_value >", value, "monthValue");
			return (Criteria) this;
		}

		public Criteria andMonthValueGreaterThanOrEqualTo(Integer value) {
			addCriterion("month_value >=", value, "monthValue");
			return (Criteria) this;
		}

		public Criteria andMonthValueLessThan(Integer value) {
			addCriterion("month_value <", value, "monthValue");
			return (Criteria) this;
		}

		public Criteria andMonthValueLessThanOrEqualTo(Integer value) {
			addCriterion("month_value <=", value, "monthValue");
			return (Criteria) this;
		}

		public Criteria andMonthValueIn(List<Integer> values) {
			addCriterion("month_value in", values, "monthValue");
			return (Criteria) this;
		}

		public Criteria andMonthValueNotIn(List<Integer> values) {
			addCriterion("month_value not in", values, "monthValue");
			return (Criteria) this;
		}

		public Criteria andMonthValueBetween(Integer value1, Integer value2) {
			addCriterion("month_value between", value1, value2, "monthValue");
			return (Criteria) this;
		}

		public Criteria andMonthValueNotBetween(Integer value1, Integer value2) {
			addCriterion("month_value not between", value1, value2, "monthValue");
			return (Criteria) this;
		}

		public Criteria andVehicleTypeIsNull() {
			addCriterion("vehicle_type is null");
			return (Criteria) this;
		}

		public Criteria andVehicleTypeIsNotNull() {
			addCriterion("vehicle_type is not null");
			return (Criteria) this;
		}

		public Criteria andVehicleTypeEqualTo(String value) {
			addCriterion("vehicle_type =", value, "vehicleType");
			return (Criteria) this;
		}

		public Criteria andVehicleTypeNotEqualTo(String value) {
			addCriterion("vehicle_type <>", value, "vehicleType");
			return (Criteria) this;
		}

		public Criteria andVehicleTypeGreaterThan(String value) {
			addCriterion("vehicle_type >", value, "vehicleType");
			return (Criteria) this;
		}

		public Criteria andVehicleTypeGreaterThanOrEqualTo(String value) {
			addCriterion("vehicle_type >=", value, "vehicleType");
			return (Criteria) this;
		}

		public Criteria andVehicleTypeLessThan(String value) {
			addCriterion("vehicle_type <", value, "vehicleType");
			return (Criteria) this;
		}

		public Criteria andVehicleTypeLessThanOrEqualTo(String value) {
			addCriterion("vehicle_type <=", value, "vehicleType");
			return (Criteria) this;
		}

		public Criteria andVehicleTypeLike(String value) {
			addCriterion("vehicle_type like", value, "vehicleType");
			return (Criteria) this;
		}

		public Criteria andVehicleTypeNotLike(String value) {
			addCriterion("vehicle_type not like", value, "vehicleType");
			return (Criteria) this;
		}

		public Criteria andVehicleTypeIn(List<String> values) {
			addCriterion("vehicle_type in", values, "vehicleType");
			return (Criteria) this;
		}

		public Criteria andVehicleTypeNotIn(List<String> values) {
			addCriterion("vehicle_type not in", values, "vehicleType");
			return (Criteria) this;
		}

		public Criteria andVehicleTypeBetween(String value1, String value2) {
			addCriterion("vehicle_type between", value1, value2, "vehicleType");
			return (Criteria) this;
		}

		public Criteria andVehicleTypeNotBetween(String value1, String value2) {
			addCriterion("vehicle_type not between", value1, value2, "vehicleType");
			return (Criteria) this;
		}

		public Criteria andVehicleEmissionStandardIsNull() {
			addCriterion("vehicle_emission_standard is null");
			return (Criteria) this;
		}

		public Criteria andVehicleEmissionStandardIsNotNull() {
			addCriterion("vehicle_emission_standard is not null");
			return (Criteria) this;
		}

		public Criteria andVehicleEmissionStandardEqualTo(String value) {
			addCriterion("vehicle_emission_standard =", value, "vehicleEmissionStandard");
			return (Criteria) this;
		}

		public Criteria andVehicleEmissionStandardNotEqualTo(String value) {
			addCriterion("vehicle_emission_standard <>", value, "vehicleEmissionStandard");
			return (Criteria) this;
		}

		public Criteria andVehicleEmissionStandardGreaterThan(String value) {
			addCriterion("vehicle_emission_standard >", value, "vehicleEmissionStandard");
			return (Criteria) this;
		}

		public Criteria andVehicleEmissionStandardGreaterThanOrEqualTo(String value) {
			addCriterion("vehicle_emission_standard >=", value, "vehicleEmissionStandard");
			return (Criteria) this;
		}

		public Criteria andVehicleEmissionStandardLessThan(String value) {
			addCriterion("vehicle_emission_standard <", value, "vehicleEmissionStandard");
			return (Criteria) this;
		}

		public Criteria andVehicleEmissionStandardLessThanOrEqualTo(String value) {
			addCriterion("vehicle_emission_standard <=", value, "vehicleEmissionStandard");
			return (Criteria) this;
		}

		public Criteria andVehicleEmissionStandardLike(String value) {
			addCriterion("vehicle_emission_standard like", value, "vehicleEmissionStandard");
			return (Criteria) this;
		}

		public Criteria andVehicleEmissionStandardNotLike(String value) {
			addCriterion("vehicle_emission_standard not like", value, "vehicleEmissionStandard");
			return (Criteria) this;
		}

		public Criteria andVehicleEmissionStandardIn(List<String> values) {
			addCriterion("vehicle_emission_standard in", values, "vehicleEmissionStandard");
			return (Criteria) this;
		}

		public Criteria andVehicleEmissionStandardNotIn(List<String> values) {
			addCriterion("vehicle_emission_standard not in", values, "vehicleEmissionStandard");
			return (Criteria) this;
		}

		public Criteria andVehicleEmissionStandardBetween(String value1, String value2) {
			addCriterion("vehicle_emission_standard between", value1, value2, "vehicleEmissionStandard");
			return (Criteria) this;
		}

		public Criteria andVehicleEmissionStandardNotBetween(String value1, String value2) {
			addCriterion("vehicle_emission_standard not between", value1, value2, "vehicleEmissionStandard");
			return (Criteria) this;
		}

		public Criteria andVehicleLicenseIsNull() {
			addCriterion("vehicle_license is null");
			return (Criteria) this;
		}

		public Criteria andVehicleLicenseIsNotNull() {
			addCriterion("vehicle_license is not null");
			return (Criteria) this;
		}

		public Criteria andVehicleLicenseEqualTo(String value) {
			addCriterion("vehicle_license =", value, "vehicleLicense");
			return (Criteria) this;
		}

		public Criteria andVehicleLicenseNotEqualTo(String value) {
			addCriterion("vehicle_license <>", value, "vehicleLicense");
			return (Criteria) this;
		}

		public Criteria andVehicleLicenseGreaterThan(String value) {
			addCriterion("vehicle_license >", value, "vehicleLicense");
			return (Criteria) this;
		}

		public Criteria andVehicleLicenseGreaterThanOrEqualTo(String value) {
			addCriterion("vehicle_license >=", value, "vehicleLicense");
			return (Criteria) this;
		}

		public Criteria andVehicleLicenseLessThan(String value) {
			addCriterion("vehicle_license <", value, "vehicleLicense");
			return (Criteria) this;
		}

		public Criteria andVehicleLicenseLessThanOrEqualTo(String value) {
			addCriterion("vehicle_license <=", value, "vehicleLicense");
			return (Criteria) this;
		}

		public Criteria andVehicleLicenseLike(String value) {
			addCriterion("vehicle_license like", value, "vehicleLicense");
			return (Criteria) this;
		}

		public Criteria andVehicleLicenseNotLike(String value) {
			addCriterion("vehicle_license not like", value, "vehicleLicense");
			return (Criteria) this;
		}

		public Criteria andVehicleLicenseIn(List<String> values) {
			addCriterion("vehicle_license in", values, "vehicleLicense");
			return (Criteria) this;
		}

		public Criteria andVehicleLicenseNotIn(List<String> values) {
			addCriterion("vehicle_license not in", values, "vehicleLicense");
			return (Criteria) this;
		}

		public Criteria andVehicleLicenseBetween(String value1, String value2) {
			addCriterion("vehicle_license between", value1, value2, "vehicleLicense");
			return (Criteria) this;
		}

		public Criteria andVehicleLicenseNotBetween(String value1, String value2) {
			addCriterion("vehicle_license not between", value1, value2, "vehicleLicense");
			return (Criteria) this;
		}

		public Criteria andMileageIsNull() {
			addCriterion("mileage is null");
			return (Criteria) this;
		}

		public Criteria andMileageIsNotNull() {
			addCriterion("mileage is not null");
			return (Criteria) this;
		}

		public Criteria andMileageEqualTo(BigDecimal value) {
			addCriterion("mileage =", value, "mileage");
			return (Criteria) this;
		}

		public Criteria andMileageNotEqualTo(BigDecimal value) {
			addCriterion("mileage <>", value, "mileage");
			return (Criteria) this;
		}

		public Criteria andMileageGreaterThan(BigDecimal value) {
			addCriterion("mileage >", value, "mileage");
			return (Criteria) this;
		}

		public Criteria andMileageGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("mileage >=", value, "mileage");
			return (Criteria) this;
		}

		public Criteria andMileageLessThan(BigDecimal value) {
			addCriterion("mileage <", value, "mileage");
			return (Criteria) this;
		}

		public Criteria andMileageLessThanOrEqualTo(BigDecimal value) {
			addCriterion("mileage <=", value, "mileage");
			return (Criteria) this;
		}

		public Criteria andMileageIn(List<BigDecimal> values) {
			addCriterion("mileage in", values, "mileage");
			return (Criteria) this;
		}

		public Criteria andMileageNotIn(List<BigDecimal> values) {
			addCriterion("mileage not in", values, "mileage");
			return (Criteria) this;
		}

		public Criteria andMileageBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("mileage between", value1, value2, "mileage");
			return (Criteria) this;
		}

		public Criteria andMileageNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("mileage not between", value1, value2, "mileage");
			return (Criteria) this;
		}

		public Criteria andCylinderCapacityIsNull() {
			addCriterion("cylinder_capacity is null");
			return (Criteria) this;
		}

		public Criteria andCylinderCapacityIsNotNull() {
			addCriterion("cylinder_capacity is not null");
			return (Criteria) this;
		}

		public Criteria andCylinderCapacityEqualTo(BigDecimal value) {
			addCriterion("cylinder_capacity =", value, "cylinderCapacity");
			return (Criteria) this;
		}

		public Criteria andCylinderCapacityNotEqualTo(BigDecimal value) {
			addCriterion("cylinder_capacity <>", value, "cylinderCapacity");
			return (Criteria) this;
		}

		public Criteria andCylinderCapacityGreaterThan(BigDecimal value) {
			addCriterion("cylinder_capacity >", value, "cylinderCapacity");
			return (Criteria) this;
		}

		public Criteria andCylinderCapacityGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("cylinder_capacity >=", value, "cylinderCapacity");
			return (Criteria) this;
		}

		public Criteria andCylinderCapacityLessThan(BigDecimal value) {
			addCriterion("cylinder_capacity <", value, "cylinderCapacity");
			return (Criteria) this;
		}

		public Criteria andCylinderCapacityLessThanOrEqualTo(BigDecimal value) {
			addCriterion("cylinder_capacity <=", value, "cylinderCapacity");
			return (Criteria) this;
		}

		public Criteria andCylinderCapacityIn(List<BigDecimal> values) {
			addCriterion("cylinder_capacity in", values, "cylinderCapacity");
			return (Criteria) this;
		}

		public Criteria andCylinderCapacityNotIn(List<BigDecimal> values) {
			addCriterion("cylinder_capacity not in", values, "cylinderCapacity");
			return (Criteria) this;
		}

		public Criteria andCylinderCapacityBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("cylinder_capacity between", value1, value2, "cylinderCapacity");
			return (Criteria) this;
		}

		public Criteria andCylinderCapacityNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("cylinder_capacity not between", value1, value2, "cylinderCapacity");
			return (Criteria) this;
		}

		public Criteria andWeightIsNull() {
			addCriterion("weight is null");
			return (Criteria) this;
		}

		public Criteria andWeightIsNotNull() {
			addCriterion("weight is not null");
			return (Criteria) this;
		}

		public Criteria andWeightEqualTo(BigDecimal value) {
			addCriterion("weight =", value, "weight");
			return (Criteria) this;
		}

		public Criteria andWeightNotEqualTo(BigDecimal value) {
			addCriterion("weight <>", value, "weight");
			return (Criteria) this;
		}

		public Criteria andWeightGreaterThan(BigDecimal value) {
			addCriterion("weight >", value, "weight");
			return (Criteria) this;
		}

		public Criteria andWeightGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("weight >=", value, "weight");
			return (Criteria) this;
		}

		public Criteria andWeightLessThan(BigDecimal value) {
			addCriterion("weight <", value, "weight");
			return (Criteria) this;
		}

		public Criteria andWeightLessThanOrEqualTo(BigDecimal value) {
			addCriterion("weight <=", value, "weight");
			return (Criteria) this;
		}

		public Criteria andWeightIn(List<BigDecimal> values) {
			addCriterion("weight in", values, "weight");
			return (Criteria) this;
		}

		public Criteria andWeightNotIn(List<BigDecimal> values) {
			addCriterion("weight not in", values, "weight");
			return (Criteria) this;
		}

		public Criteria andWeightBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("weight between", value1, value2, "weight");
			return (Criteria) this;
		}

		public Criteria andWeightNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("weight not between", value1, value2, "weight");
			return (Criteria) this;
		}

		public Criteria andRatedPowerIsNull() {
			addCriterion("rated_power is null");
			return (Criteria) this;
		}

		public Criteria andRatedPowerIsNotNull() {
			addCriterion("rated_power is not null");
			return (Criteria) this;
		}

		public Criteria andRatedPowerEqualTo(BigDecimal value) {
			addCriterion("rated_power =", value, "ratedPower");
			return (Criteria) this;
		}

		public Criteria andRatedPowerNotEqualTo(BigDecimal value) {
			addCriterion("rated_power <>", value, "ratedPower");
			return (Criteria) this;
		}

		public Criteria andRatedPowerGreaterThan(BigDecimal value) {
			addCriterion("rated_power >", value, "ratedPower");
			return (Criteria) this;
		}

		public Criteria andRatedPowerGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("rated_power >=", value, "ratedPower");
			return (Criteria) this;
		}

		public Criteria andRatedPowerLessThan(BigDecimal value) {
			addCriterion("rated_power <", value, "ratedPower");
			return (Criteria) this;
		}

		public Criteria andRatedPowerLessThanOrEqualTo(BigDecimal value) {
			addCriterion("rated_power <=", value, "ratedPower");
			return (Criteria) this;
		}

		public Criteria andRatedPowerIn(List<BigDecimal> values) {
			addCriterion("rated_power in", values, "ratedPower");
			return (Criteria) this;
		}

		public Criteria andRatedPowerNotIn(List<BigDecimal> values) {
			addCriterion("rated_power not in", values, "ratedPower");
			return (Criteria) this;
		}

		public Criteria andRatedPowerBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("rated_power between", value1, value2, "ratedPower");
			return (Criteria) this;
		}

		public Criteria andRatedPowerNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("rated_power not between", value1, value2, "ratedPower");
			return (Criteria) this;
		}

		public Criteria andFuelTypeIsNull() {
			addCriterion("fuel_type is null");
			return (Criteria) this;
		}

		public Criteria andFuelTypeIsNotNull() {
			addCriterion("fuel_type is not null");
			return (Criteria) this;
		}

		public Criteria andFuelTypeEqualTo(String value) {
			addCriterion("fuel_type =", value, "fuelType");
			return (Criteria) this;
		}

		public Criteria andFuelTypeNotEqualTo(String value) {
			addCriterion("fuel_type <>", value, "fuelType");
			return (Criteria) this;
		}

		public Criteria andFuelTypeGreaterThan(String value) {
			addCriterion("fuel_type >", value, "fuelType");
			return (Criteria) this;
		}

		public Criteria andFuelTypeGreaterThanOrEqualTo(String value) {
			addCriterion("fuel_type >=", value, "fuelType");
			return (Criteria) this;
		}

		public Criteria andFuelTypeLessThan(String value) {
			addCriterion("fuel_type <", value, "fuelType");
			return (Criteria) this;
		}

		public Criteria andFuelTypeLessThanOrEqualTo(String value) {
			addCriterion("fuel_type <=", value, "fuelType");
			return (Criteria) this;
		}

		public Criteria andFuelTypeLike(String value) {
			addCriterion("fuel_type like", value, "fuelType");
			return (Criteria) this;
		}

		public Criteria andFuelTypeNotLike(String value) {
			addCriterion("fuel_type not like", value, "fuelType");
			return (Criteria) this;
		}

		public Criteria andFuelTypeIn(List<String> values) {
			addCriterion("fuel_type in", values, "fuelType");
			return (Criteria) this;
		}

		public Criteria andFuelTypeNotIn(List<String> values) {
			addCriterion("fuel_type not in", values, "fuelType");
			return (Criteria) this;
		}

		public Criteria andFuelTypeBetween(String value1, String value2) {
			addCriterion("fuel_type between", value1, value2, "fuelType");
			return (Criteria) this;
		}

		public Criteria andFuelTypeNotBetween(String value1, String value2) {
			addCriterion("fuel_type not between", value1, value2, "fuelType");
			return (Criteria) this;
		}

		public Criteria andFuelUseAmountIsNull() {
			addCriterion("fuel_use_amount is null");
			return (Criteria) this;
		}

		public Criteria andFuelUseAmountIsNotNull() {
			addCriterion("fuel_use_amount is not null");
			return (Criteria) this;
		}

		public Criteria andFuelUseAmountEqualTo(BigDecimal value) {
			addCriterion("fuel_use_amount =", value, "fuelUseAmount");
			return (Criteria) this;
		}

		public Criteria andFuelUseAmountNotEqualTo(BigDecimal value) {
			addCriterion("fuel_use_amount <>", value, "fuelUseAmount");
			return (Criteria) this;
		}

		public Criteria andFuelUseAmountGreaterThan(BigDecimal value) {
			addCriterion("fuel_use_amount >", value, "fuelUseAmount");
			return (Criteria) this;
		}

		public Criteria andFuelUseAmountGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("fuel_use_amount >=", value, "fuelUseAmount");
			return (Criteria) this;
		}

		public Criteria andFuelUseAmountLessThan(BigDecimal value) {
			addCriterion("fuel_use_amount <", value, "fuelUseAmount");
			return (Criteria) this;
		}

		public Criteria andFuelUseAmountLessThanOrEqualTo(BigDecimal value) {
			addCriterion("fuel_use_amount <=", value, "fuelUseAmount");
			return (Criteria) this;
		}

		public Criteria andFuelUseAmountIn(List<BigDecimal> values) {
			addCriterion("fuel_use_amount in", values, "fuelUseAmount");
			return (Criteria) this;
		}

		public Criteria andFuelUseAmountNotIn(List<BigDecimal> values) {
			addCriterion("fuel_use_amount not in", values, "fuelUseAmount");
			return (Criteria) this;
		}

		public Criteria andFuelUseAmountBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("fuel_use_amount between", value1, value2, "fuelUseAmount");
			return (Criteria) this;
		}

		public Criteria andFuelUseAmountNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("fuel_use_amount not between", value1, value2, "fuelUseAmount");
			return (Criteria) this;
		}

		public Criteria andRemarkIsNull() {
			addCriterion("remark is null");
			return (Criteria) this;
		}

		public Criteria andRemarkIsNotNull() {
			addCriterion("remark is not null");
			return (Criteria) this;
		}

		public Criteria andRemarkEqualTo(String value) {
			addCriterion("remark =", value, "remark");
			return (Criteria) this;
		}

		public Criteria andRemarkNotEqualTo(String value) {
			addCriterion("remark <>", value, "remark");
			return (Criteria) this;
		}

		public Criteria andRemarkGreaterThan(String value) {
			addCriterion("remark >", value, "remark");
			return (Criteria) this;
		}

		public Criteria andRemarkGreaterThanOrEqualTo(String value) {
			addCriterion("remark >=", value, "remark");
			return (Criteria) this;
		}

		public Criteria andRemarkLessThan(String value) {
			addCriterion("remark <", value, "remark");
			return (Criteria) this;
		}

		public Criteria andRemarkLessThanOrEqualTo(String value) {
			addCriterion("remark <=", value, "remark");
			return (Criteria) this;
		}

		public Criteria andRemarkLike(String value) {
			addCriterion("remark like", value, "remark");
			return (Criteria) this;
		}

		public Criteria andRemarkNotLike(String value) {
			addCriterion("remark not like", value, "remark");
			return (Criteria) this;
		}

		public Criteria andRemarkIn(List<String> values) {
			addCriterion("remark in", values, "remark");
			return (Criteria) this;
		}

		public Criteria andRemarkNotIn(List<String> values) {
			addCriterion("remark not in", values, "remark");
			return (Criteria) this;
		}

		public Criteria andRemarkBetween(String value1, String value2) {
			addCriterion("remark between", value1, value2, "remark");
			return (Criteria) this;
		}

		public Criteria andRemarkNotBetween(String value1, String value2) {
			addCriterion("remark not between", value1, value2, "remark");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIsNull() {
			addCriterion("creation_time is null");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIsNotNull() {
			addCriterion("creation_time is not null");
			return (Criteria) this;
		}

		public Criteria andCreationTimeEqualTo(LocalDateTime value) {
			addCriterion("creation_time =", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotEqualTo(LocalDateTime value) {
			addCriterion("creation_time <>", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeGreaterThan(LocalDateTime value) {
			addCriterion("creation_time >", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("creation_time >=", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeLessThan(LocalDateTime value) {
			addCriterion("creation_time <", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("creation_time <=", value, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeIn(List<LocalDateTime> values) {
			addCriterion("creation_time in", values, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotIn(List<LocalDateTime> values) {
			addCriterion("creation_time not in", values, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("creation_time between", value1, value2, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreationTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("creation_time not between", value1, value2, "creationTime");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameIsNull() {
			addCriterion("create_username is null");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameIsNotNull() {
			addCriterion("create_username is not null");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameEqualTo(String value) {
			addCriterion("create_username =", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotEqualTo(String value) {
			addCriterion("create_username <>", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameGreaterThan(String value) {
			addCriterion("create_username >", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameGreaterThanOrEqualTo(String value) {
			addCriterion("create_username >=", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameLessThan(String value) {
			addCriterion("create_username <", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameLessThanOrEqualTo(String value) {
			addCriterion("create_username <=", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameLike(String value) {
			addCriterion("create_username like", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotLike(String value) {
			addCriterion("create_username not like", value, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameIn(List<String> values) {
			addCriterion("create_username in", values, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotIn(List<String> values) {
			addCriterion("create_username not in", values, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameBetween(String value1, String value2) {
			addCriterion("create_username between", value1, value2, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUsernameNotBetween(String value1, String value2) {
			addCriterion("create_username not between", value1, value2, "createUsername");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdIsNull() {
			addCriterion("create_user_id is null");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdIsNotNull() {
			addCriterion("create_user_id is not null");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdEqualTo(String value) {
			addCriterion("create_user_id =", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdNotEqualTo(String value) {
			addCriterion("create_user_id <>", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdGreaterThan(String value) {
			addCriterion("create_user_id >", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdGreaterThanOrEqualTo(String value) {
			addCriterion("create_user_id >=", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdLessThan(String value) {
			addCriterion("create_user_id <", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdLessThanOrEqualTo(String value) {
			addCriterion("create_user_id <=", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdLike(String value) {
			addCriterion("create_user_id like", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdNotLike(String value) {
			addCriterion("create_user_id not like", value, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdIn(List<String> values) {
			addCriterion("create_user_id in", values, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdNotIn(List<String> values) {
			addCriterion("create_user_id not in", values, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdBetween(String value1, String value2) {
			addCriterion("create_user_id between", value1, value2, "createUserId");
			return (Criteria) this;
		}

		public Criteria andCreateUserIdNotBetween(String value1, String value2) {
			addCriterion("create_user_id not between", value1, value2, "createUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIsNull() {
			addCriterion("last_update_time is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIsNotNull() {
			addCriterion("last_update_time is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeEqualTo(LocalDateTime value) {
			addCriterion("last_update_time =", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotEqualTo(LocalDateTime value) {
			addCriterion("last_update_time <>", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeGreaterThan(LocalDateTime value) {
			addCriterion("last_update_time >", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("last_update_time >=", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeLessThan(LocalDateTime value) {
			addCriterion("last_update_time <", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("last_update_time <=", value, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeIn(List<LocalDateTime> values) {
			addCriterion("last_update_time in", values, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotIn(List<LocalDateTime> values) {
			addCriterion("last_update_time not in", values, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("last_update_time between", value1, value2, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("last_update_time not between", value1, value2, "lastUpdateTime");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameIsNull() {
			addCriterion("last_update_username is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameIsNotNull() {
			addCriterion("last_update_username is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameEqualTo(String value) {
			addCriterion("last_update_username =", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotEqualTo(String value) {
			addCriterion("last_update_username <>", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameGreaterThan(String value) {
			addCriterion("last_update_username >", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameGreaterThanOrEqualTo(String value) {
			addCriterion("last_update_username >=", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameLessThan(String value) {
			addCriterion("last_update_username <", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameLessThanOrEqualTo(String value) {
			addCriterion("last_update_username <=", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameLike(String value) {
			addCriterion("last_update_username like", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotLike(String value) {
			addCriterion("last_update_username not like", value, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameIn(List<String> values) {
			addCriterion("last_update_username in", values, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotIn(List<String> values) {
			addCriterion("last_update_username not in", values, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameBetween(String value1, String value2) {
			addCriterion("last_update_username between", value1, value2, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUsernameNotBetween(String value1, String value2) {
			addCriterion("last_update_username not between", value1, value2, "lastUpdateUsername");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdIsNull() {
			addCriterion("last_update_user_id is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdIsNotNull() {
			addCriterion("last_update_user_id is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdEqualTo(String value) {
			addCriterion("last_update_user_id =", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdNotEqualTo(String value) {
			addCriterion("last_update_user_id <>", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdGreaterThan(String value) {
			addCriterion("last_update_user_id >", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdGreaterThanOrEqualTo(String value) {
			addCriterion("last_update_user_id >=", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdLessThan(String value) {
			addCriterion("last_update_user_id <", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdLessThanOrEqualTo(String value) {
			addCriterion("last_update_user_id <=", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdLike(String value) {
			addCriterion("last_update_user_id like", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdNotLike(String value) {
			addCriterion("last_update_user_id not like", value, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdIn(List<String> values) {
			addCriterion("last_update_user_id in", values, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdNotIn(List<String> values) {
			addCriterion("last_update_user_id not in", values, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdBetween(String value1, String value2) {
			addCriterion("last_update_user_id between", value1, value2, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateUserIdNotBetween(String value1, String value2) {
			addCriterion("last_update_user_id not between", value1, value2, "lastUpdateUserId");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionIsNull() {
			addCriterion("last_update_version is null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionIsNotNull() {
			addCriterion("last_update_version is not null");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionEqualTo(Integer value) {
			addCriterion("last_update_version =", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionNotEqualTo(Integer value) {
			addCriterion("last_update_version <>", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionGreaterThan(Integer value) {
			addCriterion("last_update_version >", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionGreaterThanOrEqualTo(Integer value) {
			addCriterion("last_update_version >=", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionLessThan(Integer value) {
			addCriterion("last_update_version <", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionLessThanOrEqualTo(Integer value) {
			addCriterion("last_update_version <=", value, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionIn(List<Integer> values) {
			addCriterion("last_update_version in", values, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionNotIn(List<Integer> values) {
			addCriterion("last_update_version not in", values, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionBetween(Integer value1, Integer value2) {
			addCriterion("last_update_version between", value1, value2, "lastUpdateVersion");
			return (Criteria) this;
		}

		public Criteria andLastUpdateVersionNotBetween(Integer value1, Integer value2) {
			addCriterion("last_update_version not between", value1, value2, "lastUpdateVersion");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table t_ab_vehicle_fuel_usage
	 * @mbg.generated  Tue Oct 10 15:31:38 HKT 2023
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table t_ab_vehicle_fuel_usage
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }
}