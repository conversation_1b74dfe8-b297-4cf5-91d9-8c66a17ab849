package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.FcCarbonFactorHkQO;
import com.csci.susdev.service.FcCarbonFactorHkService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.FcCarbonFactorHkVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/api/fcCarbonFactorHk", produces = "application/json")
@Tag(name = "碳排因子+香港碳排数据 接口", description = "用于接口调试")
@LogMethod
public class FcCarbonFactorHkController {

    @Resource
    private FcCarbonFactorHkService fcCarbonFactorHkService;

    @Resource
    private UserService userService;

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody FcCarbonFactorHkVO fcCarbonFactorHkVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(fcCarbonFactorHkService.saveFcCarbonFactorHk(fcCarbonFactorHkVO));
    }

    @PostMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<FcCarbonFactorHkVO> listFcCarbonFactorHk(@RequestBody FcCarbonFactorHkQO fcCarbonFactorHkQO) {
        return fcCarbonFactorHkService.listFcCarbonFactorHk(fcCarbonFactorHkQO);
    }

    @DeleteMapping("/{id}")
    @Operation(description = "删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteFcCarbonFactorHk(@PathVariable String id) {
        userService.checkIsReadOnly();

        fcCarbonFactorHkService.deleteFcCarbonFactorHk(id);
        return ResultBase.success();
    }

    @GetMapping("/{id}")
    @Operation(description = "获取记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<FcCarbonFactorHkVO> getFcCarbonFactorHk(@PathVariable String id) {
        return new ResultBean<>(fcCarbonFactorHkService.getFcCarbonFactorHk(id));
    }
}
