package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ApiLog;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.ApiLogQO;
import com.csci.susdev.qo.ListApiLogByHeadIdQO;
import com.csci.susdev.service.ApiLogService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.ApiLogVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/api/apiLog", produces = "application/json")
@Tag(name = "API日誌 接口", description = "用于接口调试")
@LogMethod
public class ApiLogController {

    @Resource
    private ApiLogService apiLogService;

    @Resource
    private UserService userService;

    @PostMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<ApiLogVO> listApiLog(@RequestBody ApiLogQO apiLogQO) {
        return apiLogService.listApiLog(apiLogQO);
    }

    /**
     * 只查询变更数据的操作
     * @param qo
     * @return
     */
    @PostMapping("/listApiLogByHeadId")
    @Operation(description = "根据业务id查询具体业务api记录（只查询变更数据的操作）")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<ApiLogVO> listApiLogByHeadId(@Validated @RequestBody ListApiLogByHeadIdQO qo) {
        return apiLogService.listApiLogByHeadId(qo);
    }

    @GetMapping("/{id}")
    @Operation(description = "获取记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<ApiLog> getApiLog(@PathVariable String id) {
        return new ResultBean<>(apiLogService.getApiLog(id));
    }
}
