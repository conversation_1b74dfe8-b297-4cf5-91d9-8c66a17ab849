package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.SubmissionMonthConfigQO;
import com.csci.susdev.service.SubmissionMonthConfigService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.SubmissionMonthConfigVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/api/submissionMonthConfig", produces = "application/json")
@Tag(name = "月度審核配置 接口", description = "用于接口调试")
@LogMethod
public class SubmissionMonthConfigController {

    @Resource
    private SubmissionMonthConfigService submissionMonthConfigService;

    @Resource
    private UserService userService;

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody SubmissionMonthConfigVO submissionMonthConfigVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(submissionMonthConfigService.saveSubmissionMonthConfig(submissionMonthConfigVO));
    }

    @PostMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<SubmissionMonthConfigVO> listSubmissionMonthConfig(@RequestBody SubmissionMonthConfigQO submissionMonthConfigQO) {
        return submissionMonthConfigService.listSubmissionMonthConfig(submissionMonthConfigQO);
    }

    @DeleteMapping("/{id}")
    @Operation(description = "删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteSubmissionMonthConfig(@PathVariable String id) {
        userService.checkIsReadOnly();

        submissionMonthConfigService.deleteSubmissionMonthConfig(id);
        return ResultBase.success();
    }

    @GetMapping("/{id}")
    @Operation(description = "获取记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<SubmissionMonthConfigVO> getSubmissionMonthConfig(@PathVariable String id) {
        return new ResultBean<>(submissionMonthConfigService.getSubmissionMonthConfig(id));
    }
}
