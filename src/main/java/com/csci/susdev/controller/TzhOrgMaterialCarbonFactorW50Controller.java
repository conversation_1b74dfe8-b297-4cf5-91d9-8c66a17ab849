package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.service.TzhOrgMaterialCarbonFactorW50Service;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.tzh.model.TzhOrgMaterialCarbonFactor;
import com.csci.tzh.model.TzhOrgMaterialCarbonFactorW50;
import com.csci.tzh.qo.TzhOrgMaterialCarbonFactorPageableQO;
import com.csci.tzh.qo.TzhOrgMaterialCarbonFactorW50PageableQO;
import com.csci.tzh.vo.TzhOrgMaterialCarbonFactorVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/api/tzh/tzh-org-material-carbon-factor-w50", produces = "application/json")
@Tag(name = "集團統籌-因子管理 接口展示", description = "用于接口调试")
@LogMethod
public class TzhOrgMaterialCarbonFactorW50Controller {

    @Autowired
    private TzhOrgMaterialCarbonFactorW50Service service;

    @Resource
    private UserService userService;

    @GetMapping("/list")
    @Operation(description = "查詢 集團統籌-因子管理 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = TzhOrgMaterialCarbonFactorW50.class)))
    public ResultPage<TzhOrgMaterialCarbonFactorW50> list(TzhOrgMaterialCarbonFactorW50PageableQO qo) {
        return service.list(qo);
    }
    
    @PostMapping("/save")
    @Operation(description = "保存 集團統籌-因子管理 所有記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<TzhOrgMaterialCarbonFactorW50> save(@RequestBody TzhOrgMaterialCarbonFactorW50 model) {
        userService.checkIsReadOnly();

        return new ResultBean<>(service.save(model));
    }
}
