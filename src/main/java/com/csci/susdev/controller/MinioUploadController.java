package com.csci.susdev.controller;

import com.csci.susdev.util.MinioUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;


@RestController
@RequestMapping("/api/minio-file")
public class MinioUploadController {
    @Autowired
    private MinioUtil minioUtil;

    /**
     * 单文件上传
     * @param file 文件
     * @return
     */
    @PostMapping("/upload")
    public Object uploadOne(MultipartFile file) {
        return minioUtil.upload(file);
    }

    /**
     * 预览文件
     */
    @GetMapping("/preview")
    public String preview(String fileName) {
        return minioUtil.getFileUrl(fileName);
    }


    /**
     *  单文件下载
     * @param fileName 文件
     * @param delete 下载完后是否删除, 请谨慎传参
     */
    @GetMapping("/download")
    public void download(@RequestParam(value = "fileName") String fileName,
                         @RequestParam(defaultValue = "false")Boolean delete, HttpServletResponse response) {
        minioUtil.fileDownload(fileName,delete,response);
    }

    /**
     * 删除文件
     */
    @GetMapping("/delete")
    public String delete(String fileName) {
        minioUtil.delete(fileName);
        return "删除成功";
    }

}
