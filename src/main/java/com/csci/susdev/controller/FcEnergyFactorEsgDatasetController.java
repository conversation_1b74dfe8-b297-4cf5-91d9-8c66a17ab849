package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.FcEnergyFactorEsgDatasetQO;
import com.csci.susdev.service.FcEnergyFactorEsgDatasetService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.FcEnergyFactorEsgDatasetVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/api/fcEnergyFactorEsgDataset", produces = "application/json")
@Tag(name = "能源因子+esg数据集 接口", description = "用于接口调试")
@LogMethod
public class FcEnergyFactorEsgDatasetController {

    @Resource
    private FcEnergyFactorEsgDatasetService fcEnergyFactorEsgDatasetService;

    @Resource
    private UserService userService;

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody FcEnergyFactorEsgDatasetVO fcEnergyFactorEsgDatasetVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(fcEnergyFactorEsgDatasetService.saveFcEnergyFactorEsgDataset(fcEnergyFactorEsgDatasetVO));
    }

    @PostMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<FcEnergyFactorEsgDatasetVO> listFcEnergyFactorEsgDataset(@RequestBody FcEnergyFactorEsgDatasetQO fcEnergyFactorEsgDatasetQO) {
        return fcEnergyFactorEsgDatasetService.listFcEnergyFactorEsgDataset(fcEnergyFactorEsgDatasetQO);
    }

    @DeleteMapping("/{id}")
    @Operation(description = "删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteFcEnergyFactorEsgDataset(@PathVariable String id) {
        userService.checkIsReadOnly();

        fcEnergyFactorEsgDatasetService.deleteFcEnergyFactorEsgDataset(id);
        return ResultBase.success();
    }

    @GetMapping("/{id}")
    @Operation(description = "获取记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<FcEnergyFactorEsgDatasetVO> getFcEnergyFactorEsgDataset(@PathVariable String id) {
        return new ResultBean<>(fcEnergyFactorEsgDatasetService.getFcEnergyFactorEsgDataset(id));
    }
}
