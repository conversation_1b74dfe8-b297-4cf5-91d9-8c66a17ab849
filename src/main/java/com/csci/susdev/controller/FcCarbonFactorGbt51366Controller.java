package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.FcCarbonFactorGbt51366QO;
import com.csci.susdev.service.FcCarbonFactorGbt51366Service;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.FcCarbonFactorGbt51366VO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/api/fcCarbonFactorGbt51366", produces = "application/json")
@Tag(name = "碳排因子+GBT 51366 接口", description = "用于接口调试")
@LogMethod
public class FcCarbonFactorGbt51366Controller {

    @Resource
    private FcCarbonFactorGbt51366Service fcCarbonFactorGbt51366Service;

    @Resource
    private UserService userService;

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody FcCarbonFactorGbt51366VO fcCarbonFactorGbt51366VO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(fcCarbonFactorGbt51366Service.saveFcCarbonFactorGbt51366(fcCarbonFactorGbt51366VO));
    }

    @PostMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<FcCarbonFactorGbt51366VO> listFcCarbonFactorGbt51366(@RequestBody FcCarbonFactorGbt51366QO fcCarbonFactorGbt51366QO) {
        return fcCarbonFactorGbt51366Service.listFcCarbonFactorGbt51366(fcCarbonFactorGbt51366QO);
    }

    @DeleteMapping("/{id}")
    @Operation(description = "删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteFcCarbonFactorGbt51366(@PathVariable String id) {
        userService.checkIsReadOnly();

        fcCarbonFactorGbt51366Service.deleteFcCarbonFactorGbt51366(id);
        return ResultBase.success();
    }

    @GetMapping("/{id}")
    @Operation(description = "获取记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<FcCarbonFactorGbt51366VO> getFcCarbonFactorGbt51366(@PathVariable String id) {
        return new ResultBean<>(fcCarbonFactorGbt51366Service.getFcCarbonFactorGbt51366(id));
    }
}
