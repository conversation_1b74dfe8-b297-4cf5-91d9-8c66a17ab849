package com.csci.susdev.controller;

import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.util.OnceTokenManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/api/once-token")
@Tag(name = "OnceToken 接口展示", description = "用于接口调试")
public class OnceTokenController {

    @Resource
    private OnceTokenManager onceTokenManager;

    @GetMapping("/fetch")
    @Operation(description = "获取一次性token")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> getOnceToken() {
        return new ResultBean<>(onceTokenManager.generateOnceToken());
    }

}
