package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.model.SocialPerfTwoActivity;
import com.csci.susdev.modelcovt.SocialPerfTwoActivityConverter;
import com.csci.susdev.qo.SocialPerfTwoActivityQO;
import com.csci.susdev.service.SocialPerfTwoActivityService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.vo.SocialPerfTwoActivityVO;
import com.csci.susdev.vo.IdVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/social-perf-two-activity")
@Tag(name = "社會積效活動", description = "社會積效活動")
@LogMethod
public class SocialPerfTwoActivityController {


    @Resource
    private UserService userService;

    @Resource
    private SocialPerfTwoActivityService socialPerfTwoActivityService;

    @GetMapping("/list")
    @Operation(description = "分页查询数据")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<SocialPerfTwoActivityVO> listSocialPerfTwoActivity(SocialPerfTwoActivityQO socialPerfTwoActivityQO) {
        return socialPerfTwoActivityService.listSocialPerfTwoActivity(socialPerfTwoActivityQO);
    }

    @PostMapping("/integrate")
    @Operation(description = "垂直數據整合")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> integrate(@RequestBody SocialPerfTwoActivityQO SocialPerfTwoActivityQO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(socialPerfTwoActivityService.integrateSocialPerfTwoActivity(SocialPerfTwoActivityQO));
    }

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody SocialPerfTwoActivityVO socialPerfTwoActivityVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(socialPerfTwoActivityService.saveSocialPerfTwoActivity(socialPerfTwoActivityVO));
    }

    @PostMapping("/duplicate")
    @Operation(description = "复制记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<SocialPerfTwoActivityVO> duplicate(@RequestBody IdVO idVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(SocialPerfTwoActivityConverter.convert(socialPerfTwoActivityService.duplicateSocialPerfTwoActivity(idVO.getId())));
    }

    @GetMapping("/{id}")
    @Operation(description = "根据id查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<SocialPerfTwoActivityVO> getSocialPerfTwoActivityById(@PathVariable String id) {
        return new ResultBean<>(socialPerfTwoActivityService.getSocialPerfTwoActivityById(id));
    }

    @DeleteMapping("/{id}")
    @Operation(description = "根据id删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteSocialPerfTwoActivityById(@PathVariable String id) {
        userService.checkIsReadOnly();

        socialPerfTwoActivityService.deleteSocialPerfTwoActivityById(id);
        return new ResultBase();
    }
}
