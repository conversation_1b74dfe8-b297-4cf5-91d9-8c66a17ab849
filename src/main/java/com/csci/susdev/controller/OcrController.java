package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.service.OcrService;
import com.csci.susdev.vo.OcrElectricityBillVO;
import com.csci.susdev.vo.OcrNaturalGasBillVO;
import com.csci.susdev.vo.OcrWaterBillVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping(value = "/api/ocr", produces = "application/json")
@Tag(name = "OCR 接口展示", description = "用于接口调试")
@LogMethod
public class OcrController {

    @Autowired
    private OcrService ocrService;
    
    @PostMapping(value = "/extractwaterbillinfo", produces = "application/json", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(description = "提取 水費收據 數據")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200")
    public ResultBean<OcrWaterBillVO> extractWaterBillInfo(@RequestPart("file") MultipartFile file) throws Exception {
    	String json = ocrService.convertPdf2Json(file);
        return new ResultBean<OcrWaterBillVO>(ocrService.extractWaterBillInfo(json));
    }
    
    @PostMapping(value = "/extractelectricitybillinfo", produces = "application/json", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(description = "提取 電費收據 數據")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200")
    public ResultBean<OcrElectricityBillVO> extractElectricityBillInfo(@RequestPart("file") MultipartFile file) throws Exception {
    	String json = ocrService.convertPdf2Json(file);
        return new ResultBean<OcrElectricityBillVO>(ocrService.extractElectricityBillInfo(json));
    }

    @PostMapping(value = "/extractnaturalgasbillinfo", produces = "application/json", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(description = "提取 天然氣收據 數據")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200")
    public ResultBean<OcrNaturalGasBillVO> extractNaturalGasBillInfo(@RequestPart("file") MultipartFile file) throws Exception {
    	String json = ocrService.convertPdf2Json(file);

        return new ResultBean<OcrNaturalGasBillVO>(ocrService.extractNaturalGasBillInfo(json));
    }
}
