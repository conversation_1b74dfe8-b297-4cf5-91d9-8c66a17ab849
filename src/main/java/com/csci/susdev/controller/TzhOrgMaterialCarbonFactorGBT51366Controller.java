package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.service.TzhOrgMaterialCarbonFactorGBT51366Service;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.tzh.model.TzhOrgMaterialCarbonFactorGBT51366;
import com.csci.tzh.qo.TzhOrgMaterialCarbonFactorGBT51366PageableQO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/api/tzh/tzh-org-material-carbon-factor-gbt51366", produces = "application/json")
@Tag(name = "集團統籌-因子管理 接口展示", description = "用于接口调试")
@LogMethod
public class TzhOrgMaterialCarbonFactorGBT51366Controller {

    @Autowired
    private TzhOrgMaterialCarbonFactorGBT51366Service service;

    @Resource
    private UserService userService;

    @GetMapping("/list")
    @Operation(description = "查詢 集團統籌-因子管理 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = TzhOrgMaterialCarbonFactorGBT51366.class)))
    public ResultPage<TzhOrgMaterialCarbonFactorGBT51366> list(TzhOrgMaterialCarbonFactorGBT51366PageableQO qo) {
        return service.list(qo);
    }
    
    @PostMapping("/save")
    @Operation(description = "保存 集團統籌-因子管理 所有記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<TzhOrgMaterialCarbonFactorGBT51366> save(@RequestBody TzhOrgMaterialCarbonFactorGBT51366 model) {
        userService.checkIsReadOnly();

        return new ResultBean<>(service.save(model));
    }
}
