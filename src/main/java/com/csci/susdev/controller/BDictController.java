package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.BDictQO;
import com.csci.susdev.service.BDictService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.BDictVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/api/bDict", produces = "application/json")
@Tag(name = "数据字典 接口", description = "用于接口调试")
@LogMethod
public class BDictController {

    @Resource
    private BDictService bDictService;

    @Resource
    private UserService userService;

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody BDictVO bDictVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(bDictService.saveBDict(bDictVO));
    }

    @PostMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<BDictVO> listBDict(@RequestBody BDictQO bDictQO) {
        return bDictService.listBDict(bDictQO);
    }

    @DeleteMapping("/{id}")
    @Operation(description = "删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteBDict(@PathVariable String id) {
        userService.checkIsReadOnly();

        bDictService.deleteBDict(id);
        return ResultBase.success();
    }

    @GetMapping("/{id}")
    @Operation(description = "获取记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<BDictVO> getBDict(@PathVariable String id) {
        return new ResultBean<>(bDictService.getBDict(id));
    }

    @PostMapping("/save/all")
    @Operation(description = "批量保存数据字典")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<String>> saveBDictList(@RequestBody List<BDictVO> bDictVOList) {
        userService.checkIsReadOnly();

        return new ResultBean<>(bDictService.saveBDictList(bDictVOList));
    }


}
