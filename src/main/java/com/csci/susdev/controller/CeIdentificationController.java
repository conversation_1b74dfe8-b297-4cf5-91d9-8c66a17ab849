package com.csci.susdev.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.facade.WorkflowFacade;
import com.csci.susdev.model.*;
import com.csci.susdev.qo.CeIdentificationQO;
import com.csci.susdev.qo.CeIdentificationQO;
import com.csci.susdev.service.*;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.redis.RedisLockUtil;
import com.csci.susdev.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@RestController
@RequestMapping(value = "/api/ce-identification", produces = "application/json")
@Tag(name = "碳排識別 接口展示", description = "用于接口调试")
@LogMethod
public class CeIdentificationController {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(CeIdentificationController.class);


    @Resource
    private UserService userService;

    @Resource
    private CeIdentificationService ceIdentificationService;

    @Resource
    private WorkflowFacade workflowFacade;

    @Resource
    private FormService formService;

    @Resource
    private WorkflowControlService workflowControlService;

    @Resource
    private WorkflowService workflowService;

    private String formId;

    @PostMapping("/get-or-init")
    @Operation(description = "获取表格")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<CeIdentificationHeadVO> getOrInit(@RequestBody CeIdentificationQO ceIdentificationQO) {
        return new ResultBean<>(ceIdentificationService.getOrInit(ceIdentificationQO));

    }

    @GetMapping("/detail/list")
    @Operation(description = "获取碳排識別明细列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultList<CeIdentificationDetailVO> listCeIdentificationDetail(String headId) {
        return new ResultList<>(ceIdentificationService.listCeIdentificationDetail(headId));
    }

    @GetMapping("/detail/table-list")
    @Operation(description = "获取碳排識別明细表格列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultList<CeIdentificationTableDataVO> listCeIdentificationTable(String headId) {
        return new ResultList<>(ceIdentificationService.listCeIdentificationTable(headId));
    }

    @PostMapping("/update")
    @Operation(description = "更新碳排識別")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase updateCeIdentification(@RequestBody CeIdentificationHeadVO ceIdentificationHeadVO) {
        userService.checkIsReadOnly();

        ceIdentificationService.updateCeIdentificationWithDetail(ceIdentificationHeadVO);
        return new ResultBase();
    }

    @PostMapping("/submit")
    @Operation(description = "提交碳排識別")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase submit(@RequestBody IdVersionVO idVersionVO) {
        userService.checkIsReadOnly();

        ceIdentificationService.submit(idVersionVO);
        return new ResultBase();
    }

    @PostMapping("/approve")
    @Operation(description = "审批碳排識別")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase approve(@RequestBody ApproveParamVO approveParamVO) {
        userService.checkIsReadOnly();

        checkExist(approveParamVO.getId(), "碳排識別id不能为空");
        CeIdentificationHead ceIdentificationHead = ceIdentificationService.selectByPrimaryKey(approveParamVO.getId());

        WorkflowControl workflowControl = workflowControlService.findWorkflowControlByBusinessId(approveParamVO.getId());
        if (Objects.isNull(workflowControl)) {
            workflowFacade.startWorkflow(ceIdentificationHead.getOrganizationId(), getFormId(), approveParamVO.getId());
        } else {
            workflowFacade.approve(approveParamVO.getId(), approveParamVO.getRemark());
        }
        workflowControlService.sendReviewEmail(approveParamVO.getId());
        return new ResultBase();
    }

    @PostMapping("/reject")
    @Operation(description = "驳回碳排識別")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase reject(@RequestBody ApproveParamVO approveParamVO) {
        userService.checkIsReadOnly();

        checkExist(approveParamVO.getId(), "碳排識別id不能为空");
        CeIdentificationHead ceIdentificationHead = ceIdentificationService.selectByPrimaryKey(approveParamVO.getId());

        Integer year = workflowControlService.findFormYearByBusinessId(approveParamVO.getId());
        Integer month = workflowControlService.findFormMonthByBusinessId(approveParamVO.getId());
        if(Objects.isNull(year)) {
            throw new ServiceException("找不到相關表單年份");
        }
        if(Objects.isNull(month)) {
            throw new ServiceException("找不到相關表單月份");
        }

        Workflow workflow = workflowService.findWorkflowByOrgIdAndFormId(ceIdentificationHead.getOrganizationId(),
                getFormId(), year, month);
        checkExist(workflow, "工作流未配置");

        workflowFacade.reject(approveParamVO.getId(), approveParamVO.getRemark());
        return new ResultBase();
    }

    @PostMapping("/recall")
    @Operation(description = "撤回碳排識別")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase recall(@RequestBody ApproveParamVO approveParamVO) {
        userService.checkIsReadOnly();

        checkExist(approveParamVO.getId(), "碳排識別id不能为空");
        CeIdentificationHead ceIdentificationHead = ceIdentificationService.selectByPrimaryKey(approveParamVO.getId());

        Integer year = workflowControlService.findFormYearByBusinessId(approveParamVO.getId());
        Integer month = workflowControlService.findFormMonthByBusinessId(approveParamVO.getId());
        if(Objects.isNull(year)) {
            throw new ServiceException("找不到相關表單年份");
        }
        if(Objects.isNull(month)) {
            throw new ServiceException("找不到相關表單月份");
        }

        Workflow workflow = workflowService.findWorkflowByOrgIdAndFormId(ceIdentificationHead.getOrganizationId(),
                getFormId(), year, month);
        checkExist(workflow, "工作流未配置");

        workflowFacade.recall(approveParamVO.getId(), approveParamVO.getRemark());
        return new ResultBase();
    }

    @PostMapping("/terminate")
    @Operation(description = "终止碳排識別")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase terminate(@RequestBody ApproveParamVO approveParamVO) {
        userService.checkIsReadOnly();

        checkExist(approveParamVO.getId(), "碳排識別id不能为空");
        CeIdentificationHead ceIdentificationHead = ceIdentificationService.selectByPrimaryKey(approveParamVO.getId());

        Integer year = workflowControlService.findFormYearByBusinessId(approveParamVO.getId());
        Integer month = workflowControlService.findFormMonthByBusinessId(approveParamVO.getId());
        if(Objects.isNull(year)) {
            throw new ServiceException("找不到相關表單年份");
        }
        if(Objects.isNull(month)) {
            throw new ServiceException("找不到相關表單月份");
        }

        Workflow workflow = workflowService.findWorkflowByOrgIdAndFormId(ceIdentificationHead.getOrganizationId(),
                getFormId(), year, month);
        checkExist(workflow, "工作流未配置");

        workflowFacade.terminate(approveParamVO.getId(), approveParamVO.getRemark());
        return new ResultBase();
    }

    private String getFormId() {
        if (StringUtils.isBlank(formId)) {
            Form form = formService.findFormByCode("ce-identification");
            formId = Optional.ofNullable(form).map(Form::getId).orElseThrow(() -> new ServiceException("未找到表单"));
        }
        return formId;
    }
}
