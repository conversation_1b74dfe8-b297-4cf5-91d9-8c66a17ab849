package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultList;
import com.csci.susdev.qo.BatchIdPageQO;
import com.csci.susdev.qo.BatchQO;
import com.csci.susdev.service.*;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/api/cf/")
@Tag(name = "碳因子排放管理接口", description = "CFBatchController")
@LogMethod
public class CFBatchController {


    @Resource
    private UserService userService;

    @Autowired
    private BatchService batchService;

    @Autowired
    private AreaService areaService;

    @Autowired
    private EmissionFactorService emissionFactorService;

    @Autowired
    private AirPollutantsService airPollutantsService;

    @GetMapping("/v1/area/list")
    @Operation(description = "获取区域列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultList<AreaVO> listArea() {
        return new ResultList<>(areaService.listArea());
    }

    @GetMapping("/v1/batch-or-init")
    @Operation(description = "获取批次信息")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> getOrInitBatch(BatchQO batchQO) {
        return new ResultBean<>(batchService.getOrInitBatch(batchQO.getAreaId(), batchQO.getYear(), batchQO.getMonth()).getId());
    }

    @PostMapping("/v1/batch/save/emission-factor")
    @Operation(description = "保存排放因子")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase saveBatchWithEmissionFactor(@RequestBody BatchWithEmissionFactorVO batchWithEmissionFactorVO) {
        userService.checkIsReadOnly();

        batchService.saveBatchWithEmissionFactors(batchWithEmissionFactorVO);
        return ResultBase.success();
    }

    @PostMapping("/v1/batch/save/air-pollutants")
    @Operation(description = "保存空气污染物因子")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase saveBatchWithAirPollutants(@RequestBody BatchWithAirPollutantsVO batchWithAirPollutantsVO) {
        userService.checkIsReadOnly();

        batchService.saveBatchWithAirPollutants(batchWithAirPollutantsVO);
        return ResultBase.success();
    }

    @GetMapping("/v1/batch/emission-factor/list")
    @Operation(description = "获取排放因子列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultList<EmissionFactorVO> listEmissionFactor(BatchIdPageQO emissionFactorQO) {
        return emissionFactorService.listEmissionFactorByPage(emissionFactorQO);
    }

    @GetMapping("/v1/batch/air-pollutants/list")
    @Operation(description = "获取空气污染物列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultList<AirPollutantsVO> listAirPollutants(BatchIdPageQO batchIdPageQO) {
        return airPollutantsService.listAirPollutantsByPage(batchIdPageQO);
    }

    @PostMapping("/v1/batch/emission-factor/add")
    @Operation(description = "添加排放因子")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> addEmissionFactor(@RequestBody EmissionFactorVO emissionFactorVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(emissionFactorService.addEmissionFactor(emissionFactorVO));
    }

    @PostMapping("/v1/batch/air-pollutants/add")
    @Operation(description = "添加空气污染物")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> addAirPollutants(@RequestBody AirPollutantsVO airPollutantsVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(airPollutantsService.addAirPollutants(airPollutantsVO));
    }

    @PostMapping("/v1/batch/emission-factor/update")
    @Operation(description = "更新排放因子")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase updateEmissionFactor(@RequestBody EmissionFactorVO emissionFactorVO) {
        userService.checkIsReadOnly();

        batchService.updateEmissionFactor(emissionFactorVO);
        return ResultBase.success();
    }

    @PostMapping("/v1/batch/air-pollutants/update")
    @Operation(description = "更新空气污染物")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase updateAirPollutants(@RequestBody AirPollutantsVO airPollutantsVO) {
        userService.checkIsReadOnly();

        batchService.updateAirPollutants(airPollutantsVO);
        return ResultBase.success();
    }

    @PostMapping("/v1/batch/emission-factor/delete")
    @Operation(description = "删除排放因子")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteEmissionFactor(@RequestBody IdVO idVO) {
        userService.checkIsReadOnly();

        emissionFactorService.deleteByPrimaryKey(idVO.getId());
        return ResultBase.success();
    }

    @PostMapping("/v1/batch/air-pollutants/delete")
    @Operation(description = "删除空气污染物")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteAirPollutants(@RequestBody IdVO idVO) {
        userService.checkIsReadOnly();

        airPollutantsService.deleteAirPollutants(idVO.getId());
        return ResultBase.success();
    }

    @GetMapping("/v1/air-pollutants/get/{id}")
    @Operation(description = "获取空气污染物")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<AirPollutantsVO> getAirPollutantsById(@PathVariable String id) {
        return new ResultBean<>(airPollutantsService.getAirPollutantsById(id));
    }

    @PostMapping("/v1/air-pollutants/save")
    @Operation(description = "保存空气污染物")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> saveAirPollutants(@RequestBody AirPollutantsVO airPollutantsVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(airPollutantsService.saveAirPollutants(airPollutantsVO).getId());
    }
}
