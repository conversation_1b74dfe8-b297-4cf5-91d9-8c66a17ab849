package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.modelcovt.EmissionReductionInfoConverter;
import com.csci.susdev.qo.EmissionReductionInfoQO;
import com.csci.susdev.service.EmissionReductionInfoService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.vo.EmissionReductionBatchDeleteVO;
import com.csci.susdev.vo.EmissionReductionInfoVO;
import com.csci.susdev.vo.IdVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/api/emission-reduction-info")
@Tag(name = "节能减排信息采集表", description = "碳排識別分判商")
@LogMethod
public class EmissionReductionInfoController {

    @Resource
    private EmissionReductionInfoService emissionReductionInfoService;

    @Resource
    private UserService userService;

    @GetMapping("/list")
    @Operation(description = "分页查询数据")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<EmissionReductionInfoVO> listEmissionReductionInfo(EmissionReductionInfoQO emissionReductionInfoQO) {
        return emissionReductionInfoService.listEmissionReductionInfo(emissionReductionInfoQO);
    }

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody EmissionReductionInfoVO emissionReductionInfoVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(emissionReductionInfoService.saveEmissionReductionInfo(emissionReductionInfoVO));
    }

    @PostMapping("/batch-delete")
    @Operation(description = "批量删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> batchDelete(@RequestBody EmissionReductionBatchDeleteVO emissionReductionBatchDeleteVO) {
        userService.checkIsReadOnly();
        return new ResultBean<>(emissionReductionInfoService.batchDeleteEmissionReductionInfo(emissionReductionBatchDeleteVO));
    }

    @PostMapping("/duplicate")
    @Operation(description = "复制记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<EmissionReductionInfoVO> duplicate(@RequestBody IdVO idVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(EmissionReductionInfoConverter.convert(emissionReductionInfoService.duplicateEmissionReductionInfo(idVO.getId())));
    }

    @GetMapping("/{id}")
    @Operation(description = "根据id查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<EmissionReductionInfoVO> getEmissionReductionInfoById(@PathVariable String id) {
        return new ResultBean<>(emissionReductionInfoService.getEmissionReductionInfoById(id));
    }

    @DeleteMapping("/{id}")
    @Operation(description = "根据id删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteEmissionReductionInfoById(@PathVariable String id) {
        userService.checkIsReadOnly();

        emissionReductionInfoService.deleteEmissionReductionInfoById(id);
        return new ResultBase();
    }
}
