package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.service.TzhExpectedEmissionIsoService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.tzh.model.TzhExpectedEmissionIso;
import com.csci.tzh.vo.TzhExpectedEmissionIsoVO;
import com.csci.tzh.qo.TzhExpectedEmissionIsoQO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/api/tzh/expectedemissioniso", produces = "application/json")
@Tag(name = "預期碳排放ISO 接口展示", description = "用于接口调试")
@LogMethod
public class TzhExpectedEmissionIsoController {

    @Autowired
    private TzhExpectedEmissionIsoService tzhExpectedEmissionIsoService;

    @Resource
    private UserService userService;

    
    @GetMapping("/list")
    @Operation(description = "查詢 預期碳排放ISO 數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<TzhExpectedEmissionIsoVO>> list(TzhExpectedEmissionIsoQO qo) {
        return new ResultBean<>(tzhExpectedEmissionIsoService.list(qo));
    }

    @PostMapping("/submit")
    @Operation(description = "提交 預期碳排放ISO 數據")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<Integer> submit(@RequestBody List<TzhExpectedEmissionIso> listModel) {
        userService.checkIsReadOnly();

        return new ResultBean<>(tzhExpectedEmissionIsoService.update(listModel));
    }
}
