package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.CeIdentificationRunawayEmissionQO;
import com.csci.susdev.service.CeIdentificationRunawayEmissionService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.CeIdentificationRunawayEmissionVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/api/ceIdentificationRunawayEmission", produces = "application/json")
@Tag(name = "碳排识别-逸散排放（弃用）", description = "用于接口调试")
@LogMethod
public class CeIdentificationRunawayEmissionController {

    @Resource
    private CeIdentificationRunawayEmissionService ceIdentificationRunawayEmissionService;

    @Resource
    private UserService userService;

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody CeIdentificationRunawayEmissionVO ceIdentificationRunawayEmissionVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(ceIdentificationRunawayEmissionService.saveCeIdentificationRunawayEmission(ceIdentificationRunawayEmissionVO));
    }

    @PostMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<CeIdentificationRunawayEmissionVO> listCeIdentificationRunawayEmission(@RequestBody CeIdentificationRunawayEmissionQO ceIdentificationRunawayEmissionQO) {
        return ceIdentificationRunawayEmissionService.listCeIdentificationRunawayEmission(ceIdentificationRunawayEmissionQO);
    }

    @DeleteMapping("/{id}")
    @Operation(description = "删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteCeIdentificationRunawayEmission(@PathVariable String id) {
        userService.checkIsReadOnly();

        ceIdentificationRunawayEmissionService.deleteCeIdentificationRunawayEmission(id);
        return ResultBase.success();
    }

    @GetMapping("/{id}")
    @Operation(description = "获取记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<CeIdentificationRunawayEmissionVO> getCeIdentificationRunawayEmission(@PathVariable String id) {
        return new ResultBean<>(ceIdentificationRunawayEmissionService.getCeIdentificationRunawayEmission(id));
    }
}
