package com.csci.susdev.controller;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.BusinessTripQO;
import com.csci.susdev.qo.SyncAmbientEnergyBillQO;
import com.csci.susdev.service.BusinessTripService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.vo.BusinessTripBatchDeleteVO;
import com.csci.susdev.vo.BusinessTripVO;
import com.csci.susdev.vo.UserBasicVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/api/businessTrip", produces = "application/json")
@Tag(name = "商务旅行接口", description = "用于接口调试")
@LogMethod
public class BusinessTripController {

    @Resource
    private BusinessTripService businessTripService;

    @Resource
    private UserService userService;

    @PostMapping("/save")
    @Operation(description = "保存商务旅行记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody BusinessTripVO businessTripVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(businessTripService.saveBusinessTrip(businessTripVO));
    }

    @PostMapping("/save/all")
    @Operation(description = "保存商务旅行记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<String>> save(@RequestBody List<BusinessTripVO> businessTripVOList) {
        userService.checkIsReadOnly();

        return new ResultBean<>(businessTripService.saveBusinessTripList(businessTripVOList));
    }

    @PostMapping("/list")
    @Operation(description = "查询商务旅行记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<BusinessTripVO> listBusinessTrip(@RequestBody BusinessTripQO businessTripQO) {
        return businessTripService.listBusinessTrip(businessTripQO);
    }

    @DeleteMapping("/{id}")
    @Operation(description = "删除商务旅行记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteBusinessTrip(@PathVariable String id) {
        userService.checkIsReadOnly();

        businessTripService.deleteBusinessTrip(id);
        return ResultBase.success();
    }

    @PostMapping("/batchDelete")
    @Operation(description = "批量删除商务旅行记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> batchDelete(@RequestBody BusinessTripBatchDeleteVO businessTripBatchDeleteVO) {
        userService.checkIsReadOnly();
        return new ResultBean<>(businessTripService.batchDeleteBusinessTrip(businessTripBatchDeleteVO));
    }

    @GetMapping("/{id}")
    @Operation(description = "获取商务旅行记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<BusinessTripVO> getBusinessTrip(@PathVariable String id) {
        return new ResultBean<>(businessTripService.getBusinessTrip(id));
    }

    @PostMapping("/synchronization-data")
    @Operation(description = "同步数据")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase synchronizationData(@RequestBody SyncAmbientEnergyBillQO syncAmbientEnergyBillQO) {
        userService.checkIsReadOnly();

        businessTripService.synchronizationData(syncAmbientEnergyBillQO);
        return new ResultBase();
    }
}
