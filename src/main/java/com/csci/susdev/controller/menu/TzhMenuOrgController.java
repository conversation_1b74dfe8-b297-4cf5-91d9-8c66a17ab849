package com.csci.susdev.controller.menu;

import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.model.TzhBsOrgMenu;
import com.csci.susdev.qo.TzhBsOrgMenuQO;
import com.csci.susdev.service.ITzhBsOrgMenuService;
import com.csci.susdev.util.ConvertBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description:
 * @author: barry
 * @create: 2024-05-15 08:42
 */
@RestController
@Tag(name = "大屏机构菜单配置管理", description = "大屏机构菜单配置管理")
public class TzhMenuOrgController {
    @Resource
    private ITzhBsOrgMenuService tzhBsOrgMenuService;

    @PostMapping("/api/bi/menu/config/getAll")
    @Operation(description = "查询所有机构菜单配置")
    public ResultPage searchAll(@Validated @RequestBody(required = false) PageDTO<TzhBsOrgMenuQO> pageDTO) {
        return tzhBsOrgMenuService.getAll(pageDTO);
    }

    @PostMapping("/api/bi/menu/config/save")
    @Operation(description = "保存机构菜单配置")
    public ResultBean save(@Validated @RequestBody TzhBsOrgMenuQO tzhBsOrgMenuQO){
        return tzhBsOrgMenuService.save(ConvertBeanUtils.convert(tzhBsOrgMenuQO, TzhBsOrgMenu.class));
    }

    @PostMapping("/api/bi/menu/config/edit")
    @Operation(description = "编辑机构菜单配置")
    public ResultBean edit(@Validated @RequestBody TzhBsOrgMenuQO tzhBsOrgMenuQO) {
        return tzhBsOrgMenuService.edit(ConvertBeanUtils.convert(tzhBsOrgMenuQO, TzhBsOrgMenu.class));
    }

    @PostMapping("/api/bi/menu/config/delete")
    @Operation(description = "删除机构菜单配置")
    public ResultBean delete(@RequestBody TzhBsOrgMenuQO tzhBsOrgMenuQO) {
        if (tzhBsOrgMenuQO == null || tzhBsOrgMenuQO.getId() == null) {
            return ResultBean.fail("id不能为空");
        }
        return tzhBsOrgMenuService.deleteById(tzhBsOrgMenuQO.getId());
    }
}
