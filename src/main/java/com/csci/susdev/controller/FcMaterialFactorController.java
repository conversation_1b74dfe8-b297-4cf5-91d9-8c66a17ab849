package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.FcCarbonFactorHkQO;
import com.csci.susdev.qo.FcMaterialFactorQO;
import com.csci.susdev.service.FcCarbonFactorHkService;
import com.csci.susdev.service.FcMaterialFactorService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.FcCarbonFactorHkVO;
import com.csci.susdev.vo.FcMaterialFactorVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/api/fcMaterialFactor", produces = "application/json")
@Tag(name = "因子管理-物料因子 接口", description = "用于接口调试")
@LogMethod
public class FcMaterialFactorController {

    @Resource
    private FcMaterialFactorService fcMaterialFactorService;

    @Resource
    private UserService userService;

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody FcMaterialFactorVO fcMaterialFactorVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(fcMaterialFactorService.saveFcMaterialFactor(fcMaterialFactorVO));
    }

    @PostMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<FcMaterialFactorVO> listFcMaterialFactor(@RequestBody FcMaterialFactorQO fcMaterialFactorQO) {
        return fcMaterialFactorService.listFcMaterialFactor(fcMaterialFactorQO);
    }

    @DeleteMapping("/{id}")
    @Operation(description = "删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteFcMaterialFactor(@PathVariable String id) {
        userService.checkIsReadOnly();

        fcMaterialFactorService.deleteFcMaterialFactor(id);
        return ResultBase.success();
    }

    @GetMapping("/{id}")
    @Operation(description = "获取记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<FcMaterialFactorVO> getFcMaterialFactor(@PathVariable String id) {
        return new ResultBean<>(fcMaterialFactorService.getFcMaterialFactor(id));
    }
}
