package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.UserRole;
import com.csci.susdev.service.UserRoleService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.ContextUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/api/userRole", produces = "application/json")
@Tag(name = "用戶角色關聯 接口展示", description = "用于接口调试")
@LogMethod
public class UserRoleController {

    @Autowired
    private UserRoleService userRoleService;

    @Resource
    private UserService userService;
    
    /**
     * 查找 用戶角色關聯 数据列表
     *
     * @param username
     * @return
     */
    @GetMapping("/list")
    @Operation(description = "分页查询 用戶角色關聯 统计数据列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200")
    public List<UserRole> list(String username) {
        return userRoleService.listUserRoleByUsername(username);
    }
    
    @PostMapping("/save")
    @Operation(description = "保存 用戶角色關聯 記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody UserRole userRole) {
        userService.checkIsReadOnly();

        return new ResultBean<>(userRoleService.saveUserRole(userRole));
    }
    
    @PostMapping("/savelist")
    @Operation(description = "保存 用戶角色關聯 記錄列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<String>> savelist(@RequestBody List<UserRole> userRoleLst) {
        userService.checkIsReadOnly();

        return new ResultBean<>(userRoleService.saveUserRoleList(userRoleLst));
    }

    @DeleteMapping("/delete/{id}")
    @Operation(description = "刪除 用戶角色關聯 記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<Integer> delete(@PathVariable String id) {
        userService.checkIsReadOnly();

        return new ResultBean<>(userRoleService.deleteUserRole(id));
    }

    @GetMapping("/show-org-tree")
    @Operation(description = "查询是否显示组织机构数")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<Boolean> canShowOrgTree() {
        return new ResultBean<>(userRoleService.hasRole(ContextUtils.getCurrentUser().getUsername(), "OrgTree"));
    }
}
