package com.csci.susdev.controller;

import com.csci.susdev.util.DateUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

@RestController
@RequestMapping(value = "/")
public class HomeController {

    public static void main(String[] args) {
        System.out.println(DateUtils.toDatetimeString(LocalDateTime.now()));
    }

    @GetMapping("/")
    String index() {
        return "Welcome to SUS-DEV";
    }

    @ResponseBody
    @GetMapping("/health-check")
    String healthCheck() {
        return "Hello Spring!!";
    }
}
