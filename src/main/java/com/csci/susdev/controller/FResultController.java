package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.tzh.model.*;
import com.csci.tzh.qo.*;
import com.csci.tzh.vo.FResultTotalVO;
import com.csci.tzh.vo.FResultVO;
import com.csci.susdev.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

import javax.servlet.http.HttpServletResponse;

import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping(value = "/api/tzh/fresult", produces = "application/json")
@Tag(name = "碳中和 - 月度排放表 接口展示", description = "用于接口调试")
@LogMethod
public class FResultController {

    @Autowired
    private FResultService fResultService;

    private class ResultPageOfFResultVO extends ResultPage<FResultVO>{
		public ResultPageOfFResultVO(List<?> page) {
			super(page);
		}
	}

    private class ResultPageOfFResultTotalVO extends ResultPage<FResultTotalVO>{
		public ResultPageOfFResultTotalVO(List<?> page) {
			super(page);
		}
    }

    @GetMapping("/list")
    @Operation(description = "查詢 碳中和 - 月度排放表 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultPageOfFResultVO.class)))
    public ResultPage<FResultVO> listFResult(FResultPageableQO fResultQO) {
        return fResultService.listFResult(fResultQO);
    }

    @GetMapping("/total/list")
    @Operation(description = "查詢 碳中和 - 月度排放表 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultPageOfFResultTotalVO.class)))
    public ResultPage<FResultTotalVO> listFResultTotal(FResultPageableQO fResultQO) {
        return fResultService.listFResultTotal(fResultQO);
    }
}
