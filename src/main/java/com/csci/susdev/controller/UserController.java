package com.csci.susdev.controller;

import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.KeywordPageQO;
import com.csci.susdev.qo.UserPageableQO;
import com.csci.susdev.qo.UserRolePageableQO;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.RsaUtil;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.vo.MenuVO;
import com.csci.susdev.vo.UserBasicVO;
import com.csci.susdev.vo.UserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/user", produces = "application/json")
@Tag(name = "用户信息获取", description = "用户信息获取")
public class UserController {

    @Autowired
    private UserService userService;

    @Value("${rsa.publicKey}")
    private String publicKeyString;

    @Value(("${rsa.privateKey}"))
    private String privateKeyString;

    private class ResultBeanOfUserVO extends ResultBean<UserVO>{}
    
    private class ResultPageOfUserVO extends ResultPage<UserVO>{
		public ResultPageOfUserVO(List<?> page) {
			super(page);
		}
	}

    @GetMapping("/currentUser/menu")
    @Operation(description = "获取当前用户信息")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultBeanOfUserVO.class)))
    ResultBean<List<MenuVO>> currentUserMenu() {
        return new ResultBean<>(userService.currentUserMenu());
    }

    @GetMapping("/list")
    @Operation(description = "查詢 用户 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultPageOfUserVO.class)))
    public ResultPage<UserVO> list(KeywordPageQO keywordPageQO) {
        return userService.listUser(keywordPageQO);
    }

    @GetMapping("/list/detail")
    @Operation(description = "查詢 用户 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultPageOfUserVO.class)))
    public ResultPage<UserVO> listDetail(UserPageableQO userPageableVO) {
        return userService.listUserDetail(userPageableVO);
    }

    @PostMapping("/save")
    @Operation(description = "保存 用户 及 組織和角色關聯 記錄")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> saveUser(@RequestBody UserVO userVO) throws Exception {
        userService.checkIsReadOnly();

        String decryptedPwd = RsaUtil.decrypt(userVO.getPassword(),privateKeyString);
        userVO.setPassword(decryptedPwd);
        return new ResultBean<>(userService.saveUser(userVO));
    }

    @PostMapping("/list/save")
    @Operation(description = "保存 用户 及 組織和角色關聯 列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<Map<String, String>> saveUserList(@RequestBody List<UserVO> userVOList) throws Exception {
        userService.checkIsReadOnly();

        for(UserVO vo : userVOList) {
            String decryptedPwd = RsaUtil.decrypt(vo.getPassword(),privateKeyString);
            vo.setPassword(decryptedPwd);
        }
        return new ResultBean<>(userService.saveUserList(userVOList));
    }

    @DeleteMapping("/delete/{id}")
    @Operation(description = "删除制定用户")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteUser(@PathVariable String id) {
        userService.checkIsReadOnly();

        userService.deleteUserById(id);
        return ResultBase.success();
    }

    @GetMapping("/get/{id}")
    @Operation(description = "获取指定用户")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<UserVO> getUserById(@PathVariable String id) {
        return new ResultBean<>(userService.getUserById(id));
    }

    @GetMapping("/list/listUserByRoleId")
    @Operation(description = "根据角色ID查詢 用户 接口數據列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ResultPageOfUserVO.class)))
    public ResultPage<UserVO> listUserByRoleId(UserRolePageableQO userRolePageableQO) {
        return userService.listUserByRoleId(userRolePageableQO);
    }
}
