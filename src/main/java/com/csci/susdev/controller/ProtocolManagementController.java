package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.ProtocolManagementQO;
import com.csci.susdev.service.ProtocolManagementService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.ProtocolManagementVO;
import com.csci.susdev.vo.ProtocolVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/api/protocolManagement", produces = "application/json")
@Tag(name = "協議管理 接口", description = "用于接口调试")
@LogMethod
public class ProtocolManagementController {

    @Resource
    private ProtocolManagementService protocolManagementService;

    @Resource
    private UserService userService;


    @PostMapping("/list/tree")
    @Operation(description = "查询协议管理树桩结构数据")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<List<ProtocolVO>> listProtocolManagement(@RequestBody ProtocolManagementQO protocolManagementQO) {
        return new ResultBean<>(protocolManagementService.listProtocolManagement(protocolManagementQO));
    }

    @PostMapping("/list")
    @Operation(description = "查询协议管理数据")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<ProtocolManagementVO> list(@RequestBody ProtocolManagementQO protocolManagementQO) {
        return protocolManagementService.list(protocolManagementQO);
    }

}
