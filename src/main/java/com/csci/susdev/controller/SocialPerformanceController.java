package com.csci.susdev.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.facade.WorkflowFacade;
import com.csci.susdev.model.*;
import com.csci.susdev.qo.SocialPerformanceQO;
import com.csci.susdev.service.*;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.redis.RedisLockUtil;
import com.csci.susdev.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@RestController
@RequestMapping(value = "/api/social-performance", produces = "application/json")
@Tag(name = "社会绩效 接口展示", description = "用于接口调试")
@LogMethod
public class SocialPerformanceController {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(SocialPerformanceController.class);

    @Resource
    private SocialPerformanceService socialPerformanceService;

    @Resource
    private UserService userService;

    @Resource
    private WorkflowFacade workflowFacade;

    @Resource
    private FormService formService;

    @Resource
    private WorkflowControlService workflowControlService;

    @Resource
    private WorkflowService workflowService;

    private String formId;

    @PostMapping("/get-or-init")
    @Operation(description = "获取或初始化社会绩效")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<SocialPerformanceHeadVO> getOrInit(@RequestBody SocialPerformanceQO socialPerformanceQO) {
        return new ResultBean<>(socialPerformanceService.getOrInit(socialPerformanceQO));
    }

    @GetMapping("/detail/list")
    @Operation(description = "获取社会绩效明细列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultList<SocialPerformanceDetailVO> listSocialPerformanceDetail(String headId) {
        return new ResultList<>(socialPerformanceService.listSocialPerformanceDetail(headId));
    }

    @GetMapping("/detail/table-list")
    @Operation(description = "获取社会绩效明细表格列表")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultList<SocialPerfOneTableVO> listSocialPerfOneTable(String headId) {
        return new ResultList<>(socialPerformanceService.listSocialPerfOneTable(headId));
    }

    @PostMapping("/update")
    @Operation(description = "更新社会绩效")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase updateSocialPerformance(@RequestBody SocialPerformanceHeadVO socialPerformanceHeadVO) {
        userService.checkIsReadOnly();

        socialPerformanceService.updateSocialPerfWithDetail(socialPerformanceHeadVO);
        return new ResultBase();
    }

    @PostMapping("/submit")
    @Operation(description = "提交社会绩效")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase submit(@RequestBody IdVersionVO idVersionVO) {
        userService.checkIsReadOnly();

        socialPerformanceService.submit(idVersionVO);
        return new ResultBase();
    }

    @GetMapping("/export")
    @Operation(description = "导出社会绩效")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public void export(String headId, HttpServletResponse response) throws IOException {

        String fileName = MessageFormat.format("社会绩效一({0}).xlsx", DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()));
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);

        try (InputStream is = SocialPerformanceController.class.getClassLoader().getResourceAsStream("template/social_perf_one_tplt.xlsx"); ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(is).build()) {
            logger.info("ExcelController.class.getClassLoader().getResourceAsStream(\"templates/social_perf_one_tplt.xlsx\") = {}", is);
            List<SocialPerformanceDetailVO> lstSocialDetail = socialPerformanceService.listSocialPerformanceDetail(headId);
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(lstSocialDetail, fillConfig, writeSheet);
        }
    }

    @PostMapping("/approve")
    @Operation(description = "审批社会绩效")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase approve(@RequestBody ApproveParamVO approveParamVO) {
        userService.checkIsReadOnly();

        checkExist(approveParamVO.getId(), "社会绩效id不能为空");
        SocialPerformanceHead socialPerformanceHead = socialPerformanceService.selectByPrimaryKey(approveParamVO.getId());

        WorkflowControl workflowControl = workflowControlService.findWorkflowControlByBusinessId(approveParamVO.getId());
        if (Objects.isNull(workflowControl)) {
            workflowFacade.startWorkflow(socialPerformanceHead.getOrganizationId(), getFormId(), approveParamVO.getId());
        } else {
            workflowFacade.approve(approveParamVO.getId(), approveParamVO.getRemark());
        }
        workflowControlService.sendReviewEmail(approveParamVO.getId());
        return new ResultBase();
    }

    @PostMapping("/reject")
    @Operation(description = "驳回社会绩效")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase reject(@RequestBody ApproveParamVO approveParamVO) {
        userService.checkIsReadOnly();

        checkExist(approveParamVO.getId(), "社会绩效id不能为空");
        SocialPerformanceHead socialPerformanceHead = socialPerformanceService.selectByPrimaryKey(approveParamVO.getId());

        Integer year = workflowControlService.findFormYearByBusinessId(approveParamVO.getId());
        Integer month = workflowControlService.findFormMonthByBusinessId(approveParamVO.getId());
        if(Objects.isNull(year)) {
            throw new ServiceException("找不到相關表單年份");
        }
        if(Objects.isNull(month)) {
            throw new ServiceException("找不到相關表單月份");
        }

        Workflow workflow = workflowService.findWorkflowByOrgIdAndFormId(socialPerformanceHead.getOrganizationId(),
                getFormId(), year, month);
        checkExist(workflow, "工作流未配置");

        workflowFacade.reject(approveParamVO.getId(), approveParamVO.getRemark());
        return new ResultBase();
    }

    @PostMapping("/recall")
    @Operation(description = "撤回社会绩效")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase recall(@RequestBody ApproveParamVO approveParamVO) {
        userService.checkIsReadOnly();

        checkExist(approveParamVO.getId(), "社会绩效id不能为空");
        SocialPerformanceHead socialPerformanceHead = socialPerformanceService.selectByPrimaryKey(approveParamVO.getId());

        Integer year = workflowControlService.findFormYearByBusinessId(approveParamVO.getId());
        Integer month = workflowControlService.findFormMonthByBusinessId(approveParamVO.getId());
        if(Objects.isNull(year)) {
            throw new ServiceException("找不到相關表單年份");
        }
        if(Objects.isNull(month)) {
            throw new ServiceException("找不到相關表單月份");
        }

        Workflow workflow = workflowService.findWorkflowByOrgIdAndFormId(socialPerformanceHead.getOrganizationId(),
                getFormId(), year, month);
        checkExist(workflow, "工作流未配置");

        workflowFacade.recall(approveParamVO.getId(), approveParamVO.getRemark());
        return new ResultBase();
    }

    @PostMapping("/terminate")
    @Operation(description = "终止社会绩效")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase terminate(@RequestBody ApproveParamVO approveParamVO) {
        userService.checkIsReadOnly();

        checkExist(approveParamVO.getId(), "社会绩效id不能为空");
        SocialPerformanceHead socialPerformanceHead = socialPerformanceService.selectByPrimaryKey(approveParamVO.getId());

        Integer year = workflowControlService.findFormYearByBusinessId(approveParamVO.getId());
        Integer month = workflowControlService.findFormMonthByBusinessId(approveParamVO.getId());
        if(Objects.isNull(year)) {
            throw new ServiceException("找不到相關表單年份");
        }
        if(Objects.isNull(month)) {
            throw new ServiceException("找不到相關表單月份");
        }

        Workflow workflow = workflowService.findWorkflowByOrgIdAndFormId(socialPerformanceHead.getOrganizationId(),
                getFormId(), year, month);
        checkExist(workflow, "工作流未配置");

        workflowFacade.terminate(approveParamVO.getId(), approveParamVO.getRemark());
        return new ResultBase();
    }

    private String getFormId() {
        if (StringUtils.isBlank(formId)) {
            Form form = formService.findFormByCode("sociology-index-one");
            formId = Optional.ofNullable(form).map(Form::getId).orElseThrow(() -> new ServiceException("未找到表单"));
        }
        return formId;
    }
}
