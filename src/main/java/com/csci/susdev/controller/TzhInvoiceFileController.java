package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.annotation.NoApiLogging;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.service.TzhInvoiceFileService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.tzh.qo.TzhInvoiceFileQO;
import com.csci.tzh.model.TzhInvoiceFile;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Base64;
import java.util.Optional;

@RestController
@RequestMapping(value = "/api/tzh/tzhinvoicefile", produces = "application/json")
@Tag(name = "票據檔案 接口展示", description = "用于接口调试")
@LogMethod
public class TzhInvoiceFileController {

    @Autowired
    private TzhInvoiceFileService tzhInvoiceFileService;

    @Resource
    private UserService userService;

    @NoApiLogging
    @GetMapping("/get")
    @Operation(description = "查詢 票據檔案")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> getByBillNo(@RequestParam("billno") Optional<String> billNo) throws Exception{
        String sBillNo = billNo.orElse("");
        TzhInvoiceFile x = tzhInvoiceFileService.get(sBillNo);
        if(x == null) {
            throw new Exception("找不到票據");
        }
        return new ResultBean<>(Base64.getEncoder().encodeToString(x.getPdf()));
    }

    @NoApiLogging
    @PostMapping("/upload")
    @Operation(description = "上傳 票據檔案")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<Integer> updateInvoicePdf(@ModelAttribute TzhInvoiceFileQO qo,
                                                @RequestParam("pdf") MultipartFile multipartFile) throws IOException {
        userService.checkIsReadOnly();

        return new ResultBean<>(tzhInvoiceFileService.upload(qo, multipartFile));
    }

    @PostMapping("/delete")
    @Operation(description = "刪除 票據檔案")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<Integer> deleteInvoicePdf(@RequestBody TzhInvoiceFileQO qo) {
        userService.checkIsReadOnly();

        return new ResultBean<>(tzhInvoiceFileService.delete(qo.getBillno()));
    }
}
