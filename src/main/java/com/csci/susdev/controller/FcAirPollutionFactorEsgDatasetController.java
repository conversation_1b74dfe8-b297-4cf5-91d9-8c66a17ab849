package com.csci.susdev.controller;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.ResultBase;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.model.ResultPage;
import com.csci.susdev.qo.FcAirPollutionFactorEsgDatasetQO;
import com.csci.susdev.service.FcAirPollutionFactorEsgDatasetService;
import com.csci.susdev.service.UserService;
import com.csci.susdev.vo.FcAirPollutionFactorEsgDatasetVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/api/fcAirPollutionFactorEsgDataset", produces = "application/json")
@Tag(name = "空气污染因子+esg数据集 接口", description = "用于接口调试")
@LogMethod
public class FcAirPollutionFactorEsgDatasetController {

    @Resource
    private FcAirPollutionFactorEsgDatasetService fcAirPollutionFactorEsgDatasetService;

    @Resource
    private UserService userService;

    @PostMapping("/save")
    @Operation(description = "保存记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<String> save(@RequestBody FcAirPollutionFactorEsgDatasetVO fcAirPollutionFactorEsgDatasetVO) {
        userService.checkIsReadOnly();

        return new ResultBean<>(fcAirPollutionFactorEsgDatasetService.saveFcAirPollutionFactorEsgDataset(fcAirPollutionFactorEsgDatasetVO));
    }

    @PostMapping("/list")
    @Operation(description = "查询记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultPage<FcAirPollutionFactorEsgDatasetVO> listFcAirPollutionFactorEsgDataset(@RequestBody FcAirPollutionFactorEsgDatasetQO fcAirPollutionFactorEsgDatasetQO) {
        return fcAirPollutionFactorEsgDatasetService.listFcAirPollutionFactorEsgDataset(fcAirPollutionFactorEsgDatasetQO);
    }

    @DeleteMapping("/{id}")
    @Operation(description = "删除记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase deleteFcAirPollutionFactorEsgDataset(@PathVariable String id) {
        userService.checkIsReadOnly();

        fcAirPollutionFactorEsgDatasetService.deleteFcAirPollutionFactorEsgDataset(id);
        return ResultBase.success();
    }

    @GetMapping("/{id}")
    @Operation(description = "获取记录")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<FcAirPollutionFactorEsgDatasetVO> getFcAirPollutionFactorEsgDataset(@PathVariable String id) {
        return new ResultBean<>(fcAirPollutionFactorEsgDatasetService.getFcAirPollutionFactorEsgDataset(id));
    }
}
