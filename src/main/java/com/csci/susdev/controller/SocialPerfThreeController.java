package com.csci.susdev.controller;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.facade.WorkflowFacade;
import com.csci.susdev.model.*;
import com.csci.susdev.qo.SocialPerfThreeQO;
import com.csci.susdev.service.*;
import com.csci.susdev.util.redis.RedisLockUtil;
import com.csci.susdev.vo.ApproveParamVO;
import com.csci.susdev.vo.SocialPerfThreeHeadVO;
import com.csci.susdev.vo.SocialPerfThreeTableDataVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@RestController
@RequestMapping("/api/social-perf-three")
@Tag(name = "社会绩效3", description = "用于接口调试")
@LogMethod
public class SocialPerfThreeController {

    private static String formId;
    @Resource
    private SocialPerfThreeHeadService socialPerfThreeHeadService;

    @Resource
    private UserService userService;
    @Resource
    private SocialPerfThreeDetailService socialPerfThreeDetailService;
    @Resource
    private WorkflowFacade workflowFacade;
    @Resource
    private WorkflowService workflowService;
    @Resource
    private WorkflowControlService workflowControlService;
    @Resource
    private FormService formService;

    @PostMapping("/head")
    @Operation(description = "获取社会绩效3")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBean<SocialPerfThreeHeadVO> getSocialPerfThreeHead(@RequestBody SocialPerfThreeQO socialPerfThreeQO) {
        return new ResultBean<>(socialPerfThreeHeadService.getOrInitSocialPerfThree(
                socialPerfThreeQO.getOrganizationId(), socialPerfThreeQO.getYear(), socialPerfThreeQO.getMonth()));
    }

    @GetMapping("/list-detail")
    @Operation(description = "获取社会绩效3明细")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultList<SocialPerfThreeTableDataVO> listSocialPerfThreeDetail(String headId) {
        return new ResultList<>(socialPerfThreeDetailService.listSocialPerfThreeTableData(headId));
    }

    @PostMapping("/update")
    @Operation(description = "更新社会绩效3")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase updateSocialPerfThree(@RequestBody SocialPerfThreeHeadVO socialPerfThreeHeadVO) {
        userService.checkIsReadOnly();

        socialPerfThreeHeadService.updateSocialPerfThree(socialPerfThreeHeadVO);
        return ResultBase.success();
    }

    @PostMapping("/approve")
    @Operation(description = "审批社会绩效")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase approve(@RequestBody ApproveParamVO approveParamVO) {
        userService.checkIsReadOnly();

        checkExist(approveParamVO.getId(), "社会绩效id不能为空");

        SocialPerfThreeHead socialPerfThreeHead = socialPerfThreeHeadService.selectByPrimaryKey(approveParamVO.getId());

        WorkflowControl workflowControl = workflowControlService.findWorkflowControlByBusinessId(approveParamVO.getId());
        if (Objects.isNull(workflowControl)) {
            workflowFacade.startWorkflow(socialPerfThreeHead.getOrganizationId(), getFormId(), approveParamVO.getId());
        } else {
            workflowFacade.approve(approveParamVO.getId(), approveParamVO.getRemark());
        }
        workflowControlService.sendReviewEmail(approveParamVO.getId());
        return new ResultBase();
    }

    @PostMapping("/reject")
    @Operation(description = "驳回社会绩效")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase reject(@RequestBody ApproveParamVO approveParamVO) {
        userService.checkIsReadOnly();

        checkExist(approveParamVO.getId(), "社会绩效id不能为空");
        SocialPerfThreeHead head = socialPerfThreeHeadService.selectByPrimaryKey(approveParamVO.getId());

        Integer year = workflowControlService.findFormYearByBusinessId(approveParamVO.getId());
        Integer month = workflowControlService.findFormMonthByBusinessId(approveParamVO.getId());
        if(Objects.isNull(year)) {
            throw new ServiceException("找不到相關表單年份");
        }
        if(Objects.isNull(month)) {
            throw new ServiceException("找不到相關表單月份");
        }

        Workflow workflow = workflowService.findWorkflowByOrgIdAndFormId(head.getOrganizationId(),
                getFormId(), year, month);
        checkExist(workflow, "工作流未配置");

        workflowFacade.reject(approveParamVO.getId(), approveParamVO.getRemark());
        return new ResultBase();
    }

    @PostMapping("/recall")
    @Operation(description = "撤回社会绩效")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase recall(@RequestBody ApproveParamVO approveParamVO) {
        userService.checkIsReadOnly();

        checkExist(approveParamVO.getId(), "社会绩效id不能为空");
        SocialPerfThreeHead head = socialPerfThreeHeadService.selectByPrimaryKey(approveParamVO.getId());

        Integer year = workflowControlService.findFormYearByBusinessId(approveParamVO.getId());
        Integer month = workflowControlService.findFormMonthByBusinessId(approveParamVO.getId());
        if(Objects.isNull(year)) {
            throw new ServiceException("找不到相關表單年份");
        }
        if(Objects.isNull(month)) {
            throw new ServiceException("找不到相關表單月份");
        }

        Workflow workflow = workflowService.findWorkflowByOrgIdAndFormId(head.getOrganizationId(),
                getFormId(), year, month);
        checkExist(workflow, "工作流未配置");

        workflowFacade.recall(approveParamVO.getId(), approveParamVO.getRemark());
        return new ResultBase();
    }

    @PostMapping("/terminate")
    @Operation(description = "终止社会绩效")
    @Parameter(in = ParameterIn.HEADER, name = SusDevConsts.HEADER_TOKEN_KEY, schema = @Schema(type = "string"), required = true)
    public ResultBase terminate(@RequestBody ApproveParamVO approveParamVO) {
        userService.checkIsReadOnly();

        checkExist(approveParamVO.getId(), "社会绩效id不能为空");
        SocialPerfThreeHead head = socialPerfThreeHeadService.selectByPrimaryKey(approveParamVO.getId());

        Integer year = workflowControlService.findFormYearByBusinessId(approveParamVO.getId());
        Integer month = workflowControlService.findFormMonthByBusinessId(approveParamVO.getId());
        if(Objects.isNull(year)) {
            throw new ServiceException("找不到相關表單年份");
        }
        if(Objects.isNull(month)) {
            throw new ServiceException("找不到相關表單月份");
        }

        Workflow workflow = workflowService.findWorkflowByOrgIdAndFormId(head.getOrganizationId(),
                getFormId(), year, month);
        checkExist(workflow, "工作流未配置");

        workflowFacade.terminate(approveParamVO.getId(), approveParamVO.getRemark());
        return new ResultBase();
    }

    private String getFormId() {
        if (StringUtils.isBlank(formId)) {
            Form form = formService.findFormByCode("sociology-index-three");
            formId = Optional.ofNullable(form).map(Form::getId).orElseThrow(() -> new ServiceException("未找到表单"));
        }
        return formId;
    }
}
