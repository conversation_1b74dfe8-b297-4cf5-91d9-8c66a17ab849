package com.csci.susdev.validation;

import com.csci.susdev.util.FileUtil;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;

/**
 * Validator for file upload constraints
 * 
 * <AUTHOR>
 * @date 2025-06-20
 */
public class FileValidator implements ConstraintValidator<FileConstraints, MultipartFile> {
    
    private long maxSize;
    private String[] allowedTypes;
    private boolean allowEmpty;
    
    @Override
    public void initialize(FileConstraints constraintAnnotation) {
        this.maxSize = constraintAnnotation.maxSize();
        this.allowedTypes = constraintAnnotation.allowedTypes();
        this.allowEmpty = constraintAnnotation.allowEmpty();
    }
    
    @Override
    public boolean isValid(MultipartFile file, ConstraintValidatorContext context) {
        if (file == null || file.isEmpty()) {
            if (!allowEmpty) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("文件不能为空")
                       .addConstraintViolation();
                return false;
            }
            return true;
        }
        
        // Check file size
        if (file.getSize() > maxSize) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate(
                String.format("文件大小超出限制。文件大小: %d bytes, 最大允许: %d bytes", 
                             file.getSize(), maxSize))
                   .addConstraintViolation();
            return false;
        }
        
        // Check file type
        try {
            String fileType = FileUtil.getFileType(file.getBytes());
            if (!Arrays.asList(allowedTypes).contains(fileType.toLowerCase())) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate(
                    String.format("不支持的文件类型: %s。支持的文件类型: %s", 
                                 fileType, String.join(", ", allowedTypes)))
                       .addConstraintViolation();
                return false;
            }
        } catch (Exception e) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate("无法识别文件类型")
                   .addConstraintViolation();
            return false;
        }
        
        return true;
    }
}
