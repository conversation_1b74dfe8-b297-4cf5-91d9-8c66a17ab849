package com.csci.susdev.configuration.parallel;

import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.util.context.RequestContextManager;
import com.csci.susdev.util.context.impl.RequestContext;
import com.csci.susdev.util.context.model.UserInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.*;

/**
 * 线程池定义
 *
 * <AUTHOR>
 */
public class ThreadPool {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(ThreadPool.class);

    /**
     * 通用线程池
     */
    private final ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    /**
     * 默认线程超时时间
     */
    private final long timeOut;
    /**
     * 默认线程超时单位
     */
    private final TimeUnit timeUnit;

    /**
     * 线程名前缀
     */
    private final String threadNamePrefix;

    /**
     * 构造器
     *
     * @param timeOut
     * @param timeUnit
     * @param threadNamePrefix
     */
    public ThreadPool(long timeOut, TimeUnit timeUnit, String threadNamePrefix) {
        this.timeOut = timeOut;
        this.timeUnit = timeUnit;
        this.threadNamePrefix = threadNamePrefix;
        this.initialize();
        executor.initialize();
    }

    /**
     * 初始化线程;
     * 设置了coolPoolSize,maxPoolSize和ThreadNamePrefix
     * 如果需要修改其他线程池配置，子类可以通过重载此方法进行修改
     */
    protected void initialize() {
        executor.setCorePoolSize(SusDevConsts.COMMON_POOL_SIZE);
        executor.setMaxPoolSize(SusDevConsts.COMMON_POOL_SIZE);
        executor.setThreadNamePrefix(this.threadNamePrefix);
    }

    /**
     * 提交一个异步任务
     *
     * @param callable
     * @return
     */
    public <T> Future<T> submit(Callable<T> callable) {
        RequestContext requestContext =
                Optional.ofNullable((RequestContext) RequestContextManager.getCurrent()).map(RequestContext::clone)
                        .orElse(new RequestContext(new HashMap<>(), new UserInfo()));
        return executor.submit(() -> {
            RequestContextManager.setCurrent(requestContext);
            return callable.call();
        });
    }

    /**
     * 提交一个runnable任务
     *
     * @param runnable
     * @return
     */
    public Future<?> submit(Runnable runnable) {
        RequestContext requestContext =
                Optional.ofNullable((RequestContext) RequestContextManager.getCurrent()).map(RequestContext::clone)
                        .orElse(new RequestContext(new HashMap<>(), new UserInfo()));
        return executor.submit(() -> {
            RequestContextManager.setCurrent(requestContext);
            runnable.run();
        });
    }

    /**
     * 添加多个并行任务并等待执行完毕
     *
     * @param callables
     */
    public void submitAndWait(Callable<?>... callables) {
        long start = 0;
        if (logger.isDebugEnabled()) {
            start = System.currentTimeMillis();
        }
        List<Future<?>> futures = new ArrayList<>(callables.length);
        // 为了保证线程安全，需要克隆一个RequestContext
        RequestContext requestContext = Optional.ofNullable((RequestContext) RequestContextManager.getCurrent()).map(RequestContext::clone)
                .orElse(new RequestContext(new HashMap<>(), new UserInfo()));

        for (Callable<?> callable : callables) {
            futures.add(executor.submit(() -> {
                // 这里可以直接对RequestContext进行克隆，因为之前的代码已经保证了RequestContext不会为空
                RequestContextManager.setCurrent(requestContext.clone());
                return callable.call();
            }));
        }
        for (Future<?> future : futures) {
            try {
                future.get(timeOut, timeUnit);
            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                logger.error("ThreadPool#submitAndWait, 并行任务执行出错");
            }
        }
        if (logger.isDebugEnabled()) {
            logger.debug("ThreadPool#submitAndWait,  duration: {}ms", System.currentTimeMillis() - start);
        }
    }

    /**
     * 添加多个并行任务并等待执行完毕
     *
     * @param tasks
     */
    public void submitAndWait(Runnable... tasks) {
        long start = 0;
        if (logger.isDebugEnabled()) {
            start = System.currentTimeMillis();
        }
        List<Future<?>> futures = new ArrayList<>(tasks.length);
        // 为了保证线程安全，需要克隆一个RequestContext
        RequestContext requestContext = Optional.ofNullable((RequestContext) RequestContextManager.getCurrent()).map(RequestContext::clone)
                .orElse(new RequestContext(new HashMap<>(), new UserInfo()));
        for (Runnable task : tasks) {
            futures.add(executor.submit(() -> {
                // 这里可以直接对RequestContext进行克隆，因为之前的代码已经保证了RequestContext不会为空
                RequestContextManager.setCurrent(requestContext.clone());
                task.run();
            }));
        }
        for (Future<?> future : futures) {
            try {
                future.get(timeOut, timeUnit);
            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                logger.error("ThreadPool#submitAndWait, 并行任务执行出错", e);
            }
        }
        if (logger.isDebugEnabled()) {
            logger.debug("ThreadPool#submitAndWait, duration: {}ms", System.currentTimeMillis() - start);
        }
    }

}
