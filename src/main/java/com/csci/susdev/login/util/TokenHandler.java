package com.csci.susdev.login.util;

import com.csci.susdev.login.Oauth2Invoker;
import com.csci.susdev.login.vo.GetAccessTokenVO;
import com.csci.susdev.login.vo.ValidateAccessTokenVO;
import com.csci.susdev.model.User;
import com.csci.susdev.service.UserService;
import com.csci.susdev.service.UserSessionService;
import com.csci.susdev.util.DemoUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class TokenHandler {

    @Autowired
    private Oauth2Invoker oauth2Invoker;

    @Autowired
    private UserService userService;

    @Autowired
    private UserSessionService userSessionService;

    /**
     * 创建用户session，并将token返回
     *
     * @param getAccessTokenVO
     * @return 此系统生成的token，非accessToken，某些情况下等于accessToken
     */
    public String generateUserSessionAndGetToken(GetAccessTokenVO getAccessTokenVO) {
        // 由于这里通过zhtSso服务获取到的accessToken是与oauth2服务通用的，因此可以通过oauth2服务来获取用户信息
        ValidateAccessTokenVO validateAccessTokenVO = oauth2Invoker.validateAccessToken(getAccessTokenVO.getAccessToken());

        // 查询用户记录
        User user = getOrInitUser(validateAccessTokenVO);

        String token = getAccessTokenVO.getAccessToken();

        userSessionService.createUserSession(user, token, token, getAccessTokenVO.getRefreshToken());
        return token;
    }

    /**
     * 根据参数对象中的username查询用户记录，如果为查询到该用户记录，则使用参数中的用户名以及真实姓名创建一条新的用户记录
     *
     * @param validateAccessTokenVO
     * @return
     */
    private User getOrInitUser(ValidateAccessTokenVO validateAccessTokenVO) {
        Map.Entry<String, String> unameEntry = DemoUtils.splitDomainUsername(validateAccessTokenVO.getUsername());
        User user = userService.getUserByUsername(unameEntry.getValue());
        if (Objects.isNull(user)) {
            // create a new user
            user = new User();
            user.setDomainPrefix(unameEntry.getKey());
            user.setName(validateAccessTokenVO.getRealName());
            user.setUsername(unameEntry.getValue());
            user.setIsAdAccount(Boolean.TRUE);
            user.setIsEnabled(Boolean.TRUE);
            user.setIsReadonly(Boolean.FALSE);
            userService.insertSelective(user);
        } else if (StringUtils.isBlank(user.getName())) {
            user.setName(validateAccessTokenVO.getRealName());
            userService.updateByPrimaryKeySelective(user);
        }
        return user;
    }

}
