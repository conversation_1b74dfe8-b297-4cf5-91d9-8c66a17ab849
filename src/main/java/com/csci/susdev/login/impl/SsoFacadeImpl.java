package com.csci.susdev.login.impl;

import com.csci.common.exception.ServiceException;
import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.login.ISsoFacade;
import com.csci.susdev.login.IZhtSsoInvoker;
import com.csci.susdev.login.util.TokenHandler;
import com.csci.susdev.login.vo.ExchangeTokenVO;
import com.csci.susdev.login.vo.GetAccessTokenVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@LogMethod
public class SsoFacadeImpl implements ISsoFacade {

    @Autowired
    private IZhtSsoInvoker zhtSsoInvoker;

    @Autowired
    private TokenHandler tokenHandler;

    @Override
    public String exchangeAccessToken(ExchangeTokenVO exchangeTokenVO) {
        if (StringUtils.isBlank(exchangeTokenVO.getCode())) {
            throw new ServiceException("交换token时code不能为空");
        }

        // 交换 code
        GetAccessTokenVO getAccessTokenVO = zhtSsoInvoker.exchangeAccessToken(exchangeTokenVO);

        // 生成用户 session, 并返回 token
        return tokenHandler.generateUserSessionAndGetToken(getAccessTokenVO);
    }


}
