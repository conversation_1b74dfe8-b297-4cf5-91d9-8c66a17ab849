package com.csci.susdev.handler;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import com.csci.common.util.CustomGsonBuilder;
import com.csci.susdev.service.FfCmFixedDetailService;
import com.csci.susdev.service.FfCmMobileService;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.util.StringUtil;
import com.csci.susdev.vo.FfCmFixedDetailVO;
import com.csci.susdev.vo.FfCmMobileDetailVO;
import com.csci.susdev.vo.MergeDataVO;
import com.google.gson.Gson;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class FfCmMobileHandler extends AbstractMergeStrategy {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(FfCmMobileHandler.class);
    private static final List<String> thirdColMergeValueList = new ArrayList<>();

    static {
        thirdColMergeValueList.add("地盤/工地、工廠、辦公室及發電廠能源消耗");
    }

    private static final Gson gson = CustomGsonBuilder.createGson();
    private List<MergeDataVO> mergeDataList = new ArrayList<>();

    public FfCmMobileHandler(String headId) {
        FfCmMobileService ffCmMobileService = SpringContextUtil.getBean(FfCmMobileService.class);
        List<FfCmMobileDetailVO> lstFfCmMobile = ffCmMobileService.listFfCmMobileDetail(headId);

        // 合并第一列
        mergeDataList.addAll(calcFirstCol(lstFfCmMobile));
        // 合并第二列
        mergeDataList.addAll(calcSecondCol(lstFfCmMobile));
        // 合并第四列
        mergeDataList.addAll(calcFourCol(lstFfCmMobile));
        // 合并第五列
        mergeDataList.addAll(calcFiveCol(lstFfCmMobile));

        // 合并最后两行
        mergeDataList.addAll(calcLastRow(lstFfCmMobile));
        mergeDataList.addAll(calcSecondLastRow(lstFfCmMobile));

        // 过滤掉同行的数据
        mergeDataList = mergeDataList.stream().filter(x -> !(Objects.equals(x.getFromRow(), x.getToRow()) && Objects.equals(x.getFromCol(), x.getToCol()))).collect(Collectors.toList());
    }

    public static List<MergeDataVO> calcFirstCol(List<FfCmMobileDetailVO> lstFfCmMobile) {
        List<MergeDataVO> lstMergeData = new ArrayList<>();
        MergeDataVO mergeDataVO = null;
        int currentRowIndex = 0;
        for (int i = 0; i < lstFfCmMobile.size(); i++) {
            FfCmMobileDetailVO ffCmMobileDetailVO = lstFfCmMobile.get(i);
            // 第一列合并计算----------start
            if (StringUtil.equals("汽油合计", ffCmMobileDetailVO.getCol3()) || StringUtil.equals("柴油合计", ffCmMobileDetailVO.getCol3())) {
                continue;
            }
            if (i == 0) {
                // 第一行
                currentRowIndex = 2;
                mergeDataVO = new MergeDataVO().setFromCol(0).setToCol(0).setFromRow(currentRowIndex).setToRow(currentRowIndex).setValue(ffCmMobileDetailVO.getCol1());
                lstMergeData.add(mergeDataVO);
                continue;
            }
            currentRowIndex++;
            if (StringUtils.isNotBlank(ffCmMobileDetailVO.getCol1()) && !StringUtils.equals(mergeDataVO.getValue(), ffCmMobileDetailVO.getCol1())) {
                // 不同类别
                mergeDataVO = new MergeDataVO().setFromCol(0).setToCol(0).setFromRow(currentRowIndex).setToRow(currentRowIndex).setValue(ffCmMobileDetailVO.getCol1());
                lstMergeData.add(mergeDataVO);
                continue;
            }

            mergeDataVO.setToRow(mergeDataVO.getToRow() + 1);
            // 第一列合并计算----------end
        }

        return lstMergeData.stream().filter(x -> !Objects.equals(x.getFromRow(), x.getToRow())).collect(Collectors.toList());
    }

    /**
     * 暂时不适用
     *
     * @param lstFfCmMobile
     */
    public static List<MergeDataVO> calcSecondCol(List<FfCmMobileDetailVO> lstFfCmMobile) {
        if (CollectionUtils.isEmpty(lstFfCmMobile)) {
            return new ArrayList<>();
        }
        int currentRow = 0;
        MergeDataVO mergeDataVO = null;
        List<MergeDataVO> mergeDataList = new ArrayList<>();
        String lastCategory = null;
        for (int i = 0; i < lstFfCmMobile.size(); i++) {
            FfCmMobileDetailVO ffCmMobileDetailVO = lstFfCmMobile.get(i);

            if (StringUtil.equals("汽油合计", ffCmMobileDetailVO.getCol3()) || StringUtil.equals("柴油合计", ffCmMobileDetailVO.getCol3())) {
                continue;
            }

            if (i == 0) {
                currentRow = 2;
                mergeDataVO = new MergeDataVO().setFromCol(1).setToCol(1).setFromRow(currentRow).setToRow(currentRow).setValue(ffCmMobileDetailVO.getCol2());
                mergeDataList.add(mergeDataVO);
                continue;
            }
            currentRow++;
            if (StringUtils.isNotBlank(ffCmMobileDetailVO.getCol2()) && !StringUtils.equals(ffCmMobileDetailVO.getCol2(), mergeDataVO.getValue())) {
                mergeDataVO = new MergeDataVO().setFromCol(1).setToCol(1).setFromRow(currentRow).setToRow(currentRow).setValue(ffCmMobileDetailVO.getCol2());
                mergeDataList.add(mergeDataVO);
                continue;
            }

            if (Objects.nonNull(mergeDataVO)) {
                mergeDataVO.setToRow(mergeDataVO.getToRow() + 1);
            }
        }

        return mergeDataList.stream().filter(x -> !Objects.equals(x.getFromRow(), x.getToRow())).collect(Collectors.toList());
    }

    public static List<MergeDataVO> calcFourCol(List<FfCmMobileDetailVO> lstFfCmMobile) {
        if (CollectionUtils.isEmpty(lstFfCmMobile)) {
            return new ArrayList<>();
        }
        int currentRow = 0;
        MergeDataVO mergeDataVO = null;
        List<MergeDataVO> mergeDataList = new ArrayList<>();
        String lastCategory = null;
        for (int i = 0; i < lstFfCmMobile.size(); i++) {
            FfCmMobileDetailVO ffCmMobileDetailVO = lstFfCmMobile.get(i);

            if (StringUtil.equals("汽油合计", ffCmMobileDetailVO.getCol3()) || StringUtil.equals("柴油合计", ffCmMobileDetailVO.getCol3())) {
                continue;
            }

            if (i == 0) {
                currentRow = 2;
                mergeDataVO = new MergeDataVO().setFromCol(3).setToCol(3).setFromRow(currentRow).setToRow(currentRow).setValue(ffCmMobileDetailVO.getCol4());
                mergeDataList.add(mergeDataVO);
                continue;
            }
            currentRow++;
            if (StringUtils.isNotBlank(ffCmMobileDetailVO.getCol4()) && !StringUtils.equals(ffCmMobileDetailVO.getCol4(), mergeDataVO.getValue())) {
                mergeDataVO = new MergeDataVO().setFromCol(3).setToCol(3).setFromRow(currentRow).setToRow(currentRow).setValue(ffCmMobileDetailVO.getCol4());
                mergeDataList.add(mergeDataVO);
                continue;
            }

            if (Objects.nonNull(mergeDataVO)) {
                mergeDataVO.setToRow(mergeDataVO.getToRow() + 1);
            }
        }

        return mergeDataList.stream().filter(x -> !Objects.equals(x.getFromRow(), x.getToRow())).collect(Collectors.toList());
    }

    public static List<MergeDataVO> calcFiveCol(List<FfCmMobileDetailVO> lstFfCmMobile) {
        if (CollectionUtils.isEmpty(lstFfCmMobile)) {
            return new ArrayList<>();
        }
        int currentRow = 0;
        MergeDataVO mergeDataVO = null;
        List<MergeDataVO> mergeDataList = new ArrayList<>();
        String lastCategory = null;
        for (int i = 0; i < lstFfCmMobile.size(); i++) {
            FfCmMobileDetailVO ffCmMobileDetailVO = lstFfCmMobile.get(i);

            if (StringUtil.equals("汽油合计", ffCmMobileDetailVO.getCol3()) || StringUtil.equals("柴油合计", ffCmMobileDetailVO.getCol3())) {
                continue;
            }

            if (i == 0) {
                currentRow = 2;
                mergeDataVO = new MergeDataVO().setFromCol(4).setToCol(4).setFromRow(currentRow).setToRow(currentRow).setValue(ffCmMobileDetailVO.getCol5());
                mergeDataList.add(mergeDataVO);
                continue;
            }
            currentRow++;
            if (StringUtils.isNotBlank(ffCmMobileDetailVO.getCol5()) && !StringUtils.equals(ffCmMobileDetailVO.getCol5(), mergeDataVO.getValue())) {
                mergeDataVO = new MergeDataVO().setFromCol(4).setToCol(4).setFromRow(currentRow).setToRow(currentRow).setValue(ffCmMobileDetailVO.getCol5());
                mergeDataList.add(mergeDataVO);
                continue;
            }

            if (Objects.nonNull(mergeDataVO)) {
                mergeDataVO.setToRow(mergeDataVO.getToRow() + 1);
            }
        }

        return mergeDataList.stream().filter(x -> !Objects.equals(x.getFromRow(), x.getToRow())).collect(Collectors.toList());
    }


    public static List<MergeDataVO> calcLastRow(List<FfCmMobileDetailVO> lstFfCmMobile) {
        if (CollectionUtils.isEmpty(lstFfCmMobile)) {
            return new ArrayList<>();
        }
        int dataSize = lstFfCmMobile.size();
        // 最后一行Excel行号 = 起始行2 + 数据条数 -1
        int lastRowNum = 2 + dataSize - 1;
        FfCmMobileDetailVO lastRowData = lstFfCmMobile.get(dataSize - 1);
        String mergedValue = lastRowData.getCol3();
        List<MergeDataVO> mergeDataList = new ArrayList<>();
        MergeDataVO mergeData = new MergeDataVO()
                .setFromCol(2)   // 第3列（索引2）
                .setToCol(12)    // 第13列（索引12）
                .setFromRow(lastRowNum)
                .setToRow(lastRowNum)
                .setValue(mergedValue);
        mergeDataList.add(mergeData);

        return mergeDataList;
    }

    public static List<MergeDataVO> calcSecondLastRow(List<FfCmMobileDetailVO> lstFfCmMobile) {
        if (CollectionUtils.isEmpty(lstFfCmMobile)) {
            return new ArrayList<>();
        }
        int dataSize = lstFfCmMobile.size();
        // 倒数第二行Excel行号 = 起始行2 + 数据条数 -2
        int secondLastRowNum = 2 + dataSize - 2;
        FfCmMobileDetailVO secondLastRowData  = lstFfCmMobile.get(dataSize - 2);
        String mergedValue = secondLastRowData .getCol3();
        List<MergeDataVO> mergeDataList = new ArrayList<>();
        MergeDataVO mergeData = new MergeDataVO()
                .setFromCol(2)   // 第3列（索引2）
                .setToCol(12)    // 第13列（索引12）
                .setFromRow(secondLastRowNum)
                .setToRow(secondLastRowNum)
                .setValue(mergedValue);
        mergeDataList.add(mergeData);

        return mergeDataList;
    }


    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer relativeRowIndex) {
        if (CollectionUtils.isNotEmpty(mergeDataList)) {
            for (MergeDataVO mergeDataVO : mergeDataList) {
                if (cell.getRowIndex() == mergeDataVO.getFromRow() && cell.getColumnIndex() == mergeDataVO.getFromCol()) {
                    sheet.addMergedRegion(new CellRangeAddress(mergeDataVO.getFromRow(), mergeDataVO.getToRow(), mergeDataVO.getFromCol(), mergeDataVO.getToCol()));
                    break;
                }
            }
        }
    }
}
