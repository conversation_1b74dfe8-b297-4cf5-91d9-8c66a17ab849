package com.csci.susdev.aspectj;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Aop 控制器方法注入配置
 * <p>
 *
 * <AUTHOR> terrytao
 * @date : 10/15/2019
 */
@Aspect
@Component
public class ControllerAspectj {
    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(ControllerAspectj.class);

    @Pointcut(value = "@within(com.csci.susdev.annotation.UseTime) || @annotation(com.csci.susdev.annotation.UseTime)")
    public void injectUseTime() {
    }

    @Around("injectUseTime()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {

        long start = System.currentTimeMillis();
        try {
            return joinPoint.proceed();
        } finally {
            long duration = System.currentTimeMillis() - start;
            try {
                logger.info("UseTime: {}.{} 耗时：{}ms", joinPoint.getSignature().getDeclaringTypeName(), joinPoint.getSignature().getName(), duration);
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }
    }

}
