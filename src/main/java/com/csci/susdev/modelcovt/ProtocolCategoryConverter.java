package com.csci.susdev.modelcovt;

import com.csci.susdev.model.ProtocolCategory;
import com.csci.susdev.vo.ProtocolCategoryVO;
import org.springframework.beans.BeanUtils;

public class ProtocolCategoryConverter {

    public static ProtocolCategoryVO convertToVO(ProtocolCategory protocolCategory) {
        ProtocolCategoryVO protocolCategoryVO = new ProtocolCategoryVO();
        BeanUtils.copyProperties(protocolCategory, protocolCategoryVO);
        return protocolCategoryVO;
    }

    public static ProtocolCategory convertToModel(ProtocolCategoryVO protocolCategoryVO) {
        ProtocolCategory protocolCategory = new ProtocolCategory();
        BeanUtils.copyProperties(protocolCategoryVO, protocolCategory);
        return protocolCategory;
    }

    public static ProtocolCategory convertToModelWithBase(
            ProtocolCategoryVO protocolCategoryVO,
            ProtocolCategory protocolCategory) {
        BeanUtils.copyProperties(protocolCategoryVO, protocolCategory);
        return protocolCategory;
    }

}
