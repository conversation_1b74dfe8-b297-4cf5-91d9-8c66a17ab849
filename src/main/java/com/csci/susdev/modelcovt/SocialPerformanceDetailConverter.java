package com.csci.susdev.modelcovt;

import com.csci.susdev.model.SocialPerformanceDetail;
import com.csci.susdev.vo.SocialPerformanceDetailVO;
import org.springframework.beans.BeanUtils;
import org.springframework.core.convert.converter.Converter;

public class SocialPerformanceDetailConverter implements Converter<SocialPerformanceDetail, SocialPerformanceDetailVO> {
    @Override
    public SocialPerformanceDetailVO convert(SocialPerformanceDetail source) {
        SocialPerformanceDetailVO vo = new SocialPerformanceDetailVO();
        BeanUtils.copyProperties(source, vo);
        return vo;
    }

    public SocialPerformanceDetail revert(SocialPerformanceDetailVO vo) {
        SocialPerformanceDetail source = new SocialPerformanceDetail();
        BeanUtils.copyProperties(vo, source);
        return source;
    }
}
