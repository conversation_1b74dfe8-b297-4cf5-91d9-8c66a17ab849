package com.csci.susdev.modelcovt;

import com.csci.susdev.model.CeIdentificationDetail;
import com.csci.susdev.vo.CeIdentificationDetailVO;
import org.springframework.beans.BeanUtils;
import org.springframework.core.convert.converter.Converter;

public class CeIdentificationDetailConverter implements Converter<CeIdentificationDetail, CeIdentificationDetailVO> {
    @Override
    public CeIdentificationDetailVO convert(CeIdentificationDetail source) {
        CeIdentificationDetailVO vo = new CeIdentificationDetailVO();
        BeanUtils.copyProperties(source, vo);
        return vo;
    }

    public CeIdentificationDetail revert(CeIdentificationDetailVO vo) {
        CeIdentificationDetail source = new CeIdentificationDetail();
        BeanUtils.copyProperties(vo, source);
        return source;
    }
}
