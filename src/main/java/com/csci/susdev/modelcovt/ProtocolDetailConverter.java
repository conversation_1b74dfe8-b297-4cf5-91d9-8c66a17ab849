package com.csci.susdev.modelcovt;

import com.csci.susdev.model.*;
import com.csci.susdev.service.CarbonEmissionLocationService;
import com.csci.susdev.service.ProtocolCategoryService;
import com.csci.susdev.service.ProtocolService;
import com.csci.susdev.service.ProtocolSubCategoryService;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.vo.ProtocolDetailVO;
import org.springframework.beans.BeanUtils;

public class ProtocolDetailConverter {

    public static ProtocolDetailVO convertToVO(ProtocolDetail protocolDetail) {
        ProtocolDetailVO protocolDetailVO = new ProtocolDetailVO();
        BeanUtils.copyProperties(protocolDetail, protocolDetailVO);

        CarbonEmissionLocationService carbonEmissionLocationService = SpringContextUtil.getBean(CarbonEmissionLocationService.class);
        CarbonEmissionLocation carbonEmissionLocation = carbonEmissionLocationService.selectByPrimaryKey(protocolDetail.getCarbonEmissionLocationId());
        protocolDetailVO.setCarbonEmissionLocationId(carbonEmissionLocation.getId());
        protocolDetailVO.setCarbonEmissionLocationName(carbonEmissionLocation.getName());
        protocolDetailVO.setCarbonEmissionLocationNameSc(carbonEmissionLocation.getNameSc());
        protocolDetailVO.setCarbonEmissionLocationNameEn(carbonEmissionLocation.getNameEn());

        ProtocolSubCategoryService protocolSubCategoryService = SpringContextUtil.getBean(ProtocolSubCategoryService.class);
        ProtocolSubCategory protocolSubCategory = protocolSubCategoryService.selectByPrimaryKey(protocolDetail.getSubCategoryId());
        protocolDetailVO.setSubCategoryId(protocolSubCategory.getId());
        protocolDetailVO.setSubCategoryName(protocolSubCategory.getSubCategoryName());
        protocolDetailVO.setSubCategoryNameSc(protocolSubCategory.getSubCategoryNameSc());
        protocolDetailVO.setSubCategoryNameEn(protocolSubCategory.getSubCategoryNameEn());

        ProtocolCategoryService protocolCategoryService = SpringContextUtil.getBean(ProtocolCategoryService.class);
        ProtocolCategory protocolCategory = protocolCategoryService.selectByPrimaryKey(protocolSubCategory.getCategoryId());
        protocolDetailVO.setCategoryId(protocolCategory.getId());
        protocolDetailVO.setCategoryName(protocolCategory.getCategoryName());
        protocolDetailVO.setCategoryNameSc(protocolCategory.getCategoryNameSc());
        protocolDetailVO.setCategoryNameEn(protocolCategory.getCategoryNameEn());

        ProtocolService protocolService = SpringContextUtil.getBean(ProtocolService.class);
        Protocol protocol = protocolService.selectByPrimaryKey(protocolCategory.getProtocolId());
        protocolDetailVO.setProtocolId(protocol.getId());
        protocolDetailVO.setProtocolName(protocol.getName());
        protocolDetailVO.setProtocolNameSc(protocol.getNameSc());
        protocolDetailVO.setProtocolNameEn(protocol.getNameEn());

        return protocolDetailVO;
    }

    public static ProtocolDetail convertToModel(ProtocolDetailVO protocolDetailVO) {
        ProtocolDetail protocolDetail = new ProtocolDetail();
        BeanUtils.copyProperties(protocolDetailVO, protocolDetail);
        return protocolDetail;
    }

    public static ProtocolDetail convertToModelWithBase(
            ProtocolDetailVO protocolDetailVO,
            ProtocolDetail protocolDetail) {
        BeanUtils.copyProperties(protocolDetailVO, protocolDetail);
        return protocolDetail;
    }

}
