package com.csci.susdev.modelcovt;

import com.csci.susdev.model.AmbientDetailExt;
import com.csci.susdev.vo.AmbientTableDataExtVO;
import org.springframework.beans.BeanUtils;

public class AmbientTableDataExtConverter {

    public static AmbientTableDataExtVO convert(AmbientDetailExt ambientDetailExt) {
        AmbientTableDataExtVO ambientTableDataExtVO = new AmbientTableDataExtVO();
        BeanUtils.copyProperties(ambientDetailExt, ambientTableDataExtVO);
        return ambientTableDataExtVO;
    }

    public static AmbientDetailExt convert(AmbientTableDataExtVO ambientTableDataExtVO) {
        AmbientDetailExt ambientDetailExt = new AmbientDetailExt();
        BeanUtils.copyProperties(ambientTableDataExtVO, ambientDetailExt);
        return ambientDetailExt;
    }

}
