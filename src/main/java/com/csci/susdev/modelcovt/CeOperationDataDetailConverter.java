package com.csci.susdev.modelcovt;

import com.csci.susdev.model.CeOperationDataDetail;
import com.csci.susdev.vo.CeOperationDataDetailVO;
import org.springframework.beans.BeanUtils;
import org.springframework.core.convert.converter.Converter;

public class CeOperationDataDetailConverter implements Converter<CeOperationDataDetail, CeOperationDataDetailVO> {
    @Override
    public CeOperationDataDetailVO convert(CeOperationDataDetail source) {
        CeOperationDataDetailVO vo = new CeOperationDataDetailVO();
        BeanUtils.copyProperties(source, vo);
        return vo;
    }

    public CeOperationDataDetail revert(CeOperationDataDetailVO vo) {
        CeOperationDataDetail source = new CeOperationDataDetail();
        BeanUtils.copyProperties(vo, source);
        return source;
    }
}
