package com.csci.susdev.modelcovt;

import com.csci.susdev.model.Protocol;
import com.csci.susdev.vo.ProtocolVO;
import org.springframework.beans.BeanUtils;

public class ProtocolConverter {

    public static ProtocolVO convertToVO(Protocol protocol) {
        ProtocolVO protocolVO = new ProtocolVO();
        BeanUtils.copyProperties(protocol, protocolVO);
        return protocolVO;
    }

    public static Protocol convertToModel(ProtocolVO protocolVO) {
        Protocol protocol = new Protocol();
        BeanUtils.copyProperties(protocolVO, protocol);
        return protocol;
    }

    public static Protocol convertToModelWithBase(
            ProtocolVO protocolVO,
            Protocol protocol) {
        BeanUtils.copyProperties(protocolVO, protocol);
        return protocol;
    }

}
