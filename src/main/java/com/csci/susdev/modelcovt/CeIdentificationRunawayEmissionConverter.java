package com.csci.susdev.modelcovt;

import com.csci.susdev.model.CeIdentificationRunawayEmission;
import com.csci.susdev.vo.CeIdentificationRunawayEmissionVO;
import org.springframework.beans.BeanUtils;

public class CeIdentificationRunawayEmissionConverter {

    public static CeIdentificationRunawayEmissionVO convertToVO(CeIdentificationRunawayEmission ceIdentificationRunawayEmission) {
        CeIdentificationRunawayEmissionVO ceIdentificationRunawayEmissionVO = new CeIdentificationRunawayEmissionVO();
        BeanUtils.copyProperties(ceIdentificationRunawayEmission, ceIdentificationRunawayEmissionVO);
        return ceIdentificationRunawayEmissionVO;
    }

    public static CeIdentificationRunawayEmission convertToModel(CeIdentificationRunawayEmissionVO ceIdentificationRunawayEmissionVO) {
        CeIdentificationRunawayEmission ceIdentificationRunawayEmission = new CeIdentificationRunawayEmission();
        BeanUtils.copyProperties(ceIdentificationRunawayEmissionVO, ceIdentificationRunawayEmission);
        return ceIdentificationRunawayEmission;
    }

    public static CeIdentificationRunawayEmission convertToModelWithBase(
            CeIdentificationRunawayEmissionVO ceIdentificationRunawayEmissionVO,
            CeIdentificationRunawayEmission ceIdentificationRunawayEmission) {
        BeanUtils.copyProperties(ceIdentificationRunawayEmissionVO, ceIdentificationRunawayEmission);
        return ceIdentificationRunawayEmission;
    }

}
