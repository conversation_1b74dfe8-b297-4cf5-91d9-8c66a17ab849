package com.csci.susdev.modelcovt;

import com.csci.susdev.model.AirPollutants;
import com.csci.susdev.vo.AirPollutantsVO;
import org.springframework.beans.BeanUtils;
import org.springframework.core.convert.converter.Converter;

public class AirPollutantsConverter implements Converter<AirPollutants, AirPollutantsVO> {
    @Override
    public AirPollutantsVO convert(AirPollutants source) {
        AirPollutantsVO airPollutantsVO = new AirPollutantsVO();
        BeanUtils.copyProperties(source, airPollutantsVO);
        return airPollutantsVO;
    }

    public AirPollutants revert(AirPollutantsVO source) {
        AirPollutants airPollutants = new AirPollutants();
        BeanUtils.copyProperties(source, airPollutants);
        return airPollutants;
    }
}
