package com.csci.susdev.modelcovt;

import com.csci.susdev.constant.ApproveStatus;
import com.csci.susdev.model.SocialPerformanceHead;
import com.csci.susdev.vo.SocialPerformanceHeadVO;
import org.springframework.beans.BeanUtils;

public class SocialPerformanceHeadConverter {

    static String getApproveStatusName(Integer approveStatus) {
        ApproveStatus[] approveStatuses = ApproveStatus.values();
        for (ApproveStatus status : approveStatuses) {
            if (status.getCode().equals(approveStatus)) {
                return status.getName();
            }
        }
        return "未知状态";
    }

    public static SocialPerformanceHeadVO convert(SocialPerformanceHead source) {
        SocialPerformanceHeadVO socialPerformanceHeadVO = new SocialPerformanceHeadVO();
        BeanUtils.copyProperties(source, socialPerformanceHeadVO);
        // 将approveStatus转换为approveStatusName
        socialPerformanceHeadVO.setApproveStatusName(getApproveStatusName(source.getApproveStatus()));
        return socialPerformanceHeadVO;
    }

    public static SocialPerformanceHead convert(SocialPerformanceHeadVO source) {
        SocialPerformanceHead socialPerformanceHead = new SocialPerformanceHead();
        BeanUtils.copyProperties(source, socialPerformanceHead);
        return socialPerformanceHead;
    }

}
