package com.csci.susdev.modelcovt;

import com.csci.susdev.model.AmbientEnergyBillExt;
import com.csci.susdev.vo.AmbientEnergyBillExtVO;
import org.springframework.beans.BeanUtils;

public class AmbientEnergyBillExtVOConverter {

    public static AmbientEnergyBillExtVO convert(AmbientEnergyBillExt ambientEnergyBillExt) {
        AmbientEnergyBillExtVO ambientEnergyExtBillVO = new AmbientEnergyBillExtVO();
        BeanUtils.copyProperties(ambientEnergyBillExt, ambientEnergyExtBillVO);
        return ambientEnergyExtBillVO;
    }

    public static AmbientEnergyBillExt convert(AmbientEnergyBillExtVO ambientEnergyExtBillVO) {
        AmbientEnergyBillExt ambientEnergyBillExt = new AmbientEnergyBillExt();
        BeanUtils.copyProperties(ambientEnergyExtBillVO, ambientEnergyBillExt);
        return ambientEnergyBillExt;
    }

}
