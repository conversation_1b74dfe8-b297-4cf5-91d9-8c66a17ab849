package com.csci.susdev.modelcovt;

import com.csci.susdev.model.*;
import com.csci.susdev.service.*;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.vo.FactorScopeVO;
import org.springframework.beans.BeanUtils;

public class FactorScopeConverter {

    public static FactorScopeVO convertToVO(FactorScope factorScope) {
        FactorScopeVO factorScopeVO = new FactorScopeVO();
        BeanUtils.copyProperties(factorScope, factorScopeVO);

        ProtocolDetailService protocolDetailService = SpringContextUtil.getBean(ProtocolDetailService.class);
        ProtocolDetail protocolDetail = protocolDetailService.selectByPrimaryKey(factorScope.getProtocolDetailId());

        ProtocolSubCategoryService protocolSubCategoryService = SpringContextUtil.getBean(ProtocolSubCategoryService.class);
        ProtocolSubCategory protocolSubCategory = protocolSubCategoryService.selectByPrimaryKey(protocolDetail.getSubCategoryId());
        factorScopeVO.setSubCategoryId(protocolSubCategory.getId());
        factorScopeVO.setSubCategoryName(protocolSubCategory.getSubCategoryName());
        factorScopeVO.setSubCategoryNameSc(protocolSubCategory.getSubCategoryNameSc());
        factorScopeVO.setSubCategoryNameEn(protocolSubCategory.getSubCategoryNameEn());

        ProtocolCategoryService protocolCategoryService = SpringContextUtil.getBean(ProtocolCategoryService.class);
        ProtocolCategory protocolCategory = protocolCategoryService.selectByPrimaryKey(protocolSubCategory.getCategoryId());
        factorScopeVO.setCategoryId(protocolCategory.getId());
        factorScopeVO.setCategoryName(protocolCategory.getCategoryName());
        factorScopeVO.setCategoryNameSc(protocolCategory.getCategoryNameSc());
        factorScopeVO.setCategoryNameEn(protocolCategory.getCategoryNameEn());

        ProtocolService protocolService = SpringContextUtil.getBean(ProtocolService.class);
        Protocol protocol = protocolService.selectByPrimaryKey(protocolCategory.getProtocolId());
        factorScopeVO.setProtocolId(protocol.getId());
        factorScopeVO.setProtocolName(protocol.getName());
        factorScopeVO.setProtocolNameSc(protocol.getNameSc());
        factorScopeVO.setProtocolNameEn(protocol.getNameEn());

        CarbonEmissionLocationService carbonEmissionLocationService = SpringContextUtil.getBean(CarbonEmissionLocationService.class);
        CarbonEmissionLocation carbonEmissionLocation = carbonEmissionLocationService.selectByPrimaryKey(protocolDetail.getCarbonEmissionLocationId());
        factorScopeVO.setCarbonEmissionLocationId(carbonEmissionLocation.getId());
        factorScopeVO.setCarbonEmissionLocationName(carbonEmissionLocation.getName());
        factorScopeVO.setCarbonEmissionLocationNameSc(carbonEmissionLocation.getNameSc());
        factorScopeVO.setCarbonEmissionLocationNameEn(carbonEmissionLocation.getNameEn());

        FormDetailService formDetailService = SpringContextUtil.getBean(FormDetailService.class);
        FormDetail formDetail = formDetailService.selectByPrimaryKey(factorScope.getFormDetailId());
        factorScopeVO.setFormCode(formDetail.getFormCode());
        factorScopeVO.setYear(formDetail.getYear());
        factorScopeVO.setTypeA(formDetail.getTypeA());
        factorScopeVO.setTypeB(formDetail.getTypeB());
        factorScopeVO.setTypeC(formDetail.getTypeC());
        factorScopeVO.setTypeD(formDetail.getTypeD());
        factorScopeVO.setTypeE(formDetail.getTypeE());

        return factorScopeVO;
    }

    public static FactorScope convertToModel(FactorScopeVO factorScopeVO) {
        FactorScope factorScope = new FactorScope();
        BeanUtils.copyProperties(factorScopeVO, factorScope);
        return factorScope;
    }

    public static FactorScope convertToModelWithBase(
            FactorScopeVO factorScopeVO,
            FactorScope factorScope) {
        BeanUtils.copyProperties(factorScopeVO, factorScope);
        return factorScope;
    }

}
