package com.csci.susdev.modelcovt;

import com.csci.susdev.model.ProtocolSubCategory;
import com.csci.susdev.vo.ProtocolSubCategoryVO;
import org.springframework.beans.BeanUtils;

public class ProtocolSubCategoryConverter {

    public static ProtocolSubCategoryVO convertToVO(ProtocolSubCategory protocolSubCategory) {
        ProtocolSubCategoryVO protocolSubCategoryVO = new ProtocolSubCategoryVO();
        BeanUtils.copyProperties(protocolSubCategory, protocolSubCategoryVO);
        return protocolSubCategoryVO;
    }

    public static ProtocolSubCategory convertToModel(ProtocolSubCategoryVO protocolSubCategoryVO) {
        ProtocolSubCategory protocolSubCategory = new ProtocolSubCategory();
        BeanUtils.copyProperties(protocolSubCategoryVO, protocolSubCategory);
        return protocolSubCategory;
    }

    public static ProtocolSubCategory convertToModelWithBase(
            ProtocolSubCategoryVO protocolSubCategoryVO,
            ProtocolSubCategory protocolSubCategory) {
        BeanUtils.copyProperties(protocolSubCategoryVO, protocolSubCategory);
        return protocolSubCategory;
    }

}
