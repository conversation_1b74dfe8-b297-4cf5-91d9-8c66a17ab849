package com.csci.susdev.modelcovt;

import com.csci.susdev.model.CeIdentificationDetail;
import com.csci.susdev.vo.CeIdentificationTableDataVO;
import org.springframework.beans.BeanUtils;

public class CeIdentificationTableDataConverter {

    public static CeIdentificationTableDataVO convert(CeIdentificationDetail ceIdentificationDetail) {
        CeIdentificationTableDataVO ceIdentificationTableDataVO = new CeIdentificationTableDataVO();
        BeanUtils.copyProperties(ceIdentificationDetail, ceIdentificationTableDataVO);
        return ceIdentificationTableDataVO;
    }

    public static CeIdentificationDetail convert(CeIdentificationTableDataVO ceIdentificationTableDataVO) {
        CeIdentificationDetail ceIdentificationDetail = new CeIdentificationDetail();
        BeanUtils.copyProperties(ceIdentificationTableDataVO, ceIdentificationDetail);
        return ceIdentificationDetail;
    }

}
