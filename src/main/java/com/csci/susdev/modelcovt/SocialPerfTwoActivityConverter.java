package com.csci.susdev.modelcovt;

import com.csci.susdev.model.SocialPerfTwoActivity;
import com.csci.susdev.vo.SocialPerfTwoActivityVO;
import org.springframework.beans.BeanUtils;

public class SocialPerfTwoActivityConverter {

    public static SocialPerfTwoActivityVO convert(SocialPerfTwoActivity socialPerfTwoActivity) {
        SocialPerfTwoActivityVO socialPerfTwoActivityVO = new SocialPerfTwoActivityVO();
        BeanUtils.copyProperties(socialPerfTwoActivity, socialPerfTwoActivityVO);
        return socialPerfTwoActivityVO;
    }

    public static SocialPerfTwoActivity convert(SocialPerfTwoActivityVO socialPerfTwoActivityVO) {
        SocialPerfTwoActivity socialPerfTwoActivity = new SocialPerfTwoActivity();
        BeanUtils.copyProperties(socialPerfTwoActivityVO, socialPerfTwoActivity);
        return socialPerfTwoActivity;
    }

}
