package com.csci.susdev.modelcovt;

import com.csci.susdev.model.SocialPerformanceDetail;
import com.csci.susdev.vo.Classification;
import com.csci.susdev.vo.SocialPerfOneTableVO;
import org.springframework.beans.BeanUtils;

import java.util.Optional;

public class SocialPerfOneTableConverter {

    public static SocialPerfOneTableVO convert(SocialPerformanceDetail source) {
        SocialPerfOneTableVO socialPerfOneTableVO = new SocialPerfOneTableVO();
        BeanUtils.copyProperties(source, socialPerfOneTableVO);
        Classification classification = new Classification();
        classification.setClassification1(source.getClassification1());
        classification.setClassification2(source.getClassification2());
        classification.setClassification3(source.getClassification3());
        classification.setClassification4(source.getClassification4());
        classification.setClassification5(source.getClassification5());
        classification.setClassification6(source.getClassification6());
        classification.setClassification7(source.getClassification7());
        socialPerfOneTableVO.setClassification(classification);
        return socialPerfOneTableVO;
    }

    public static SocialPerformanceDetail convert(SocialPerfOneTableVO source) {
        SocialPerformanceDetail socialPerformanceDetail = new SocialPerformanceDetail();
        BeanUtils.copyProperties(source, socialPerformanceDetail);
        socialPerformanceDetail.setClassification1(Optional.of(source).map(SocialPerfOneTableVO::getClassification).map(Classification::getClassification1).orElse(""));
        socialPerformanceDetail.setClassification2(Optional.of(source).map(SocialPerfOneTableVO::getClassification).map(Classification::getClassification2).orElse(""));
        socialPerformanceDetail.setClassification3(Optional.of(source).map(SocialPerfOneTableVO::getClassification).map(Classification::getClassification3).orElse(""));
        socialPerformanceDetail.setClassification4(Optional.of(source).map(SocialPerfOneTableVO::getClassification).map(Classification::getClassification4).orElse(""));
        socialPerformanceDetail.setClassification5(Optional.of(source).map(SocialPerfOneTableVO::getClassification).map(Classification::getClassification5).orElse(""));
        socialPerformanceDetail.setClassification6(Optional.of(source).map(SocialPerfOneTableVO::getClassification).map(Classification::getClassification6).orElse(""));
        socialPerformanceDetail.setClassification7(Optional.of(source).map(SocialPerfOneTableVO::getClassification).map(Classification::getClassification7).orElse(""));
        return socialPerformanceDetail;
    }
}
