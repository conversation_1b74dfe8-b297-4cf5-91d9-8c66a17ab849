package com.csci.susdev.modelcovt;

import com.csci.common.util.CustomGsonBuilder;
import com.csci.susdev.model.User;
import com.csci.susdev.model.UserHistory;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class UserHistoryConverter {

    private static final Gson GSON = CustomGsonBuilder.createGson();

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(UserHistoryConverter.class);

    public static UserHistory convert(User user) {
        UserHistory userHistory = new UserHistory();
        userHistory.setUsername(user.getUsername());
        userHistory.setRealName(user.getName());
        try {
            userHistory.setJsonContent(GSON.toJson(user));
        } catch (Exception e) {
            logger.error("convert user to json error", e);
        }
        return userHistory;
    }
}
