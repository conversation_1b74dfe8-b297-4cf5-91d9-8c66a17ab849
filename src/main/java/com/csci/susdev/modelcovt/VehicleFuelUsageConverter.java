package com.csci.susdev.modelcovt;

import com.csci.susdev.model.VehicleFuelUsage;
import com.csci.susdev.vo.VehicleFuelUsageVO;
import org.springframework.beans.BeanUtils;

public class VehicleFuelUsageConverter {

    public static VehicleFuelUsageVO convert(VehicleFuelUsage vehicleFuelUsage) {
        VehicleFuelUsageVO vehicleFuelUsageVO = new VehicleFuelUsageVO();
        BeanUtils.copyProperties(vehicleFuelUsage, vehicleFuelUsageVO);
        return vehicleFuelUsageVO;
    }

    public static VehicleFuelUsage convert(VehicleFuelUsageVO vehicleFuelUsageVO) {
        VehicleFuelUsage vehicleFuelUsage = new VehicleFuelUsage();
        BeanUtils.copyProperties(vehicleFuelUsageVO, vehicleFuelUsage);
        return vehicleFuelUsage;
    }

}
