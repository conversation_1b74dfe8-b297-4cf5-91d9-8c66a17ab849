package com.csci.susdev.modelcovt;

import com.csci.susdev.model.SocialPerfThreeHead;
import com.csci.susdev.vo.SocialPerfThreeHeadVO;
import org.springframework.beans.BeanUtils;

public class SocialPerfThreeHeadConverter {

    public static SocialPerfThreeHeadVO convert(SocialPerfThreeHead source) {
        SocialPerfThreeHeadVO target = new SocialPerfThreeHeadVO();
        BeanUtils.copyProperties(source, target);
        return target;
    }

    public static SocialPerfThreeHead convert(SocialPerfThreeHeadVO source) {
        SocialPerfThreeHead target = new SocialPerfThreeHead();
        BeanUtils.copyProperties(source, target);
        return target;
    }

}
