package com.csci.susdev.interceptor;

import com.csci.common.util.CommonUtils;
import com.csci.susdev.constant.SusDevConsts;
import com.csci.susdev.model.UserSession;
import com.csci.susdev.service.RoleService;
import com.csci.susdev.service.UserSessionService;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.util.context.ContextUtils;
import com.csci.susdev.util.context.RequestContextManager;
import com.csci.susdev.util.context.impl.RequestContext;
import com.csci.susdev.util.context.model.UserInfo;
import com.csci.susdev.vo.RoleVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户认证拦截器
 *
 * <AUTHOR>
 * @date 2019-03-26
 */
@Order
public class AuthenticateInterceptor extends HandlerInterceptorAdapter {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(AuthenticateInterceptor.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (RequestMethod.OPTIONS.toString().equalsIgnoreCase(request.getMethod())) {
            return true;
        }
        String token = request.getHeader(SusDevConsts.HEADER_TOKEN_KEY);

        if (checkTokenAndSetContext(request, response, token)) {
            return true;
        }

        String errorMessage;
        if (StringUtils.isBlank(token)) {
            errorMessage = SusDevConsts.HEADER_TOKEN_KEY + " 缺少在請求標頭中";
        } else {
            errorMessage = SusDevConsts.HEADER_TOKEN_KEY + " 過期或無效, 請重新登錄";
        }
//        if (request.getRequestURI().contains("bi")) {
//            request.setAttribute("username", "baoshijie");
//            return true;
//        }
        logger.error("AuthenticateInterceptor#preHandle, url:{},cause: {}", request.getRequestURI(),errorMessage);
        response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "沒有权限访问, " + errorMessage);
        return false;
    }

    /**
     * 验证 token, 通过token获取当前登录用户信息并设置到session中
     *
     * @param sourceToken
     */
    private boolean checkTokenAndSetContext(HttpServletRequest request, HttpServletResponse response,String sourceToken) {
        try {
            if (StringUtils.isBlank(sourceToken)) {
                return false;
            }

            String oriToken = StringUtils.trim(sourceToken);
            oriToken = CommonUtils.removePrefixForToken(oriToken);

            UserSessionService userSessionService = SpringContextUtil.getBean(UserSessionService.class);
            UserSession userSession = userSessionService.getUserSessionByTokenOrAccessToken(oriToken);

            if (userSession == null) {
                return false;
            }
            setRequestContext(userSession);
            request.setAttribute("username", userSession.getUsername());
        } catch (Exception e) {
            logger.error("验证token出错", e);
            return false;
        }
        return true;
    }

    private void setRequestContext(UserSession userSession) {
        UserInfo userInfo = new UserInfo();
        userInfo.setId(userSession.getUserId());
        userInfo.setRoles(getRoleList(userSession.getUserId()));
        userInfo.setName(userSession.getName());
        userInfo.setUsername(userSession.getUsername());

        Map<String, Object> context = new HashMap<>();
        context.put(SusDevConsts.AUTH_TOKEN_KEY, userSession.getToken());
        RequestContextManager.setCurrent(new RequestContext(context, userInfo));
    }

    private List<String> getRoleList(String userId) {
        RoleService roleService = SpringContextUtil.getBean(RoleService.class);
        return roleService.listRoleByUserId(userId).stream().map(RoleVO::getCode).collect(Collectors.toList());
    }


    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        RequestContextManager.removeCurrent();
        super.afterCompletion(request, response, handler, ex);
    }
}
