package com.csci.susdev.facade;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.model.Organization;
import com.csci.susdev.model.VehicleFuelUsageExample;
import com.csci.susdev.service.OrganizationService;
import com.csci.susdev.service.VehicleFuelUsageService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static com.csci.susdev.service.ServiceHelper.checkExist;

@Component
@LogMethod
public class VehicleExportFacade {

    @Resource
    private OrganizationService organizationService;

    @Resource
    private VehicleFuelUsageService vehicleFuelUsageService;

    /**
     * 导出车辆数据
     *
     * @param organizationId 组织机构id
     * @param type           1表示导出传入公司的车辆数据，2表示导出传入公司所有部门车辆数据
     */
    public void exportVehicleData(String organizationId, Integer year, Integer type) {
        checkExist(organizationId, "组织机构id不能为空");
        Organization organization = organizationService.selectByPrimaryKey(organizationId);
        checkExist(organization, "组织机构不存在");
        if (Objects.equals(type, 1)) {
            exportOrgVehicleData(organization, year);
        } else if (Objects.equals(type, 2)) {
            exportOrgChildrenVehicleData(organization, year);
        } else {
            throw new IllegalArgumentException("type参数不合法");
        }

    }

    /**
     * 导出传入公司的车辆数据
     *
     * @param organization
     */
    private void exportOrgVehicleData(Organization organization, Integer year) {

    }


    /**
     * 导出传入公司所有部门车辆数据
     *
     * @param organization
     */
    private void exportOrgChildrenVehicleData(Organization organization, Integer year) {

        List<Organization> lstOrgs = organizationService.findLeafChildrenByNo(organization.getNo());

        // 查询出所有公司的车辆数据，按公司排序
        VehicleFuelUsageExample example = new VehicleFuelUsageExample();
        // example.createCriteria().andOrganizationIdIn(lstOrgs.stream().map(Organization::getId).collect(Collectors.toList()));
        // vehicleFuelUsageService.selectByExample()

    }

}
