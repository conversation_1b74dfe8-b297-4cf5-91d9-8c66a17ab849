package com.csci.susdev.facade;

import com.csci.susdev.annotation.LogMethod;
import com.csci.susdev.model.Organization;
import com.csci.susdev.model.OrganizationExample;
import com.csci.susdev.model.User;
import com.csci.susdev.service.OrganizationService;
import com.csci.susdev.service.ServiceHelper;
import com.csci.susdev.service.UserOrganizationService;
import com.csci.susdev.service.UserService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户组织关系Facade
 */
@Component
@LogMethod
public class UserOrganizationFacade {

    @Resource
    private UserService userService;

    @Resource
    private OrganizationService organizationService;

    @Resource
    private UserOrganizationService userOrganizationService;

    /**
     * 将所有
     *
     * @param username
     */
    public void addAllOrgsToUser(String username) {
        // 查询出用户以及所有组织机构信息
        User user = userService.getUserByUsername(username);
        OrganizationExample example = new OrganizationExample();
        example.or().andIsDeletedEqualTo(Boolean.FALSE);
        List<Organization> lstOrgs = organizationService.selectByExample(example);
        for (Organization org : lstOrgs) {
            // 如果组织机构是项目或者部门，则添加到用户组织关系表中
            // if (organizationService.isOrganizationLeaf(org.getId())) {
            ServiceHelper.addOrgToUser(org.getId(), user.getId());
            // }
        }
    }

}
