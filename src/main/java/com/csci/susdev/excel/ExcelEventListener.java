package com.csci.susdev.excel;

import cn.hutool.core.lang.Console;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.csci.susdev.mapper.ProcedureMapper;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.vo.ProcedureVO;
import com.csci.susdev.vo.SiteNameExcelVO;
import com.csci.susdev.vo.TExcelVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * @description:
 * @author: barry
 * @create: 2024-11-20 10:27
 */
@Slf4j
@Component
public class ExcelEventListener extends AnalysisEventListener<TExcelVO>  {
    List<TExcelVO> list = new LinkedList<>();
    List<SiteNameExcelVO> siteNameExcelVOS=new ArrayList<>();
    List<SiteNameExcelVO> siteNameExcelVOSErrors=new ArrayList<>();
    List<SiteNameExcelVO> siteNameExcelVOSNotYiZhi=new ArrayList<>();
    @Override
    public void invoke(TExcelVO data, AnalysisContext context) {
        list.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

        String siteNamePath = "/Users/<USER>/Desktop/环境绩效/地盘.xlsx";
        EasyExcel.read(siteNamePath, SiteNameExcelVO.class, new AnalysisEventListener<SiteNameExcelVO>() {
            @Override
            public void invoke(SiteNameExcelVO data, AnalysisContext context) {
                siteNameExcelVOS.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {

            }
        }).sheet().doRead();

        for (SiteNameExcelVO siteNameExcelVO : siteNameExcelVOS) {
            try {
                process(siteNameExcelVO.getSiteName());
            } catch (Exception e) {
                log.error("处理失败:{}",siteNameExcelVO.getSiteName(),e);
                siteNameExcelVOSErrors.add(siteNameExcelVO);
            }
        }
//        process("两江曲院风荷项目（C44-1/02、C45-1/02地块）EPC");
        if (siteNameExcelVOSErrors.size()>0){
            EasyExcel.write("/Users/<USER>/Desktop/环境绩效/地盘错误-办公场所.xlsx", SiteNameExcelVO.class)
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet("Sheet1")
                    .doWrite(siteNameExcelVOSErrors);
        }
        if (siteNameExcelVOSNotYiZhi.size()>0){
            EasyExcel.write("/Users/<USER>/Desktop/环境绩效/地盘不一致-办公场所.xlsx", SiteNameExcelVO.class)
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet("Sheet1")
                    .doWrite(siteNameExcelVOSNotYiZhi);
        }


    }

    private void process(String SiteName) {
        ProcedureMapper procedureMapper = SpringContextUtil.getBean(ProcedureMapper.class);
//        List<ProcedureVO> procedureVOS = procedureMapper.callHouseProcedure(SiteName);
//        List<ProcedureVO> procedureVOS = procedureMapper.callMajorProcedure(SiteName);
//        List<ProcedureVO> procedureVOS = procedureMapper.callManufactureProcedure(SiteName);
//        List<ProcedureVO> procedureVOS = procedureMapper.callManufactureProcedure(SiteName);
        List<ProcedureVO> procedureVOS = procedureMapper.callWorkProcedure(SiteName);
        if (procedureVOS.size() == list.size()) {
            Console.log("数据一致");
            for (int i = 0; i < list.size(); i++) {
                TExcelVO tExcelVO = list.get(i);
                ProcedureVO procedureVO = procedureVOS.get(i);
                if (tExcelVO.getRowColumnNumber().equals(procedureVO.getMe())) {
                    tExcelVO.setResourceConsumption(procedureVO.getMonthValue());
                }
            }
            EasyExcel.write("/Users/<USER>/Desktop/环境绩效/模板文件-办公场所/" + SiteName.replaceAll("/","-") + ".xlsx", TExcelVO.class)
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet("Sheet1")
                    .doWrite(list);
        } else {
            log.info("数据不一致:{}",SiteName);
            if (procedureVOS.size() != 202) {
                siteNameExcelVOSNotYiZhi.add(new SiteNameExcelVO(SiteName));
            }
        }

    }

}
