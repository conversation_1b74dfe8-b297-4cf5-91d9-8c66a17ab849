<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.TzhInvoiceFileMapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.TzhInvoiceFile">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 12 14:23:06 HKT 2023.
    -->
    <result column="Id" jdbcType="CHAR" property="id" />
    <result column="ParentId" jdbcType="NVARCHAR" property="parentid" />
    <result column="SiteName" jdbcType="NVARCHAR" property="sitename" />
    <result column="RecordYearMonth" jdbcType="INTEGER" property="recordyearmonth" />
    <result column="CarbonEmissionLocation" jdbcType="NVARCHAR" property="carbonemissionlocation" />
    <result column="Section" jdbcType="NVARCHAR" property="section" />
    <result column="MaterialName" jdbcType="NVARCHAR" property="materialname" />
    <result column="MaterialCode" jdbcType="NVARCHAR" property="materialcode" />
    <result column="MaterialAttribute" jdbcType="NVARCHAR" property="materialattribute" />
    <result column="BillNo" jdbcType="NVARCHAR" property="billno" />
    <result column="CreatedBy" jdbcType="VARCHAR" property="createdby" />
    <result column="CreatedTime" jdbcType="TIMESTAMP" property="createdtime" />
    <result column="DeletedBy" jdbcType="VARCHAR" property="deletedby" />
    <result column="DeletedTime" jdbcType="TIMESTAMP" property="deletedtime" />
    <result column="IsDeleted" jdbcType="BIT" property="isdeleted" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.csci.tzh.model.TzhInvoiceFile">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 12 14:23:06 HKT 2023.
    -->
    <result column="Pdf" jdbcType="VARBINARY" property="pdf" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 12 14:23:06 HKT 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 12 14:23:06 HKT 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 12 14:23:06 HKT 2023.
    -->
    Id, ParentId, SiteName, RecordYearMonth, CarbonEmissionLocation, Section, MaterialName, 
    MaterialCode, MaterialAttribute, BillNo, CreatedBy, CreatedTime, DeletedBy, DeletedTime, 
    IsDeleted
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 12 14:23:06 HKT 2023.
    -->
    Pdf
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.csci.tzh.model.TzhInvoiceFileExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 12 14:23:06 HKT 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from Tzh_Invoice_File
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.csci.tzh.model.TzhInvoiceFileExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 12 14:23:06 HKT 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from Tzh_Invoice_File
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.TzhInvoiceFileExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 12 14:23:06 HKT 2023.
    -->
    delete from Tzh_Invoice_File
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.TzhInvoiceFile">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 12 14:23:06 HKT 2023.
    -->
    insert into Tzh_Invoice_File (Id, ParentId, SiteName, 
      RecordYearMonth, CarbonEmissionLocation, 
      Section, MaterialName, MaterialCode, 
      MaterialAttribute, BillNo, CreatedBy, 
      CreatedTime, DeletedBy, DeletedTime, 
      IsDeleted, Pdf)
    values (#{id,jdbcType=CHAR}, #{parentid,jdbcType=NVARCHAR}, #{sitename,jdbcType=NVARCHAR}, 
      #{recordyearmonth,jdbcType=INTEGER}, #{carbonemissionlocation,jdbcType=NVARCHAR}, 
      #{section,jdbcType=NVARCHAR}, #{materialname,jdbcType=NVARCHAR}, #{materialcode,jdbcType=NVARCHAR}, 
      #{materialattribute,jdbcType=NVARCHAR}, #{billno,jdbcType=NVARCHAR}, #{createdby,jdbcType=VARCHAR}, 
      #{createdtime,jdbcType=TIMESTAMP}, #{deletedby,jdbcType=VARCHAR}, #{deletedtime,jdbcType=TIMESTAMP}, 
      #{isdeleted,jdbcType=BIT}, #{pdf,jdbcType=VARBINARY})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.TzhInvoiceFile">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 12 14:23:06 HKT 2023.
    -->
    insert into Tzh_Invoice_File
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="parentid != null">
        ParentId,
      </if>
      <if test="sitename != null">
        SiteName,
      </if>
      <if test="recordyearmonth != null">
        RecordYearMonth,
      </if>
      <if test="carbonemissionlocation != null">
        CarbonEmissionLocation,
      </if>
      <if test="section != null">
        Section,
      </if>
      <if test="materialname != null">
        MaterialName,
      </if>
      <if test="materialcode != null">
        MaterialCode,
      </if>
      <if test="materialattribute != null">
        MaterialAttribute,
      </if>
      <if test="billno != null">
        BillNo,
      </if>
      <if test="createdby != null">
        CreatedBy,
      </if>
      <if test="createdtime != null">
        CreatedTime,
      </if>
      <if test="deletedby != null">
        DeletedBy,
      </if>
      <if test="deletedtime != null">
        DeletedTime,
      </if>
      <if test="isdeleted != null">
        IsDeleted,
      </if>
      <if test="pdf != null">
        Pdf,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="parentid != null">
        #{parentid,jdbcType=NVARCHAR},
      </if>
      <if test="sitename != null">
        #{sitename,jdbcType=NVARCHAR},
      </if>
      <if test="recordyearmonth != null">
        #{recordyearmonth,jdbcType=INTEGER},
      </if>
      <if test="carbonemissionlocation != null">
        #{carbonemissionlocation,jdbcType=NVARCHAR},
      </if>
      <if test="section != null">
        #{section,jdbcType=NVARCHAR},
      </if>
      <if test="materialname != null">
        #{materialname,jdbcType=NVARCHAR},
      </if>
      <if test="materialcode != null">
        #{materialcode,jdbcType=NVARCHAR},
      </if>
      <if test="materialattribute != null">
        #{materialattribute,jdbcType=NVARCHAR},
      </if>
      <if test="billno != null">
        #{billno,jdbcType=NVARCHAR},
      </if>
      <if test="createdby != null">
        #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="createdtime != null">
        #{createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedby != null">
        #{deletedby,jdbcType=VARCHAR},
      </if>
      <if test="deletedtime != null">
        #{deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isdeleted != null">
        #{isdeleted,jdbcType=BIT},
      </if>
      <if test="pdf != null">
        #{pdf,jdbcType=VARBINARY},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.TzhInvoiceFileExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 12 14:23:06 HKT 2023.
    -->
    select count(*) from Tzh_Invoice_File
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 12 14:23:06 HKT 2023.
    -->
    update Tzh_Invoice_File
    <set>
      <if test="row.id != null">
        Id = #{row.id,jdbcType=CHAR},
      </if>
      <if test="row.parentid != null">
        ParentId = #{row.parentid,jdbcType=NVARCHAR},
      </if>
      <if test="row.sitename != null">
        SiteName = #{row.sitename,jdbcType=NVARCHAR},
      </if>
      <if test="row.recordyearmonth != null">
        RecordYearMonth = #{row.recordyearmonth,jdbcType=INTEGER},
      </if>
      <if test="row.carbonemissionlocation != null">
        CarbonEmissionLocation = #{row.carbonemissionlocation,jdbcType=NVARCHAR},
      </if>
      <if test="row.section != null">
        Section = #{row.section,jdbcType=NVARCHAR},
      </if>
      <if test="row.materialname != null">
        MaterialName = #{row.materialname,jdbcType=NVARCHAR},
      </if>
      <if test="row.materialcode != null">
        MaterialCode = #{row.materialcode,jdbcType=NVARCHAR},
      </if>
      <if test="row.materialattribute != null">
        MaterialAttribute = #{row.materialattribute,jdbcType=NVARCHAR},
      </if>
      <if test="row.billno != null">
        BillNo = #{row.billno,jdbcType=NVARCHAR},
      </if>
      <if test="row.createdby != null">
        CreatedBy = #{row.createdby,jdbcType=VARCHAR},
      </if>
      <if test="row.createdtime != null">
        CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.deletedby != null">
        DeletedBy = #{row.deletedby,jdbcType=VARCHAR},
      </if>
      <if test="row.deletedtime != null">
        DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.isdeleted != null">
        IsDeleted = #{row.isdeleted,jdbcType=BIT},
      </if>
      <if test="row.pdf != null">
        Pdf = #{row.pdf,jdbcType=VARBINARY},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 12 14:23:06 HKT 2023.
    -->
    update Tzh_Invoice_File
    set Id = #{row.id,jdbcType=CHAR},
      ParentId = #{row.parentid,jdbcType=NVARCHAR},
      SiteName = #{row.sitename,jdbcType=NVARCHAR},
      RecordYearMonth = #{row.recordyearmonth,jdbcType=INTEGER},
      CarbonEmissionLocation = #{row.carbonemissionlocation,jdbcType=NVARCHAR},
      Section = #{row.section,jdbcType=NVARCHAR},
      MaterialName = #{row.materialname,jdbcType=NVARCHAR},
      MaterialCode = #{row.materialcode,jdbcType=NVARCHAR},
      MaterialAttribute = #{row.materialattribute,jdbcType=NVARCHAR},
      BillNo = #{row.billno,jdbcType=NVARCHAR},
      CreatedBy = #{row.createdby,jdbcType=VARCHAR},
      CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      DeletedBy = #{row.deletedby,jdbcType=VARCHAR},
      DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      IsDeleted = #{row.isdeleted,jdbcType=BIT},
      Pdf = #{row.pdf,jdbcType=VARBINARY}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 12 14:23:06 HKT 2023.
    -->
    update Tzh_Invoice_File
    set Id = #{row.id,jdbcType=CHAR},
      ParentId = #{row.parentid,jdbcType=NVARCHAR},
      SiteName = #{row.sitename,jdbcType=NVARCHAR},
      RecordYearMonth = #{row.recordyearmonth,jdbcType=INTEGER},
      CarbonEmissionLocation = #{row.carbonemissionlocation,jdbcType=NVARCHAR},
      Section = #{row.section,jdbcType=NVARCHAR},
      MaterialName = #{row.materialname,jdbcType=NVARCHAR},
      MaterialCode = #{row.materialcode,jdbcType=NVARCHAR},
      MaterialAttribute = #{row.materialattribute,jdbcType=NVARCHAR},
      BillNo = #{row.billno,jdbcType=NVARCHAR},
      CreatedBy = #{row.createdby,jdbcType=VARCHAR},
      CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      DeletedBy = #{row.deletedby,jdbcType=VARCHAR},
      DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      IsDeleted = #{row.isdeleted,jdbcType=BIT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>