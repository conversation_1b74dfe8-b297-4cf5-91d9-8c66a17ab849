package com.csci.tzh.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csci.cohl.beans.vo.TzhProtocolCategoryVO;
import com.csci.tzh.model.TzhProtocolCategory;
import com.csci.tzh.model.TzhProtocolCategoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface TzhProtocolCategoryMapper extends BaseMapper<com.csci.cohl.model.TzhProtocolCategory> {

    @Select("""
            SELECT C.Id, P.Name AS Protocol, P.NameSC AS ProtocolSC, P.NameEN AS ProtocolEN,
            C.CategoryName, C.CategoryNameSC, C.CategoryNameEN
            FROM Tzh_Protocol P
            LEFT JOIN Tzh_Protocol_Category C ON P.Id = C.ProtocolId
            WHERE P.NameEN = #{protocol}
            ORDER BY P.Name, C.CategoryName
            """)
    List<TzhProtocolCategoryVO> listCategory(@Param("protocol") String protocol);
   /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol_Category
     *
     * @mbg.generated Tue Apr 09 10:58:47 HKT 2024
     */
    long countByExample(TzhProtocolCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol_Category
     *
     * @mbg.generated Tue Apr 09 10:58:47 HKT 2024
     */
    int deleteByExample(TzhProtocolCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol_Category
     *
     * @mbg.generated Tue Apr 09 10:58:47 HKT 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol_Category
     *
     * @mbg.generated Tue Apr 09 10:58:47 HKT 2024
     */
    int insert(TzhProtocolCategory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol_Category
     *
     * @mbg.generated Tue Apr 09 10:58:47 HKT 2024
     */
    int insertSelective(TzhProtocolCategory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol_Category
     *
     * @mbg.generated Tue Apr 09 10:58:47 HKT 2024
     */
    List<TzhProtocolCategory> selectByExample(TzhProtocolCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol_Category
     *
     * @mbg.generated Tue Apr 09 10:58:47 HKT 2024
     */
    TzhProtocolCategory selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol_Category
     *
     * @mbg.generated Tue Apr 09 10:58:47 HKT 2024
     */
    int updateByExampleSelective(@Param("record") TzhProtocolCategory record, @Param("example") TzhProtocolCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol_Category
     *
     * @mbg.generated Tue Apr 09 10:58:47 HKT 2024
     */
    int updateByExample(@Param("record") TzhProtocolCategory record, @Param("example") TzhProtocolCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol_Category
     *
     * @mbg.generated Tue Apr 09 10:58:47 HKT 2024
     */
    int updateByPrimaryKeySelective(TzhProtocolCategory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Protocol_Category
     *
     * @mbg.generated Tue Apr 09 10:58:47 HKT 2024
     */
    int updateByPrimaryKey(TzhProtocolCategory record);
}
