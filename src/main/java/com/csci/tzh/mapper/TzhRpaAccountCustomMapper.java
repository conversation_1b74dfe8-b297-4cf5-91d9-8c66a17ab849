package com.csci.tzh.mapper;

import com.csci.tzh.model.TzhRpaAccount;
import com.csci.tzh.vo.TzhProjectDetailVO;
import com.csci.tzh.vo.TzhRpaAccountVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface TzhRpaAccountCustomMapper {
	
    @Select("""
			SELECT RA.*, protocol = P.Name, protocolsc = P.NameSC, protocolen = P.NameEN,
			carbonemissionlocation = CEL.Name, carbonemissionlocationsc = CEL.NameSC, carbonemissionlocationen = CEL.NameEN
			FROM Tzh_RpaAccount RA
			LEFT JOIN Tzh_CarbonEmissionLocation CEL ON RA.CarbonEmissionLocationId = CEL.Id
			LEFT JOIN Tzh_Protocol P ON RA.ProtocolId = P.Id
			WHERE RA.ProjectName = #{projectName} AND (RA.AccountType = #{accountType} OR #{accountType} = '') AND RA.IsDeleted = 0
			""")
    public List<TzhRpaAccountVO> list(@Param("projectName") String projectName, @Param("accountType") String accountType);

}