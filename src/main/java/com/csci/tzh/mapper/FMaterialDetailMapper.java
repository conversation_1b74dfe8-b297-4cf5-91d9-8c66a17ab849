package com.csci.tzh.mapper;

import com.csci.tzh.model.FMaterialDetail;
import com.csci.tzh.model.FMaterialDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FMaterialDetailMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Material_Detail
     *
     * @mbg.generated Wed Oct 19 17:17:08 HKT 2022
     */
    long countByExample(FMaterialDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Material_Detail
     *
     * @mbg.generated Wed Oct 19 17:17:08 HKT 2022
     */
    int deleteByExample(FMaterialDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Material_Detail
     *
     * @mbg.generated Wed Oct 19 17:17:08 HKT 2022
     */
    int insert(FMaterialDetail row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Material_Detail
     *
     * @mbg.generated Wed Oct 19 17:17:08 HKT 2022
     */
    int insertSelective(FMaterialDetail row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Material_Detail
     *
     * @mbg.generated Wed Oct 19 17:17:08 HKT 2022
     */
    List<FMaterialDetail> selectByExample(FMaterialDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Material_Detail
     *
     * @mbg.generated Wed Oct 19 17:17:08 HKT 2022
     */
    int updateByExampleSelective(@Param("row") FMaterialDetail row, @Param("example") FMaterialDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table F_Material_Detail
     *
     * @mbg.generated Wed Oct 19 17:17:08 HKT 2022
     */
    int updateByExample(@Param("row") FMaterialDetail row, @Param("example") FMaterialDetailExample example);
}