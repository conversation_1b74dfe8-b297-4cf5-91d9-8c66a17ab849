package com.csci.tzh.mapper;

import com.csci.tzh.vo.FCdmsGasCarbonFactorVO;
import com.csci.tzh.vo.TzhOrgMaterialCarbonFactorVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface TzhOrgMaterialCarbonFactorCustomMapper {
	
    @Select("""
			SELECT OMCF.*, 
			Protocol = P.Name, ProtocolSC = P.NameSC, ProtocolEN = P.NameEN,
			C.CategoryName, C.CategoryNameSC, C.CategoryNameEN, SC.SubCategoryName, SC.SubCategoryNameSC, SC.SubCategoryNameEN
			FROM Tzh_Org_MaterialCarbonFactor OMCF
			LEFT JOIN Tzh_Protocol_SubCategory SC ON OMCF.ProtocolSubCategoryId = SC.Id
			LEFT JOIN Tzh_Protocol_Category C ON SC.CategoryId = C.Id
			LEFT JOIN Tzh_Protocol P ON C.ProtocolId = P.Id
			WHERE (OMCF.ChineseName LIKE '%' + #{chineseName} + '%' OR #{chineseName} = '')
			AND OMCF.IsDeleted = 0
			""")
    public List<TzhOrgMaterialCarbonFactorVO> list(@Param("chineseName") String chineseName);

}