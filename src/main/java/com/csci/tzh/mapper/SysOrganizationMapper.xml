<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.SysOrganizationMapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.SysOrganization">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 24 16:25:25 HKT 2022.
    -->
    <result column="Id" jdbcType="INTEGER" property="id" />
    <result column="InnerNo" jdbcType="NVARCHAR" property="innerno" />
    <result column="Code" jdbcType="NVARCHAR" property="code" />
    <result column="Name" jdbcType="NVARCHAR" property="name" />
    <result column="Type" jdbcType="INTEGER" property="type" />
    <result column="RelevantId" jdbcType="INTEGER" property="relevantid" />
    <result column="SortKey" jdbcType="INTEGER" property="sortkey" />
    <result column="TenantId" jdbcType="INTEGER" property="tenantid" />
    <result column="IsDeleted" jdbcType="BIT" property="isdeleted" />
    <result column="DeleterUserId" jdbcType="BIGINT" property="deleteruserid" />
    <result column="DeletionTime" jdbcType="TIMESTAMP" property="deletiontime" />
    <result column="LastModificationTime" jdbcType="TIMESTAMP" property="lastmodificationtime" />
    <result column="LastModifierUserId" jdbcType="BIGINT" property="lastmodifieruserid" />
    <result column="CreationTime" jdbcType="TIMESTAMP" property="creationtime" />
    <result column="CreatorUserId" jdbcType="BIGINT" property="creatoruserid" />
    <result column="SourceId" jdbcType="CHAR" property="sourceid" />
    <result column="KeyInMdm" jdbcType="NVARCHAR" property="keyinmdm" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.csci.tzh.model.SysOrganization">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 24 16:25:25 HKT 2022.
    -->
    <result column="TimeStamp" jdbcType="BINARY" property="timestamp" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 24 16:25:25 HKT 2022.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 24 16:25:25 HKT 2022.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 24 16:25:25 HKT 2022.
    -->
    Id, InnerNo, Code, Name, Type, RelevantId, SortKey, TenantId, IsDeleted, DeleterUserId, 
    DeletionTime, LastModificationTime, LastModifierUserId, CreationTime, CreatorUserId, 
    SourceId, KeyInMdm
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 24 16:25:25 HKT 2022.
    -->
    TimeStamp
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.csci.tzh.model.SysOrganizationExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 24 16:25:25 HKT 2022.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from sysOrganization
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.csci.tzh.model.SysOrganizationExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 24 16:25:25 HKT 2022.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from sysOrganization
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.SysOrganizationExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 24 16:25:25 HKT 2022.
    -->
    delete from sysOrganization
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.SysOrganization">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 24 16:25:25 HKT 2022.
    -->
    insert into sysOrganization (Id, InnerNo, Code, 
      Name, Type, RelevantId, 
      SortKey, TenantId, IsDeleted, 
      DeleterUserId, DeletionTime, LastModificationTime, 
      LastModifierUserId, CreationTime, CreatorUserId, 
      SourceId, KeyInMdm, TimeStamp
      )
    values (#{id,jdbcType=INTEGER}, #{innerno,jdbcType=NVARCHAR}, #{code,jdbcType=NVARCHAR}, 
      #{name,jdbcType=NVARCHAR}, #{type,jdbcType=INTEGER}, #{relevantid,jdbcType=INTEGER}, 
      #{sortkey,jdbcType=INTEGER}, #{tenantid,jdbcType=INTEGER}, #{isdeleted,jdbcType=BIT}, 
      #{deleteruserid,jdbcType=BIGINT}, #{deletiontime,jdbcType=TIMESTAMP}, #{lastmodificationtime,jdbcType=TIMESTAMP}, 
      #{lastmodifieruserid,jdbcType=BIGINT}, #{creationtime,jdbcType=TIMESTAMP}, #{creatoruserid,jdbcType=BIGINT}, 
      #{sourceid,jdbcType=CHAR}, #{keyinmdm,jdbcType=NVARCHAR}, #{timestamp,jdbcType=BINARY}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.SysOrganization">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 24 16:25:25 HKT 2022.
    -->
    insert into sysOrganization
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="innerno != null">
        InnerNo,
      </if>
      <if test="code != null">
        Code,
      </if>
      <if test="name != null">
        Name,
      </if>
      <if test="type != null">
        Type,
      </if>
      <if test="relevantid != null">
        RelevantId,
      </if>
      <if test="sortkey != null">
        SortKey,
      </if>
      <if test="tenantid != null">
        TenantId,
      </if>
      <if test="isdeleted != null">
        IsDeleted,
      </if>
      <if test="deleteruserid != null">
        DeleterUserId,
      </if>
      <if test="deletiontime != null">
        DeletionTime,
      </if>
      <if test="lastmodificationtime != null">
        LastModificationTime,
      </if>
      <if test="lastmodifieruserid != null">
        LastModifierUserId,
      </if>
      <if test="creationtime != null">
        CreationTime,
      </if>
      <if test="creatoruserid != null">
        CreatorUserId,
      </if>
      <if test="sourceid != null">
        SourceId,
      </if>
      <if test="keyinmdm != null">
        KeyInMdm,
      </if>
      <if test="timestamp != null">
        TimeStamp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="innerno != null">
        #{innerno,jdbcType=NVARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=NVARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=NVARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="relevantid != null">
        #{relevantid,jdbcType=INTEGER},
      </if>
      <if test="sortkey != null">
        #{sortkey,jdbcType=INTEGER},
      </if>
      <if test="tenantid != null">
        #{tenantid,jdbcType=INTEGER},
      </if>
      <if test="isdeleted != null">
        #{isdeleted,jdbcType=BIT},
      </if>
      <if test="deleteruserid != null">
        #{deleteruserid,jdbcType=BIGINT},
      </if>
      <if test="deletiontime != null">
        #{deletiontime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastmodificationtime != null">
        #{lastmodificationtime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastmodifieruserid != null">
        #{lastmodifieruserid,jdbcType=BIGINT},
      </if>
      <if test="creationtime != null">
        #{creationtime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatoruserid != null">
        #{creatoruserid,jdbcType=BIGINT},
      </if>
      <if test="sourceid != null">
        #{sourceid,jdbcType=CHAR},
      </if>
      <if test="keyinmdm != null">
        #{keyinmdm,jdbcType=NVARCHAR},
      </if>
      <if test="timestamp != null">
        #{timestamp,jdbcType=BINARY},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.SysOrganizationExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 24 16:25:25 HKT 2022.
    -->
    select count(*) from sysOrganization
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 24 16:25:25 HKT 2022.
    -->
    update sysOrganization
    <set>
      <if test="row.id != null">
        Id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.innerno != null">
        InnerNo = #{row.innerno,jdbcType=NVARCHAR},
      </if>
      <if test="row.code != null">
        Code = #{row.code,jdbcType=NVARCHAR},
      </if>
      <if test="row.name != null">
        Name = #{row.name,jdbcType=NVARCHAR},
      </if>
      <if test="row.type != null">
        Type = #{row.type,jdbcType=INTEGER},
      </if>
      <if test="row.relevantid != null">
        RelevantId = #{row.relevantid,jdbcType=INTEGER},
      </if>
      <if test="row.sortkey != null">
        SortKey = #{row.sortkey,jdbcType=INTEGER},
      </if>
      <if test="row.tenantid != null">
        TenantId = #{row.tenantid,jdbcType=INTEGER},
      </if>
      <if test="row.isdeleted != null">
        IsDeleted = #{row.isdeleted,jdbcType=BIT},
      </if>
      <if test="row.deleteruserid != null">
        DeleterUserId = #{row.deleteruserid,jdbcType=BIGINT},
      </if>
      <if test="row.deletiontime != null">
        DeletionTime = #{row.deletiontime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.lastmodificationtime != null">
        LastModificationTime = #{row.lastmodificationtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.lastmodifieruserid != null">
        LastModifierUserId = #{row.lastmodifieruserid,jdbcType=BIGINT},
      </if>
      <if test="row.creationtime != null">
        CreationTime = #{row.creationtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.creatoruserid != null">
        CreatorUserId = #{row.creatoruserid,jdbcType=BIGINT},
      </if>
      <if test="row.sourceid != null">
        SourceId = #{row.sourceid,jdbcType=CHAR},
      </if>
      <if test="row.keyinmdm != null">
        KeyInMdm = #{row.keyinmdm,jdbcType=NVARCHAR},
      </if>
      <if test="row.timestamp != null">
        TimeStamp = #{row.timestamp,jdbcType=BINARY},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 24 16:25:25 HKT 2022.
    -->
    update sysOrganization
    set Id = #{row.id,jdbcType=INTEGER},
      InnerNo = #{row.innerno,jdbcType=NVARCHAR},
      Code = #{row.code,jdbcType=NVARCHAR},
      Name = #{row.name,jdbcType=NVARCHAR},
      Type = #{row.type,jdbcType=INTEGER},
      RelevantId = #{row.relevantid,jdbcType=INTEGER},
      SortKey = #{row.sortkey,jdbcType=INTEGER},
      TenantId = #{row.tenantid,jdbcType=INTEGER},
      IsDeleted = #{row.isdeleted,jdbcType=BIT},
      DeleterUserId = #{row.deleteruserid,jdbcType=BIGINT},
      DeletionTime = #{row.deletiontime,jdbcType=TIMESTAMP},
      LastModificationTime = #{row.lastmodificationtime,jdbcType=TIMESTAMP},
      LastModifierUserId = #{row.lastmodifieruserid,jdbcType=BIGINT},
      CreationTime = #{row.creationtime,jdbcType=TIMESTAMP},
      CreatorUserId = #{row.creatoruserid,jdbcType=BIGINT},
      SourceId = #{row.sourceid,jdbcType=CHAR},
      KeyInMdm = #{row.keyinmdm,jdbcType=NVARCHAR},
      TimeStamp = #{row.timestamp,jdbcType=BINARY}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 24 16:25:25 HKT 2022.
    -->
    update sysOrganization
    set Id = #{row.id,jdbcType=INTEGER},
      InnerNo = #{row.innerno,jdbcType=NVARCHAR},
      Code = #{row.code,jdbcType=NVARCHAR},
      Name = #{row.name,jdbcType=NVARCHAR},
      Type = #{row.type,jdbcType=INTEGER},
      RelevantId = #{row.relevantid,jdbcType=INTEGER},
      SortKey = #{row.sortkey,jdbcType=INTEGER},
      TenantId = #{row.tenantid,jdbcType=INTEGER},
      IsDeleted = #{row.isdeleted,jdbcType=BIT},
      DeleterUserId = #{row.deleteruserid,jdbcType=BIGINT},
      DeletionTime = #{row.deletiontime,jdbcType=TIMESTAMP},
      LastModificationTime = #{row.lastmodificationtime,jdbcType=TIMESTAMP},
      LastModifierUserId = #{row.lastmodifieruserid,jdbcType=BIGINT},
      CreationTime = #{row.creationtime,jdbcType=TIMESTAMP},
      CreatorUserId = #{row.creatoruserid,jdbcType=BIGINT},
      SourceId = #{row.sourceid,jdbcType=CHAR},
      KeyInMdm = #{row.keyinmdm,jdbcType=NVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>