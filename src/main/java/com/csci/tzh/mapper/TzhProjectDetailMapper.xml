<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.TzhProjectDetailMapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.TzhProjectDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 06 09:05:56 CST 2025.
    -->
    <result column="Id" jdbcType="CHAR" property="id" />
    <result column="SiteName" jdbcType="NVARCHAR" property="sitename" />
    <result column="ProtocolId" jdbcType="CHAR" property="protocolid" />
    <result column="CarbonEmissionLocationId" jdbcType="CHAR" property="carbonemissionlocationid" />
    <result column="EmissionReductionTarget" jdbcType="NUMERIC" property="emissionreductiontarget" />
    <result column="EpdWasteAccount" jdbcType="NVARCHAR" property="epdwasteaccount" />
    <result column="EpdWasteAccountPwd" jdbcType="NVARCHAR" property="epdwasteaccountpwd" />
    <result column="HasFoodCourt" jdbcType="NVARCHAR" property="hasfoodcourt" />
    <result column="DataSourceId" jdbcType="CHAR" property="datasourceid" />
    <result column="WasterWaterCarbonFactor" jdbcType="NUMERIC" property="wasterwatercarbonfactor" />
    <result column="WasterWaterCarbonFactorUnit" jdbcType="NVARCHAR" property="wasterwatercarbonfactorunit" />
    <result column="WaterElectricityBillSource" jdbcType="NVARCHAR" property="waterelectricitybillsource" />
    <result column="AccountingCenterNo" jdbcType="NVARCHAR" property="accountingcenterno" />
    <result column="CreatedBy" jdbcType="VARCHAR" property="createdby" />
    <result column="CreatedTime" jdbcType="TIMESTAMP" property="createdtime" />
    <result column="DeletedBy" jdbcType="VARCHAR" property="deletedby" />
    <result column="DeletedTime" jdbcType="TIMESTAMP" property="deletedtime" />
    <result column="IsDeleted" jdbcType="BIT" property="isdeleted" />
    <result column="SiteId" jdbcType="NVARCHAR" property="siteid" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 06 09:05:56 CST 2025.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 06 09:05:56 CST 2025.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 06 09:05:56 CST 2025.
    -->
    Id, SiteName, ProtocolId, CarbonEmissionLocationId, EmissionReductionTarget, EpdWasteAccount, 
    EpdWasteAccountPwd, HasFoodCourt, DataSourceId, WasterWaterCarbonFactor, WasterWaterCarbonFactorUnit, 
    WaterElectricityBillSource, AccountingCenterNo, CreatedBy, CreatedTime, DeletedBy, 
    DeletedTime, IsDeleted, SiteId
  </sql>
  <select id="selectByExample" parameterType="com.csci.tzh.model.TzhProjectDetailExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 06 09:05:56 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from Tzh_ProjectDetail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.TzhProjectDetailExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 06 09:05:56 CST 2025.
    -->
    delete from Tzh_ProjectDetail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.TzhProjectDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 06 09:05:56 CST 2025.
    -->
    insert into Tzh_ProjectDetail (Id, SiteName, ProtocolId, 
      CarbonEmissionLocationId, EmissionReductionTarget, 
      EpdWasteAccount, EpdWasteAccountPwd, 
      HasFoodCourt, DataSourceId, WasterWaterCarbonFactor, 
      WasterWaterCarbonFactorUnit, WaterElectricityBillSource, 
      AccountingCenterNo, CreatedBy, CreatedTime, 
      DeletedBy, DeletedTime, IsDeleted, 
      SiteId)
    values (#{id,jdbcType=CHAR}, #{sitename,jdbcType=NVARCHAR}, #{protocolid,jdbcType=CHAR}, 
      #{carbonemissionlocationid,jdbcType=CHAR}, #{emissionreductiontarget,jdbcType=NUMERIC}, 
      #{epdwasteaccount,jdbcType=NVARCHAR}, #{epdwasteaccountpwd,jdbcType=NVARCHAR}, 
      #{hasfoodcourt,jdbcType=NVARCHAR}, #{datasourceid,jdbcType=CHAR}, #{wasterwatercarbonfactor,jdbcType=NUMERIC}, 
      #{wasterwatercarbonfactorunit,jdbcType=NVARCHAR}, #{waterelectricitybillsource,jdbcType=NVARCHAR}, 
      #{accountingcenterno,jdbcType=NVARCHAR}, #{createdby,jdbcType=VARCHAR}, #{createdtime,jdbcType=TIMESTAMP}, 
      #{deletedby,jdbcType=VARCHAR}, #{deletedtime,jdbcType=TIMESTAMP}, #{isdeleted,jdbcType=BIT}, 
      #{siteid,jdbcType=NVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.TzhProjectDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 06 09:05:56 CST 2025.
    -->
    insert into Tzh_ProjectDetail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="sitename != null">
        SiteName,
      </if>
      <if test="protocolid != null">
        ProtocolId,
      </if>
      <if test="carbonemissionlocationid != null">
        CarbonEmissionLocationId,
      </if>
      <if test="emissionreductiontarget != null">
        EmissionReductionTarget,
      </if>
      <if test="epdwasteaccount != null">
        EpdWasteAccount,
      </if>
      <if test="epdwasteaccountpwd != null">
        EpdWasteAccountPwd,
      </if>
      <if test="hasfoodcourt != null">
        HasFoodCourt,
      </if>
      <if test="datasourceid != null">
        DataSourceId,
      </if>
      <if test="wasterwatercarbonfactor != null">
        WasterWaterCarbonFactor,
      </if>
      <if test="wasterwatercarbonfactorunit != null">
        WasterWaterCarbonFactorUnit,
      </if>
      <if test="waterelectricitybillsource != null">
        WaterElectricityBillSource,
      </if>
      <if test="accountingcenterno != null">
        AccountingCenterNo,
      </if>
      <if test="createdby != null">
        CreatedBy,
      </if>
      <if test="createdtime != null">
        CreatedTime,
      </if>
      <if test="deletedby != null">
        DeletedBy,
      </if>
      <if test="deletedtime != null">
        DeletedTime,
      </if>
      <if test="isdeleted != null">
        IsDeleted,
      </if>
      <if test="siteid != null">
        SiteId,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="sitename != null">
        #{sitename,jdbcType=NVARCHAR},
      </if>
      <if test="protocolid != null">
        #{protocolid,jdbcType=CHAR},
      </if>
      <if test="carbonemissionlocationid != null">
        #{carbonemissionlocationid,jdbcType=CHAR},
      </if>
      <if test="emissionreductiontarget != null">
        #{emissionreductiontarget,jdbcType=NUMERIC},
      </if>
      <if test="epdwasteaccount != null">
        #{epdwasteaccount,jdbcType=NVARCHAR},
      </if>
      <if test="epdwasteaccountpwd != null">
        #{epdwasteaccountpwd,jdbcType=NVARCHAR},
      </if>
      <if test="hasfoodcourt != null">
        #{hasfoodcourt,jdbcType=NVARCHAR},
      </if>
      <if test="datasourceid != null">
        #{datasourceid,jdbcType=CHAR},
      </if>
      <if test="wasterwatercarbonfactor != null">
        #{wasterwatercarbonfactor,jdbcType=NUMERIC},
      </if>
      <if test="wasterwatercarbonfactorunit != null">
        #{wasterwatercarbonfactorunit,jdbcType=NVARCHAR},
      </if>
      <if test="waterelectricitybillsource != null">
        #{waterelectricitybillsource,jdbcType=NVARCHAR},
      </if>
      <if test="accountingcenterno != null">
        #{accountingcenterno,jdbcType=NVARCHAR},
      </if>
      <if test="createdby != null">
        #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="createdtime != null">
        #{createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedby != null">
        #{deletedby,jdbcType=VARCHAR},
      </if>
      <if test="deletedtime != null">
        #{deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isdeleted != null">
        #{isdeleted,jdbcType=BIT},
      </if>
      <if test="siteid != null">
        #{siteid,jdbcType=NVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.TzhProjectDetailExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 06 09:05:56 CST 2025.
    -->
    select count(*) from Tzh_ProjectDetail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 06 09:05:56 CST 2025.
    -->
    update Tzh_ProjectDetail
    <set>
      <if test="record.id != null">
        Id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.sitename != null">
        SiteName = #{record.sitename,jdbcType=NVARCHAR},
      </if>
      <if test="record.protocolid != null">
        ProtocolId = #{record.protocolid,jdbcType=CHAR},
      </if>
      <if test="record.carbonemissionlocationid != null">
        CarbonEmissionLocationId = #{record.carbonemissionlocationid,jdbcType=CHAR},
      </if>
      <if test="record.emissionreductiontarget != null">
        EmissionReductionTarget = #{record.emissionreductiontarget,jdbcType=NUMERIC},
      </if>
      <if test="record.epdwasteaccount != null">
        EpdWasteAccount = #{record.epdwasteaccount,jdbcType=NVARCHAR},
      </if>
      <if test="record.epdwasteaccountpwd != null">
        EpdWasteAccountPwd = #{record.epdwasteaccountpwd,jdbcType=NVARCHAR},
      </if>
      <if test="record.hasfoodcourt != null">
        HasFoodCourt = #{record.hasfoodcourt,jdbcType=NVARCHAR},
      </if>
      <if test="record.datasourceid != null">
        DataSourceId = #{record.datasourceid,jdbcType=CHAR},
      </if>
      <if test="record.wasterwatercarbonfactor != null">
        WasterWaterCarbonFactor = #{record.wasterwatercarbonfactor,jdbcType=NUMERIC},
      </if>
      <if test="record.wasterwatercarbonfactorunit != null">
        WasterWaterCarbonFactorUnit = #{record.wasterwatercarbonfactorunit,jdbcType=NVARCHAR},
      </if>
      <if test="record.waterelectricitybillsource != null">
        WaterElectricityBillSource = #{record.waterelectricitybillsource,jdbcType=NVARCHAR},
      </if>
      <if test="record.accountingcenterno != null">
        AccountingCenterNo = #{record.accountingcenterno,jdbcType=NVARCHAR},
      </if>
      <if test="record.createdby != null">
        CreatedBy = #{record.createdby,jdbcType=VARCHAR},
      </if>
      <if test="record.createdtime != null">
        CreatedTime = #{record.createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deletedby != null">
        DeletedBy = #{record.deletedby,jdbcType=VARCHAR},
      </if>
      <if test="record.deletedtime != null">
        DeletedTime = #{record.deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isdeleted != null">
        IsDeleted = #{record.isdeleted,jdbcType=BIT},
      </if>
      <if test="record.siteid != null">
        SiteId = #{record.siteid,jdbcType=NVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Feb 06 09:05:56 CST 2025.
    -->
    update Tzh_ProjectDetail
    set Id = #{record.id,jdbcType=CHAR},
      SiteName = #{record.sitename,jdbcType=NVARCHAR},
      ProtocolId = #{record.protocolid,jdbcType=CHAR},
      CarbonEmissionLocationId = #{record.carbonemissionlocationid,jdbcType=CHAR},
      EmissionReductionTarget = #{record.emissionreductiontarget,jdbcType=NUMERIC},
      EpdWasteAccount = #{record.epdwasteaccount,jdbcType=NVARCHAR},
      EpdWasteAccountPwd = #{record.epdwasteaccountpwd,jdbcType=NVARCHAR},
      HasFoodCourt = #{record.hasfoodcourt,jdbcType=NVARCHAR},
      DataSourceId = #{record.datasourceid,jdbcType=CHAR},
      WasterWaterCarbonFactor = #{record.wasterwatercarbonfactor,jdbcType=NUMERIC},
      WasterWaterCarbonFactorUnit = #{record.wasterwatercarbonfactorunit,jdbcType=NVARCHAR},
      WaterElectricityBillSource = #{record.waterelectricitybillsource,jdbcType=NVARCHAR},
      AccountingCenterNo = #{record.accountingcenterno,jdbcType=NVARCHAR},
      CreatedBy = #{record.createdby,jdbcType=VARCHAR},
      CreatedTime = #{record.createdtime,jdbcType=TIMESTAMP},
      DeletedBy = #{record.deletedby,jdbcType=VARCHAR},
      DeletedTime = #{record.deletedtime,jdbcType=TIMESTAMP},
      IsDeleted = #{record.isdeleted,jdbcType=BIT},
      SiteId = #{record.siteid,jdbcType=NVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>