package com.csci.tzh.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csci.cohl.beans.vo.TzhEmissionReductionDescriptionVO;
import com.csci.tzh.model.TzhEmissionReductionDescription;
import com.csci.tzh.model.TzhEmissionReductionDescriptionExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface TzhEmissionReductionDescriptionMapper extends BaseMapper<com.csci.cohl.model.TzhEmissionReductionDescription> {
	@Select("""
            <script>
            SELECT * FROM Tzh_EmissionReductionDescription ERD
            LEFT JOIN Tzh_Protocol PT ON ERD.ProtocolId = PT.Id
            left join t_organization o  on o.name = ERD.SiteName and o.is_deleted = 0
            WHERE ERD.IsDeleted = 0 AND ERD.SiteName = #{siteName} AND PT.NameEN = #{protocol}
            <if test="siteId != null and siteId != ''">
                and o.id = #{siteId}
			</if>
            ORDER BY Seq
            </script>
            """)
	List<TzhEmissionReductionDescriptionVO> listVO(@Param("siteName") String siteName, @Param("protocol") String protocol, @Param("siteId") String siteId);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionDescription
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	long countByExample(TzhEmissionReductionDescriptionExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionDescription
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	int deleteByExample(TzhEmissionReductionDescriptionExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionDescription
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	int insert(TzhEmissionReductionDescription row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionDescription
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	int insertSelective(TzhEmissionReductionDescription row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionDescription
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	List<TzhEmissionReductionDescription> selectByExample(TzhEmissionReductionDescriptionExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionDescription
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") TzhEmissionReductionDescription row,
			@Param("example") TzhEmissionReductionDescriptionExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EmissionReductionDescription
	 * @mbg.generated  Fri Apr 21 14:26:05 HKT 2023
	 */
	int updateByExample(@Param("row") TzhEmissionReductionDescription row,
			@Param("example") TzhEmissionReductionDescriptionExample example);
}
