package com.csci.tzh.mapper;

import com.csci.tzh.model.TzhExternalRequestPayload;
import com.csci.tzh.model.TzhExternalRequestPayloadExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TzhExternalRequestPayloadMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_External_RequestPayload
     *
     * @mbg.generated Fri Feb 17 16:44:10 HKT 2023
     */
    long countByExample(TzhExternalRequestPayloadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_External_RequestPayload
     *
     * @mbg.generated Fri Feb 17 16:44:10 HKT 2023
     */
    int deleteByExample(TzhExternalRequestPayloadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_External_RequestPayload
     *
     * @mbg.generated Fri Feb 17 16:44:10 HKT 2023
     */
    int insert(TzhExternalRequestPayload row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_External_RequestPayload
     *
     * @mbg.generated Fri Feb 17 16:44:10 HKT 2023
     */
    int insertSelective(TzhExternalRequestPayload row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_External_RequestPayload
     *
     * @mbg.generated Fri Feb 17 16:44:10 HKT 2023
     */
    List<TzhExternalRequestPayload> selectByExample(TzhExternalRequestPayloadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_External_RequestPayload
     *
     * @mbg.generated Fri Feb 17 16:44:10 HKT 2023
     */
    int updateByExampleSelective(@Param("row") TzhExternalRequestPayload row, @Param("example") TzhExternalRequestPayloadExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_External_RequestPayload
     *
     * @mbg.generated Fri Feb 17 16:44:10 HKT 2023
     */
    int updateByExample(@Param("row") TzhExternalRequestPayload row, @Param("example") TzhExternalRequestPayloadExample example);
}