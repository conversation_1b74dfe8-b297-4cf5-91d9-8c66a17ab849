<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.TzhRpaAccountMapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.TzhRpaAccount">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 18 09:36:09 HKT 2023.
    -->
    <result column="Id" jdbcType="CHAR" property="id" />
    <result column="ProjectName" jdbcType="NVARCHAR" property="projectname" />
    <result column="ProtocolId" jdbcType="CHAR" property="protocolid" />
    <result column="AccountType" jdbcType="NVARCHAR" property="accounttype" />
    <result column="AccountNo" jdbcType="NVARCHAR" property="accountno" />
    <result column="Username" jdbcType="NVARCHAR" property="username" />
    <result column="Password" jdbcType="NVARCHAR" property="password" />
    <result column="Supplier" jdbcType="NVARCHAR" property="supplier" />
    <result column="CarbonEmissionLocationId" jdbcType="CHAR" property="carbonemissionlocationid" />
    <result column="StartDate" jdbcType="DATE" property="startdate" />
    <result column="EndDate" jdbcType="DATE" property="enddate" />
    <result column="CreatedBy" jdbcType="VARCHAR" property="createdby" />
    <result column="CreatedTime" jdbcType="TIMESTAMP" property="createdtime" />
    <result column="DeletedBy" jdbcType="VARCHAR" property="deletedby" />
    <result column="DeletedTime" jdbcType="TIMESTAMP" property="deletedtime" />
    <result column="IsDeleted" jdbcType="BIT" property="isdeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 18 09:36:09 HKT 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 18 09:36:09 HKT 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 18 09:36:09 HKT 2023.
    -->
    Id, ProjectName, ProtocolId, AccountType, AccountNo, Username, Password, Supplier, 
    CarbonEmissionLocationId, StartDate, EndDate, CreatedBy, CreatedTime, DeletedBy, 
    DeletedTime, IsDeleted
  </sql>
  <select id="selectByExample" parameterType="com.csci.tzh.model.TzhRpaAccountExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 18 09:36:09 HKT 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from Tzh_RpaAccount
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.TzhRpaAccountExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 18 09:36:09 HKT 2023.
    -->
    delete from Tzh_RpaAccount
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.TzhRpaAccount">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 18 09:36:09 HKT 2023.
    -->
    insert into Tzh_RpaAccount (Id, ProjectName, ProtocolId, 
      AccountType, AccountNo, Username, 
      Password, Supplier, CarbonEmissionLocationId, 
      StartDate, EndDate, CreatedBy, 
      CreatedTime, DeletedBy, DeletedTime, 
      IsDeleted)
    values (#{id,jdbcType=CHAR}, #{projectname,jdbcType=NVARCHAR}, #{protocolid,jdbcType=CHAR}, 
      #{accounttype,jdbcType=NVARCHAR}, #{accountno,jdbcType=NVARCHAR}, #{username,jdbcType=NVARCHAR}, 
      #{password,jdbcType=NVARCHAR}, #{supplier,jdbcType=NVARCHAR}, #{carbonemissionlocationid,jdbcType=CHAR}, 
      #{startdate,jdbcType=DATE}, #{enddate,jdbcType=DATE}, #{createdby,jdbcType=VARCHAR}, 
      #{createdtime,jdbcType=TIMESTAMP}, #{deletedby,jdbcType=VARCHAR}, #{deletedtime,jdbcType=TIMESTAMP}, 
      #{isdeleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.TzhRpaAccount">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 18 09:36:09 HKT 2023.
    -->
    insert into Tzh_RpaAccount
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="projectname != null">
        ProjectName,
      </if>
      <if test="protocolid != null">
        ProtocolId,
      </if>
      <if test="accounttype != null">
        AccountType,
      </if>
      <if test="accountno != null">
        AccountNo,
      </if>
      <if test="username != null">
        Username,
      </if>
      <if test="password != null">
        Password,
      </if>
      <if test="supplier != null">
        Supplier,
      </if>
      <if test="carbonemissionlocationid != null">
        CarbonEmissionLocationId,
      </if>
      <if test="startdate != null">
        StartDate,
      </if>
      <if test="enddate != null">
        EndDate,
      </if>
      <if test="createdby != null">
        CreatedBy,
      </if>
      <if test="createdtime != null">
        CreatedTime,
      </if>
      <if test="deletedby != null">
        DeletedBy,
      </if>
      <if test="deletedtime != null">
        DeletedTime,
      </if>
      <if test="isdeleted != null">
        IsDeleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="projectname != null">
        #{projectname,jdbcType=NVARCHAR},
      </if>
      <if test="protocolid != null">
        #{protocolid,jdbcType=CHAR},
      </if>
      <if test="accounttype != null">
        #{accounttype,jdbcType=NVARCHAR},
      </if>
      <if test="accountno != null">
        #{accountno,jdbcType=NVARCHAR},
      </if>
      <if test="username != null">
        #{username,jdbcType=NVARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=NVARCHAR},
      </if>
      <if test="supplier != null">
        #{supplier,jdbcType=NVARCHAR},
      </if>
      <if test="carbonemissionlocationid != null">
        #{carbonemissionlocationid,jdbcType=CHAR},
      </if>
      <if test="startdate != null">
        #{startdate,jdbcType=DATE},
      </if>
      <if test="enddate != null">
        #{enddate,jdbcType=DATE},
      </if>
      <if test="createdby != null">
        #{createdby,jdbcType=VARCHAR},
      </if>
      <if test="createdtime != null">
        #{createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedby != null">
        #{deletedby,jdbcType=VARCHAR},
      </if>
      <if test="deletedtime != null">
        #{deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isdeleted != null">
        #{isdeleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.TzhRpaAccountExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 18 09:36:09 HKT 2023.
    -->
    select count(*) from Tzh_RpaAccount
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 18 09:36:09 HKT 2023.
    -->
    update Tzh_RpaAccount
    <set>
      <if test="row.id != null">
        Id = #{row.id,jdbcType=CHAR},
      </if>
      <if test="row.projectname != null">
        ProjectName = #{row.projectname,jdbcType=NVARCHAR},
      </if>
      <if test="row.protocolid != null">
        ProtocolId = #{row.protocolid,jdbcType=CHAR},
      </if>
      <if test="row.accounttype != null">
        AccountType = #{row.accounttype,jdbcType=NVARCHAR},
      </if>
      <if test="row.accountno != null">
        AccountNo = #{row.accountno,jdbcType=NVARCHAR},
      </if>
      <if test="row.username != null">
        Username = #{row.username,jdbcType=NVARCHAR},
      </if>
      <if test="row.password != null">
        Password = #{row.password,jdbcType=NVARCHAR},
      </if>
      <if test="row.supplier != null">
        Supplier = #{row.supplier,jdbcType=NVARCHAR},
      </if>
      <if test="row.carbonemissionlocationid != null">
        CarbonEmissionLocationId = #{row.carbonemissionlocationid,jdbcType=CHAR},
      </if>
      <if test="row.startdate != null">
        StartDate = #{row.startdate,jdbcType=DATE},
      </if>
      <if test="row.enddate != null">
        EndDate = #{row.enddate,jdbcType=DATE},
      </if>
      <if test="row.createdby != null">
        CreatedBy = #{row.createdby,jdbcType=VARCHAR},
      </if>
      <if test="row.createdtime != null">
        CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.deletedby != null">
        DeletedBy = #{row.deletedby,jdbcType=VARCHAR},
      </if>
      <if test="row.deletedtime != null">
        DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.isdeleted != null">
        IsDeleted = #{row.isdeleted,jdbcType=BIT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 18 09:36:09 HKT 2023.
    -->
    update Tzh_RpaAccount
    set Id = #{row.id,jdbcType=CHAR},
      ProjectName = #{row.projectname,jdbcType=NVARCHAR},
      ProtocolId = #{row.protocolid,jdbcType=CHAR},
      AccountType = #{row.accounttype,jdbcType=NVARCHAR},
      AccountNo = #{row.accountno,jdbcType=NVARCHAR},
      Username = #{row.username,jdbcType=NVARCHAR},
      Password = #{row.password,jdbcType=NVARCHAR},
      Supplier = #{row.supplier,jdbcType=NVARCHAR},
      CarbonEmissionLocationId = #{row.carbonemissionlocationid,jdbcType=CHAR},
      StartDate = #{row.startdate,jdbcType=DATE},
      EndDate = #{row.enddate,jdbcType=DATE},
      CreatedBy = #{row.createdby,jdbcType=VARCHAR},
      CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      DeletedBy = #{row.deletedby,jdbcType=VARCHAR},
      DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      IsDeleted = #{row.isdeleted,jdbcType=BIT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>