<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.DCdmsElectricityCarbonFactorMapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.DCdmsElectricityCarbonFactor">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 07 12:03:33 HKT 2023.
    -->
    <result column="Id" jdbcType="CHAR" property="id" />
    <result column="SiteName" jdbcType="NVARCHAR" property="sitename" />
    <result column="ProtocolId" jdbcType="CHAR" property="protocolid" />
    <result column="CarbonEmissionLocationId" jdbcType="CHAR" property="carbonemissionlocationid" />
    <result column="CarbonFactorRecordYearMonth" jdbcType="INTEGER" property="carbonfactorrecordyearmonth" />
    <result column="AccountNo" jdbcType="NVARCHAR" property="accountno" />
    <result column="ElectricConsumption" jdbcType="NUMERIC" property="electricconsumption" />
    <result column="ElectricUnit" jdbcType="NVARCHAR" property="electricunit" />
    <result column="CarbonFactor" jdbcType="NUMERIC" property="carbonfactor" />
    <result column="CarbonFactorUnit" jdbcType="NVARCHAR" property="carbonfactorunit" />
    <result column="Source" jdbcType="NVARCHAR" property="source" />
    <result column="SourceRefId" jdbcType="NVARCHAR" property="sourcerefid" />
    <result column="CreatedBy" jdbcType="NVARCHAR" property="createdby" />
    <result column="CreatedTime" jdbcType="TIMESTAMP" property="createdtime" />
    <result column="DeletedBy" jdbcType="NVARCHAR" property="deletedby" />
    <result column="DeletedTime" jdbcType="TIMESTAMP" property="deletedtime" />
    <result column="IsDeleted" jdbcType="BIT" property="isdeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 07 12:03:33 HKT 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 07 12:03:33 HKT 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 07 12:03:33 HKT 2023.
    -->
    Id, SiteName, ProtocolId, CarbonEmissionLocationId, CarbonFactorRecordYearMonth, 
    AccountNo, ElectricConsumption, ElectricUnit, CarbonFactor, CarbonFactorUnit, Source, 
    SourceRefId, CreatedBy, CreatedTime, DeletedBy, DeletedTime, IsDeleted
  </sql>
  <select id="selectByExample" parameterType="com.csci.tzh.model.DCdmsElectricityCarbonFactorExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 07 12:03:33 HKT 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from D_CDMS_ElectricityCarbonFactor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.DCdmsElectricityCarbonFactorExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 07 12:03:33 HKT 2023.
    -->
    delete from D_CDMS_ElectricityCarbonFactor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.DCdmsElectricityCarbonFactor">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 07 12:03:33 HKT 2023.
    -->
    insert into D_CDMS_ElectricityCarbonFactor (Id, SiteName, ProtocolId, 
      CarbonEmissionLocationId, CarbonFactorRecordYearMonth, 
      AccountNo, ElectricConsumption, ElectricUnit, 
      CarbonFactor, CarbonFactorUnit, Source, 
      SourceRefId, CreatedBy, CreatedTime, 
      DeletedBy, DeletedTime, IsDeleted
      )
    values (#{id,jdbcType=CHAR}, #{sitename,jdbcType=NVARCHAR}, #{protocolid,jdbcType=CHAR}, 
      #{carbonemissionlocationid,jdbcType=CHAR}, #{carbonfactorrecordyearmonth,jdbcType=INTEGER}, 
      #{accountno,jdbcType=NVARCHAR}, #{electricconsumption,jdbcType=NUMERIC}, #{electricunit,jdbcType=NVARCHAR}, 
      #{carbonfactor,jdbcType=NUMERIC}, #{carbonfactorunit,jdbcType=NVARCHAR}, #{source,jdbcType=NVARCHAR}, 
      #{sourcerefid,jdbcType=NVARCHAR}, #{createdby,jdbcType=NVARCHAR}, #{createdtime,jdbcType=TIMESTAMP}, 
      #{deletedby,jdbcType=NVARCHAR}, #{deletedtime,jdbcType=TIMESTAMP}, #{isdeleted,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.DCdmsElectricityCarbonFactor">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 07 12:03:33 HKT 2023.
    -->
    insert into D_CDMS_ElectricityCarbonFactor
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="sitename != null">
        SiteName,
      </if>
      <if test="protocolid != null">
        ProtocolId,
      </if>
      <if test="carbonemissionlocationid != null">
        CarbonEmissionLocationId,
      </if>
      <if test="carbonfactorrecordyearmonth != null">
        CarbonFactorRecordYearMonth,
      </if>
      <if test="accountno != null">
        AccountNo,
      </if>
      <if test="electricconsumption != null">
        ElectricConsumption,
      </if>
      <if test="electricunit != null">
        ElectricUnit,
      </if>
      <if test="carbonfactor != null">
        CarbonFactor,
      </if>
      <if test="carbonfactorunit != null">
        CarbonFactorUnit,
      </if>
      <if test="source != null">
        Source,
      </if>
      <if test="sourcerefid != null">
        SourceRefId,
      </if>
      <if test="createdby != null">
        CreatedBy,
      </if>
      <if test="createdtime != null">
        CreatedTime,
      </if>
      <if test="deletedby != null">
        DeletedBy,
      </if>
      <if test="deletedtime != null">
        DeletedTime,
      </if>
      <if test="isdeleted != null">
        IsDeleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="sitename != null">
        #{sitename,jdbcType=NVARCHAR},
      </if>
      <if test="protocolid != null">
        #{protocolid,jdbcType=CHAR},
      </if>
      <if test="carbonemissionlocationid != null">
        #{carbonemissionlocationid,jdbcType=CHAR},
      </if>
      <if test="carbonfactorrecordyearmonth != null">
        #{carbonfactorrecordyearmonth,jdbcType=INTEGER},
      </if>
      <if test="accountno != null">
        #{accountno,jdbcType=NVARCHAR},
      </if>
      <if test="electricconsumption != null">
        #{electricconsumption,jdbcType=NUMERIC},
      </if>
      <if test="electricunit != null">
        #{electricunit,jdbcType=NVARCHAR},
      </if>
      <if test="carbonfactor != null">
        #{carbonfactor,jdbcType=NUMERIC},
      </if>
      <if test="carbonfactorunit != null">
        #{carbonfactorunit,jdbcType=NVARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=NVARCHAR},
      </if>
      <if test="sourcerefid != null">
        #{sourcerefid,jdbcType=NVARCHAR},
      </if>
      <if test="createdby != null">
        #{createdby,jdbcType=NVARCHAR},
      </if>
      <if test="createdtime != null">
        #{createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedby != null">
        #{deletedby,jdbcType=NVARCHAR},
      </if>
      <if test="deletedtime != null">
        #{deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isdeleted != null">
        #{isdeleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.DCdmsElectricityCarbonFactorExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 07 12:03:33 HKT 2023.
    -->
    select count(*) from D_CDMS_ElectricityCarbonFactor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 07 12:03:33 HKT 2023.
    -->
    update D_CDMS_ElectricityCarbonFactor
    <set>
      <if test="row.id != null">
        Id = #{row.id,jdbcType=CHAR},
      </if>
      <if test="row.sitename != null">
        SiteName = #{row.sitename,jdbcType=NVARCHAR},
      </if>
      <if test="row.protocolid != null">
        ProtocolId = #{row.protocolid,jdbcType=CHAR},
      </if>
      <if test="row.carbonemissionlocationid != null">
        CarbonEmissionLocationId = #{row.carbonemissionlocationid,jdbcType=CHAR},
      </if>
      <if test="row.carbonfactorrecordyearmonth != null">
        CarbonFactorRecordYearMonth = #{row.carbonfactorrecordyearmonth,jdbcType=INTEGER},
      </if>
      <if test="row.accountno != null">
        AccountNo = #{row.accountno,jdbcType=NVARCHAR},
      </if>
      <if test="row.electricconsumption != null">
        ElectricConsumption = #{row.electricconsumption,jdbcType=NUMERIC},
      </if>
      <if test="row.electricunit != null">
        ElectricUnit = #{row.electricunit,jdbcType=NVARCHAR},
      </if>
      <if test="row.carbonfactor != null">
        CarbonFactor = #{row.carbonfactor,jdbcType=NUMERIC},
      </if>
      <if test="row.carbonfactorunit != null">
        CarbonFactorUnit = #{row.carbonfactorunit,jdbcType=NVARCHAR},
      </if>
      <if test="row.source != null">
        Source = #{row.source,jdbcType=NVARCHAR},
      </if>
      <if test="row.sourcerefid != null">
        SourceRefId = #{row.sourcerefid,jdbcType=NVARCHAR},
      </if>
      <if test="row.createdby != null">
        CreatedBy = #{row.createdby,jdbcType=NVARCHAR},
      </if>
      <if test="row.createdtime != null">
        CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.deletedby != null">
        DeletedBy = #{row.deletedby,jdbcType=NVARCHAR},
      </if>
      <if test="row.deletedtime != null">
        DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.isdeleted != null">
        IsDeleted = #{row.isdeleted,jdbcType=BIT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 07 12:03:33 HKT 2023.
    -->
    update D_CDMS_ElectricityCarbonFactor
    set Id = #{row.id,jdbcType=CHAR},
      SiteName = #{row.sitename,jdbcType=NVARCHAR},
      ProtocolId = #{row.protocolid,jdbcType=CHAR},
      CarbonEmissionLocationId = #{row.carbonemissionlocationid,jdbcType=CHAR},
      CarbonFactorRecordYearMonth = #{row.carbonfactorrecordyearmonth,jdbcType=INTEGER},
      AccountNo = #{row.accountno,jdbcType=NVARCHAR},
      ElectricConsumption = #{row.electricconsumption,jdbcType=NUMERIC},
      ElectricUnit = #{row.electricunit,jdbcType=NVARCHAR},
      CarbonFactor = #{row.carbonfactor,jdbcType=NUMERIC},
      CarbonFactorUnit = #{row.carbonfactorunit,jdbcType=NVARCHAR},
      Source = #{row.source,jdbcType=NVARCHAR},
      SourceRefId = #{row.sourcerefid,jdbcType=NVARCHAR},
      CreatedBy = #{row.createdby,jdbcType=NVARCHAR},
      CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      DeletedBy = #{row.deletedby,jdbcType=NVARCHAR},
      DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      IsDeleted = #{row.isdeleted,jdbcType=BIT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>