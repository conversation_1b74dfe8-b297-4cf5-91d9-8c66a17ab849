package com.csci.tzh.mapper;

import com.csci.tzh.vo.TzhEstimateConstructionWasteVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface TzhEstimateConstructionWasteCustomMapper {
	
    @Select("""
			SELECT ECW.*, protocol = P.Name, protocolsc = P.NameSC, protocolen = P.NameEN, 
			C.CategoryName, C.CategoryNameSC, C.CategoryNameEN, SC.SubCategoryName, SC.SubCategoryNameSC, SC.SubCategoryNameEN,
			disposalmethod = DM.Name, disposalmethodsc = DM.NameSC, disposalmethoden = DM.NameEN,
			estimatewasteqty = ISNULL(ECW.Qty,0) * ISNULL(ECW.TargetAttritionRate, 0), attritionrate =  ECW.WasteQty/ ISNULL(ECW.Qty, 1)
			FROM Tzh_EstimateConstructionWaste ECW
			LEFT JOIN Tzh_Protocol P ON ECW.ProtocolId = P.Id
			LEFT JOIN Tzh_EstimateConstructionWaste_SubCategory SC ON ECW.SubCategoryId = SC.Id
			LEFT JOIN Tzh_EstimateConstructionWaste_Category C ON SC.CategoryId = C.Id AND C.ProtocolId = ECW.ProtocolId
			LEFT JOIN Tzh_EstimateConstructionWaste_DisposalMethod DM ON DM.Id = ECW.DisposalMethodId
			WHERE ECW.SiteName = #{siteName}
			AND P.NameEN = #{protocol}
			AND ECW.IsDeleted = 0
 			""")
    public List<TzhEstimateConstructionWasteVO> list(@Param("siteName") String siteName,
												 @Param("protocol") String protocol);

}