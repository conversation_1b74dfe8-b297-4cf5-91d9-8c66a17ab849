package com.csci.tzh.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csci.cohl.beans.vo.TzhEstimateConstructionWasteVO;
import com.csci.tzh.model.TzhEstimateConstructionWaste;
import com.csci.tzh.model.TzhEstimateConstructionWasteExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface TzhEstimateConstructionWasteMapper extends BaseMapper<com.csci.cohl.model.TzhEstimateConstructionWaste> {
	@Select("""
   			<script>
			SELECT C.CategoryName, C.CategoryNameSC, C.CategoryNameEN, WasteQty = ISNULL(T.WasteQty, 0)
			FROM Tzh_EstimateConstructionWaste_Category C 
			LEFT JOIN (
				SELECT C.CategoryName, C.CategoryNameSC, C.CategoryNameEN, WasteQty = SUM(ECW.WasteQty)
				FROM Tzh_EstimateConstructionWaste ECW
				LEFT JOIN Tzh_Protocol P ON ECW.ProtocolId = P.Id
				left join t_organization o  on o.name = ECW.SiteName and o.is_deleted = 0
				LEFT JOIN Tzh_EstimateConstructionWaste_SubCategory SC ON ECW.SubCategoryId = SC.Id
				LEFT JOIN Tzh_EstimateConstructionWaste_Category C ON SC.CategoryId = C.Id AND C.ProtocolId = ECW.ProtocolId
				WHERE ECW.DisposalMethodId != (SELECT Id FROM Tzh_EstimateConstructionWaste_DisposalMethod WHERE Name = N'回收')
				AND ECW.SiteName = #{siteName}
				AND P.NameEN = #{protocol}
				AND ECW.IsDeleted = 0
				<if test="siteId != null and siteId != ''">
                and o.id = #{siteId}
				</if>
				GROUP BY C.CategoryName, C.CategoryNameSC, C.CategoryNameEN, ECW.WasteQtyUnit
			) T ON C.CategoryName = T.CategoryName
			ORDER BY C.CategoryName
			</script>
            """)
	List<TzhEstimateConstructionWasteVO> list(@Param("siteName") String siteName, @Param("protocol") String protocol, @Param("siteId") String siteId);


	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EstimateConstructionWaste
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	long countByExample(TzhEstimateConstructionWasteExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EstimateConstructionWaste
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	int deleteByExample(TzhEstimateConstructionWasteExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EstimateConstructionWaste
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	int insert(TzhEstimateConstructionWaste row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EstimateConstructionWaste
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	int insertSelective(TzhEstimateConstructionWaste row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EstimateConstructionWaste
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	List<TzhEstimateConstructionWaste> selectByExample(TzhEstimateConstructionWasteExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EstimateConstructionWaste
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") TzhEstimateConstructionWaste row,
			@Param("example") TzhEstimateConstructionWasteExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EstimateConstructionWaste
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	int updateByExample(@Param("row") TzhEstimateConstructionWaste row,
			@Param("example") TzhEstimateConstructionWasteExample example);
}
