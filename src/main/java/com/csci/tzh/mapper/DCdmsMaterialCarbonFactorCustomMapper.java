package com.csci.tzh.mapper;

import com.csci.tzh.vo.DCdmsMaterialCarbonFactorVO;
import com.csci.tzh.vo.TzhExpectedEmissionIsoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface DCdmsMaterialCarbonFactorCustomMapper {
	
    @Select("""
			SELECT MCF.*, protocolid = P.id, protocol = P.Name, protocolen = P.NameEN, protocolsc = P.NameSC,
			CarbonEmissionLocation = CEL.Name, CarbonEmissionLocationSC = CEL.NameSC,
			CarbonEmissionLocationEN = CEL.NameEN, PSC.SubCategoryName, PSC.SubCategoryNameSC, PSC.SubCategoryNameEN
			FROM D_CDMS_MaterialCarbonFactor MCF
			LEFT JOIN Tzh_CarbonEmissionLocation CEL ON MCF.CarbonEmissionLocationId = CEL.Id
			LEFT JOIN Tzh_Protocol_SubCategory PSC ON MCF.ProtocolSubCategoryId = PSC.Id
			LEFT JOIN Tzh_Protocol_Category PC ON PSC.CategoryId = PC.Id
			LEFT JOIN Tzh_Protocol P ON PC.ProtocolId = P.Id
			WHERE MCF.SiteName = #{siteName} AND P.NameEN = #{protocol} AND MCF.IsDeleted = 0
			""")
    public List<DCdmsMaterialCarbonFactorVO> list(@Param("siteName") String siteName, @Param("protocol") String protocol);

}