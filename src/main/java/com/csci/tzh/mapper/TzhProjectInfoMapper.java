package com.csci.tzh.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csci.cohl.beans.vo.TzhProjectInfoVO;
import com.csci.tzh.model.TzhProjectInfo;
import com.csci.tzh.model.TzhProjectInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface TzhProjectInfoMapper extends BaseMapper<com.csci.cohl.model.TzhProjectInfo> {
	@Select("""
            SELECT PI.Id, PI.Code, PI.Name, PI.Type, PI.IsConsumptionMeasurable, PI.IsPeriodicallyReportable,
            PI.InvestmentTotal, PI.ContractAmount, PI.Area, PI.StartDate, PI.EndDate, PI.Address, PI.Contractor,
            PI.Owner, PI.Architect, PI.Supervisor, PI.ManagerZhtAccount, PI.Longitude, PI.Latitude, PI.RegionId,
            PI.Province, PI.City, PI.District, PI.MajorProjectNum, PI.SiteId,
            R.Name AS Region, R.NameSC AS RegionSC, R.NameEN AS RegionEN
            FROM Tzh_ProjectInfo PI
            LEFT JOIN Tzh_Region R ON PI.RegionId = R.Id
            WHERE PI.Name = #{siteName} AND PI.IsDeleted = 0
            """)
	List<TzhProjectInfoVO> getProjectInfo(@Param("siteName") String siteName);

	@Select("""
            SELECT PI.Id, PI.Code, PI.Name, PI.Type, PI.IsConsumptionMeasurable, PI.IsPeriodicallyReportable,
            PI.InvestmentTotal, PI.ContractAmount, PI.Area, PI.StartDate, PI.EndDate, PI.Address, PI.Contractor,
            PI.Owner, PI.Architect, PI.Supervisor, PI.ManagerZhtAccount, PI.Longitude, PI.Latitude, PI.RegionId,
            PI.Province, PI.City, PI.District, PI.MajorProjectNum, PI.SiteId,
            R.Name AS Region, R.NameSC AS RegionSC, R.NameEN AS RegionEN
            FROM Tzh_ProjectInfo PI
            LEFT JOIN Tzh_Region R ON PI.RegionId = R.Id
            WHERE PI.SiteId = #{siteId} AND PI.IsDeleted = 0
            """)
	List<TzhProjectInfoVO> getProjectInfoBySiteId(@Param("siteId") String siteId);
	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ProjectInfo
	 * @mbg.generated  Mon Apr 17 10:44:00 HKT 2023
	 */
	long countByExample(TzhProjectInfoExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ProjectInfo
	 * @mbg.generated  Mon Apr 17 10:44:00 HKT 2023
	 */
	int deleteByExample(TzhProjectInfoExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ProjectInfo
	 * @mbg.generated  Mon Apr 17 10:44:00 HKT 2023
	 */
	int insert(TzhProjectInfo row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ProjectInfo
	 * @mbg.generated  Mon Apr 17 10:44:00 HKT 2023
	 */
	int insertSelective(TzhProjectInfo row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ProjectInfo
	 * @mbg.generated  Mon Apr 17 10:44:00 HKT 2023
	 */
	List<TzhProjectInfo> selectByExample(TzhProjectInfoExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ProjectInfo
	 * @mbg.generated  Mon Apr 17 10:44:00 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") TzhProjectInfo row, @Param("example") TzhProjectInfoExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ProjectInfo
	 * @mbg.generated  Mon Apr 17 10:44:00 HKT 2023
	 */
	int updateByExample(@Param("row") TzhProjectInfo row, @Param("example") TzhProjectInfoExample example);
}
