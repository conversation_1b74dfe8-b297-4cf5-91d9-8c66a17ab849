package com.csci.tzh.mapper;

import com.csci.tzh.model.TzhOrgMaterialCarbonFactorW50;
import com.csci.tzh.model.TzhOrgMaterialCarbonFactorW50Example;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TzhOrgMaterialCarbonFactorW50Mapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_W50
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    long countByExample(TzhOrgMaterialCarbonFactorW50Example example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_W50
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    int deleteByExample(TzhOrgMaterialCarbonFactorW50Example example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_W50
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    int insert(TzhOrgMaterialCarbonFactorW50 row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_W50
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    int insertSelective(TzhOrgMaterialCarbonFactorW50 row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_W50
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    List<TzhOrgMaterialCarbonFactorW50> selectByExample(TzhOrgMaterialCarbonFactorW50Example example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_W50
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    int updateByExampleSelective(@Param("row") TzhOrgMaterialCarbonFactorW50 row, @Param("example") TzhOrgMaterialCarbonFactorW50Example example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_Org_MaterialCarbonFactor_W50
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    int updateByExample(@Param("row") TzhOrgMaterialCarbonFactorW50 row, @Param("example") TzhOrgMaterialCarbonFactorW50Example example);
}