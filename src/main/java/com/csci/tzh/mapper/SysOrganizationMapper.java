package com.csci.tzh.mapper;

import com.csci.tzh.model.SysOrganization;
import com.csci.tzh.model.SysOrganizationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SysOrganizationMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	long countByExample(SysOrganizationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	int deleteByExample(SysOrganizationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	int insert(SysOrganization row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	int insertSelective(SysOrganization row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	List<SysOrganization> selectByExampleWithBLOBs(SysOrganizationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	List<SysOrganization> selectByExample(SysOrganizationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	int updateByExampleSelective(@Param("row") SysOrganization row, @Param("example") SysOrganizationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	int updateByExampleWithBLOBs(@Param("row") SysOrganization row, @Param("example") SysOrganizationExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	int updateByExample(@Param("row") SysOrganization row, @Param("example") SysOrganizationExample example);
}