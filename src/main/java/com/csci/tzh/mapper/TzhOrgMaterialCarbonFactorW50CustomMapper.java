package com.csci.tzh.mapper;

import com.csci.tzh.model.TzhOrgMaterialCarbonFactorW50;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface TzhOrgMaterialCarbonFactorW50CustomMapper {
	
    @Select("""
			SELECT *
			FROM Tzh_Org_MaterialCarbonFactor_W50 OMCF
			WHERE (OMCF.ChineseName LIKE '%' + #{chineseName} + '%' OR #{chineseName} = '')
			AND OMCF.IsDeleted = 0
			""")
    public List<TzhOrgMaterialCarbonFactorW50> list(@Param("chineseName") String chineseName);

}