package com.csci.tzh.mapper;

import com.csci.tzh.vo.DCdmsElectricityCarbonFactorVO;
import com.csci.tzh.vo.FCdmsWaterCarbonFactorVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface FCdmsWaterCarbonFactorCustomMapper {
	
    @Select("""
			SELECT WCF.*, region = R.Name, regionsc = R.NameSC, regionen = R.NameEN,
			CarbonEmissionLocation = CEL.Name, CarbonEmissionLocationSC = CEL.NameSC,
			CarbonEmissionLocationEN = CEL.NameEN, Protocol = P.Name, ProtocolSC = P.NameSC, ProtocolEN = P.NameEN
			FROM F_CDMS_WaterCarbonFactor WCF
			LEFT JOIN Tzh_CarbonEmissionLocation CEL ON WCF.CarbonEmissionLocationId = CEL.Id
			LEFT JOIN Tzh_Protocol P ON WCF.ProtocolId = P.Id
			LEFT JOIN Tzh_ProjectInfo PI ON PI.Name = WCF.SiteName AND PI.IsDeleted = 0
			LEFT JOIN Tzh_Region R ON R.Id = PI.RegionId
			WHERE R.Name = #{region} 
			AND WCF.SiteName = #{siteName} 
			AND P.NameEN = #{protocol}
			AND (CEL.Name = #{carbonEmissionLocation} OR #{carbonEmissionLocation} = '')
			AND	(WCF.RecordYearMonth = #{recordYearMonth} OR #{recordYearMonth} IS NULL)
			AND WCF.IsDeleted = 0
			""")
    public List<FCdmsWaterCarbonFactorVO> list(@Param("region") String region,
											   @Param("siteName") String siteName,
											   @Param("protocol") String protocol,
											   @Param("carbonEmissionLocation") String carbonEmissionLocation,
											   @Param("recordYearMonth") Integer recordYearMonth);

}