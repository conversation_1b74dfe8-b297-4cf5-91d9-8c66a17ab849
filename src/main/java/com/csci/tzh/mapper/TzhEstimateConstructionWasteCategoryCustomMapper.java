package com.csci.tzh.mapper;

import com.csci.tzh.vo.TzhEstimateConstructionWasteCategoryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface TzhEstimateConstructionWasteCategoryCustomMapper {
	
    @Select("""
            SELECT *
            FROM Tzh_EstimateConstructionWaste_Category C
            LEFT JOIN Tzh_Protocol P ON C.ProtocolId = P.Id
            WHERE P.NameEN = #{protocol}
            ORDER BY CategoryName
            """)
    public List<TzhEstimateConstructionWasteCategoryVO> list(@Param("protocol") String protocol);

}