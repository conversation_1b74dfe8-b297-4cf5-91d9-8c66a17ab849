<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.TzhProtocolSubCategoryMapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.TzhProtocolSubCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 10:58:33 HKT 2024.
    -->
    <result column="Id" jdbcType="CHAR" property="id" />
    <result column="CategoryId" jdbcType="CHAR" property="categoryid" />
    <result column="SubCategoryName" jdbcType="NVARCHAR" property="subcategoryname" />
    <result column="SubCategoryNameSC" jdbcType="NVARCHAR" property="subcategorynamesc" />
    <result column="SubCategoryNameEN" jdbcType="NVARCHAR" property="subcategorynameen" />
    <result column="Description" jdbcType="NVARCHAR" property="description" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="NVARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="NVARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="NVARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="NVARCHAR" property="lastUpdateUserId" />
    <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 10:58:33 HKT 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 10:58:33 HKT 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 10:58:33 HKT 2024.
    -->
    Id, CategoryId, SubCategoryName, SubCategoryNameSC, SubCategoryNameEN, Description, 
    creation_time, create_username, create_user_id, last_update_time, last_update_username, 
    last_update_user_id, last_update_version, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.csci.tzh.model.TzhProtocolSubCategoryExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 10:58:33 HKT 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from Tzh_Protocol_SubCategory
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.TzhProtocolSubCategoryExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 10:58:33 HKT 2024.
    -->
    delete from Tzh_Protocol_SubCategory
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.TzhProtocolSubCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 10:58:33 HKT 2024.
    -->
    insert into Tzh_Protocol_SubCategory (Id, CategoryId, SubCategoryName, 
      SubCategoryNameSC, SubCategoryNameEN, 
      Description, creation_time, create_username, 
      create_user_id, last_update_time, last_update_username, 
      last_update_user_id, last_update_version, is_deleted
      )
    values (#{id,jdbcType=CHAR}, #{categoryid,jdbcType=CHAR}, #{subcategoryname,jdbcType=NVARCHAR}, 
      #{subcategorynamesc,jdbcType=NVARCHAR}, #{subcategorynameen,jdbcType=NVARCHAR}, 
      #{description,jdbcType=NVARCHAR}, #{creationTime,jdbcType=TIMESTAMP}, #{createUsername,jdbcType=NVARCHAR}, 
      #{createUserId,jdbcType=NVARCHAR}, #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUsername,jdbcType=NVARCHAR}, 
      #{lastUpdateUserId,jdbcType=NVARCHAR}, #{lastUpdateVersion,jdbcType=INTEGER}, #{isDeleted,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.TzhProtocolSubCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 10:58:33 HKT 2024.
    -->
    insert into Tzh_Protocol_SubCategory
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="categoryid != null">
        CategoryId,
      </if>
      <if test="subcategoryname != null">
        SubCategoryName,
      </if>
      <if test="subcategorynamesc != null">
        SubCategoryNameSC,
      </if>
      <if test="subcategorynameen != null">
        SubCategoryNameEN,
      </if>
      <if test="description != null">
        Description,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="categoryid != null">
        #{categoryid,jdbcType=CHAR},
      </if>
      <if test="subcategoryname != null">
        #{subcategoryname,jdbcType=NVARCHAR},
      </if>
      <if test="subcategorynamesc != null">
        #{subcategorynamesc,jdbcType=NVARCHAR},
      </if>
      <if test="subcategorynameen != null">
        #{subcategorynameen,jdbcType=NVARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=NVARCHAR},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.TzhProtocolSubCategoryExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 10:58:33 HKT 2024.
    -->
    select count(*) from Tzh_Protocol_SubCategory
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 10:58:33 HKT 2024.
    -->
    update Tzh_Protocol_SubCategory
    <set>
      <if test="record.id != null">
        Id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.categoryid != null">
        CategoryId = #{record.categoryid,jdbcType=CHAR},
      </if>
      <if test="record.subcategoryname != null">
        SubCategoryName = #{record.subcategoryname,jdbcType=NVARCHAR},
      </if>
      <if test="record.subcategorynamesc != null">
        SubCategoryNameSC = #{record.subcategorynamesc,jdbcType=NVARCHAR},
      </if>
      <if test="record.subcategorynameen != null">
        SubCategoryNameEN = #{record.subcategorynameen,jdbcType=NVARCHAR},
      </if>
      <if test="record.description != null">
        Description = #{record.description,jdbcType=NVARCHAR},
      </if>
      <if test="record.creationTime != null">
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUsername != null">
        create_username = #{record.createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateTime != null">
        last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateUsername != null">
        last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateUserId != null">
        last_update_user_id = #{record.lastUpdateUserId,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateVersion != null">
        last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 10:58:33 HKT 2024.
    -->
    update Tzh_Protocol_SubCategory
    set Id = #{record.id,jdbcType=CHAR},
      CategoryId = #{record.categoryid,jdbcType=CHAR},
      SubCategoryName = #{record.subcategoryname,jdbcType=NVARCHAR},
      SubCategoryNameSC = #{record.subcategorynamesc,jdbcType=NVARCHAR},
      SubCategoryNameEN = #{record.subcategorynameen,jdbcType=NVARCHAR},
      Description = #{record.description,jdbcType=NVARCHAR},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      create_username = #{record.createUsername,jdbcType=NVARCHAR},
      create_user_id = #{record.createUserId,jdbcType=NVARCHAR},
      last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{record.lastUpdateUserId,jdbcType=NVARCHAR},
      last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>