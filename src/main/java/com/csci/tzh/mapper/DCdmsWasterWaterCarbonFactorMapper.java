package com.csci.tzh.mapper;

import com.csci.tzh.model.DCdmsWasterWaterCarbonFactor;
import com.csci.tzh.model.DCdmsWasterWaterCarbonFactorExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DCdmsWasterWaterCarbonFactorMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_CDMS_WasterWaterCarbonFactor
     *
     * @mbg.generated Tue Dec 06 14:43:53 HKT 2022
     */
    long countByExample(DCdmsWasterWaterCarbonFactorExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_CDMS_WasterWaterCarbonFactor
     *
     * @mbg.generated Tue Dec 06 14:43:53 HKT 2022
     */
    int deleteByExample(DCdmsWasterWaterCarbonFactorExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_CDMS_WasterWaterCarbonFactor
     *
     * @mbg.generated Tue Dec 06 14:43:53 HKT 2022
     */
    int insert(DCdmsWasterWaterCarbonFactor row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_CDMS_WasterWaterCarbonFactor
     *
     * @mbg.generated Tue Dec 06 14:43:53 HKT 2022
     */
    int insertSelective(DCdmsWasterWaterCarbonFactor row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_CDMS_WasterWaterCarbonFactor
     *
     * @mbg.generated Tue Dec 06 14:43:53 HKT 2022
     */
    List<DCdmsWasterWaterCarbonFactor> selectByExample(DCdmsWasterWaterCarbonFactorExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_CDMS_WasterWaterCarbonFactor
     *
     * @mbg.generated Tue Dec 06 14:43:53 HKT 2022
     */
    int updateByExampleSelective(@Param("row") DCdmsWasterWaterCarbonFactor row, @Param("example") DCdmsWasterWaterCarbonFactorExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table D_CDMS_WasterWaterCarbonFactor
     *
     * @mbg.generated Tue Dec 06 14:43:53 HKT 2022
     */
    int updateByExample(@Param("row") DCdmsWasterWaterCarbonFactor row, @Param("example") DCdmsWasterWaterCarbonFactorExample example);
}