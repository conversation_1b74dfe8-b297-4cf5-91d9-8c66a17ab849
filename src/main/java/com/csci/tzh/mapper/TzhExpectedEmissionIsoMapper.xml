<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.TzhExpectedEmissionIsoMapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.TzhExpectedEmissionIso">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 17 15:55:17 HKT 2023.
    -->
    <result column="Id" jdbcType="CHAR" property="id" />
    <result column="SiteName" jdbcType="NVARCHAR" property="sitename" />
    <result column="ProtocolSubCategoryId" jdbcType="CHAR" property="protocolsubcategoryid" />
    <result column="CarbonEmissionLocationId" jdbcType="CHAR" property="carbonemissionlocationid" />
    <result column="CarbonAmount" jdbcType="NUMERIC" property="carbonamount" />
    <result column="CarbonAmountUnderMeasure" jdbcType="NUMERIC" property="carbonamountundermeasure" />
    <result column="CarbonUnit" jdbcType="NVARCHAR" property="carbonunit" />
    <result column="CreatedTime" jdbcType="TIMESTAMP" property="createdtime" />
    <result column="CreatedBy" jdbcType="NVARCHAR" property="createdby" />
    <result column="DeletedTime" jdbcType="TIMESTAMP" property="deletedtime" />
    <result column="DeletedBy" jdbcType="NVARCHAR" property="deletedby" />
    <result column="IsDeleted" jdbcType="BIT" property="isdeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 17 15:55:17 HKT 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 17 15:55:17 HKT 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 17 15:55:17 HKT 2023.
    -->
    Id, SiteName, ProtocolSubCategoryId, CarbonEmissionLocationId, CarbonAmount, CarbonAmountUnderMeasure, 
    CarbonUnit, CreatedTime, CreatedBy, DeletedTime, DeletedBy, IsDeleted
  </sql>
  <select id="selectByExample" parameterType="com.csci.tzh.model.TzhExpectedEmissionIsoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 17 15:55:17 HKT 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from Tzh_ExpectedEmissionIso
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.TzhExpectedEmissionIsoExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 17 15:55:17 HKT 2023.
    -->
    delete from Tzh_ExpectedEmissionIso
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.TzhExpectedEmissionIso">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 17 15:55:17 HKT 2023.
    -->
    insert into Tzh_ExpectedEmissionIso (Id, SiteName, ProtocolSubCategoryId, 
      CarbonEmissionLocationId, CarbonAmount, CarbonAmountUnderMeasure, 
      CarbonUnit, CreatedTime, CreatedBy, 
      DeletedTime, DeletedBy, IsDeleted
      )
    values (#{id,jdbcType=CHAR}, #{sitename,jdbcType=NVARCHAR}, #{protocolsubcategoryid,jdbcType=CHAR}, 
      #{carbonemissionlocationid,jdbcType=CHAR}, #{carbonamount,jdbcType=NUMERIC}, #{carbonamountundermeasure,jdbcType=NUMERIC}, 
      #{carbonunit,jdbcType=NVARCHAR}, #{createdtime,jdbcType=TIMESTAMP}, #{createdby,jdbcType=NVARCHAR}, 
      #{deletedtime,jdbcType=TIMESTAMP}, #{deletedby,jdbcType=NVARCHAR}, #{isdeleted,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.TzhExpectedEmissionIso">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 17 15:55:17 HKT 2023.
    -->
    insert into Tzh_ExpectedEmissionIso
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="sitename != null">
        SiteName,
      </if>
      <if test="protocolsubcategoryid != null">
        ProtocolSubCategoryId,
      </if>
      <if test="carbonemissionlocationid != null">
        CarbonEmissionLocationId,
      </if>
      <if test="carbonamount != null">
        CarbonAmount,
      </if>
      <if test="carbonamountundermeasure != null">
        CarbonAmountUnderMeasure,
      </if>
      <if test="carbonunit != null">
        CarbonUnit,
      </if>
      <if test="createdtime != null">
        CreatedTime,
      </if>
      <if test="createdby != null">
        CreatedBy,
      </if>
      <if test="deletedtime != null">
        DeletedTime,
      </if>
      <if test="deletedby != null">
        DeletedBy,
      </if>
      <if test="isdeleted != null">
        IsDeleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="sitename != null">
        #{sitename,jdbcType=NVARCHAR},
      </if>
      <if test="protocolsubcategoryid != null">
        #{protocolsubcategoryid,jdbcType=CHAR},
      </if>
      <if test="carbonemissionlocationid != null">
        #{carbonemissionlocationid,jdbcType=CHAR},
      </if>
      <if test="carbonamount != null">
        #{carbonamount,jdbcType=NUMERIC},
      </if>
      <if test="carbonamountundermeasure != null">
        #{carbonamountundermeasure,jdbcType=NUMERIC},
      </if>
      <if test="carbonunit != null">
        #{carbonunit,jdbcType=NVARCHAR},
      </if>
      <if test="createdtime != null">
        #{createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdby != null">
        #{createdby,jdbcType=NVARCHAR},
      </if>
      <if test="deletedtime != null">
        #{deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedby != null">
        #{deletedby,jdbcType=NVARCHAR},
      </if>
      <if test="isdeleted != null">
        #{isdeleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.TzhExpectedEmissionIsoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 17 15:55:17 HKT 2023.
    -->
    select count(*) from Tzh_ExpectedEmissionIso
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 17 15:55:17 HKT 2023.
    -->
    update Tzh_ExpectedEmissionIso
    <set>
      <if test="row.id != null">
        Id = #{row.id,jdbcType=CHAR},
      </if>
      <if test="row.sitename != null">
        SiteName = #{row.sitename,jdbcType=NVARCHAR},
      </if>
      <if test="row.protocolsubcategoryid != null">
        ProtocolSubCategoryId = #{row.protocolsubcategoryid,jdbcType=CHAR},
      </if>
      <if test="row.carbonemissionlocationid != null">
        CarbonEmissionLocationId = #{row.carbonemissionlocationid,jdbcType=CHAR},
      </if>
      <if test="row.carbonamount != null">
        CarbonAmount = #{row.carbonamount,jdbcType=NUMERIC},
      </if>
      <if test="row.carbonamountundermeasure != null">
        CarbonAmountUnderMeasure = #{row.carbonamountundermeasure,jdbcType=NUMERIC},
      </if>
      <if test="row.carbonunit != null">
        CarbonUnit = #{row.carbonunit,jdbcType=NVARCHAR},
      </if>
      <if test="row.createdtime != null">
        CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createdby != null">
        CreatedBy = #{row.createdby,jdbcType=NVARCHAR},
      </if>
      <if test="row.deletedtime != null">
        DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.deletedby != null">
        DeletedBy = #{row.deletedby,jdbcType=NVARCHAR},
      </if>
      <if test="row.isdeleted != null">
        IsDeleted = #{row.isdeleted,jdbcType=BIT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 17 15:55:17 HKT 2023.
    -->
    update Tzh_ExpectedEmissionIso
    set Id = #{row.id,jdbcType=CHAR},
      SiteName = #{row.sitename,jdbcType=NVARCHAR},
      ProtocolSubCategoryId = #{row.protocolsubcategoryid,jdbcType=CHAR},
      CarbonEmissionLocationId = #{row.carbonemissionlocationid,jdbcType=CHAR},
      CarbonAmount = #{row.carbonamount,jdbcType=NUMERIC},
      CarbonAmountUnderMeasure = #{row.carbonamountundermeasure,jdbcType=NUMERIC},
      CarbonUnit = #{row.carbonunit,jdbcType=NVARCHAR},
      CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      CreatedBy = #{row.createdby,jdbcType=NVARCHAR},
      DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      DeletedBy = #{row.deletedby,jdbcType=NVARCHAR},
      IsDeleted = #{row.isdeleted,jdbcType=BIT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>