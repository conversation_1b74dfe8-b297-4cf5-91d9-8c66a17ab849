package com.csci.tzh.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csci.cohl.beans.vo.TzhProjectDetailVO;
import com.csci.tzh.model.TzhProjectDetail;
import com.csci.tzh.model.TzhProjectDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface TzhProjectDetailMapper extends BaseMapper<com.csci.cohl.model.TzhProjectDetail> {
	@Select("""
            SELECT PD.*, Protocol = P.Name, ProtocolSC = P.NameSC, ProtocolEN = P.NameEN,
            CarbonEmissionLocation = CEL.Name, CarbonEmissionLocationSC = CEL.NameSC,
            CarbonEmissionLocationEN = CEL.NameEN, DataSource = DS.Name, DataSourceSC = DS.NameSC,
            DataSourceEN = DS.NameEN
            FROM Tzh_ProjectDetail PD
            LEFT JOIN Tzh_Protocol P ON PD.ProtocolId = P.Id
            LEFT JOIN Tzh_CarbonEmissionLocation CEL ON PD.CarbonEmissionLocationId = CEL.Id
            LEFT JOIN Tzh_DataSource DS ON PD.DataSourceId = DS.Id
            WHERE PD.SiteName = #{siteName} AND PD.IsDeleted = 0
            """)
	List<TzhProjectDetailVO> list(@Param("siteName") String siteName);

	@Select("""
            SELECT PD.*, Protocol = P.Name, ProtocolSC = P.NameSC, ProtocolEN = P.NameEN,
            CarbonEmissionLocation = CEL.Name, CarbonEmissionLocationSC = CEL.NameSC,
            CarbonEmissionLocationEN = CEL.NameEN, DataSource = DS.Name, DataSourceSC = DS.NameSC,
            DataSourceEN = DS.NameEN
            FROM Tzh_ProjectDetail PD
            LEFT JOIN Tzh_Protocol P ON PD.ProtocolId = P.Id
            LEFT JOIN Tzh_CarbonEmissionLocation CEL ON PD.CarbonEmissionLocationId = CEL.Id
            LEFT JOIN Tzh_DataSource DS ON PD.DataSourceId = DS.Id
            WHERE PD.SiteId = #{siteId} AND PD.IsDeleted = 0
            """)
	List<TzhProjectDetailVO> listBySiteId(@Param("siteId") String siteId);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ProjectDetail
	 * @mbg.generated  Wed Jul 05 16:05:37 HKT 2023
	 */
	long countByExample(TzhProjectDetailExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ProjectDetail
	 * @mbg.generated  Wed Jul 05 16:05:37 HKT 2023
	 */
	int deleteByExample(TzhProjectDetailExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ProjectDetail
	 * @mbg.generated  Wed Jul 05 16:05:37 HKT 2023
	 */
	int insert(TzhProjectDetail row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ProjectDetail
	 * @mbg.generated  Wed Jul 05 16:05:37 HKT 2023
	 */
	int insertSelective(TzhProjectDetail row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ProjectDetail
	 * @mbg.generated  Wed Jul 05 16:05:37 HKT 2023
	 */
	List<TzhProjectDetail> selectByExample(TzhProjectDetailExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ProjectDetail
	 * @mbg.generated  Wed Jul 05 16:05:37 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") TzhProjectDetail row, @Param("example") TzhProjectDetailExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ProjectDetail
	 * @mbg.generated  Wed Jul 05 16:05:37 HKT 2023
	 */
	int updateByExample(@Param("row") TzhProjectDetail row, @Param("example") TzhProjectDetailExample example);
}
