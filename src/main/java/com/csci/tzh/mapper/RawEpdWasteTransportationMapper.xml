<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.RawEpdWasteTransportationMapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.RawEpdWasteTransportation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 17:16:15 HKT 2022.
    -->
    <result column="Id" jdbcType="CHAR" property="id" />
    <result column="Facility" jdbcType="NVARCHAR" property="facility" />
    <result column="TransactionDate" jdbcType="NVARCHAR" property="transactiondate" />
    <result column="VehicleNo" jdbcType="NVARCHAR" property="vehicleno" />
    <result column="AccountNo" jdbcType="NVARCHAR" property="accountno" />
    <result column="ChitNo" jdbcType="NVARCHAR" property="chitno" />
    <result column="TimeIn" jdbcType="NVARCHAR" property="timein" />
    <result column="TimeOut" jdbcType="NVARCHAR" property="timeout" />
    <result column="WasteDepth" jdbcType="NVARCHAR" property="wastedepth" />
    <result column="WeightIn" jdbcType="NVARCHAR" property="weightin" />
    <result column="WeightOut" jdbcType="NVARCHAR" property="weightout" />
    <result column="NetWeight" jdbcType="NVARCHAR" property="netweight" />
    <result column="InputDatetime" jdbcType="TIMESTAMP" property="inputdatetime" />
    <result column="SyncDatetime" jdbcType="TIMESTAMP" property="syncdatetime" />
    <result column="SyncLogId" jdbcType="NVARCHAR" property="synclogid" />
    <result column="ProcessDatetime" jdbcType="TIMESTAMP" property="processdatetime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.csci.tzh.model.RawEpdWasteTransportation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 17:16:15 HKT 2022.
    -->
    <result column="ProcessErrorLog" jdbcType="LONGVARCHAR" property="processerrorlog" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 17:16:15 HKT 2022.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 17:16:15 HKT 2022.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 17:16:15 HKT 2022.
    -->
    Id, Facility, TransactionDate, VehicleNo, AccountNo, ChitNo, TimeIn, TimeOut, WasteDepth, 
    WeightIn, WeightOut, NetWeight, InputDatetime, SyncDatetime, SyncLogId, ProcessDatetime
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 17:16:15 HKT 2022.
    -->
    ProcessErrorLog
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.csci.tzh.model.RawEpdWasteTransportationExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 17:16:15 HKT 2022.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from RawEpdWasteTransportation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.csci.tzh.model.RawEpdWasteTransportationExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 17:16:15 HKT 2022.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from RawEpdWasteTransportation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.RawEpdWasteTransportationExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 17:16:15 HKT 2022.
    -->
    delete from RawEpdWasteTransportation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.RawEpdWasteTransportation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 17:16:15 HKT 2022.
    -->
    insert into RawEpdWasteTransportation (Id, Facility, TransactionDate, 
      VehicleNo, AccountNo, ChitNo, 
      TimeIn, TimeOut, WasteDepth, 
      WeightIn, WeightOut, NetWeight, 
      InputDatetime, SyncDatetime, SyncLogId, 
      ProcessDatetime, ProcessErrorLog)
    values (#{id,jdbcType=CHAR}, #{facility,jdbcType=NVARCHAR}, #{transactiondate,jdbcType=NVARCHAR}, 
      #{vehicleno,jdbcType=NVARCHAR}, #{accountno,jdbcType=NVARCHAR}, #{chitno,jdbcType=NVARCHAR}, 
      #{timein,jdbcType=NVARCHAR}, #{timeout,jdbcType=NVARCHAR}, #{wastedepth,jdbcType=NVARCHAR}, 
      #{weightin,jdbcType=NVARCHAR}, #{weightout,jdbcType=NVARCHAR}, #{netweight,jdbcType=NVARCHAR}, 
      #{inputdatetime,jdbcType=TIMESTAMP}, #{syncdatetime,jdbcType=TIMESTAMP}, #{synclogid,jdbcType=NVARCHAR}, 
      #{processdatetime,jdbcType=TIMESTAMP}, #{processerrorlog,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.RawEpdWasteTransportation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 17:16:15 HKT 2022.
    -->
    insert into RawEpdWasteTransportation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="facility != null">
        Facility,
      </if>
      <if test="transactiondate != null">
        TransactionDate,
      </if>
      <if test="vehicleno != null">
        VehicleNo,
      </if>
      <if test="accountno != null">
        AccountNo,
      </if>
      <if test="chitno != null">
        ChitNo,
      </if>
      <if test="timein != null">
        TimeIn,
      </if>
      <if test="timeout != null">
        TimeOut,
      </if>
      <if test="wastedepth != null">
        WasteDepth,
      </if>
      <if test="weightin != null">
        WeightIn,
      </if>
      <if test="weightout != null">
        WeightOut,
      </if>
      <if test="netweight != null">
        NetWeight,
      </if>
      <if test="inputdatetime != null">
        InputDatetime,
      </if>
      <if test="syncdatetime != null">
        SyncDatetime,
      </if>
      <if test="synclogid != null">
        SyncLogId,
      </if>
      <if test="processdatetime != null">
        ProcessDatetime,
      </if>
      <if test="processerrorlog != null">
        ProcessErrorLog,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="facility != null">
        #{facility,jdbcType=NVARCHAR},
      </if>
      <if test="transactiondate != null">
        #{transactiondate,jdbcType=NVARCHAR},
      </if>
      <if test="vehicleno != null">
        #{vehicleno,jdbcType=NVARCHAR},
      </if>
      <if test="accountno != null">
        #{accountno,jdbcType=NVARCHAR},
      </if>
      <if test="chitno != null">
        #{chitno,jdbcType=NVARCHAR},
      </if>
      <if test="timein != null">
        #{timein,jdbcType=NVARCHAR},
      </if>
      <if test="timeout != null">
        #{timeout,jdbcType=NVARCHAR},
      </if>
      <if test="wastedepth != null">
        #{wastedepth,jdbcType=NVARCHAR},
      </if>
      <if test="weightin != null">
        #{weightin,jdbcType=NVARCHAR},
      </if>
      <if test="weightout != null">
        #{weightout,jdbcType=NVARCHAR},
      </if>
      <if test="netweight != null">
        #{netweight,jdbcType=NVARCHAR},
      </if>
      <if test="inputdatetime != null">
        #{inputdatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="syncdatetime != null">
        #{syncdatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="synclogid != null">
        #{synclogid,jdbcType=NVARCHAR},
      </if>
      <if test="processdatetime != null">
        #{processdatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="processerrorlog != null">
        #{processerrorlog,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.RawEpdWasteTransportationExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 17:16:15 HKT 2022.
    -->
    select count(*) from RawEpdWasteTransportation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 17:16:15 HKT 2022.
    -->
    update RawEpdWasteTransportation
    <set>
      <if test="row.id != null">
        Id = #{row.id,jdbcType=CHAR},
      </if>
      <if test="row.facility != null">
        Facility = #{row.facility,jdbcType=NVARCHAR},
      </if>
      <if test="row.transactiondate != null">
        TransactionDate = #{row.transactiondate,jdbcType=NVARCHAR},
      </if>
      <if test="row.vehicleno != null">
        VehicleNo = #{row.vehicleno,jdbcType=NVARCHAR},
      </if>
      <if test="row.accountno != null">
        AccountNo = #{row.accountno,jdbcType=NVARCHAR},
      </if>
      <if test="row.chitno != null">
        ChitNo = #{row.chitno,jdbcType=NVARCHAR},
      </if>
      <if test="row.timein != null">
        TimeIn = #{row.timein,jdbcType=NVARCHAR},
      </if>
      <if test="row.timeout != null">
        TimeOut = #{row.timeout,jdbcType=NVARCHAR},
      </if>
      <if test="row.wastedepth != null">
        WasteDepth = #{row.wastedepth,jdbcType=NVARCHAR},
      </if>
      <if test="row.weightin != null">
        WeightIn = #{row.weightin,jdbcType=NVARCHAR},
      </if>
      <if test="row.weightout != null">
        WeightOut = #{row.weightout,jdbcType=NVARCHAR},
      </if>
      <if test="row.netweight != null">
        NetWeight = #{row.netweight,jdbcType=NVARCHAR},
      </if>
      <if test="row.inputdatetime != null">
        InputDatetime = #{row.inputdatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.syncdatetime != null">
        SyncDatetime = #{row.syncdatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.synclogid != null">
        SyncLogId = #{row.synclogid,jdbcType=NVARCHAR},
      </if>
      <if test="row.processdatetime != null">
        ProcessDatetime = #{row.processdatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.processerrorlog != null">
        ProcessErrorLog = #{row.processerrorlog,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 17:16:15 HKT 2022.
    -->
    update RawEpdWasteTransportation
    set Id = #{row.id,jdbcType=CHAR},
      Facility = #{row.facility,jdbcType=NVARCHAR},
      TransactionDate = #{row.transactiondate,jdbcType=NVARCHAR},
      VehicleNo = #{row.vehicleno,jdbcType=NVARCHAR},
      AccountNo = #{row.accountno,jdbcType=NVARCHAR},
      ChitNo = #{row.chitno,jdbcType=NVARCHAR},
      TimeIn = #{row.timein,jdbcType=NVARCHAR},
      TimeOut = #{row.timeout,jdbcType=NVARCHAR},
      WasteDepth = #{row.wastedepth,jdbcType=NVARCHAR},
      WeightIn = #{row.weightin,jdbcType=NVARCHAR},
      WeightOut = #{row.weightout,jdbcType=NVARCHAR},
      NetWeight = #{row.netweight,jdbcType=NVARCHAR},
      InputDatetime = #{row.inputdatetime,jdbcType=TIMESTAMP},
      SyncDatetime = #{row.syncdatetime,jdbcType=TIMESTAMP},
      SyncLogId = #{row.synclogid,jdbcType=NVARCHAR},
      ProcessDatetime = #{row.processdatetime,jdbcType=TIMESTAMP},
      ProcessErrorLog = #{row.processerrorlog,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 14 17:16:15 HKT 2022.
    -->
    update RawEpdWasteTransportation
    set Id = #{row.id,jdbcType=CHAR},
      Facility = #{row.facility,jdbcType=NVARCHAR},
      TransactionDate = #{row.transactiondate,jdbcType=NVARCHAR},
      VehicleNo = #{row.vehicleno,jdbcType=NVARCHAR},
      AccountNo = #{row.accountno,jdbcType=NVARCHAR},
      ChitNo = #{row.chitno,jdbcType=NVARCHAR},
      TimeIn = #{row.timein,jdbcType=NVARCHAR},
      TimeOut = #{row.timeout,jdbcType=NVARCHAR},
      WasteDepth = #{row.wastedepth,jdbcType=NVARCHAR},
      WeightIn = #{row.weightin,jdbcType=NVARCHAR},
      WeightOut = #{row.weightout,jdbcType=NVARCHAR},
      NetWeight = #{row.netweight,jdbcType=NVARCHAR},
      InputDatetime = #{row.inputdatetime,jdbcType=TIMESTAMP},
      SyncDatetime = #{row.syncdatetime,jdbcType=TIMESTAMP},
      SyncLogId = #{row.synclogid,jdbcType=NVARCHAR},
      ProcessDatetime = #{row.processdatetime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>