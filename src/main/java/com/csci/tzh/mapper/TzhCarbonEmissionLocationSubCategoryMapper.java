package com.csci.tzh.mapper;

import com.csci.tzh.model.TzhCarbonEmissionLocationSubCategory;
import com.csci.tzh.model.TzhCarbonEmissionLocationSubCategoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TzhCarbonEmissionLocationSubCategoryMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_CarbonEmissionLocation_SubCategory
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    long countByExample(TzhCarbonEmissionLocationSubCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_CarbonEmissionLocation_SubCategory
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    int deleteByExample(TzhCarbonEmissionLocationSubCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_CarbonEmissionLocation_SubCategory
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    int insert(TzhCarbonEmissionLocationSubCategory row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_CarbonEmissionLocation_SubCategory
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    int insertSelective(TzhCarbonEmissionLocationSubCategory row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_CarbonEmissionLocation_SubCategory
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    List<TzhCarbonEmissionLocationSubCategory> selectByExample(TzhCarbonEmissionLocationSubCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_CarbonEmissionLocation_SubCategory
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    int updateByExampleSelective(@Param("row") TzhCarbonEmissionLocationSubCategory row, @Param("example") TzhCarbonEmissionLocationSubCategoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table Tzh_CarbonEmissionLocation_SubCategory
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    int updateByExample(@Param("row") TzhCarbonEmissionLocationSubCategory row, @Param("example") TzhCarbonEmissionLocationSubCategoryExample example);
}