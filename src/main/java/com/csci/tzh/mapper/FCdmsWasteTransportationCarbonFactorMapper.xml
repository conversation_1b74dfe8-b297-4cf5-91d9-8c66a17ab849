<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.tzh.mapper.FCdmsWasteTransportationCarbonFactorMapper">
  <resultMap id="BaseResultMap" type="com.csci.tzh.model.FCdmsWasteTransportationCarbonFactor">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 21 10:13:35 HKT 2023.
    -->
    <result column="Id" jdbcType="CHAR" property="id" />
    <result column="SiteName" jdbcType="NVARCHAR" property="sitename" />
    <result column="ProtocolId" jdbcType="CHAR" property="protocolid" />
    <result column="CarbonEmissionLocationId" jdbcType="CHAR" property="carbonemissionlocationid" />
    <result column="RecordYearMonth" jdbcType="INTEGER" property="recordyearmonth" />
    <result column="RecordDate" jdbcType="DATE" property="recorddate" />
    <result column="VehicleNo" jdbcType="NVARCHAR" property="vehicleno" />
    <result column="ChitNo" jdbcType="NVARCHAR" property="chitno" />
    <result column="WasterType" jdbcType="NVARCHAR" property="wastertype" />
    <result column="HandleMethod" jdbcType="NVARCHAR" property="handlemethod" />
    <result column="HandleLocation" jdbcType="NVARCHAR" property="handlelocation" />
    <result column="TransportationType" jdbcType="NVARCHAR" property="transportationtype" />
    <result column="Qty" jdbcType="NUMERIC" property="qty" />
    <result column="Qty_Unit" jdbcType="NVARCHAR" property="qtyUnit" />
    <result column="TransportFactor" jdbcType="NUMERIC" property="transportfactor" />
    <result column="TransportFactorUnit" jdbcType="NVARCHAR" property="transportfactorunit" />
    <result column="TransportScope" jdbcType="NVARCHAR" property="transportscope" />
    <result column="DataSource" jdbcType="NVARCHAR" property="datasource" />
    <result column="DataSourceId" jdbcType="CHAR" property="datasourceid" />
    <result column="RawDataSourceId" jdbcType="NVARCHAR" property="rawdatasourceid" />
    <result column="CreatedBy" jdbcType="NVARCHAR" property="createdby" />
    <result column="CreatedTime" jdbcType="TIMESTAMP" property="createdtime" />
    <result column="DeletedBy" jdbcType="NVARCHAR" property="deletedby" />
    <result column="DeletedTime" jdbcType="TIMESTAMP" property="deletedtime" />
    <result column="IsDeleted" jdbcType="BIT" property="isdeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 21 10:13:35 HKT 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 21 10:13:35 HKT 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 21 10:13:35 HKT 2023.
    -->
    Id, SiteName, ProtocolId, CarbonEmissionLocationId, RecordYearMonth, RecordDate, 
    VehicleNo, ChitNo, WasterType, HandleMethod, HandleLocation, TransportationType, 
    Qty, Qty_Unit, TransportFactor, TransportFactorUnit, TransportScope, DataSource, 
    DataSourceId, RawDataSourceId, CreatedBy, CreatedTime, DeletedBy, DeletedTime, IsDeleted
  </sql>
  <select id="selectByExample" parameterType="com.csci.tzh.model.FCdmsWasteTransportationCarbonFactorExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 21 10:13:35 HKT 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from F_CDMS_WasteTransportationCarbonFactor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.csci.tzh.model.FCdmsWasteTransportationCarbonFactorExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 21 10:13:35 HKT 2023.
    -->
    delete from F_CDMS_WasteTransportationCarbonFactor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.tzh.model.FCdmsWasteTransportationCarbonFactor">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 21 10:13:35 HKT 2023.
    -->
    insert into F_CDMS_WasteTransportationCarbonFactor (Id, SiteName, ProtocolId, 
      CarbonEmissionLocationId, RecordYearMonth, 
      RecordDate, VehicleNo, ChitNo, 
      WasterType, HandleMethod, HandleLocation, 
      TransportationType, Qty, Qty_Unit, 
      TransportFactor, TransportFactorUnit, 
      TransportScope, DataSource, DataSourceId, 
      RawDataSourceId, CreatedBy, CreatedTime, 
      DeletedBy, DeletedTime, IsDeleted
      )
    values (#{id,jdbcType=CHAR}, #{sitename,jdbcType=NVARCHAR}, #{protocolid,jdbcType=CHAR}, 
      #{carbonemissionlocationid,jdbcType=CHAR}, #{recordyearmonth,jdbcType=INTEGER}, 
      #{recorddate,jdbcType=DATE}, #{vehicleno,jdbcType=NVARCHAR}, #{chitno,jdbcType=NVARCHAR}, 
      #{wastertype,jdbcType=NVARCHAR}, #{handlemethod,jdbcType=NVARCHAR}, #{handlelocation,jdbcType=NVARCHAR}, 
      #{transportationtype,jdbcType=NVARCHAR}, #{qty,jdbcType=NUMERIC}, #{qtyUnit,jdbcType=NVARCHAR}, 
      #{transportfactor,jdbcType=NUMERIC}, #{transportfactorunit,jdbcType=NVARCHAR}, 
      #{transportscope,jdbcType=NVARCHAR}, #{datasource,jdbcType=NVARCHAR}, #{datasourceid,jdbcType=CHAR}, 
      #{rawdatasourceid,jdbcType=NVARCHAR}, #{createdby,jdbcType=NVARCHAR}, #{createdtime,jdbcType=TIMESTAMP}, 
      #{deletedby,jdbcType=NVARCHAR}, #{deletedtime,jdbcType=TIMESTAMP}, #{isdeleted,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.tzh.model.FCdmsWasteTransportationCarbonFactor">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 21 10:13:35 HKT 2023.
    -->
    insert into F_CDMS_WasteTransportationCarbonFactor
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        Id,
      </if>
      <if test="sitename != null">
        SiteName,
      </if>
      <if test="protocolid != null">
        ProtocolId,
      </if>
      <if test="carbonemissionlocationid != null">
        CarbonEmissionLocationId,
      </if>
      <if test="recordyearmonth != null">
        RecordYearMonth,
      </if>
      <if test="recorddate != null">
        RecordDate,
      </if>
      <if test="vehicleno != null">
        VehicleNo,
      </if>
      <if test="chitno != null">
        ChitNo,
      </if>
      <if test="wastertype != null">
        WasterType,
      </if>
      <if test="handlemethod != null">
        HandleMethod,
      </if>
      <if test="handlelocation != null">
        HandleLocation,
      </if>
      <if test="transportationtype != null">
        TransportationType,
      </if>
      <if test="qty != null">
        Qty,
      </if>
      <if test="qtyUnit != null">
        Qty_Unit,
      </if>
      <if test="transportfactor != null">
        TransportFactor,
      </if>
      <if test="transportfactorunit != null">
        TransportFactorUnit,
      </if>
      <if test="transportscope != null">
        TransportScope,
      </if>
      <if test="datasource != null">
        DataSource,
      </if>
      <if test="datasourceid != null">
        DataSourceId,
      </if>
      <if test="rawdatasourceid != null">
        RawDataSourceId,
      </if>
      <if test="createdby != null">
        CreatedBy,
      </if>
      <if test="createdtime != null">
        CreatedTime,
      </if>
      <if test="deletedby != null">
        DeletedBy,
      </if>
      <if test="deletedtime != null">
        DeletedTime,
      </if>
      <if test="isdeleted != null">
        IsDeleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="sitename != null">
        #{sitename,jdbcType=NVARCHAR},
      </if>
      <if test="protocolid != null">
        #{protocolid,jdbcType=CHAR},
      </if>
      <if test="carbonemissionlocationid != null">
        #{carbonemissionlocationid,jdbcType=CHAR},
      </if>
      <if test="recordyearmonth != null">
        #{recordyearmonth,jdbcType=INTEGER},
      </if>
      <if test="recorddate != null">
        #{recorddate,jdbcType=DATE},
      </if>
      <if test="vehicleno != null">
        #{vehicleno,jdbcType=NVARCHAR},
      </if>
      <if test="chitno != null">
        #{chitno,jdbcType=NVARCHAR},
      </if>
      <if test="wastertype != null">
        #{wastertype,jdbcType=NVARCHAR},
      </if>
      <if test="handlemethod != null">
        #{handlemethod,jdbcType=NVARCHAR},
      </if>
      <if test="handlelocation != null">
        #{handlelocation,jdbcType=NVARCHAR},
      </if>
      <if test="transportationtype != null">
        #{transportationtype,jdbcType=NVARCHAR},
      </if>
      <if test="qty != null">
        #{qty,jdbcType=NUMERIC},
      </if>
      <if test="qtyUnit != null">
        #{qtyUnit,jdbcType=NVARCHAR},
      </if>
      <if test="transportfactor != null">
        #{transportfactor,jdbcType=NUMERIC},
      </if>
      <if test="transportfactorunit != null">
        #{transportfactorunit,jdbcType=NVARCHAR},
      </if>
      <if test="transportscope != null">
        #{transportscope,jdbcType=NVARCHAR},
      </if>
      <if test="datasource != null">
        #{datasource,jdbcType=NVARCHAR},
      </if>
      <if test="datasourceid != null">
        #{datasourceid,jdbcType=CHAR},
      </if>
      <if test="rawdatasourceid != null">
        #{rawdatasourceid,jdbcType=NVARCHAR},
      </if>
      <if test="createdby != null">
        #{createdby,jdbcType=NVARCHAR},
      </if>
      <if test="createdtime != null">
        #{createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedby != null">
        #{deletedby,jdbcType=NVARCHAR},
      </if>
      <if test="deletedtime != null">
        #{deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isdeleted != null">
        #{isdeleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.tzh.model.FCdmsWasteTransportationCarbonFactorExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 21 10:13:35 HKT 2023.
    -->
    select count(*) from F_CDMS_WasteTransportationCarbonFactor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 21 10:13:35 HKT 2023.
    -->
    update F_CDMS_WasteTransportationCarbonFactor
    <set>
      <if test="row.id != null">
        Id = #{row.id,jdbcType=CHAR},
      </if>
      <if test="row.sitename != null">
        SiteName = #{row.sitename,jdbcType=NVARCHAR},
      </if>
      <if test="row.protocolid != null">
        ProtocolId = #{row.protocolid,jdbcType=CHAR},
      </if>
      <if test="row.carbonemissionlocationid != null">
        CarbonEmissionLocationId = #{row.carbonemissionlocationid,jdbcType=CHAR},
      </if>
      <if test="row.recordyearmonth != null">
        RecordYearMonth = #{row.recordyearmonth,jdbcType=INTEGER},
      </if>
      <if test="row.recorddate != null">
        RecordDate = #{row.recorddate,jdbcType=DATE},
      </if>
      <if test="row.vehicleno != null">
        VehicleNo = #{row.vehicleno,jdbcType=NVARCHAR},
      </if>
      <if test="row.chitno != null">
        ChitNo = #{row.chitno,jdbcType=NVARCHAR},
      </if>
      <if test="row.wastertype != null">
        WasterType = #{row.wastertype,jdbcType=NVARCHAR},
      </if>
      <if test="row.handlemethod != null">
        HandleMethod = #{row.handlemethod,jdbcType=NVARCHAR},
      </if>
      <if test="row.handlelocation != null">
        HandleLocation = #{row.handlelocation,jdbcType=NVARCHAR},
      </if>
      <if test="row.transportationtype != null">
        TransportationType = #{row.transportationtype,jdbcType=NVARCHAR},
      </if>
      <if test="row.qty != null">
        Qty = #{row.qty,jdbcType=NUMERIC},
      </if>
      <if test="row.qtyUnit != null">
        Qty_Unit = #{row.qtyUnit,jdbcType=NVARCHAR},
      </if>
      <if test="row.transportfactor != null">
        TransportFactor = #{row.transportfactor,jdbcType=NUMERIC},
      </if>
      <if test="row.transportfactorunit != null">
        TransportFactorUnit = #{row.transportfactorunit,jdbcType=NVARCHAR},
      </if>
      <if test="row.transportscope != null">
        TransportScope = #{row.transportscope,jdbcType=NVARCHAR},
      </if>
      <if test="row.datasource != null">
        DataSource = #{row.datasource,jdbcType=NVARCHAR},
      </if>
      <if test="row.datasourceid != null">
        DataSourceId = #{row.datasourceid,jdbcType=CHAR},
      </if>
      <if test="row.rawdatasourceid != null">
        RawDataSourceId = #{row.rawdatasourceid,jdbcType=NVARCHAR},
      </if>
      <if test="row.createdby != null">
        CreatedBy = #{row.createdby,jdbcType=NVARCHAR},
      </if>
      <if test="row.createdtime != null">
        CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.deletedby != null">
        DeletedBy = #{row.deletedby,jdbcType=NVARCHAR},
      </if>
      <if test="row.deletedtime != null">
        DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.isdeleted != null">
        IsDeleted = #{row.isdeleted,jdbcType=BIT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Apr 21 10:13:35 HKT 2023.
    -->
    update F_CDMS_WasteTransportationCarbonFactor
    set Id = #{row.id,jdbcType=CHAR},
      SiteName = #{row.sitename,jdbcType=NVARCHAR},
      ProtocolId = #{row.protocolid,jdbcType=CHAR},
      CarbonEmissionLocationId = #{row.carbonemissionlocationid,jdbcType=CHAR},
      RecordYearMonth = #{row.recordyearmonth,jdbcType=INTEGER},
      RecordDate = #{row.recorddate,jdbcType=DATE},
      VehicleNo = #{row.vehicleno,jdbcType=NVARCHAR},
      ChitNo = #{row.chitno,jdbcType=NVARCHAR},
      WasterType = #{row.wastertype,jdbcType=NVARCHAR},
      HandleMethod = #{row.handlemethod,jdbcType=NVARCHAR},
      HandleLocation = #{row.handlelocation,jdbcType=NVARCHAR},
      TransportationType = #{row.transportationtype,jdbcType=NVARCHAR},
      Qty = #{row.qty,jdbcType=NUMERIC},
      Qty_Unit = #{row.qtyUnit,jdbcType=NVARCHAR},
      TransportFactor = #{row.transportfactor,jdbcType=NUMERIC},
      TransportFactorUnit = #{row.transportfactorunit,jdbcType=NVARCHAR},
      TransportScope = #{row.transportscope,jdbcType=NVARCHAR},
      DataSource = #{row.datasource,jdbcType=NVARCHAR},
      DataSourceId = #{row.datasourceid,jdbcType=CHAR},
      RawDataSourceId = #{row.rawdatasourceid,jdbcType=NVARCHAR},
      CreatedBy = #{row.createdby,jdbcType=NVARCHAR},
      CreatedTime = #{row.createdtime,jdbcType=TIMESTAMP},
      DeletedBy = #{row.deletedby,jdbcType=NVARCHAR},
      DeletedTime = #{row.deletedtime,jdbcType=TIMESTAMP},
      IsDeleted = #{row.isdeleted,jdbcType=BIT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>