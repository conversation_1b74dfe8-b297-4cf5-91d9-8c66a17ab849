package com.csci.tzh.mapper;

import com.csci.tzh.vo.FCdmsGasCarbonFactorVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface FCdmsGasCarbonFactorCustomMapper {
	
    @Select("""
			SELECT GCF.*, region = R.Name, regionsc = R.NameSC, regionen = R.NameEN,
			CarbonEmissionLocation = CEL.Name, CarbonEmissionLocationSC = CEL.NameSC,
			CarbonEmissionLocationEN = CEL.NameEN, Protocol = P.Name, ProtocolSC = P.NameSC, ProtocolEN = P.NameEN
			FROM F_CDMS_GasCarbonFactor GCF
			LEFT JOIN Tzh_CarbonEmissionLocation CEL ON GCF.CarbonEmissionLocationId = CEL.Id
			LEFT JOIN Tzh_Protocol P ON GCF.ProtocolId = P.Id
			LEFT JOIN Tzh_ProjectInfo PI ON PI.Name = GCF.SiteName AND PI.IsDeleted = 0
			LEFT JOIN Tzh_Region R ON R.Id = PI.RegionId
			WHERE R.Name = #{region} 
			AND GCF.SiteName = #{siteName} 
			AND P.NameEN = #{protocol}
			AND (CEL.Name = #{carbonEmissionLocation} OR #{carbonEmissionLocation} = '')
			AND	(GCF.RecordYearMonth = #{recordYearMonth} OR #{recordYearMonth} IS NULL)
			AND GCF.IsDeleted = 0
			""")
    public List<FCdmsGasCarbonFactorVO> list(@Param("region") String region,
											 @Param("siteName") String siteName,
											 @Param("protocol") String protocol,
											 @Param("carbonEmissionLocation") String carbonEmissionLocation,
											 @Param("recordYearMonth") Integer recordYearMonth);

}