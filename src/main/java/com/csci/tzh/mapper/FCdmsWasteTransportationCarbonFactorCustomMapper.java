package com.csci.tzh.mapper;

import com.csci.tzh.model.FCdmsWasteTransportationCarbonFactor;
import com.csci.tzh.vo.FCdmsGasCarbonFactorVO;
import com.csci.tzh.vo.FCdmsWasteTransportationCarbonFactorVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface FCdmsWasteTransportationCarbonFactorCustomMapper {
	
    @Select("""
			SELECT DISTINCT WTCF.*, WTCF.Qty_Unit AS QtyUnit,region = R.Name, regionsc = R.NameSC, regionen = R.NameEN,
			CarbonEmissionLocation = CEL.Name, CarbonEmissionLocationSC = CEL.NameSC,
			CarbonEmissionLocationEN = CEL.NameEN, Protocol = P.Name, ProtocolSC = P.NameSC, ProtocolEN = P.NameEN
			FROM F_CDMS_WasteTransportationCarbonFactor WTCF
			LEFT JOIN Tzh_ProjectInfo PI ON PI.Name = WTCF.SiteName AND PI.IsDeleted = 0
			LEFT JOIN Tzh_Region R ON R.Id = PI.RegionId
			LEFT JOIN Tzh_Protocol P ON WTCF.ProtocolId = P.Id
			LEFT JOIN Tzh_CarbonEmissionLocation CEL ON WTCF.CarbonEmissionLocationId = CEL.Id
			WHERE  WTCF.SiteName = #{siteName} 
			AND P.NameEN = #{protocol}
			AND	(WTCF.RecordYearMonth >= #{recordYearMonthFrom} OR #{recordYearMonthFrom} IS NULL)
			AND	(WTCF.RecordYearMonth <= #{recordYearMonthTo} OR #{recordYearMonthTo} IS NULL)
			AND WTCF.IsDeleted = 0
			""")
    public List<FCdmsWasteTransportationCarbonFactorVO> list(@Param("siteName") String siteName,
															 @Param("protocol") String protocol,
															 @Param("recordYearMonthFrom") Integer recordYearMonthFrom,
															 @Param("recordYearMonthTo") Integer recordYearMonthTo);

}