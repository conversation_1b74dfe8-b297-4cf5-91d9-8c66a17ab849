package com.csci.tzh.mapper;

import com.csci.tzh.model.DCdmsElectricityCarbonFactor;
import com.csci.tzh.model.DCdmsElectricityCarbonFactorExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DCdmsElectricityCarbonFactorMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_ElectricityCarbonFactor
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	long countByExample(DCdmsElectricityCarbonFactorExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_ElectricityCarbonFactor
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	int deleteByExample(DCdmsElectricityCarbonFactorExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_ElectricityCarbonFactor
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	int insert(DCdmsElectricityCarbonFactor row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_ElectricityCarbonFactor
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	int insertSelective(DCdmsElectricityCarbonFactor row);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_ElectricityCarbonFactor
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	List<DCdmsElectricityCarbonFactor> selectByExample(DCdmsElectricityCarbonFactorExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_ElectricityCarbonFactor
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	int updateByExampleSelective(@Param("row") DCdmsElectricityCarbonFactor row,
			@Param("example") DCdmsElectricityCarbonFactorExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table D_CDMS_ElectricityCarbonFactor
	 * @mbg.generated  Fri Jul 07 12:03:33 HKT 2023
	 */
	int updateByExample(@Param("row") DCdmsElectricityCarbonFactor row,
			@Param("example") DCdmsElectricityCarbonFactorExample example);
}