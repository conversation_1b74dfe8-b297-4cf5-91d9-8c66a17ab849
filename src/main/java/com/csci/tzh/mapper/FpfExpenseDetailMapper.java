package com.csci.tzh.mapper;

import com.csci.tzh.model.FpfExpenseDetail;
import com.csci.tzh.model.FpfExpenseDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FpfExpenseDetailMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fpfExpenseDetail
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    long countByExample(FpfExpenseDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fpfExpenseDetail
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    int deleteByExample(FpfExpenseDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fpfExpenseDetail
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    int insert(FpfExpenseDetail row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fpfExpenseDetail
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    int insertSelective(FpfExpenseDetail row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fpfExpenseDetail
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    List<FpfExpenseDetail> selectByExample(FpfExpenseDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fpfExpenseDetail
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    int updateByExampleSelective(@Param("row") FpfExpenseDetail row, @Param("example") FpfExpenseDetailExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table fpfExpenseDetail
     *
     * @mbg.generated Mon May 30 11:24:32 HKT 2022
     */
    int updateByExample(@Param("row") FpfExpenseDetail row, @Param("example") FpfExpenseDetailExample example);
}