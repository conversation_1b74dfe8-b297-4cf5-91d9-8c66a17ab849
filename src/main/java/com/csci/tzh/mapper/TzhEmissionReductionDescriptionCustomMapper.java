package com.csci.tzh.mapper;

import com.csci.tzh.model.TzhEmissionReductionDescription;
import com.csci.tzh.vo.TzhEmissionReductionDescriptionVO;
import com.csci.tzh.vo.TzhEmissionReductionHeadVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface TzhEmissionReductionDescriptionCustomMapper {
	
    @Select("""
			SELECT ERD.*, region = R.Name, regionsc = R.NameSC, regionen = R.NameEN,
			Protocol = P.Name, ProtocolSC = P.NameSC, ProtocolEN = P.NameEN
			FROM Tzh_EmissionReductionDescription ERD
			LEFT JOIN Tzh_Protocol P ON ERD.ProtocolId = P.Id
			LEFT JOIN Tzh_ProjectInfo PI ON PI.Name = ERD.SiteName AND PI.IsDeleted = 0
			LEFT JOIN Tzh_Region R ON R.Id = PI.RegionId
			WHERE ERD.SiteName = #{siteName} 
			AND P.NameEN = #{protocol}
			AND ERD.IsDeleted = 0
			""")
    public List<TzhEmissionReductionDescriptionVO> list(@Param("siteName") String siteName,
														@Param("protocol") String protocol);

}