package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class TzhOrgMaterialCarbonFactorGBT51366 {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_GBT51366.Id
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_GBT51366.ChineseName
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	private String chinesename;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_GBT51366.Specification
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	private String specification;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_GBT51366.EnergyConsumption
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	private String energyconsumption;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_GBT51366.HeatCo2Factor
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	private BigDecimal heatco2factor;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_GBT51366.EffectiveCo2FactorDefault
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	private BigDecimal effectiveco2factordefault;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_GBT51366.EffectiveCo2FactorLower
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	private BigDecimal effectiveco2factorlower;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_GBT51366.EffectiveCo2FactorUpper
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	private BigDecimal effectiveco2factorupper;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_GBT51366.CarbonFactor
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	private BigDecimal carbonfactor;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_GBT51366.CarbonFactorUnit
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	private String carbonfactorunit;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_GBT51366.CreatedBy
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	private String createdby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_GBT51366.CreatedTime
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	private LocalDateTime createdtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_GBT51366.DeletedBy
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	private String deletedby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_GBT51366.DeletedTime
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	private LocalDateTime deletedtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_GBT51366.IsDeleted
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	private Boolean isdeleted;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.Id
	 * @return  the value of Tzh_Org_MaterialCarbonFactor_GBT51366.Id
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.Id
	 * @param id  the value for Tzh_Org_MaterialCarbonFactor_GBT51366.Id
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.ChineseName
	 * @return  the value of Tzh_Org_MaterialCarbonFactor_GBT51366.ChineseName
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public String getChinesename() {
		return chinesename;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.ChineseName
	 * @param chinesename  the value for Tzh_Org_MaterialCarbonFactor_GBT51366.ChineseName
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public void setChinesename(String chinesename) {
		this.chinesename = chinesename == null ? null : chinesename.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.Specification
	 * @return  the value of Tzh_Org_MaterialCarbonFactor_GBT51366.Specification
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public String getSpecification() {
		return specification;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.Specification
	 * @param specification  the value for Tzh_Org_MaterialCarbonFactor_GBT51366.Specification
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public void setSpecification(String specification) {
		this.specification = specification == null ? null : specification.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.EnergyConsumption
	 * @return  the value of Tzh_Org_MaterialCarbonFactor_GBT51366.EnergyConsumption
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public String getEnergyconsumption() {
		return energyconsumption;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.EnergyConsumption
	 * @param energyconsumption  the value for Tzh_Org_MaterialCarbonFactor_GBT51366.EnergyConsumption
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public void setEnergyconsumption(String energyconsumption) {
		this.energyconsumption = energyconsumption == null ? null : energyconsumption.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.HeatCo2Factor
	 * @return  the value of Tzh_Org_MaterialCarbonFactor_GBT51366.HeatCo2Factor
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public BigDecimal getHeatco2factor() {
		return heatco2factor;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.HeatCo2Factor
	 * @param heatco2factor  the value for Tzh_Org_MaterialCarbonFactor_GBT51366.HeatCo2Factor
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public void setHeatco2factor(BigDecimal heatco2factor) {
		this.heatco2factor = heatco2factor;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.EffectiveCo2FactorDefault
	 * @return  the value of Tzh_Org_MaterialCarbonFactor_GBT51366.EffectiveCo2FactorDefault
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public BigDecimal getEffectiveco2factordefault() {
		return effectiveco2factordefault;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.EffectiveCo2FactorDefault
	 * @param effectiveco2factordefault  the value for Tzh_Org_MaterialCarbonFactor_GBT51366.EffectiveCo2FactorDefault
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public void setEffectiveco2factordefault(BigDecimal effectiveco2factordefault) {
		this.effectiveco2factordefault = effectiveco2factordefault;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.EffectiveCo2FactorLower
	 * @return  the value of Tzh_Org_MaterialCarbonFactor_GBT51366.EffectiveCo2FactorLower
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public BigDecimal getEffectiveco2factorlower() {
		return effectiveco2factorlower;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.EffectiveCo2FactorLower
	 * @param effectiveco2factorlower  the value for Tzh_Org_MaterialCarbonFactor_GBT51366.EffectiveCo2FactorLower
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public void setEffectiveco2factorlower(BigDecimal effectiveco2factorlower) {
		this.effectiveco2factorlower = effectiveco2factorlower;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.EffectiveCo2FactorUpper
	 * @return  the value of Tzh_Org_MaterialCarbonFactor_GBT51366.EffectiveCo2FactorUpper
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public BigDecimal getEffectiveco2factorupper() {
		return effectiveco2factorupper;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.EffectiveCo2FactorUpper
	 * @param effectiveco2factorupper  the value for Tzh_Org_MaterialCarbonFactor_GBT51366.EffectiveCo2FactorUpper
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public void setEffectiveco2factorupper(BigDecimal effectiveco2factorupper) {
		this.effectiveco2factorupper = effectiveco2factorupper;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.CarbonFactor
	 * @return  the value of Tzh_Org_MaterialCarbonFactor_GBT51366.CarbonFactor
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public BigDecimal getCarbonfactor() {
		return carbonfactor;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.CarbonFactor
	 * @param carbonfactor  the value for Tzh_Org_MaterialCarbonFactor_GBT51366.CarbonFactor
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public void setCarbonfactor(BigDecimal carbonfactor) {
		this.carbonfactor = carbonfactor;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.CarbonFactorUnit
	 * @return  the value of Tzh_Org_MaterialCarbonFactor_GBT51366.CarbonFactorUnit
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public String getCarbonfactorunit() {
		return carbonfactorunit;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.CarbonFactorUnit
	 * @param carbonfactorunit  the value for Tzh_Org_MaterialCarbonFactor_GBT51366.CarbonFactorUnit
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public void setCarbonfactorunit(String carbonfactorunit) {
		this.carbonfactorunit = carbonfactorunit == null ? null : carbonfactorunit.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.CreatedBy
	 * @return  the value of Tzh_Org_MaterialCarbonFactor_GBT51366.CreatedBy
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public String getCreatedby() {
		return createdby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.CreatedBy
	 * @param createdby  the value for Tzh_Org_MaterialCarbonFactor_GBT51366.CreatedBy
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public void setCreatedby(String createdby) {
		this.createdby = createdby == null ? null : createdby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.CreatedTime
	 * @return  the value of Tzh_Org_MaterialCarbonFactor_GBT51366.CreatedTime
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public LocalDateTime getCreatedtime() {
		return createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.CreatedTime
	 * @param createdtime  the value for Tzh_Org_MaterialCarbonFactor_GBT51366.CreatedTime
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public void setCreatedtime(LocalDateTime createdtime) {
		this.createdtime = createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.DeletedBy
	 * @return  the value of Tzh_Org_MaterialCarbonFactor_GBT51366.DeletedBy
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public String getDeletedby() {
		return deletedby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.DeletedBy
	 * @param deletedby  the value for Tzh_Org_MaterialCarbonFactor_GBT51366.DeletedBy
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public void setDeletedby(String deletedby) {
		this.deletedby = deletedby == null ? null : deletedby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.DeletedTime
	 * @return  the value of Tzh_Org_MaterialCarbonFactor_GBT51366.DeletedTime
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public LocalDateTime getDeletedtime() {
		return deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.DeletedTime
	 * @param deletedtime  the value for Tzh_Org_MaterialCarbonFactor_GBT51366.DeletedTime
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public void setDeletedtime(LocalDateTime deletedtime) {
		this.deletedtime = deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.IsDeleted
	 * @return  the value of Tzh_Org_MaterialCarbonFactor_GBT51366.IsDeleted
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public Boolean getIsdeleted() {
		return isdeleted;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_GBT51366.IsDeleted
	 * @param isdeleted  the value for Tzh_Org_MaterialCarbonFactor_GBT51366.IsDeleted
	 * @mbg.generated  Fri Aug 11 15:13:04 HKT 2023
	 */
	public void setIsdeleted(Boolean isdeleted) {
		this.isdeleted = isdeleted;
	}
}