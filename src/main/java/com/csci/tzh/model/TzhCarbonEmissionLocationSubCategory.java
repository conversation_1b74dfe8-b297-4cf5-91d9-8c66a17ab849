package com.csci.tzh.model;

public class TzhCarbonEmissionLocationSubCategory {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_CarbonEmissionLocation_SubCategory.Id
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_CarbonEmissionLocation_SubCategory.CarbonEmissionLocationId
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    private String carbonemissionlocationid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_CarbonEmissionLocation_SubCategory.SubCategoryId
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    private String subcategoryid;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_CarbonEmissionLocation_SubCategory.Id
     *
     * @return the value of Tzh_CarbonEmissionLocation_SubCategory.Id
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_CarbonEmissionLocation_SubCategory.Id
     *
     * @param id the value for Tzh_CarbonEmissionLocation_SubCategory.Id
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_CarbonEmissionLocation_SubCategory.CarbonEmissionLocationId
     *
     * @return the value of Tzh_CarbonEmissionLocation_SubCategory.CarbonEmissionLocationId
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    public String getCarbonemissionlocationid() {
        return carbonemissionlocationid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_CarbonEmissionLocation_SubCategory.CarbonEmissionLocationId
     *
     * @param carbonemissionlocationid the value for Tzh_CarbonEmissionLocation_SubCategory.CarbonEmissionLocationId
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    public void setCarbonemissionlocationid(String carbonemissionlocationid) {
        this.carbonemissionlocationid = carbonemissionlocationid == null ? null : carbonemissionlocationid.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_CarbonEmissionLocation_SubCategory.SubCategoryId
     *
     * @return the value of Tzh_CarbonEmissionLocation_SubCategory.SubCategoryId
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    public String getSubcategoryid() {
        return subcategoryid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_CarbonEmissionLocation_SubCategory.SubCategoryId
     *
     * @param subcategoryid the value for Tzh_CarbonEmissionLocation_SubCategory.SubCategoryId
     *
     * @mbg.generated Tue Mar 28 17:54:24 HKT 2023
     */
    public void setSubcategoryid(String subcategoryid) {
        this.subcategoryid = subcategoryid == null ? null : subcategoryid.trim();
    }
}