package com.csci.tzh.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class SysOrganizationExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public SysOrganizationExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("Id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("Id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(Integer value) {
			addCriterion("Id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(Integer value) {
			addCriterion("Id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(Integer value) {
			addCriterion("Id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(Integer value) {
			addCriterion("Id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(Integer value) {
			addCriterion("Id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(Integer value) {
			addCriterion("Id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<Integer> values) {
			addCriterion("Id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<Integer> values) {
			addCriterion("Id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(Integer value1, Integer value2) {
			addCriterion("Id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(Integer value1, Integer value2) {
			addCriterion("Id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andInnernoIsNull() {
			addCriterion("InnerNo is null");
			return (Criteria) this;
		}

		public Criteria andInnernoIsNotNull() {
			addCriterion("InnerNo is not null");
			return (Criteria) this;
		}

		public Criteria andInnernoEqualTo(String value) {
			addCriterion("InnerNo =", value, "innerno");
			return (Criteria) this;
		}

		public Criteria andInnernoNotEqualTo(String value) {
			addCriterion("InnerNo <>", value, "innerno");
			return (Criteria) this;
		}

		public Criteria andInnernoGreaterThan(String value) {
			addCriterion("InnerNo >", value, "innerno");
			return (Criteria) this;
		}

		public Criteria andInnernoGreaterThanOrEqualTo(String value) {
			addCriterion("InnerNo >=", value, "innerno");
			return (Criteria) this;
		}

		public Criteria andInnernoLessThan(String value) {
			addCriterion("InnerNo <", value, "innerno");
			return (Criteria) this;
		}

		public Criteria andInnernoLessThanOrEqualTo(String value) {
			addCriterion("InnerNo <=", value, "innerno");
			return (Criteria) this;
		}

		public Criteria andInnernoLike(String value) {
			addCriterion("InnerNo like", value, "innerno");
			return (Criteria) this;
		}

		public Criteria andInnernoNotLike(String value) {
			addCriterion("InnerNo not like", value, "innerno");
			return (Criteria) this;
		}

		public Criteria andInnernoIn(List<String> values) {
			addCriterion("InnerNo in", values, "innerno");
			return (Criteria) this;
		}

		public Criteria andInnernoNotIn(List<String> values) {
			addCriterion("InnerNo not in", values, "innerno");
			return (Criteria) this;
		}

		public Criteria andInnernoBetween(String value1, String value2) {
			addCriterion("InnerNo between", value1, value2, "innerno");
			return (Criteria) this;
		}

		public Criteria andInnernoNotBetween(String value1, String value2) {
			addCriterion("InnerNo not between", value1, value2, "innerno");
			return (Criteria) this;
		}

		public Criteria andCodeIsNull() {
			addCriterion("Code is null");
			return (Criteria) this;
		}

		public Criteria andCodeIsNotNull() {
			addCriterion("Code is not null");
			return (Criteria) this;
		}

		public Criteria andCodeEqualTo(String value) {
			addCriterion("Code =", value, "code");
			return (Criteria) this;
		}

		public Criteria andCodeNotEqualTo(String value) {
			addCriterion("Code <>", value, "code");
			return (Criteria) this;
		}

		public Criteria andCodeGreaterThan(String value) {
			addCriterion("Code >", value, "code");
			return (Criteria) this;
		}

		public Criteria andCodeGreaterThanOrEqualTo(String value) {
			addCriterion("Code >=", value, "code");
			return (Criteria) this;
		}

		public Criteria andCodeLessThan(String value) {
			addCriterion("Code <", value, "code");
			return (Criteria) this;
		}

		public Criteria andCodeLessThanOrEqualTo(String value) {
			addCriterion("Code <=", value, "code");
			return (Criteria) this;
		}

		public Criteria andCodeLike(String value) {
			addCriterion("Code like", value, "code");
			return (Criteria) this;
		}

		public Criteria andCodeNotLike(String value) {
			addCriterion("Code not like", value, "code");
			return (Criteria) this;
		}

		public Criteria andCodeIn(List<String> values) {
			addCriterion("Code in", values, "code");
			return (Criteria) this;
		}

		public Criteria andCodeNotIn(List<String> values) {
			addCriterion("Code not in", values, "code");
			return (Criteria) this;
		}

		public Criteria andCodeBetween(String value1, String value2) {
			addCriterion("Code between", value1, value2, "code");
			return (Criteria) this;
		}

		public Criteria andCodeNotBetween(String value1, String value2) {
			addCriterion("Code not between", value1, value2, "code");
			return (Criteria) this;
		}

		public Criteria andNameIsNull() {
			addCriterion("Name is null");
			return (Criteria) this;
		}

		public Criteria andNameIsNotNull() {
			addCriterion("Name is not null");
			return (Criteria) this;
		}

		public Criteria andNameEqualTo(String value) {
			addCriterion("Name =", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameNotEqualTo(String value) {
			addCriterion("Name <>", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameGreaterThan(String value) {
			addCriterion("Name >", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameGreaterThanOrEqualTo(String value) {
			addCriterion("Name >=", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameLessThan(String value) {
			addCriterion("Name <", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameLessThanOrEqualTo(String value) {
			addCriterion("Name <=", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameLike(String value) {
			addCriterion("Name like", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameNotLike(String value) {
			addCriterion("Name not like", value, "name");
			return (Criteria) this;
		}

		public Criteria andNameIn(List<String> values) {
			addCriterion("Name in", values, "name");
			return (Criteria) this;
		}

		public Criteria andNameNotIn(List<String> values) {
			addCriterion("Name not in", values, "name");
			return (Criteria) this;
		}

		public Criteria andNameBetween(String value1, String value2) {
			addCriterion("Name between", value1, value2, "name");
			return (Criteria) this;
		}

		public Criteria andNameNotBetween(String value1, String value2) {
			addCriterion("Name not between", value1, value2, "name");
			return (Criteria) this;
		}

		public Criteria andTypeIsNull() {
			addCriterion("Type is null");
			return (Criteria) this;
		}

		public Criteria andTypeIsNotNull() {
			addCriterion("Type is not null");
			return (Criteria) this;
		}

		public Criteria andTypeEqualTo(Integer value) {
			addCriterion("Type =", value, "type");
			return (Criteria) this;
		}

		public Criteria andTypeNotEqualTo(Integer value) {
			addCriterion("Type <>", value, "type");
			return (Criteria) this;
		}

		public Criteria andTypeGreaterThan(Integer value) {
			addCriterion("Type >", value, "type");
			return (Criteria) this;
		}

		public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
			addCriterion("Type >=", value, "type");
			return (Criteria) this;
		}

		public Criteria andTypeLessThan(Integer value) {
			addCriterion("Type <", value, "type");
			return (Criteria) this;
		}

		public Criteria andTypeLessThanOrEqualTo(Integer value) {
			addCriterion("Type <=", value, "type");
			return (Criteria) this;
		}

		public Criteria andTypeIn(List<Integer> values) {
			addCriterion("Type in", values, "type");
			return (Criteria) this;
		}

		public Criteria andTypeNotIn(List<Integer> values) {
			addCriterion("Type not in", values, "type");
			return (Criteria) this;
		}

		public Criteria andTypeBetween(Integer value1, Integer value2) {
			addCriterion("Type between", value1, value2, "type");
			return (Criteria) this;
		}

		public Criteria andTypeNotBetween(Integer value1, Integer value2) {
			addCriterion("Type not between", value1, value2, "type");
			return (Criteria) this;
		}

		public Criteria andRelevantidIsNull() {
			addCriterion("RelevantId is null");
			return (Criteria) this;
		}

		public Criteria andRelevantidIsNotNull() {
			addCriterion("RelevantId is not null");
			return (Criteria) this;
		}

		public Criteria andRelevantidEqualTo(Integer value) {
			addCriterion("RelevantId =", value, "relevantid");
			return (Criteria) this;
		}

		public Criteria andRelevantidNotEqualTo(Integer value) {
			addCriterion("RelevantId <>", value, "relevantid");
			return (Criteria) this;
		}

		public Criteria andRelevantidGreaterThan(Integer value) {
			addCriterion("RelevantId >", value, "relevantid");
			return (Criteria) this;
		}

		public Criteria andRelevantidGreaterThanOrEqualTo(Integer value) {
			addCriterion("RelevantId >=", value, "relevantid");
			return (Criteria) this;
		}

		public Criteria andRelevantidLessThan(Integer value) {
			addCriterion("RelevantId <", value, "relevantid");
			return (Criteria) this;
		}

		public Criteria andRelevantidLessThanOrEqualTo(Integer value) {
			addCriterion("RelevantId <=", value, "relevantid");
			return (Criteria) this;
		}

		public Criteria andRelevantidIn(List<Integer> values) {
			addCriterion("RelevantId in", values, "relevantid");
			return (Criteria) this;
		}

		public Criteria andRelevantidNotIn(List<Integer> values) {
			addCriterion("RelevantId not in", values, "relevantid");
			return (Criteria) this;
		}

		public Criteria andRelevantidBetween(Integer value1, Integer value2) {
			addCriterion("RelevantId between", value1, value2, "relevantid");
			return (Criteria) this;
		}

		public Criteria andRelevantidNotBetween(Integer value1, Integer value2) {
			addCriterion("RelevantId not between", value1, value2, "relevantid");
			return (Criteria) this;
		}

		public Criteria andSortkeyIsNull() {
			addCriterion("SortKey is null");
			return (Criteria) this;
		}

		public Criteria andSortkeyIsNotNull() {
			addCriterion("SortKey is not null");
			return (Criteria) this;
		}

		public Criteria andSortkeyEqualTo(Integer value) {
			addCriterion("SortKey =", value, "sortkey");
			return (Criteria) this;
		}

		public Criteria andSortkeyNotEqualTo(Integer value) {
			addCriterion("SortKey <>", value, "sortkey");
			return (Criteria) this;
		}

		public Criteria andSortkeyGreaterThan(Integer value) {
			addCriterion("SortKey >", value, "sortkey");
			return (Criteria) this;
		}

		public Criteria andSortkeyGreaterThanOrEqualTo(Integer value) {
			addCriterion("SortKey >=", value, "sortkey");
			return (Criteria) this;
		}

		public Criteria andSortkeyLessThan(Integer value) {
			addCriterion("SortKey <", value, "sortkey");
			return (Criteria) this;
		}

		public Criteria andSortkeyLessThanOrEqualTo(Integer value) {
			addCriterion("SortKey <=", value, "sortkey");
			return (Criteria) this;
		}

		public Criteria andSortkeyIn(List<Integer> values) {
			addCriterion("SortKey in", values, "sortkey");
			return (Criteria) this;
		}

		public Criteria andSortkeyNotIn(List<Integer> values) {
			addCriterion("SortKey not in", values, "sortkey");
			return (Criteria) this;
		}

		public Criteria andSortkeyBetween(Integer value1, Integer value2) {
			addCriterion("SortKey between", value1, value2, "sortkey");
			return (Criteria) this;
		}

		public Criteria andSortkeyNotBetween(Integer value1, Integer value2) {
			addCriterion("SortKey not between", value1, value2, "sortkey");
			return (Criteria) this;
		}

		public Criteria andTenantidIsNull() {
			addCriterion("TenantId is null");
			return (Criteria) this;
		}

		public Criteria andTenantidIsNotNull() {
			addCriterion("TenantId is not null");
			return (Criteria) this;
		}

		public Criteria andTenantidEqualTo(Integer value) {
			addCriterion("TenantId =", value, "tenantid");
			return (Criteria) this;
		}

		public Criteria andTenantidNotEqualTo(Integer value) {
			addCriterion("TenantId <>", value, "tenantid");
			return (Criteria) this;
		}

		public Criteria andTenantidGreaterThan(Integer value) {
			addCriterion("TenantId >", value, "tenantid");
			return (Criteria) this;
		}

		public Criteria andTenantidGreaterThanOrEqualTo(Integer value) {
			addCriterion("TenantId >=", value, "tenantid");
			return (Criteria) this;
		}

		public Criteria andTenantidLessThan(Integer value) {
			addCriterion("TenantId <", value, "tenantid");
			return (Criteria) this;
		}

		public Criteria andTenantidLessThanOrEqualTo(Integer value) {
			addCriterion("TenantId <=", value, "tenantid");
			return (Criteria) this;
		}

		public Criteria andTenantidIn(List<Integer> values) {
			addCriterion("TenantId in", values, "tenantid");
			return (Criteria) this;
		}

		public Criteria andTenantidNotIn(List<Integer> values) {
			addCriterion("TenantId not in", values, "tenantid");
			return (Criteria) this;
		}

		public Criteria andTenantidBetween(Integer value1, Integer value2) {
			addCriterion("TenantId between", value1, value2, "tenantid");
			return (Criteria) this;
		}

		public Criteria andTenantidNotBetween(Integer value1, Integer value2) {
			addCriterion("TenantId not between", value1, value2, "tenantid");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIsNull() {
			addCriterion("IsDeleted is null");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIsNotNull() {
			addCriterion("IsDeleted is not null");
			return (Criteria) this;
		}

		public Criteria andIsdeletedEqualTo(Boolean value) {
			addCriterion("IsDeleted =", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotEqualTo(Boolean value) {
			addCriterion("IsDeleted <>", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedGreaterThan(Boolean value) {
			addCriterion("IsDeleted >", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedGreaterThanOrEqualTo(Boolean value) {
			addCriterion("IsDeleted >=", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedLessThan(Boolean value) {
			addCriterion("IsDeleted <", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedLessThanOrEqualTo(Boolean value) {
			addCriterion("IsDeleted <=", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIn(List<Boolean> values) {
			addCriterion("IsDeleted in", values, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotIn(List<Boolean> values) {
			addCriterion("IsDeleted not in", values, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedBetween(Boolean value1, Boolean value2) {
			addCriterion("IsDeleted between", value1, value2, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotBetween(Boolean value1, Boolean value2) {
			addCriterion("IsDeleted not between", value1, value2, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andDeleteruseridIsNull() {
			addCriterion("DeleterUserId is null");
			return (Criteria) this;
		}

		public Criteria andDeleteruseridIsNotNull() {
			addCriterion("DeleterUserId is not null");
			return (Criteria) this;
		}

		public Criteria andDeleteruseridEqualTo(Long value) {
			addCriterion("DeleterUserId =", value, "deleteruserid");
			return (Criteria) this;
		}

		public Criteria andDeleteruseridNotEqualTo(Long value) {
			addCriterion("DeleterUserId <>", value, "deleteruserid");
			return (Criteria) this;
		}

		public Criteria andDeleteruseridGreaterThan(Long value) {
			addCriterion("DeleterUserId >", value, "deleteruserid");
			return (Criteria) this;
		}

		public Criteria andDeleteruseridGreaterThanOrEqualTo(Long value) {
			addCriterion("DeleterUserId >=", value, "deleteruserid");
			return (Criteria) this;
		}

		public Criteria andDeleteruseridLessThan(Long value) {
			addCriterion("DeleterUserId <", value, "deleteruserid");
			return (Criteria) this;
		}

		public Criteria andDeleteruseridLessThanOrEqualTo(Long value) {
			addCriterion("DeleterUserId <=", value, "deleteruserid");
			return (Criteria) this;
		}

		public Criteria andDeleteruseridIn(List<Long> values) {
			addCriterion("DeleterUserId in", values, "deleteruserid");
			return (Criteria) this;
		}

		public Criteria andDeleteruseridNotIn(List<Long> values) {
			addCriterion("DeleterUserId not in", values, "deleteruserid");
			return (Criteria) this;
		}

		public Criteria andDeleteruseridBetween(Long value1, Long value2) {
			addCriterion("DeleterUserId between", value1, value2, "deleteruserid");
			return (Criteria) this;
		}

		public Criteria andDeleteruseridNotBetween(Long value1, Long value2) {
			addCriterion("DeleterUserId not between", value1, value2, "deleteruserid");
			return (Criteria) this;
		}

		public Criteria andDeletiontimeIsNull() {
			addCriterion("DeletionTime is null");
			return (Criteria) this;
		}

		public Criteria andDeletiontimeIsNotNull() {
			addCriterion("DeletionTime is not null");
			return (Criteria) this;
		}

		public Criteria andDeletiontimeEqualTo(LocalDateTime value) {
			addCriterion("DeletionTime =", value, "deletiontime");
			return (Criteria) this;
		}

		public Criteria andDeletiontimeNotEqualTo(LocalDateTime value) {
			addCriterion("DeletionTime <>", value, "deletiontime");
			return (Criteria) this;
		}

		public Criteria andDeletiontimeGreaterThan(LocalDateTime value) {
			addCriterion("DeletionTime >", value, "deletiontime");
			return (Criteria) this;
		}

		public Criteria andDeletiontimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("DeletionTime >=", value, "deletiontime");
			return (Criteria) this;
		}

		public Criteria andDeletiontimeLessThan(LocalDateTime value) {
			addCriterion("DeletionTime <", value, "deletiontime");
			return (Criteria) this;
		}

		public Criteria andDeletiontimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("DeletionTime <=", value, "deletiontime");
			return (Criteria) this;
		}

		public Criteria andDeletiontimeIn(List<LocalDateTime> values) {
			addCriterion("DeletionTime in", values, "deletiontime");
			return (Criteria) this;
		}

		public Criteria andDeletiontimeNotIn(List<LocalDateTime> values) {
			addCriterion("DeletionTime not in", values, "deletiontime");
			return (Criteria) this;
		}

		public Criteria andDeletiontimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("DeletionTime between", value1, value2, "deletiontime");
			return (Criteria) this;
		}

		public Criteria andDeletiontimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("DeletionTime not between", value1, value2, "deletiontime");
			return (Criteria) this;
		}

		public Criteria andLastmodificationtimeIsNull() {
			addCriterion("LastModificationTime is null");
			return (Criteria) this;
		}

		public Criteria andLastmodificationtimeIsNotNull() {
			addCriterion("LastModificationTime is not null");
			return (Criteria) this;
		}

		public Criteria andLastmodificationtimeEqualTo(LocalDateTime value) {
			addCriterion("LastModificationTime =", value, "lastmodificationtime");
			return (Criteria) this;
		}

		public Criteria andLastmodificationtimeNotEqualTo(LocalDateTime value) {
			addCriterion("LastModificationTime <>", value, "lastmodificationtime");
			return (Criteria) this;
		}

		public Criteria andLastmodificationtimeGreaterThan(LocalDateTime value) {
			addCriterion("LastModificationTime >", value, "lastmodificationtime");
			return (Criteria) this;
		}

		public Criteria andLastmodificationtimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("LastModificationTime >=", value, "lastmodificationtime");
			return (Criteria) this;
		}

		public Criteria andLastmodificationtimeLessThan(LocalDateTime value) {
			addCriterion("LastModificationTime <", value, "lastmodificationtime");
			return (Criteria) this;
		}

		public Criteria andLastmodificationtimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("LastModificationTime <=", value, "lastmodificationtime");
			return (Criteria) this;
		}

		public Criteria andLastmodificationtimeIn(List<LocalDateTime> values) {
			addCriterion("LastModificationTime in", values, "lastmodificationtime");
			return (Criteria) this;
		}

		public Criteria andLastmodificationtimeNotIn(List<LocalDateTime> values) {
			addCriterion("LastModificationTime not in", values, "lastmodificationtime");
			return (Criteria) this;
		}

		public Criteria andLastmodificationtimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("LastModificationTime between", value1, value2, "lastmodificationtime");
			return (Criteria) this;
		}

		public Criteria andLastmodificationtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("LastModificationTime not between", value1, value2, "lastmodificationtime");
			return (Criteria) this;
		}

		public Criteria andLastmodifieruseridIsNull() {
			addCriterion("LastModifierUserId is null");
			return (Criteria) this;
		}

		public Criteria andLastmodifieruseridIsNotNull() {
			addCriterion("LastModifierUserId is not null");
			return (Criteria) this;
		}

		public Criteria andLastmodifieruseridEqualTo(Long value) {
			addCriterion("LastModifierUserId =", value, "lastmodifieruserid");
			return (Criteria) this;
		}

		public Criteria andLastmodifieruseridNotEqualTo(Long value) {
			addCriterion("LastModifierUserId <>", value, "lastmodifieruserid");
			return (Criteria) this;
		}

		public Criteria andLastmodifieruseridGreaterThan(Long value) {
			addCriterion("LastModifierUserId >", value, "lastmodifieruserid");
			return (Criteria) this;
		}

		public Criteria andLastmodifieruseridGreaterThanOrEqualTo(Long value) {
			addCriterion("LastModifierUserId >=", value, "lastmodifieruserid");
			return (Criteria) this;
		}

		public Criteria andLastmodifieruseridLessThan(Long value) {
			addCriterion("LastModifierUserId <", value, "lastmodifieruserid");
			return (Criteria) this;
		}

		public Criteria andLastmodifieruseridLessThanOrEqualTo(Long value) {
			addCriterion("LastModifierUserId <=", value, "lastmodifieruserid");
			return (Criteria) this;
		}

		public Criteria andLastmodifieruseridIn(List<Long> values) {
			addCriterion("LastModifierUserId in", values, "lastmodifieruserid");
			return (Criteria) this;
		}

		public Criteria andLastmodifieruseridNotIn(List<Long> values) {
			addCriterion("LastModifierUserId not in", values, "lastmodifieruserid");
			return (Criteria) this;
		}

		public Criteria andLastmodifieruseridBetween(Long value1, Long value2) {
			addCriterion("LastModifierUserId between", value1, value2, "lastmodifieruserid");
			return (Criteria) this;
		}

		public Criteria andLastmodifieruseridNotBetween(Long value1, Long value2) {
			addCriterion("LastModifierUserId not between", value1, value2, "lastmodifieruserid");
			return (Criteria) this;
		}

		public Criteria andCreationtimeIsNull() {
			addCriterion("CreationTime is null");
			return (Criteria) this;
		}

		public Criteria andCreationtimeIsNotNull() {
			addCriterion("CreationTime is not null");
			return (Criteria) this;
		}

		public Criteria andCreationtimeEqualTo(LocalDateTime value) {
			addCriterion("CreationTime =", value, "creationtime");
			return (Criteria) this;
		}

		public Criteria andCreationtimeNotEqualTo(LocalDateTime value) {
			addCriterion("CreationTime <>", value, "creationtime");
			return (Criteria) this;
		}

		public Criteria andCreationtimeGreaterThan(LocalDateTime value) {
			addCriterion("CreationTime >", value, "creationtime");
			return (Criteria) this;
		}

		public Criteria andCreationtimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("CreationTime >=", value, "creationtime");
			return (Criteria) this;
		}

		public Criteria andCreationtimeLessThan(LocalDateTime value) {
			addCriterion("CreationTime <", value, "creationtime");
			return (Criteria) this;
		}

		public Criteria andCreationtimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("CreationTime <=", value, "creationtime");
			return (Criteria) this;
		}

		public Criteria andCreationtimeIn(List<LocalDateTime> values) {
			addCriterion("CreationTime in", values, "creationtime");
			return (Criteria) this;
		}

		public Criteria andCreationtimeNotIn(List<LocalDateTime> values) {
			addCriterion("CreationTime not in", values, "creationtime");
			return (Criteria) this;
		}

		public Criteria andCreationtimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("CreationTime between", value1, value2, "creationtime");
			return (Criteria) this;
		}

		public Criteria andCreationtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("CreationTime not between", value1, value2, "creationtime");
			return (Criteria) this;
		}

		public Criteria andCreatoruseridIsNull() {
			addCriterion("CreatorUserId is null");
			return (Criteria) this;
		}

		public Criteria andCreatoruseridIsNotNull() {
			addCriterion("CreatorUserId is not null");
			return (Criteria) this;
		}

		public Criteria andCreatoruseridEqualTo(Long value) {
			addCriterion("CreatorUserId =", value, "creatoruserid");
			return (Criteria) this;
		}

		public Criteria andCreatoruseridNotEqualTo(Long value) {
			addCriterion("CreatorUserId <>", value, "creatoruserid");
			return (Criteria) this;
		}

		public Criteria andCreatoruseridGreaterThan(Long value) {
			addCriterion("CreatorUserId >", value, "creatoruserid");
			return (Criteria) this;
		}

		public Criteria andCreatoruseridGreaterThanOrEqualTo(Long value) {
			addCriterion("CreatorUserId >=", value, "creatoruserid");
			return (Criteria) this;
		}

		public Criteria andCreatoruseridLessThan(Long value) {
			addCriterion("CreatorUserId <", value, "creatoruserid");
			return (Criteria) this;
		}

		public Criteria andCreatoruseridLessThanOrEqualTo(Long value) {
			addCriterion("CreatorUserId <=", value, "creatoruserid");
			return (Criteria) this;
		}

		public Criteria andCreatoruseridIn(List<Long> values) {
			addCriterion("CreatorUserId in", values, "creatoruserid");
			return (Criteria) this;
		}

		public Criteria andCreatoruseridNotIn(List<Long> values) {
			addCriterion("CreatorUserId not in", values, "creatoruserid");
			return (Criteria) this;
		}

		public Criteria andCreatoruseridBetween(Long value1, Long value2) {
			addCriterion("CreatorUserId between", value1, value2, "creatoruserid");
			return (Criteria) this;
		}

		public Criteria andCreatoruseridNotBetween(Long value1, Long value2) {
			addCriterion("CreatorUserId not between", value1, value2, "creatoruserid");
			return (Criteria) this;
		}

		public Criteria andSourceidIsNull() {
			addCriterion("SourceId is null");
			return (Criteria) this;
		}

		public Criteria andSourceidIsNotNull() {
			addCriterion("SourceId is not null");
			return (Criteria) this;
		}

		public Criteria andSourceidEqualTo(String value) {
			addCriterion("SourceId =", value, "sourceid");
			return (Criteria) this;
		}

		public Criteria andSourceidNotEqualTo(String value) {
			addCriterion("SourceId <>", value, "sourceid");
			return (Criteria) this;
		}

		public Criteria andSourceidGreaterThan(String value) {
			addCriterion("SourceId >", value, "sourceid");
			return (Criteria) this;
		}

		public Criteria andSourceidGreaterThanOrEqualTo(String value) {
			addCriterion("SourceId >=", value, "sourceid");
			return (Criteria) this;
		}

		public Criteria andSourceidLessThan(String value) {
			addCriterion("SourceId <", value, "sourceid");
			return (Criteria) this;
		}

		public Criteria andSourceidLessThanOrEqualTo(String value) {
			addCriterion("SourceId <=", value, "sourceid");
			return (Criteria) this;
		}

		public Criteria andSourceidLike(String value) {
			addCriterion("SourceId like", value, "sourceid");
			return (Criteria) this;
		}

		public Criteria andSourceidNotLike(String value) {
			addCriterion("SourceId not like", value, "sourceid");
			return (Criteria) this;
		}

		public Criteria andSourceidIn(List<String> values) {
			addCriterion("SourceId in", values, "sourceid");
			return (Criteria) this;
		}

		public Criteria andSourceidNotIn(List<String> values) {
			addCriterion("SourceId not in", values, "sourceid");
			return (Criteria) this;
		}

		public Criteria andSourceidBetween(String value1, String value2) {
			addCriterion("SourceId between", value1, value2, "sourceid");
			return (Criteria) this;
		}

		public Criteria andSourceidNotBetween(String value1, String value2) {
			addCriterion("SourceId not between", value1, value2, "sourceid");
			return (Criteria) this;
		}

		public Criteria andKeyinmdmIsNull() {
			addCriterion("KeyInMdm is null");
			return (Criteria) this;
		}

		public Criteria andKeyinmdmIsNotNull() {
			addCriterion("KeyInMdm is not null");
			return (Criteria) this;
		}

		public Criteria andKeyinmdmEqualTo(String value) {
			addCriterion("KeyInMdm =", value, "keyinmdm");
			return (Criteria) this;
		}

		public Criteria andKeyinmdmNotEqualTo(String value) {
			addCriterion("KeyInMdm <>", value, "keyinmdm");
			return (Criteria) this;
		}

		public Criteria andKeyinmdmGreaterThan(String value) {
			addCriterion("KeyInMdm >", value, "keyinmdm");
			return (Criteria) this;
		}

		public Criteria andKeyinmdmGreaterThanOrEqualTo(String value) {
			addCriterion("KeyInMdm >=", value, "keyinmdm");
			return (Criteria) this;
		}

		public Criteria andKeyinmdmLessThan(String value) {
			addCriterion("KeyInMdm <", value, "keyinmdm");
			return (Criteria) this;
		}

		public Criteria andKeyinmdmLessThanOrEqualTo(String value) {
			addCriterion("KeyInMdm <=", value, "keyinmdm");
			return (Criteria) this;
		}

		public Criteria andKeyinmdmLike(String value) {
			addCriterion("KeyInMdm like", value, "keyinmdm");
			return (Criteria) this;
		}

		public Criteria andKeyinmdmNotLike(String value) {
			addCriterion("KeyInMdm not like", value, "keyinmdm");
			return (Criteria) this;
		}

		public Criteria andKeyinmdmIn(List<String> values) {
			addCriterion("KeyInMdm in", values, "keyinmdm");
			return (Criteria) this;
		}

		public Criteria andKeyinmdmNotIn(List<String> values) {
			addCriterion("KeyInMdm not in", values, "keyinmdm");
			return (Criteria) this;
		}

		public Criteria andKeyinmdmBetween(String value1, String value2) {
			addCriterion("KeyInMdm between", value1, value2, "keyinmdm");
			return (Criteria) this;
		}

		public Criteria andKeyinmdmNotBetween(String value1, String value2) {
			addCriterion("KeyInMdm not between", value1, value2, "keyinmdm");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table sysOrganization
	 * @mbg.generated  Tue May 24 16:25:25 HKT 2022
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table sysOrganization
     *
     * @mbg.generated do_not_delete_during_merge Mon May 23 15:34:13 HKT 2022
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}