package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDate;

public class FMaterial {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column F_Material.Region
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    private String region;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column F_Material.SiteId
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    private Integer siteid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column F_Material.SiteName
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    private String sitename;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column F_Material.RecordYearMonth
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    private Integer recordyearmonth;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column F_Material.CarbonEmissionLocation
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    private String carbonemissionlocation;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column F_Material.MaterialCode
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    private String materialcode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column F_Material.ChineseName
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    private String chinesename;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column F_Material.Unit
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    private String unit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column F_Material.Description
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column F_Material.CarbonFactor
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    private BigDecimal carbonfactor;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column F_Material.CarbonFactorUnit
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    private String carbonfactorunit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column F_Material.TransportFactor
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    private BigDecimal transportfactor;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column F_Material.TransportFactorUnit
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    private String transportfactorunit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column F_Material.TransportDistance
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    private BigDecimal transportdistance;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column F_Material.TransportDistanceUnit
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    private String transportdistanceunit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column F_Material.Qty
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    private BigDecimal qty;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column F_Material.CarbonAmount
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    private BigDecimal carbonamount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column F_Material.TransportCarbonAmount
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    private BigDecimal transportcarbonamount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column F_Material.Scope
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    private String scope;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column F_Material.TransportScope
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    private String transportscope;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column F_Material.CalculateDate
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    private LocalDate calculatedate;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column F_Material.Region
     *
     * @return the value of F_Material.Region
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public String getRegion() {
        return region;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column F_Material.Region
     *
     * @param region the value for F_Material.Region
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void setRegion(String region) {
        this.region = region == null ? null : region.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column F_Material.SiteId
     *
     * @return the value of F_Material.SiteId
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public Integer getSiteid() {
        return siteid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column F_Material.SiteId
     *
     * @param siteid the value for F_Material.SiteId
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void setSiteid(Integer siteid) {
        this.siteid = siteid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column F_Material.SiteName
     *
     * @return the value of F_Material.SiteName
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public String getSitename() {
        return sitename;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column F_Material.SiteName
     *
     * @param sitename the value for F_Material.SiteName
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void setSitename(String sitename) {
        this.sitename = sitename == null ? null : sitename.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column F_Material.RecordYearMonth
     *
     * @return the value of F_Material.RecordYearMonth
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public Integer getRecordyearmonth() {
        return recordyearmonth;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column F_Material.RecordYearMonth
     *
     * @param recordyearmonth the value for F_Material.RecordYearMonth
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void setRecordyearmonth(Integer recordyearmonth) {
        this.recordyearmonth = recordyearmonth;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column F_Material.CarbonEmissionLocation
     *
     * @return the value of F_Material.CarbonEmissionLocation
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public String getCarbonemissionlocation() {
        return carbonemissionlocation;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column F_Material.CarbonEmissionLocation
     *
     * @param carbonemissionlocation the value for F_Material.CarbonEmissionLocation
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void setCarbonemissionlocation(String carbonemissionlocation) {
        this.carbonemissionlocation = carbonemissionlocation == null ? null : carbonemissionlocation.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column F_Material.MaterialCode
     *
     * @return the value of F_Material.MaterialCode
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public String getMaterialcode() {
        return materialcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column F_Material.MaterialCode
     *
     * @param materialcode the value for F_Material.MaterialCode
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void setMaterialcode(String materialcode) {
        this.materialcode = materialcode == null ? null : materialcode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column F_Material.ChineseName
     *
     * @return the value of F_Material.ChineseName
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public String getChinesename() {
        return chinesename;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column F_Material.ChineseName
     *
     * @param chinesename the value for F_Material.ChineseName
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void setChinesename(String chinesename) {
        this.chinesename = chinesename == null ? null : chinesename.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column F_Material.Unit
     *
     * @return the value of F_Material.Unit
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public String getUnit() {
        return unit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column F_Material.Unit
     *
     * @param unit the value for F_Material.Unit
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column F_Material.Description
     *
     * @return the value of F_Material.Description
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column F_Material.Description
     *
     * @param description the value for F_Material.Description
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column F_Material.CarbonFactor
     *
     * @return the value of F_Material.CarbonFactor
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public BigDecimal getCarbonfactor() {
        return carbonfactor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column F_Material.CarbonFactor
     *
     * @param carbonfactor the value for F_Material.CarbonFactor
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void setCarbonfactor(BigDecimal carbonfactor) {
        this.carbonfactor = carbonfactor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column F_Material.CarbonFactorUnit
     *
     * @return the value of F_Material.CarbonFactorUnit
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public String getCarbonfactorunit() {
        return carbonfactorunit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column F_Material.CarbonFactorUnit
     *
     * @param carbonfactorunit the value for F_Material.CarbonFactorUnit
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void setCarbonfactorunit(String carbonfactorunit) {
        this.carbonfactorunit = carbonfactorunit == null ? null : carbonfactorunit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column F_Material.TransportFactor
     *
     * @return the value of F_Material.TransportFactor
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public BigDecimal getTransportfactor() {
        return transportfactor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column F_Material.TransportFactor
     *
     * @param transportfactor the value for F_Material.TransportFactor
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void setTransportfactor(BigDecimal transportfactor) {
        this.transportfactor = transportfactor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column F_Material.TransportFactorUnit
     *
     * @return the value of F_Material.TransportFactorUnit
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public String getTransportfactorunit() {
        return transportfactorunit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column F_Material.TransportFactorUnit
     *
     * @param transportfactorunit the value for F_Material.TransportFactorUnit
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void setTransportfactorunit(String transportfactorunit) {
        this.transportfactorunit = transportfactorunit == null ? null : transportfactorunit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column F_Material.TransportDistance
     *
     * @return the value of F_Material.TransportDistance
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public BigDecimal getTransportdistance() {
        return transportdistance;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column F_Material.TransportDistance
     *
     * @param transportdistance the value for F_Material.TransportDistance
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void setTransportdistance(BigDecimal transportdistance) {
        this.transportdistance = transportdistance;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column F_Material.TransportDistanceUnit
     *
     * @return the value of F_Material.TransportDistanceUnit
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public String getTransportdistanceunit() {
        return transportdistanceunit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column F_Material.TransportDistanceUnit
     *
     * @param transportdistanceunit the value for F_Material.TransportDistanceUnit
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void setTransportdistanceunit(String transportdistanceunit) {
        this.transportdistanceunit = transportdistanceunit == null ? null : transportdistanceunit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column F_Material.Qty
     *
     * @return the value of F_Material.Qty
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public BigDecimal getQty() {
        return qty;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column F_Material.Qty
     *
     * @param qty the value for F_Material.Qty
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column F_Material.CarbonAmount
     *
     * @return the value of F_Material.CarbonAmount
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public BigDecimal getCarbonamount() {
        return carbonamount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column F_Material.CarbonAmount
     *
     * @param carbonamount the value for F_Material.CarbonAmount
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void setCarbonamount(BigDecimal carbonamount) {
        this.carbonamount = carbonamount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column F_Material.TransportCarbonAmount
     *
     * @return the value of F_Material.TransportCarbonAmount
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public BigDecimal getTransportcarbonamount() {
        return transportcarbonamount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column F_Material.TransportCarbonAmount
     *
     * @param transportcarbonamount the value for F_Material.TransportCarbonAmount
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void setTransportcarbonamount(BigDecimal transportcarbonamount) {
        this.transportcarbonamount = transportcarbonamount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column F_Material.Scope
     *
     * @return the value of F_Material.Scope
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public String getScope() {
        return scope;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column F_Material.Scope
     *
     * @param scope the value for F_Material.Scope
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void setScope(String scope) {
        this.scope = scope == null ? null : scope.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column F_Material.TransportScope
     *
     * @return the value of F_Material.TransportScope
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public String getTransportscope() {
        return transportscope;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column F_Material.TransportScope
     *
     * @param transportscope the value for F_Material.TransportScope
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void setTransportscope(String transportscope) {
        this.transportscope = transportscope == null ? null : transportscope.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column F_Material.CalculateDate
     *
     * @return the value of F_Material.CalculateDate
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public LocalDate getCalculatedate() {
        return calculatedate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column F_Material.CalculateDate
     *
     * @param calculatedate the value for F_Material.CalculateDate
     *
     * @mbg.generated Wed Oct 19 17:12:17 HKT 2022
     */
    public void setCalculatedate(LocalDate calculatedate) {
        this.calculatedate = calculatedate;
    }
}