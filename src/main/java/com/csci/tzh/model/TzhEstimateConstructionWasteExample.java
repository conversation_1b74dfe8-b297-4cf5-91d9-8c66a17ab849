package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class TzhEstimateConstructionWasteExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_EstimateConstructionWaste
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_EstimateConstructionWaste
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_EstimateConstructionWaste
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EstimateConstructionWaste
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public TzhEstimateConstructionWasteExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EstimateConstructionWaste
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EstimateConstructionWaste
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EstimateConstructionWaste
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EstimateConstructionWaste
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EstimateConstructionWaste
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EstimateConstructionWaste
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EstimateConstructionWaste
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EstimateConstructionWaste
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EstimateConstructionWaste
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_EstimateConstructionWaste
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table Tzh_EstimateConstructionWaste
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("Id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("Id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(String value) {
			addCriterion("Id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(String value) {
			addCriterion("Id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(String value) {
			addCriterion("Id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(String value) {
			addCriterion("Id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(String value) {
			addCriterion("Id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(String value) {
			addCriterion("Id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLike(String value) {
			addCriterion("Id like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotLike(String value) {
			addCriterion("Id not like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<String> values) {
			addCriterion("Id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<String> values) {
			addCriterion("Id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(String value1, String value2) {
			addCriterion("Id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(String value1, String value2) {
			addCriterion("Id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andSitenameIsNull() {
			addCriterion("SiteName is null");
			return (Criteria) this;
		}

		public Criteria andSitenameIsNotNull() {
			addCriterion("SiteName is not null");
			return (Criteria) this;
		}

		public Criteria andSitenameEqualTo(String value) {
			addCriterion("SiteName =", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotEqualTo(String value) {
			addCriterion("SiteName <>", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameGreaterThan(String value) {
			addCriterion("SiteName >", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameGreaterThanOrEqualTo(String value) {
			addCriterion("SiteName >=", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameLessThan(String value) {
			addCriterion("SiteName <", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameLessThanOrEqualTo(String value) {
			addCriterion("SiteName <=", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameLike(String value) {
			addCriterion("SiteName like", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotLike(String value) {
			addCriterion("SiteName not like", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameIn(List<String> values) {
			addCriterion("SiteName in", values, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotIn(List<String> values) {
			addCriterion("SiteName not in", values, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameBetween(String value1, String value2) {
			addCriterion("SiteName between", value1, value2, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotBetween(String value1, String value2) {
			addCriterion("SiteName not between", value1, value2, "sitename");
			return (Criteria) this;
		}

		public Criteria andProtocolidIsNull() {
			addCriterion("ProtocolId is null");
			return (Criteria) this;
		}

		public Criteria andProtocolidIsNotNull() {
			addCriterion("ProtocolId is not null");
			return (Criteria) this;
		}

		public Criteria andProtocolidEqualTo(String value) {
			addCriterion("ProtocolId =", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidNotEqualTo(String value) {
			addCriterion("ProtocolId <>", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidGreaterThan(String value) {
			addCriterion("ProtocolId >", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidGreaterThanOrEqualTo(String value) {
			addCriterion("ProtocolId >=", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidLessThan(String value) {
			addCriterion("ProtocolId <", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidLessThanOrEqualTo(String value) {
			addCriterion("ProtocolId <=", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidLike(String value) {
			addCriterion("ProtocolId like", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidNotLike(String value) {
			addCriterion("ProtocolId not like", value, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidIn(List<String> values) {
			addCriterion("ProtocolId in", values, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidNotIn(List<String> values) {
			addCriterion("ProtocolId not in", values, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidBetween(String value1, String value2) {
			addCriterion("ProtocolId between", value1, value2, "protocolid");
			return (Criteria) this;
		}

		public Criteria andProtocolidNotBetween(String value1, String value2) {
			addCriterion("ProtocolId not between", value1, value2, "protocolid");
			return (Criteria) this;
		}

		public Criteria andSubcategoryidIsNull() {
			addCriterion("SubCategoryId is null");
			return (Criteria) this;
		}

		public Criteria andSubcategoryidIsNotNull() {
			addCriterion("SubCategoryId is not null");
			return (Criteria) this;
		}

		public Criteria andSubcategoryidEqualTo(String value) {
			addCriterion("SubCategoryId =", value, "subcategoryid");
			return (Criteria) this;
		}

		public Criteria andSubcategoryidNotEqualTo(String value) {
			addCriterion("SubCategoryId <>", value, "subcategoryid");
			return (Criteria) this;
		}

		public Criteria andSubcategoryidGreaterThan(String value) {
			addCriterion("SubCategoryId >", value, "subcategoryid");
			return (Criteria) this;
		}

		public Criteria andSubcategoryidGreaterThanOrEqualTo(String value) {
			addCriterion("SubCategoryId >=", value, "subcategoryid");
			return (Criteria) this;
		}

		public Criteria andSubcategoryidLessThan(String value) {
			addCriterion("SubCategoryId <", value, "subcategoryid");
			return (Criteria) this;
		}

		public Criteria andSubcategoryidLessThanOrEqualTo(String value) {
			addCriterion("SubCategoryId <=", value, "subcategoryid");
			return (Criteria) this;
		}

		public Criteria andSubcategoryidLike(String value) {
			addCriterion("SubCategoryId like", value, "subcategoryid");
			return (Criteria) this;
		}

		public Criteria andSubcategoryidNotLike(String value) {
			addCriterion("SubCategoryId not like", value, "subcategoryid");
			return (Criteria) this;
		}

		public Criteria andSubcategoryidIn(List<String> values) {
			addCriterion("SubCategoryId in", values, "subcategoryid");
			return (Criteria) this;
		}

		public Criteria andSubcategoryidNotIn(List<String> values) {
			addCriterion("SubCategoryId not in", values, "subcategoryid");
			return (Criteria) this;
		}

		public Criteria andSubcategoryidBetween(String value1, String value2) {
			addCriterion("SubCategoryId between", value1, value2, "subcategoryid");
			return (Criteria) this;
		}

		public Criteria andSubcategoryidNotBetween(String value1, String value2) {
			addCriterion("SubCategoryId not between", value1, value2, "subcategoryid");
			return (Criteria) this;
		}

		public Criteria andDisposalmethodidIsNull() {
			addCriterion("DisposalMethodId is null");
			return (Criteria) this;
		}

		public Criteria andDisposalmethodidIsNotNull() {
			addCriterion("DisposalMethodId is not null");
			return (Criteria) this;
		}

		public Criteria andDisposalmethodidEqualTo(String value) {
			addCriterion("DisposalMethodId =", value, "disposalmethodid");
			return (Criteria) this;
		}

		public Criteria andDisposalmethodidNotEqualTo(String value) {
			addCriterion("DisposalMethodId <>", value, "disposalmethodid");
			return (Criteria) this;
		}

		public Criteria andDisposalmethodidGreaterThan(String value) {
			addCriterion("DisposalMethodId >", value, "disposalmethodid");
			return (Criteria) this;
		}

		public Criteria andDisposalmethodidGreaterThanOrEqualTo(String value) {
			addCriterion("DisposalMethodId >=", value, "disposalmethodid");
			return (Criteria) this;
		}

		public Criteria andDisposalmethodidLessThan(String value) {
			addCriterion("DisposalMethodId <", value, "disposalmethodid");
			return (Criteria) this;
		}

		public Criteria andDisposalmethodidLessThanOrEqualTo(String value) {
			addCriterion("DisposalMethodId <=", value, "disposalmethodid");
			return (Criteria) this;
		}

		public Criteria andDisposalmethodidLike(String value) {
			addCriterion("DisposalMethodId like", value, "disposalmethodid");
			return (Criteria) this;
		}

		public Criteria andDisposalmethodidNotLike(String value) {
			addCriterion("DisposalMethodId not like", value, "disposalmethodid");
			return (Criteria) this;
		}

		public Criteria andDisposalmethodidIn(List<String> values) {
			addCriterion("DisposalMethodId in", values, "disposalmethodid");
			return (Criteria) this;
		}

		public Criteria andDisposalmethodidNotIn(List<String> values) {
			addCriterion("DisposalMethodId not in", values, "disposalmethodid");
			return (Criteria) this;
		}

		public Criteria andDisposalmethodidBetween(String value1, String value2) {
			addCriterion("DisposalMethodId between", value1, value2, "disposalmethodid");
			return (Criteria) this;
		}

		public Criteria andDisposalmethodidNotBetween(String value1, String value2) {
			addCriterion("DisposalMethodId not between", value1, value2, "disposalmethodid");
			return (Criteria) this;
		}

		public Criteria andQtyIsNull() {
			addCriterion("Qty is null");
			return (Criteria) this;
		}

		public Criteria andQtyIsNotNull() {
			addCriterion("Qty is not null");
			return (Criteria) this;
		}

		public Criteria andQtyEqualTo(BigDecimal value) {
			addCriterion("Qty =", value, "qty");
			return (Criteria) this;
		}

		public Criteria andQtyNotEqualTo(BigDecimal value) {
			addCriterion("Qty <>", value, "qty");
			return (Criteria) this;
		}

		public Criteria andQtyGreaterThan(BigDecimal value) {
			addCriterion("Qty >", value, "qty");
			return (Criteria) this;
		}

		public Criteria andQtyGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("Qty >=", value, "qty");
			return (Criteria) this;
		}

		public Criteria andQtyLessThan(BigDecimal value) {
			addCriterion("Qty <", value, "qty");
			return (Criteria) this;
		}

		public Criteria andQtyLessThanOrEqualTo(BigDecimal value) {
			addCriterion("Qty <=", value, "qty");
			return (Criteria) this;
		}

		public Criteria andQtyIn(List<BigDecimal> values) {
			addCriterion("Qty in", values, "qty");
			return (Criteria) this;
		}

		public Criteria andQtyNotIn(List<BigDecimal> values) {
			addCriterion("Qty not in", values, "qty");
			return (Criteria) this;
		}

		public Criteria andQtyBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("Qty between", value1, value2, "qty");
			return (Criteria) this;
		}

		public Criteria andQtyNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("Qty not between", value1, value2, "qty");
			return (Criteria) this;
		}

		public Criteria andQtyunitIsNull() {
			addCriterion("QtyUnit is null");
			return (Criteria) this;
		}

		public Criteria andQtyunitIsNotNull() {
			addCriterion("QtyUnit is not null");
			return (Criteria) this;
		}

		public Criteria andQtyunitEqualTo(String value) {
			addCriterion("QtyUnit =", value, "qtyunit");
			return (Criteria) this;
		}

		public Criteria andQtyunitNotEqualTo(String value) {
			addCriterion("QtyUnit <>", value, "qtyunit");
			return (Criteria) this;
		}

		public Criteria andQtyunitGreaterThan(String value) {
			addCriterion("QtyUnit >", value, "qtyunit");
			return (Criteria) this;
		}

		public Criteria andQtyunitGreaterThanOrEqualTo(String value) {
			addCriterion("QtyUnit >=", value, "qtyunit");
			return (Criteria) this;
		}

		public Criteria andQtyunitLessThan(String value) {
			addCriterion("QtyUnit <", value, "qtyunit");
			return (Criteria) this;
		}

		public Criteria andQtyunitLessThanOrEqualTo(String value) {
			addCriterion("QtyUnit <=", value, "qtyunit");
			return (Criteria) this;
		}

		public Criteria andQtyunitLike(String value) {
			addCriterion("QtyUnit like", value, "qtyunit");
			return (Criteria) this;
		}

		public Criteria andQtyunitNotLike(String value) {
			addCriterion("QtyUnit not like", value, "qtyunit");
			return (Criteria) this;
		}

		public Criteria andQtyunitIn(List<String> values) {
			addCriterion("QtyUnit in", values, "qtyunit");
			return (Criteria) this;
		}

		public Criteria andQtyunitNotIn(List<String> values) {
			addCriterion("QtyUnit not in", values, "qtyunit");
			return (Criteria) this;
		}

		public Criteria andQtyunitBetween(String value1, String value2) {
			addCriterion("QtyUnit between", value1, value2, "qtyunit");
			return (Criteria) this;
		}

		public Criteria andQtyunitNotBetween(String value1, String value2) {
			addCriterion("QtyUnit not between", value1, value2, "qtyunit");
			return (Criteria) this;
		}

		public Criteria andTurnovercountIsNull() {
			addCriterion("TurnoverCount is null");
			return (Criteria) this;
		}

		public Criteria andTurnovercountIsNotNull() {
			addCriterion("TurnoverCount is not null");
			return (Criteria) this;
		}

		public Criteria andTurnovercountEqualTo(Integer value) {
			addCriterion("TurnoverCount =", value, "turnovercount");
			return (Criteria) this;
		}

		public Criteria andTurnovercountNotEqualTo(Integer value) {
			addCriterion("TurnoverCount <>", value, "turnovercount");
			return (Criteria) this;
		}

		public Criteria andTurnovercountGreaterThan(Integer value) {
			addCriterion("TurnoverCount >", value, "turnovercount");
			return (Criteria) this;
		}

		public Criteria andTurnovercountGreaterThanOrEqualTo(Integer value) {
			addCriterion("TurnoverCount >=", value, "turnovercount");
			return (Criteria) this;
		}

		public Criteria andTurnovercountLessThan(Integer value) {
			addCriterion("TurnoverCount <", value, "turnovercount");
			return (Criteria) this;
		}

		public Criteria andTurnovercountLessThanOrEqualTo(Integer value) {
			addCriterion("TurnoverCount <=", value, "turnovercount");
			return (Criteria) this;
		}

		public Criteria andTurnovercountIn(List<Integer> values) {
			addCriterion("TurnoverCount in", values, "turnovercount");
			return (Criteria) this;
		}

		public Criteria andTurnovercountNotIn(List<Integer> values) {
			addCriterion("TurnoverCount not in", values, "turnovercount");
			return (Criteria) this;
		}

		public Criteria andTurnovercountBetween(Integer value1, Integer value2) {
			addCriterion("TurnoverCount between", value1, value2, "turnovercount");
			return (Criteria) this;
		}

		public Criteria andTurnovercountNotBetween(Integer value1, Integer value2) {
			addCriterion("TurnoverCount not between", value1, value2, "turnovercount");
			return (Criteria) this;
		}

		public Criteria andTargetattritionrateIsNull() {
			addCriterion("TargetAttritionRate is null");
			return (Criteria) this;
		}

		public Criteria andTargetattritionrateIsNotNull() {
			addCriterion("TargetAttritionRate is not null");
			return (Criteria) this;
		}

		public Criteria andTargetattritionrateEqualTo(BigDecimal value) {
			addCriterion("TargetAttritionRate =", value, "targetattritionrate");
			return (Criteria) this;
		}

		public Criteria andTargetattritionrateNotEqualTo(BigDecimal value) {
			addCriterion("TargetAttritionRate <>", value, "targetattritionrate");
			return (Criteria) this;
		}

		public Criteria andTargetattritionrateGreaterThan(BigDecimal value) {
			addCriterion("TargetAttritionRate >", value, "targetattritionrate");
			return (Criteria) this;
		}

		public Criteria andTargetattritionrateGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("TargetAttritionRate >=", value, "targetattritionrate");
			return (Criteria) this;
		}

		public Criteria andTargetattritionrateLessThan(BigDecimal value) {
			addCriterion("TargetAttritionRate <", value, "targetattritionrate");
			return (Criteria) this;
		}

		public Criteria andTargetattritionrateLessThanOrEqualTo(BigDecimal value) {
			addCriterion("TargetAttritionRate <=", value, "targetattritionrate");
			return (Criteria) this;
		}

		public Criteria andTargetattritionrateIn(List<BigDecimal> values) {
			addCriterion("TargetAttritionRate in", values, "targetattritionrate");
			return (Criteria) this;
		}

		public Criteria andTargetattritionrateNotIn(List<BigDecimal> values) {
			addCriterion("TargetAttritionRate not in", values, "targetattritionrate");
			return (Criteria) this;
		}

		public Criteria andTargetattritionrateBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("TargetAttritionRate between", value1, value2, "targetattritionrate");
			return (Criteria) this;
		}

		public Criteria andTargetattritionrateNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("TargetAttritionRate not between", value1, value2, "targetattritionrate");
			return (Criteria) this;
		}

		public Criteria andWasteqtyIsNull() {
			addCriterion("WasteQty is null");
			return (Criteria) this;
		}

		public Criteria andWasteqtyIsNotNull() {
			addCriterion("WasteQty is not null");
			return (Criteria) this;
		}

		public Criteria andWasteqtyEqualTo(BigDecimal value) {
			addCriterion("WasteQty =", value, "wasteqty");
			return (Criteria) this;
		}

		public Criteria andWasteqtyNotEqualTo(BigDecimal value) {
			addCriterion("WasteQty <>", value, "wasteqty");
			return (Criteria) this;
		}

		public Criteria andWasteqtyGreaterThan(BigDecimal value) {
			addCriterion("WasteQty >", value, "wasteqty");
			return (Criteria) this;
		}

		public Criteria andWasteqtyGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("WasteQty >=", value, "wasteqty");
			return (Criteria) this;
		}

		public Criteria andWasteqtyLessThan(BigDecimal value) {
			addCriterion("WasteQty <", value, "wasteqty");
			return (Criteria) this;
		}

		public Criteria andWasteqtyLessThanOrEqualTo(BigDecimal value) {
			addCriterion("WasteQty <=", value, "wasteqty");
			return (Criteria) this;
		}

		public Criteria andWasteqtyIn(List<BigDecimal> values) {
			addCriterion("WasteQty in", values, "wasteqty");
			return (Criteria) this;
		}

		public Criteria andWasteqtyNotIn(List<BigDecimal> values) {
			addCriterion("WasteQty not in", values, "wasteqty");
			return (Criteria) this;
		}

		public Criteria andWasteqtyBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("WasteQty between", value1, value2, "wasteqty");
			return (Criteria) this;
		}

		public Criteria andWasteqtyNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("WasteQty not between", value1, value2, "wasteqty");
			return (Criteria) this;
		}

		public Criteria andWasteqtyunitIsNull() {
			addCriterion("WasteQtyUnit is null");
			return (Criteria) this;
		}

		public Criteria andWasteqtyunitIsNotNull() {
			addCriterion("WasteQtyUnit is not null");
			return (Criteria) this;
		}

		public Criteria andWasteqtyunitEqualTo(String value) {
			addCriterion("WasteQtyUnit =", value, "wasteqtyunit");
			return (Criteria) this;
		}

		public Criteria andWasteqtyunitNotEqualTo(String value) {
			addCriterion("WasteQtyUnit <>", value, "wasteqtyunit");
			return (Criteria) this;
		}

		public Criteria andWasteqtyunitGreaterThan(String value) {
			addCriterion("WasteQtyUnit >", value, "wasteqtyunit");
			return (Criteria) this;
		}

		public Criteria andWasteqtyunitGreaterThanOrEqualTo(String value) {
			addCriterion("WasteQtyUnit >=", value, "wasteqtyunit");
			return (Criteria) this;
		}

		public Criteria andWasteqtyunitLessThan(String value) {
			addCriterion("WasteQtyUnit <", value, "wasteqtyunit");
			return (Criteria) this;
		}

		public Criteria andWasteqtyunitLessThanOrEqualTo(String value) {
			addCriterion("WasteQtyUnit <=", value, "wasteqtyunit");
			return (Criteria) this;
		}

		public Criteria andWasteqtyunitLike(String value) {
			addCriterion("WasteQtyUnit like", value, "wasteqtyunit");
			return (Criteria) this;
		}

		public Criteria andWasteqtyunitNotLike(String value) {
			addCriterion("WasteQtyUnit not like", value, "wasteqtyunit");
			return (Criteria) this;
		}

		public Criteria andWasteqtyunitIn(List<String> values) {
			addCriterion("WasteQtyUnit in", values, "wasteqtyunit");
			return (Criteria) this;
		}

		public Criteria andWasteqtyunitNotIn(List<String> values) {
			addCriterion("WasteQtyUnit not in", values, "wasteqtyunit");
			return (Criteria) this;
		}

		public Criteria andWasteqtyunitBetween(String value1, String value2) {
			addCriterion("WasteQtyUnit between", value1, value2, "wasteqtyunit");
			return (Criteria) this;
		}

		public Criteria andWasteqtyunitNotBetween(String value1, String value2) {
			addCriterion("WasteQtyUnit not between", value1, value2, "wasteqtyunit");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIsNull() {
			addCriterion("CreatedBy is null");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIsNotNull() {
			addCriterion("CreatedBy is not null");
			return (Criteria) this;
		}

		public Criteria andCreatedbyEqualTo(String value) {
			addCriterion("CreatedBy =", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotEqualTo(String value) {
			addCriterion("CreatedBy <>", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyGreaterThan(String value) {
			addCriterion("CreatedBy >", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyGreaterThanOrEqualTo(String value) {
			addCriterion("CreatedBy >=", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLessThan(String value) {
			addCriterion("CreatedBy <", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLessThanOrEqualTo(String value) {
			addCriterion("CreatedBy <=", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLike(String value) {
			addCriterion("CreatedBy like", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotLike(String value) {
			addCriterion("CreatedBy not like", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIn(List<String> values) {
			addCriterion("CreatedBy in", values, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotIn(List<String> values) {
			addCriterion("CreatedBy not in", values, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyBetween(String value1, String value2) {
			addCriterion("CreatedBy between", value1, value2, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotBetween(String value1, String value2) {
			addCriterion("CreatedBy not between", value1, value2, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIsNull() {
			addCriterion("CreatedTime is null");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIsNotNull() {
			addCriterion("CreatedTime is not null");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime =", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime <>", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeGreaterThan(LocalDateTime value) {
			addCriterion("CreatedTime >", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime >=", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeLessThan(LocalDateTime value) {
			addCriterion("CreatedTime <", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime <=", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIn(List<LocalDateTime> values) {
			addCriterion("CreatedTime in", values, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotIn(List<LocalDateTime> values) {
			addCriterion("CreatedTime not in", values, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("CreatedTime between", value1, value2, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("CreatedTime not between", value1, value2, "createdtime");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIsNull() {
			addCriterion("DeletedBy is null");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIsNotNull() {
			addCriterion("DeletedBy is not null");
			return (Criteria) this;
		}

		public Criteria andDeletedbyEqualTo(String value) {
			addCriterion("DeletedBy =", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotEqualTo(String value) {
			addCriterion("DeletedBy <>", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyGreaterThan(String value) {
			addCriterion("DeletedBy >", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyGreaterThanOrEqualTo(String value) {
			addCriterion("DeletedBy >=", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLessThan(String value) {
			addCriterion("DeletedBy <", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLessThanOrEqualTo(String value) {
			addCriterion("DeletedBy <=", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLike(String value) {
			addCriterion("DeletedBy like", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotLike(String value) {
			addCriterion("DeletedBy not like", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIn(List<String> values) {
			addCriterion("DeletedBy in", values, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotIn(List<String> values) {
			addCriterion("DeletedBy not in", values, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyBetween(String value1, String value2) {
			addCriterion("DeletedBy between", value1, value2, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotBetween(String value1, String value2) {
			addCriterion("DeletedBy not between", value1, value2, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIsNull() {
			addCriterion("DeletedTime is null");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIsNotNull() {
			addCriterion("DeletedTime is not null");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime =", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime <>", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeGreaterThan(LocalDateTime value) {
			addCriterion("DeletedTime >", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime >=", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeLessThan(LocalDateTime value) {
			addCriterion("DeletedTime <", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime <=", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIn(List<LocalDateTime> values) {
			addCriterion("DeletedTime in", values, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotIn(List<LocalDateTime> values) {
			addCriterion("DeletedTime not in", values, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("DeletedTime between", value1, value2, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("DeletedTime not between", value1, value2, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIsNull() {
			addCriterion("IsDeleted is null");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIsNotNull() {
			addCriterion("IsDeleted is not null");
			return (Criteria) this;
		}

		public Criteria andIsdeletedEqualTo(Boolean value) {
			addCriterion("IsDeleted =", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotEqualTo(Boolean value) {
			addCriterion("IsDeleted <>", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedGreaterThan(Boolean value) {
			addCriterion("IsDeleted >", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedGreaterThanOrEqualTo(Boolean value) {
			addCriterion("IsDeleted >=", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedLessThan(Boolean value) {
			addCriterion("IsDeleted <", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedLessThanOrEqualTo(Boolean value) {
			addCriterion("IsDeleted <=", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIn(List<Boolean> values) {
			addCriterion("IsDeleted in", values, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotIn(List<Boolean> values) {
			addCriterion("IsDeleted not in", values, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedBetween(Boolean value1, Boolean value2) {
			addCriterion("IsDeleted between", value1, value2, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotBetween(Boolean value1, Boolean value2) {
			addCriterion("IsDeleted not between", value1, value2, "isdeleted");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table Tzh_EstimateConstructionWaste
	 * @mbg.generated  Wed May 24 14:29:47 HKT 2023
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table Tzh_EstimateConstructionWaste
     *
     * @mbg.generated do_not_delete_during_merge Wed May 17 11:59:30 HKT 2023
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}