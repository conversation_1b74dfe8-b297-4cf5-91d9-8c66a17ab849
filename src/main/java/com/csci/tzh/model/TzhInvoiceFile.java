package com.csci.tzh.model;

import java.time.LocalDateTime;

public class TzhInvoiceFile {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Invoice_File.Id
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Invoice_File.ParentId
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	private String parentid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Invoice_File.SiteName
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	private String sitename;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Invoice_File.RecordYearMonth
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	private Integer recordyearmonth;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Invoice_File.CarbonEmissionLocation
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	private String carbonemissionlocation;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Invoice_File.Section
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	private String section;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Invoice_File.MaterialName
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	private String materialname;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Invoice_File.MaterialCode
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	private String materialcode;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Invoice_File.MaterialAttribute
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	private String materialattribute;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Invoice_File.BillNo
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	private String billno;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Invoice_File.CreatedBy
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	private String createdby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Invoice_File.CreatedTime
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	private LocalDateTime createdtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Invoice_File.DeletedBy
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	private String deletedby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Invoice_File.DeletedTime
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	private LocalDateTime deletedtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Invoice_File.IsDeleted
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	private Boolean isdeleted;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Invoice_File.Pdf
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	private byte[] pdf;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Invoice_File.Id
	 * @return  the value of Tzh_Invoice_File.Id
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Invoice_File.Id
	 * @param id  the value for Tzh_Invoice_File.Id
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Invoice_File.ParentId
	 * @return  the value of Tzh_Invoice_File.ParentId
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public String getParentid() {
		return parentid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Invoice_File.ParentId
	 * @param parentid  the value for Tzh_Invoice_File.ParentId
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public void setParentid(String parentid) {
		this.parentid = parentid == null ? null : parentid.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Invoice_File.SiteName
	 * @return  the value of Tzh_Invoice_File.SiteName
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public String getSitename() {
		return sitename;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Invoice_File.SiteName
	 * @param sitename  the value for Tzh_Invoice_File.SiteName
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public void setSitename(String sitename) {
		this.sitename = sitename == null ? null : sitename.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Invoice_File.RecordYearMonth
	 * @return  the value of Tzh_Invoice_File.RecordYearMonth
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public Integer getRecordyearmonth() {
		return recordyearmonth;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Invoice_File.RecordYearMonth
	 * @param recordyearmonth  the value for Tzh_Invoice_File.RecordYearMonth
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public void setRecordyearmonth(Integer recordyearmonth) {
		this.recordyearmonth = recordyearmonth;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Invoice_File.CarbonEmissionLocation
	 * @return  the value of Tzh_Invoice_File.CarbonEmissionLocation
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public String getCarbonemissionlocation() {
		return carbonemissionlocation;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Invoice_File.CarbonEmissionLocation
	 * @param carbonemissionlocation  the value for Tzh_Invoice_File.CarbonEmissionLocation
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public void setCarbonemissionlocation(String carbonemissionlocation) {
		this.carbonemissionlocation = carbonemissionlocation == null ? null : carbonemissionlocation.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Invoice_File.Section
	 * @return  the value of Tzh_Invoice_File.Section
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public String getSection() {
		return section;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Invoice_File.Section
	 * @param section  the value for Tzh_Invoice_File.Section
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public void setSection(String section) {
		this.section = section == null ? null : section.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Invoice_File.MaterialName
	 * @return  the value of Tzh_Invoice_File.MaterialName
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public String getMaterialname() {
		return materialname;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Invoice_File.MaterialName
	 * @param materialname  the value for Tzh_Invoice_File.MaterialName
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public void setMaterialname(String materialname) {
		this.materialname = materialname == null ? null : materialname.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Invoice_File.MaterialCode
	 * @return  the value of Tzh_Invoice_File.MaterialCode
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public String getMaterialcode() {
		return materialcode;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Invoice_File.MaterialCode
	 * @param materialcode  the value for Tzh_Invoice_File.MaterialCode
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public void setMaterialcode(String materialcode) {
		this.materialcode = materialcode == null ? null : materialcode.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Invoice_File.MaterialAttribute
	 * @return  the value of Tzh_Invoice_File.MaterialAttribute
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public String getMaterialattribute() {
		return materialattribute;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Invoice_File.MaterialAttribute
	 * @param materialattribute  the value for Tzh_Invoice_File.MaterialAttribute
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public void setMaterialattribute(String materialattribute) {
		this.materialattribute = materialattribute == null ? null : materialattribute.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Invoice_File.BillNo
	 * @return  the value of Tzh_Invoice_File.BillNo
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public String getBillno() {
		return billno;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Invoice_File.BillNo
	 * @param billno  the value for Tzh_Invoice_File.BillNo
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public void setBillno(String billno) {
		this.billno = billno == null ? null : billno.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Invoice_File.CreatedBy
	 * @return  the value of Tzh_Invoice_File.CreatedBy
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public String getCreatedby() {
		return createdby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Invoice_File.CreatedBy
	 * @param createdby  the value for Tzh_Invoice_File.CreatedBy
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public void setCreatedby(String createdby) {
		this.createdby = createdby == null ? null : createdby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Invoice_File.CreatedTime
	 * @return  the value of Tzh_Invoice_File.CreatedTime
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public LocalDateTime getCreatedtime() {
		return createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Invoice_File.CreatedTime
	 * @param createdtime  the value for Tzh_Invoice_File.CreatedTime
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public void setCreatedtime(LocalDateTime createdtime) {
		this.createdtime = createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Invoice_File.DeletedBy
	 * @return  the value of Tzh_Invoice_File.DeletedBy
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public String getDeletedby() {
		return deletedby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Invoice_File.DeletedBy
	 * @param deletedby  the value for Tzh_Invoice_File.DeletedBy
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public void setDeletedby(String deletedby) {
		this.deletedby = deletedby == null ? null : deletedby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Invoice_File.DeletedTime
	 * @return  the value of Tzh_Invoice_File.DeletedTime
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public LocalDateTime getDeletedtime() {
		return deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Invoice_File.DeletedTime
	 * @param deletedtime  the value for Tzh_Invoice_File.DeletedTime
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public void setDeletedtime(LocalDateTime deletedtime) {
		this.deletedtime = deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Invoice_File.IsDeleted
	 * @return  the value of Tzh_Invoice_File.IsDeleted
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public Boolean getIsdeleted() {
		return isdeleted;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Invoice_File.IsDeleted
	 * @param isdeleted  the value for Tzh_Invoice_File.IsDeleted
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public void setIsdeleted(Boolean isdeleted) {
		this.isdeleted = isdeleted;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Invoice_File.Pdf
	 * @return  the value of Tzh_Invoice_File.Pdf
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public byte[] getPdf() {
		return pdf;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Invoice_File.Pdf
	 * @param pdf  the value for Tzh_Invoice_File.Pdf
	 * @mbg.generated  Thu Jan 12 14:23:06 HKT 2023
	 */
	public void setPdf(byte[] pdf) {
		this.pdf = pdf;
	}
}