package com.csci.tzh.model;

import java.time.LocalDateTime;

public class TzhCarbonEmissionLocation {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_CarbonEmissionLocation.Id
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_CarbonEmissionLocation.Name
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_CarbonEmissionLocation.NameSC
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    private String namesc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_CarbonEmissionLocation.NameEN
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    private String nameen;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_CarbonEmissionLocation.Description
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_CarbonEmissionLocation.creation_time
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    private LocalDateTime creationTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_CarbonEmissionLocation.create_username
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    private String createUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_CarbonEmissionLocation.create_user_id
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    private String createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_CarbonEmissionLocation.last_update_time
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    private LocalDateTime lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_CarbonEmissionLocation.last_update_username
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    private String lastUpdateUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_CarbonEmissionLocation.last_update_user_id
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    private String lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_CarbonEmissionLocation.last_update_version
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    private Integer lastUpdateVersion;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_CarbonEmissionLocation.is_deleted
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    private Boolean isDeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_CarbonEmissionLocation.Id
     *
     * @return the value of Tzh_CarbonEmissionLocation.Id
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_CarbonEmissionLocation.Id
     *
     * @param id the value for Tzh_CarbonEmissionLocation.Id
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_CarbonEmissionLocation.Name
     *
     * @return the value of Tzh_CarbonEmissionLocation.Name
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_CarbonEmissionLocation.Name
     *
     * @param name the value for Tzh_CarbonEmissionLocation.Name
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_CarbonEmissionLocation.NameSC
     *
     * @return the value of Tzh_CarbonEmissionLocation.NameSC
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public String getNamesc() {
        return namesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_CarbonEmissionLocation.NameSC
     *
     * @param namesc the value for Tzh_CarbonEmissionLocation.NameSC
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public void setNamesc(String namesc) {
        this.namesc = namesc == null ? null : namesc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_CarbonEmissionLocation.NameEN
     *
     * @return the value of Tzh_CarbonEmissionLocation.NameEN
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public String getNameen() {
        return nameen;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_CarbonEmissionLocation.NameEN
     *
     * @param nameen the value for Tzh_CarbonEmissionLocation.NameEN
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public void setNameen(String nameen) {
        this.nameen = nameen == null ? null : nameen.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_CarbonEmissionLocation.Description
     *
     * @return the value of Tzh_CarbonEmissionLocation.Description
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_CarbonEmissionLocation.Description
     *
     * @param description the value for Tzh_CarbonEmissionLocation.Description
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_CarbonEmissionLocation.creation_time
     *
     * @return the value of Tzh_CarbonEmissionLocation.creation_time
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public LocalDateTime getCreationTime() {
        return creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_CarbonEmissionLocation.creation_time
     *
     * @param creationTime the value for Tzh_CarbonEmissionLocation.creation_time
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public void setCreationTime(LocalDateTime creationTime) {
        this.creationTime = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_CarbonEmissionLocation.create_username
     *
     * @return the value of Tzh_CarbonEmissionLocation.create_username
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public String getCreateUsername() {
        return createUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_CarbonEmissionLocation.create_username
     *
     * @param createUsername the value for Tzh_CarbonEmissionLocation.create_username
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public void setCreateUsername(String createUsername) {
        this.createUsername = createUsername == null ? null : createUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_CarbonEmissionLocation.create_user_id
     *
     * @return the value of Tzh_CarbonEmissionLocation.create_user_id
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_CarbonEmissionLocation.create_user_id
     *
     * @param createUserId the value for Tzh_CarbonEmissionLocation.create_user_id
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId == null ? null : createUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_CarbonEmissionLocation.last_update_time
     *
     * @return the value of Tzh_CarbonEmissionLocation.last_update_time
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_CarbonEmissionLocation.last_update_time
     *
     * @param lastUpdateTime the value for Tzh_CarbonEmissionLocation.last_update_time
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_CarbonEmissionLocation.last_update_username
     *
     * @return the value of Tzh_CarbonEmissionLocation.last_update_username
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public String getLastUpdateUsername() {
        return lastUpdateUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_CarbonEmissionLocation.last_update_username
     *
     * @param lastUpdateUsername the value for Tzh_CarbonEmissionLocation.last_update_username
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public void setLastUpdateUsername(String lastUpdateUsername) {
        this.lastUpdateUsername = lastUpdateUsername == null ? null : lastUpdateUsername.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_CarbonEmissionLocation.last_update_user_id
     *
     * @return the value of Tzh_CarbonEmissionLocation.last_update_user_id
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_CarbonEmissionLocation.last_update_user_id
     *
     * @param lastUpdateUserId the value for Tzh_CarbonEmissionLocation.last_update_user_id
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId == null ? null : lastUpdateUserId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_CarbonEmissionLocation.last_update_version
     *
     * @return the value of Tzh_CarbonEmissionLocation.last_update_version
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public Integer getLastUpdateVersion() {
        return lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_CarbonEmissionLocation.last_update_version
     *
     * @param lastUpdateVersion the value for Tzh_CarbonEmissionLocation.last_update_version
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public void setLastUpdateVersion(Integer lastUpdateVersion) {
        this.lastUpdateVersion = lastUpdateVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_CarbonEmissionLocation.is_deleted
     *
     * @return the value of Tzh_CarbonEmissionLocation.is_deleted
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public Boolean getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_CarbonEmissionLocation.is_deleted
     *
     * @param isDeleted the value for Tzh_CarbonEmissionLocation.is_deleted
     *
     * @mbg.generated Tue Apr 09 15:40:43 HKT 2024
     */
    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }
}