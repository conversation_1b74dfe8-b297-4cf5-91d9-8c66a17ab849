package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class TzhOrgMaterialCarbonFactorW50 {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_W50.Id
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_W50.ChineseName
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    private String chinesename;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_W50.UpstreamEmission
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    private BigDecimal upstreamemission;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_W50.DownstreamEmission
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    private BigDecimal downstreamemission;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_W50.EmissionUnit
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    private String emissionunit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_W50.EmissionStage
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    private String emissionstage;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_W50.Uncertainty
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    private String uncertainty;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_W50.Others
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    private String others;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_W50.DataTime
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    private String datatime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_W50.Datasource
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    private String datasource;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_W50.CreatedBy
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    private String createdby;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_W50.CreatedTime
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    private LocalDateTime createdtime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_W50.DeletedBy
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    private String deletedby;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_W50.DeletedTime
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    private LocalDateTime deletedtime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column Tzh_Org_MaterialCarbonFactor_W50.IsDeleted
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    private Boolean isdeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_W50.Id
     *
     * @return the value of Tzh_Org_MaterialCarbonFactor_W50.Id
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_W50.Id
     *
     * @param id the value for Tzh_Org_MaterialCarbonFactor_W50.Id
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_W50.ChineseName
     *
     * @return the value of Tzh_Org_MaterialCarbonFactor_W50.ChineseName
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public String getChinesename() {
        return chinesename;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_W50.ChineseName
     *
     * @param chinesename the value for Tzh_Org_MaterialCarbonFactor_W50.ChineseName
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public void setChinesename(String chinesename) {
        this.chinesename = chinesename == null ? null : chinesename.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_W50.UpstreamEmission
     *
     * @return the value of Tzh_Org_MaterialCarbonFactor_W50.UpstreamEmission
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public BigDecimal getUpstreamemission() {
        return upstreamemission;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_W50.UpstreamEmission
     *
     * @param upstreamemission the value for Tzh_Org_MaterialCarbonFactor_W50.UpstreamEmission
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public void setUpstreamemission(BigDecimal upstreamemission) {
        this.upstreamemission = upstreamemission;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_W50.DownstreamEmission
     *
     * @return the value of Tzh_Org_MaterialCarbonFactor_W50.DownstreamEmission
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public BigDecimal getDownstreamemission() {
        return downstreamemission;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_W50.DownstreamEmission
     *
     * @param downstreamemission the value for Tzh_Org_MaterialCarbonFactor_W50.DownstreamEmission
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public void setDownstreamemission(BigDecimal downstreamemission) {
        this.downstreamemission = downstreamemission;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_W50.EmissionUnit
     *
     * @return the value of Tzh_Org_MaterialCarbonFactor_W50.EmissionUnit
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public String getEmissionunit() {
        return emissionunit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_W50.EmissionUnit
     *
     * @param emissionunit the value for Tzh_Org_MaterialCarbonFactor_W50.EmissionUnit
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public void setEmissionunit(String emissionunit) {
        this.emissionunit = emissionunit == null ? null : emissionunit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_W50.EmissionStage
     *
     * @return the value of Tzh_Org_MaterialCarbonFactor_W50.EmissionStage
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public String getEmissionstage() {
        return emissionstage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_W50.EmissionStage
     *
     * @param emissionstage the value for Tzh_Org_MaterialCarbonFactor_W50.EmissionStage
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public void setEmissionstage(String emissionstage) {
        this.emissionstage = emissionstage == null ? null : emissionstage.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_W50.Uncertainty
     *
     * @return the value of Tzh_Org_MaterialCarbonFactor_W50.Uncertainty
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public String getUncertainty() {
        return uncertainty;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_W50.Uncertainty
     *
     * @param uncertainty the value for Tzh_Org_MaterialCarbonFactor_W50.Uncertainty
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public void setUncertainty(String uncertainty) {
        this.uncertainty = uncertainty == null ? null : uncertainty.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_W50.Others
     *
     * @return the value of Tzh_Org_MaterialCarbonFactor_W50.Others
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public String getOthers() {
        return others;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_W50.Others
     *
     * @param others the value for Tzh_Org_MaterialCarbonFactor_W50.Others
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public void setOthers(String others) {
        this.others = others == null ? null : others.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_W50.DataTime
     *
     * @return the value of Tzh_Org_MaterialCarbonFactor_W50.DataTime
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public String getDatatime() {
        return datatime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_W50.DataTime
     *
     * @param datatime the value for Tzh_Org_MaterialCarbonFactor_W50.DataTime
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public void setDatatime(String datatime) {
        this.datatime = datatime == null ? null : datatime.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_W50.Datasource
     *
     * @return the value of Tzh_Org_MaterialCarbonFactor_W50.Datasource
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public String getDatasource() {
        return datasource;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_W50.Datasource
     *
     * @param datasource the value for Tzh_Org_MaterialCarbonFactor_W50.Datasource
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public void setDatasource(String datasource) {
        this.datasource = datasource == null ? null : datasource.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_W50.CreatedBy
     *
     * @return the value of Tzh_Org_MaterialCarbonFactor_W50.CreatedBy
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public String getCreatedby() {
        return createdby;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_W50.CreatedBy
     *
     * @param createdby the value for Tzh_Org_MaterialCarbonFactor_W50.CreatedBy
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public void setCreatedby(String createdby) {
        this.createdby = createdby == null ? null : createdby.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_W50.CreatedTime
     *
     * @return the value of Tzh_Org_MaterialCarbonFactor_W50.CreatedTime
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public LocalDateTime getCreatedtime() {
        return createdtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_W50.CreatedTime
     *
     * @param createdtime the value for Tzh_Org_MaterialCarbonFactor_W50.CreatedTime
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public void setCreatedtime(LocalDateTime createdtime) {
        this.createdtime = createdtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_W50.DeletedBy
     *
     * @return the value of Tzh_Org_MaterialCarbonFactor_W50.DeletedBy
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public String getDeletedby() {
        return deletedby;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_W50.DeletedBy
     *
     * @param deletedby the value for Tzh_Org_MaterialCarbonFactor_W50.DeletedBy
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public void setDeletedby(String deletedby) {
        this.deletedby = deletedby == null ? null : deletedby.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_W50.DeletedTime
     *
     * @return the value of Tzh_Org_MaterialCarbonFactor_W50.DeletedTime
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public LocalDateTime getDeletedtime() {
        return deletedtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_W50.DeletedTime
     *
     * @param deletedtime the value for Tzh_Org_MaterialCarbonFactor_W50.DeletedTime
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public void setDeletedtime(LocalDateTime deletedtime) {
        this.deletedtime = deletedtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column Tzh_Org_MaterialCarbonFactor_W50.IsDeleted
     *
     * @return the value of Tzh_Org_MaterialCarbonFactor_W50.IsDeleted
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public Boolean getIsdeleted() {
        return isdeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column Tzh_Org_MaterialCarbonFactor_W50.IsDeleted
     *
     * @param isdeleted the value for Tzh_Org_MaterialCarbonFactor_W50.IsDeleted
     *
     * @mbg.generated Fri Aug 11 15:13:14 HKT 2023
     */
    public void setIsdeleted(Boolean isdeleted) {
        this.isdeleted = isdeleted;
    }
}