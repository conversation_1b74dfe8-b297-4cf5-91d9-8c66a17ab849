package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class TzhEmissionReduction {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReduction.Id
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReduction.HeadId
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	private String headid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReduction.Type
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	private String type;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReduction.RecordYearMonth
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	private Integer recordyearmonth;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReduction.CarbonReductionAmount
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	private BigDecimal carbonreductionamount;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReduction.CarbonUnit
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	private String carbonunit;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReduction.CreatedTime
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	private LocalDateTime createdtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReduction.CreatedBy
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	private String createdby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReduction.DeletedTime
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	private LocalDateTime deletedtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReduction.DeletedBy
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	private String deletedby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_EmissionReduction.IsDeleted
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	private Boolean isdeleted;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReduction.Id
	 * @return  the value of Tzh_EmissionReduction.Id
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReduction.Id
	 * @param id  the value for Tzh_EmissionReduction.Id
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReduction.HeadId
	 * @return  the value of Tzh_EmissionReduction.HeadId
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	public String getHeadid() {
		return headid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReduction.HeadId
	 * @param headid  the value for Tzh_EmissionReduction.HeadId
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	public void setHeadid(String headid) {
		this.headid = headid == null ? null : headid.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReduction.Type
	 * @return  the value of Tzh_EmissionReduction.Type
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	public String getType() {
		return type;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReduction.Type
	 * @param type  the value for Tzh_EmissionReduction.Type
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	public void setType(String type) {
		this.type = type == null ? null : type.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReduction.RecordYearMonth
	 * @return  the value of Tzh_EmissionReduction.RecordYearMonth
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	public Integer getRecordyearmonth() {
		return recordyearmonth;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReduction.RecordYearMonth
	 * @param recordyearmonth  the value for Tzh_EmissionReduction.RecordYearMonth
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	public void setRecordyearmonth(Integer recordyearmonth) {
		this.recordyearmonth = recordyearmonth;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReduction.CarbonReductionAmount
	 * @return  the value of Tzh_EmissionReduction.CarbonReductionAmount
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	public BigDecimal getCarbonreductionamount() {
		return carbonreductionamount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReduction.CarbonReductionAmount
	 * @param carbonreductionamount  the value for Tzh_EmissionReduction.CarbonReductionAmount
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	public void setCarbonreductionamount(BigDecimal carbonreductionamount) {
		this.carbonreductionamount = carbonreductionamount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReduction.CarbonUnit
	 * @return  the value of Tzh_EmissionReduction.CarbonUnit
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	public String getCarbonunit() {
		return carbonunit;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReduction.CarbonUnit
	 * @param carbonunit  the value for Tzh_EmissionReduction.CarbonUnit
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	public void setCarbonunit(String carbonunit) {
		this.carbonunit = carbonunit == null ? null : carbonunit.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReduction.CreatedTime
	 * @return  the value of Tzh_EmissionReduction.CreatedTime
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	public LocalDateTime getCreatedtime() {
		return createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReduction.CreatedTime
	 * @param createdtime  the value for Tzh_EmissionReduction.CreatedTime
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	public void setCreatedtime(LocalDateTime createdtime) {
		this.createdtime = createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReduction.CreatedBy
	 * @return  the value of Tzh_EmissionReduction.CreatedBy
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	public String getCreatedby() {
		return createdby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReduction.CreatedBy
	 * @param createdby  the value for Tzh_EmissionReduction.CreatedBy
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	public void setCreatedby(String createdby) {
		this.createdby = createdby == null ? null : createdby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReduction.DeletedTime
	 * @return  the value of Tzh_EmissionReduction.DeletedTime
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	public LocalDateTime getDeletedtime() {
		return deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReduction.DeletedTime
	 * @param deletedtime  the value for Tzh_EmissionReduction.DeletedTime
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	public void setDeletedtime(LocalDateTime deletedtime) {
		this.deletedtime = deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReduction.DeletedBy
	 * @return  the value of Tzh_EmissionReduction.DeletedBy
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	public String getDeletedby() {
		return deletedby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReduction.DeletedBy
	 * @param deletedby  the value for Tzh_EmissionReduction.DeletedBy
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	public void setDeletedby(String deletedby) {
		this.deletedby = deletedby == null ? null : deletedby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_EmissionReduction.IsDeleted
	 * @return  the value of Tzh_EmissionReduction.IsDeleted
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	public Boolean getIsdeleted() {
		return isdeleted;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_EmissionReduction.IsDeleted
	 * @param isdeleted  the value for Tzh_EmissionReduction.IsDeleted
	 * @mbg.generated  Mon May 22 15:51:12 HKT 2023
	 */
	public void setIsdeleted(Boolean isdeleted) {
		this.isdeleted = isdeleted;
	}
}