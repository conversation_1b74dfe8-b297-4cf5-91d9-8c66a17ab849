package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class TzhExpectedEmissionIso {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_ExpectedEmissionIso.Id
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_ExpectedEmissionIso.SiteName
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	private String sitename;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_ExpectedEmissionIso.ProtocolSubCategoryId
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	private String protocolsubcategoryid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_ExpectedEmissionIso.CarbonEmissionLocationId
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	private String carbonemissionlocationid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_ExpectedEmissionIso.CarbonAmount
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	private BigDecimal carbonamount;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_ExpectedEmissionIso.CarbonAmountUnderMeasure
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	private BigDecimal carbonamountundermeasure;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_ExpectedEmissionIso.CarbonUnit
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	private String carbonunit;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_ExpectedEmissionIso.CreatedTime
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	private LocalDateTime createdtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_ExpectedEmissionIso.CreatedBy
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	private String createdby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_ExpectedEmissionIso.DeletedTime
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	private LocalDateTime deletedtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_ExpectedEmissionIso.DeletedBy
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	private String deletedby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_ExpectedEmissionIso.IsDeleted
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	private Boolean isdeleted;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_ExpectedEmissionIso.Id
	 * @return  the value of Tzh_ExpectedEmissionIso.Id
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_ExpectedEmissionIso.Id
	 * @param id  the value for Tzh_ExpectedEmissionIso.Id
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_ExpectedEmissionIso.SiteName
	 * @return  the value of Tzh_ExpectedEmissionIso.SiteName
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public String getSitename() {
		return sitename;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_ExpectedEmissionIso.SiteName
	 * @param sitename  the value for Tzh_ExpectedEmissionIso.SiteName
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public void setSitename(String sitename) {
		this.sitename = sitename == null ? null : sitename.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_ExpectedEmissionIso.ProtocolSubCategoryId
	 * @return  the value of Tzh_ExpectedEmissionIso.ProtocolSubCategoryId
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public String getProtocolsubcategoryid() {
		return protocolsubcategoryid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_ExpectedEmissionIso.ProtocolSubCategoryId
	 * @param protocolsubcategoryid  the value for Tzh_ExpectedEmissionIso.ProtocolSubCategoryId
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public void setProtocolsubcategoryid(String protocolsubcategoryid) {
		this.protocolsubcategoryid = protocolsubcategoryid == null ? null : protocolsubcategoryid.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_ExpectedEmissionIso.CarbonEmissionLocationId
	 * @return  the value of Tzh_ExpectedEmissionIso.CarbonEmissionLocationId
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public String getCarbonemissionlocationid() {
		return carbonemissionlocationid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_ExpectedEmissionIso.CarbonEmissionLocationId
	 * @param carbonemissionlocationid  the value for Tzh_ExpectedEmissionIso.CarbonEmissionLocationId
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public void setCarbonemissionlocationid(String carbonemissionlocationid) {
		this.carbonemissionlocationid = carbonemissionlocationid == null ? null : carbonemissionlocationid.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_ExpectedEmissionIso.CarbonAmount
	 * @return  the value of Tzh_ExpectedEmissionIso.CarbonAmount
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public BigDecimal getCarbonamount() {
		return carbonamount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_ExpectedEmissionIso.CarbonAmount
	 * @param carbonamount  the value for Tzh_ExpectedEmissionIso.CarbonAmount
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public void setCarbonamount(BigDecimal carbonamount) {
		this.carbonamount = carbonamount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_ExpectedEmissionIso.CarbonAmountUnderMeasure
	 * @return  the value of Tzh_ExpectedEmissionIso.CarbonAmountUnderMeasure
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public BigDecimal getCarbonamountundermeasure() {
		return carbonamountundermeasure;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_ExpectedEmissionIso.CarbonAmountUnderMeasure
	 * @param carbonamountundermeasure  the value for Tzh_ExpectedEmissionIso.CarbonAmountUnderMeasure
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public void setCarbonamountundermeasure(BigDecimal carbonamountundermeasure) {
		this.carbonamountundermeasure = carbonamountundermeasure;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_ExpectedEmissionIso.CarbonUnit
	 * @return  the value of Tzh_ExpectedEmissionIso.CarbonUnit
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public String getCarbonunit() {
		return carbonunit;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_ExpectedEmissionIso.CarbonUnit
	 * @param carbonunit  the value for Tzh_ExpectedEmissionIso.CarbonUnit
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public void setCarbonunit(String carbonunit) {
		this.carbonunit = carbonunit == null ? null : carbonunit.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_ExpectedEmissionIso.CreatedTime
	 * @return  the value of Tzh_ExpectedEmissionIso.CreatedTime
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public LocalDateTime getCreatedtime() {
		return createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_ExpectedEmissionIso.CreatedTime
	 * @param createdtime  the value for Tzh_ExpectedEmissionIso.CreatedTime
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public void setCreatedtime(LocalDateTime createdtime) {
		this.createdtime = createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_ExpectedEmissionIso.CreatedBy
	 * @return  the value of Tzh_ExpectedEmissionIso.CreatedBy
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public String getCreatedby() {
		return createdby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_ExpectedEmissionIso.CreatedBy
	 * @param createdby  the value for Tzh_ExpectedEmissionIso.CreatedBy
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public void setCreatedby(String createdby) {
		this.createdby = createdby == null ? null : createdby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_ExpectedEmissionIso.DeletedTime
	 * @return  the value of Tzh_ExpectedEmissionIso.DeletedTime
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public LocalDateTime getDeletedtime() {
		return deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_ExpectedEmissionIso.DeletedTime
	 * @param deletedtime  the value for Tzh_ExpectedEmissionIso.DeletedTime
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public void setDeletedtime(LocalDateTime deletedtime) {
		this.deletedtime = deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_ExpectedEmissionIso.DeletedBy
	 * @return  the value of Tzh_ExpectedEmissionIso.DeletedBy
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public String getDeletedby() {
		return deletedby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_ExpectedEmissionIso.DeletedBy
	 * @param deletedby  the value for Tzh_ExpectedEmissionIso.DeletedBy
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public void setDeletedby(String deletedby) {
		this.deletedby = deletedby == null ? null : deletedby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_ExpectedEmissionIso.IsDeleted
	 * @return  the value of Tzh_ExpectedEmissionIso.IsDeleted
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public Boolean getIsdeleted() {
		return isdeleted;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_ExpectedEmissionIso.IsDeleted
	 * @param isdeleted  the value for Tzh_ExpectedEmissionIso.IsDeleted
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public void setIsdeleted(Boolean isdeleted) {
		this.isdeleted = isdeleted;
	}
}