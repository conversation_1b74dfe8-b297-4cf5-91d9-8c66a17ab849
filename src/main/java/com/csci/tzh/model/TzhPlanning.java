package com.csci.tzh.model;

import java.time.LocalDateTime;

public class TzhPlanning {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Planning.Id
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Planning.SiteName
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	private String sitename;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Planning.ProtocolId
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	private String protocolid;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Planning.Title
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	private String title;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Planning.TitleSC
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	private String titlesc;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Planning.TitleEN
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	private String titleen;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Planning.Seq
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	private Integer seq;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Planning.Description
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	private String description;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Planning.DescriptionSC
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	private String descriptionsc;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Planning.DescriptionEN
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	private String descriptionen;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Planning.CreatedBy
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	private String createdby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Planning.CreatedTime
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	private LocalDateTime createdtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Planning.DeletedBy
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	private String deletedby;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Planning.DeletedTime
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	private LocalDateTime deletedtime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column Tzh_Planning.IsDeleted
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	private Boolean isdeleted;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Planning.Id
	 * @return  the value of Tzh_Planning.Id
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Planning.Id
	 * @param id  the value for Tzh_Planning.Id
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Planning.SiteName
	 * @return  the value of Tzh_Planning.SiteName
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public String getSitename() {
		return sitename;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Planning.SiteName
	 * @param sitename  the value for Tzh_Planning.SiteName
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public void setSitename(String sitename) {
		this.sitename = sitename == null ? null : sitename.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Planning.ProtocolId
	 * @return  the value of Tzh_Planning.ProtocolId
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public String getProtocolid() {
		return protocolid;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Planning.ProtocolId
	 * @param protocolid  the value for Tzh_Planning.ProtocolId
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public void setProtocolid(String protocolid) {
		this.protocolid = protocolid == null ? null : protocolid.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Planning.Title
	 * @return  the value of Tzh_Planning.Title
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public String getTitle() {
		return title;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Planning.Title
	 * @param title  the value for Tzh_Planning.Title
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public void setTitle(String title) {
		this.title = title == null ? null : title.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Planning.TitleSC
	 * @return  the value of Tzh_Planning.TitleSC
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public String getTitlesc() {
		return titlesc;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Planning.TitleSC
	 * @param titlesc  the value for Tzh_Planning.TitleSC
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public void setTitlesc(String titlesc) {
		this.titlesc = titlesc == null ? null : titlesc.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Planning.TitleEN
	 * @return  the value of Tzh_Planning.TitleEN
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public String getTitleen() {
		return titleen;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Planning.TitleEN
	 * @param titleen  the value for Tzh_Planning.TitleEN
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public void setTitleen(String titleen) {
		this.titleen = titleen == null ? null : titleen.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Planning.Seq
	 * @return  the value of Tzh_Planning.Seq
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public Integer getSeq() {
		return seq;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Planning.Seq
	 * @param seq  the value for Tzh_Planning.Seq
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public void setSeq(Integer seq) {
		this.seq = seq;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Planning.Description
	 * @return  the value of Tzh_Planning.Description
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Planning.Description
	 * @param description  the value for Tzh_Planning.Description
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public void setDescription(String description) {
		this.description = description == null ? null : description.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Planning.DescriptionSC
	 * @return  the value of Tzh_Planning.DescriptionSC
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public String getDescriptionsc() {
		return descriptionsc;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Planning.DescriptionSC
	 * @param descriptionsc  the value for Tzh_Planning.DescriptionSC
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public void setDescriptionsc(String descriptionsc) {
		this.descriptionsc = descriptionsc == null ? null : descriptionsc.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Planning.DescriptionEN
	 * @return  the value of Tzh_Planning.DescriptionEN
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public String getDescriptionen() {
		return descriptionen;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Planning.DescriptionEN
	 * @param descriptionen  the value for Tzh_Planning.DescriptionEN
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public void setDescriptionen(String descriptionen) {
		this.descriptionen = descriptionen == null ? null : descriptionen.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Planning.CreatedBy
	 * @return  the value of Tzh_Planning.CreatedBy
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public String getCreatedby() {
		return createdby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Planning.CreatedBy
	 * @param createdby  the value for Tzh_Planning.CreatedBy
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public void setCreatedby(String createdby) {
		this.createdby = createdby == null ? null : createdby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Planning.CreatedTime
	 * @return  the value of Tzh_Planning.CreatedTime
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public LocalDateTime getCreatedtime() {
		return createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Planning.CreatedTime
	 * @param createdtime  the value for Tzh_Planning.CreatedTime
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public void setCreatedtime(LocalDateTime createdtime) {
		this.createdtime = createdtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Planning.DeletedBy
	 * @return  the value of Tzh_Planning.DeletedBy
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public String getDeletedby() {
		return deletedby;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Planning.DeletedBy
	 * @param deletedby  the value for Tzh_Planning.DeletedBy
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public void setDeletedby(String deletedby) {
		this.deletedby = deletedby == null ? null : deletedby.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Planning.DeletedTime
	 * @return  the value of Tzh_Planning.DeletedTime
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public LocalDateTime getDeletedtime() {
		return deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Planning.DeletedTime
	 * @param deletedtime  the value for Tzh_Planning.DeletedTime
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public void setDeletedtime(LocalDateTime deletedtime) {
		this.deletedtime = deletedtime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column Tzh_Planning.IsDeleted
	 * @return  the value of Tzh_Planning.IsDeleted
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public Boolean getIsdeleted() {
		return isdeleted;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column Tzh_Planning.IsDeleted
	 * @param isdeleted  the value for Tzh_Planning.IsDeleted
	 * @mbg.generated  Tue Apr 25 13:08:25 HKT 2023
	 */
	public void setIsdeleted(Boolean isdeleted) {
		this.isdeleted = isdeleted;
	}
}