package com.csci.tzh.model;

import java.time.LocalDateTime;

public class RawEpdWasteTransportationSyncLog {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation_SyncLog.Id
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	private String id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation_SyncLog.Filename
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	private String filename;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation_SyncLog.StartDatetime
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	private LocalDateTime startdatetime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation_SyncLog.EndDatetime
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	private LocalDateTime enddatetime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation_SyncLog.RecordCount
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	private Integer recordcount;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation_SyncLog.InsertCount
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	private Integer insertcount;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation_SyncLog.UpdateCount
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	private Integer updatecount;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation_SyncLog.DeleteCount
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	private Integer deletecount;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation_SyncLog.ErrorDatetime
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	private LocalDateTime errordatetime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column RawEpdWasteTransportation_SyncLog.ErrorLog
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	private String errorlog;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation_SyncLog.Id
	 * @return  the value of RawEpdWasteTransportation_SyncLog.Id
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public String getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation_SyncLog.Id
	 * @param id  the value for RawEpdWasteTransportation_SyncLog.Id
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation_SyncLog.Filename
	 * @return  the value of RawEpdWasteTransportation_SyncLog.Filename
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public String getFilename() {
		return filename;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation_SyncLog.Filename
	 * @param filename  the value for RawEpdWasteTransportation_SyncLog.Filename
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public void setFilename(String filename) {
		this.filename = filename == null ? null : filename.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation_SyncLog.StartDatetime
	 * @return  the value of RawEpdWasteTransportation_SyncLog.StartDatetime
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public LocalDateTime getStartdatetime() {
		return startdatetime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation_SyncLog.StartDatetime
	 * @param startdatetime  the value for RawEpdWasteTransportation_SyncLog.StartDatetime
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public void setStartdatetime(LocalDateTime startdatetime) {
		this.startdatetime = startdatetime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation_SyncLog.EndDatetime
	 * @return  the value of RawEpdWasteTransportation_SyncLog.EndDatetime
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public LocalDateTime getEnddatetime() {
		return enddatetime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation_SyncLog.EndDatetime
	 * @param enddatetime  the value for RawEpdWasteTransportation_SyncLog.EndDatetime
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public void setEnddatetime(LocalDateTime enddatetime) {
		this.enddatetime = enddatetime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation_SyncLog.RecordCount
	 * @return  the value of RawEpdWasteTransportation_SyncLog.RecordCount
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public Integer getRecordcount() {
		return recordcount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation_SyncLog.RecordCount
	 * @param recordcount  the value for RawEpdWasteTransportation_SyncLog.RecordCount
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public void setRecordcount(Integer recordcount) {
		this.recordcount = recordcount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation_SyncLog.InsertCount
	 * @return  the value of RawEpdWasteTransportation_SyncLog.InsertCount
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public Integer getInsertcount() {
		return insertcount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation_SyncLog.InsertCount
	 * @param insertcount  the value for RawEpdWasteTransportation_SyncLog.InsertCount
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public void setInsertcount(Integer insertcount) {
		this.insertcount = insertcount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation_SyncLog.UpdateCount
	 * @return  the value of RawEpdWasteTransportation_SyncLog.UpdateCount
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public Integer getUpdatecount() {
		return updatecount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation_SyncLog.UpdateCount
	 * @param updatecount  the value for RawEpdWasteTransportation_SyncLog.UpdateCount
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public void setUpdatecount(Integer updatecount) {
		this.updatecount = updatecount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation_SyncLog.DeleteCount
	 * @return  the value of RawEpdWasteTransportation_SyncLog.DeleteCount
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public Integer getDeletecount() {
		return deletecount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation_SyncLog.DeleteCount
	 * @param deletecount  the value for RawEpdWasteTransportation_SyncLog.DeleteCount
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public void setDeletecount(Integer deletecount) {
		this.deletecount = deletecount;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation_SyncLog.ErrorDatetime
	 * @return  the value of RawEpdWasteTransportation_SyncLog.ErrorDatetime
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public LocalDateTime getErrordatetime() {
		return errordatetime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation_SyncLog.ErrorDatetime
	 * @param errordatetime  the value for RawEpdWasteTransportation_SyncLog.ErrorDatetime
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public void setErrordatetime(LocalDateTime errordatetime) {
		this.errordatetime = errordatetime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column RawEpdWasteTransportation_SyncLog.ErrorLog
	 * @return  the value of RawEpdWasteTransportation_SyncLog.ErrorLog
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public String getErrorlog() {
		return errorlog;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column RawEpdWasteTransportation_SyncLog.ErrorLog
	 * @param errorlog  the value for RawEpdWasteTransportation_SyncLog.ErrorLog
	 * @mbg.generated  Wed Dec 14 15:33:44 HKT 2022
	 */
	public void setErrorlog(String errorlog) {
		this.errorlog = errorlog == null ? null : errorlog.trim();
	}
}