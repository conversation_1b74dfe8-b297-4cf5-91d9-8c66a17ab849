package com.csci.tzh.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.time.LocalDateTime;

public class TzhExpectedEmissionIsoExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_ExpectedEmissionIso
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_ExpectedEmissionIso
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table Tzh_ExpectedEmissionIso
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ExpectedEmissionIso
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public TzhExpectedEmissionIsoExample() {
		oredCriteria = new ArrayList<>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ExpectedEmissionIso
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ExpectedEmissionIso
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ExpectedEmissionIso
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ExpectedEmissionIso
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ExpectedEmissionIso
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ExpectedEmissionIso
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ExpectedEmissionIso
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ExpectedEmissionIso
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ExpectedEmissionIso
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table Tzh_ExpectedEmissionIso
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table Tzh_ExpectedEmissionIso
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andIdIsNull() {
			addCriterion("Id is null");
			return (Criteria) this;
		}

		public Criteria andIdIsNotNull() {
			addCriterion("Id is not null");
			return (Criteria) this;
		}

		public Criteria andIdEqualTo(String value) {
			addCriterion("Id =", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotEqualTo(String value) {
			addCriterion("Id <>", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThan(String value) {
			addCriterion("Id >", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdGreaterThanOrEqualTo(String value) {
			addCriterion("Id >=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThan(String value) {
			addCriterion("Id <", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLessThanOrEqualTo(String value) {
			addCriterion("Id <=", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdLike(String value) {
			addCriterion("Id like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotLike(String value) {
			addCriterion("Id not like", value, "id");
			return (Criteria) this;
		}

		public Criteria andIdIn(List<String> values) {
			addCriterion("Id in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotIn(List<String> values) {
			addCriterion("Id not in", values, "id");
			return (Criteria) this;
		}

		public Criteria andIdBetween(String value1, String value2) {
			addCriterion("Id between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andIdNotBetween(String value1, String value2) {
			addCriterion("Id not between", value1, value2, "id");
			return (Criteria) this;
		}

		public Criteria andSitenameIsNull() {
			addCriterion("SiteName is null");
			return (Criteria) this;
		}

		public Criteria andSitenameIsNotNull() {
			addCriterion("SiteName is not null");
			return (Criteria) this;
		}

		public Criteria andSitenameEqualTo(String value) {
			addCriterion("SiteName =", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotEqualTo(String value) {
			addCriterion("SiteName <>", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameGreaterThan(String value) {
			addCriterion("SiteName >", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameGreaterThanOrEqualTo(String value) {
			addCriterion("SiteName >=", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameLessThan(String value) {
			addCriterion("SiteName <", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameLessThanOrEqualTo(String value) {
			addCriterion("SiteName <=", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameLike(String value) {
			addCriterion("SiteName like", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotLike(String value) {
			addCriterion("SiteName not like", value, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameIn(List<String> values) {
			addCriterion("SiteName in", values, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotIn(List<String> values) {
			addCriterion("SiteName not in", values, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameBetween(String value1, String value2) {
			addCriterion("SiteName between", value1, value2, "sitename");
			return (Criteria) this;
		}

		public Criteria andSitenameNotBetween(String value1, String value2) {
			addCriterion("SiteName not between", value1, value2, "sitename");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidIsNull() {
			addCriterion("ProtocolSubCategoryId is null");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidIsNotNull() {
			addCriterion("ProtocolSubCategoryId is not null");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidEqualTo(String value) {
			addCriterion("ProtocolSubCategoryId =", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidNotEqualTo(String value) {
			addCriterion("ProtocolSubCategoryId <>", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidGreaterThan(String value) {
			addCriterion("ProtocolSubCategoryId >", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidGreaterThanOrEqualTo(String value) {
			addCriterion("ProtocolSubCategoryId >=", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidLessThan(String value) {
			addCriterion("ProtocolSubCategoryId <", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidLessThanOrEqualTo(String value) {
			addCriterion("ProtocolSubCategoryId <=", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidLike(String value) {
			addCriterion("ProtocolSubCategoryId like", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidNotLike(String value) {
			addCriterion("ProtocolSubCategoryId not like", value, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidIn(List<String> values) {
			addCriterion("ProtocolSubCategoryId in", values, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidNotIn(List<String> values) {
			addCriterion("ProtocolSubCategoryId not in", values, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidBetween(String value1, String value2) {
			addCriterion("ProtocolSubCategoryId between", value1, value2, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andProtocolsubcategoryidNotBetween(String value1, String value2) {
			addCriterion("ProtocolSubCategoryId not between", value1, value2, "protocolsubcategoryid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidIsNull() {
			addCriterion("CarbonEmissionLocationId is null");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidIsNotNull() {
			addCriterion("CarbonEmissionLocationId is not null");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidEqualTo(String value) {
			addCriterion("CarbonEmissionLocationId =", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidNotEqualTo(String value) {
			addCriterion("CarbonEmissionLocationId <>", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidGreaterThan(String value) {
			addCriterion("CarbonEmissionLocationId >", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidGreaterThanOrEqualTo(String value) {
			addCriterion("CarbonEmissionLocationId >=", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidLessThan(String value) {
			addCriterion("CarbonEmissionLocationId <", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidLessThanOrEqualTo(String value) {
			addCriterion("CarbonEmissionLocationId <=", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidLike(String value) {
			addCriterion("CarbonEmissionLocationId like", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidNotLike(String value) {
			addCriterion("CarbonEmissionLocationId not like", value, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidIn(List<String> values) {
			addCriterion("CarbonEmissionLocationId in", values, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidNotIn(List<String> values) {
			addCriterion("CarbonEmissionLocationId not in", values, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidBetween(String value1, String value2) {
			addCriterion("CarbonEmissionLocationId between", value1, value2, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonemissionlocationidNotBetween(String value1, String value2) {
			addCriterion("CarbonEmissionLocationId not between", value1, value2, "carbonemissionlocationid");
			return (Criteria) this;
		}

		public Criteria andCarbonamountIsNull() {
			addCriterion("CarbonAmount is null");
			return (Criteria) this;
		}

		public Criteria andCarbonamountIsNotNull() {
			addCriterion("CarbonAmount is not null");
			return (Criteria) this;
		}

		public Criteria andCarbonamountEqualTo(BigDecimal value) {
			addCriterion("CarbonAmount =", value, "carbonamount");
			return (Criteria) this;
		}

		public Criteria andCarbonamountNotEqualTo(BigDecimal value) {
			addCriterion("CarbonAmount <>", value, "carbonamount");
			return (Criteria) this;
		}

		public Criteria andCarbonamountGreaterThan(BigDecimal value) {
			addCriterion("CarbonAmount >", value, "carbonamount");
			return (Criteria) this;
		}

		public Criteria andCarbonamountGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("CarbonAmount >=", value, "carbonamount");
			return (Criteria) this;
		}

		public Criteria andCarbonamountLessThan(BigDecimal value) {
			addCriterion("CarbonAmount <", value, "carbonamount");
			return (Criteria) this;
		}

		public Criteria andCarbonamountLessThanOrEqualTo(BigDecimal value) {
			addCriterion("CarbonAmount <=", value, "carbonamount");
			return (Criteria) this;
		}

		public Criteria andCarbonamountIn(List<BigDecimal> values) {
			addCriterion("CarbonAmount in", values, "carbonamount");
			return (Criteria) this;
		}

		public Criteria andCarbonamountNotIn(List<BigDecimal> values) {
			addCriterion("CarbonAmount not in", values, "carbonamount");
			return (Criteria) this;
		}

		public Criteria andCarbonamountBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("CarbonAmount between", value1, value2, "carbonamount");
			return (Criteria) this;
		}

		public Criteria andCarbonamountNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("CarbonAmount not between", value1, value2, "carbonamount");
			return (Criteria) this;
		}

		public Criteria andCarbonamountundermeasureIsNull() {
			addCriterion("CarbonAmountUnderMeasure is null");
			return (Criteria) this;
		}

		public Criteria andCarbonamountundermeasureIsNotNull() {
			addCriterion("CarbonAmountUnderMeasure is not null");
			return (Criteria) this;
		}

		public Criteria andCarbonamountundermeasureEqualTo(BigDecimal value) {
			addCriterion("CarbonAmountUnderMeasure =", value, "carbonamountundermeasure");
			return (Criteria) this;
		}

		public Criteria andCarbonamountundermeasureNotEqualTo(BigDecimal value) {
			addCriterion("CarbonAmountUnderMeasure <>", value, "carbonamountundermeasure");
			return (Criteria) this;
		}

		public Criteria andCarbonamountundermeasureGreaterThan(BigDecimal value) {
			addCriterion("CarbonAmountUnderMeasure >", value, "carbonamountundermeasure");
			return (Criteria) this;
		}

		public Criteria andCarbonamountundermeasureGreaterThanOrEqualTo(BigDecimal value) {
			addCriterion("CarbonAmountUnderMeasure >=", value, "carbonamountundermeasure");
			return (Criteria) this;
		}

		public Criteria andCarbonamountundermeasureLessThan(BigDecimal value) {
			addCriterion("CarbonAmountUnderMeasure <", value, "carbonamountundermeasure");
			return (Criteria) this;
		}

		public Criteria andCarbonamountundermeasureLessThanOrEqualTo(BigDecimal value) {
			addCriterion("CarbonAmountUnderMeasure <=", value, "carbonamountundermeasure");
			return (Criteria) this;
		}

		public Criteria andCarbonamountundermeasureIn(List<BigDecimal> values) {
			addCriterion("CarbonAmountUnderMeasure in", values, "carbonamountundermeasure");
			return (Criteria) this;
		}

		public Criteria andCarbonamountundermeasureNotIn(List<BigDecimal> values) {
			addCriterion("CarbonAmountUnderMeasure not in", values, "carbonamountundermeasure");
			return (Criteria) this;
		}

		public Criteria andCarbonamountundermeasureBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("CarbonAmountUnderMeasure between", value1, value2, "carbonamountundermeasure");
			return (Criteria) this;
		}

		public Criteria andCarbonamountundermeasureNotBetween(BigDecimal value1, BigDecimal value2) {
			addCriterion("CarbonAmountUnderMeasure not between", value1, value2, "carbonamountundermeasure");
			return (Criteria) this;
		}

		public Criteria andCarbonunitIsNull() {
			addCriterion("CarbonUnit is null");
			return (Criteria) this;
		}

		public Criteria andCarbonunitIsNotNull() {
			addCriterion("CarbonUnit is not null");
			return (Criteria) this;
		}

		public Criteria andCarbonunitEqualTo(String value) {
			addCriterion("CarbonUnit =", value, "carbonunit");
			return (Criteria) this;
		}

		public Criteria andCarbonunitNotEqualTo(String value) {
			addCriterion("CarbonUnit <>", value, "carbonunit");
			return (Criteria) this;
		}

		public Criteria andCarbonunitGreaterThan(String value) {
			addCriterion("CarbonUnit >", value, "carbonunit");
			return (Criteria) this;
		}

		public Criteria andCarbonunitGreaterThanOrEqualTo(String value) {
			addCriterion("CarbonUnit >=", value, "carbonunit");
			return (Criteria) this;
		}

		public Criteria andCarbonunitLessThan(String value) {
			addCriterion("CarbonUnit <", value, "carbonunit");
			return (Criteria) this;
		}

		public Criteria andCarbonunitLessThanOrEqualTo(String value) {
			addCriterion("CarbonUnit <=", value, "carbonunit");
			return (Criteria) this;
		}

		public Criteria andCarbonunitLike(String value) {
			addCriterion("CarbonUnit like", value, "carbonunit");
			return (Criteria) this;
		}

		public Criteria andCarbonunitNotLike(String value) {
			addCriterion("CarbonUnit not like", value, "carbonunit");
			return (Criteria) this;
		}

		public Criteria andCarbonunitIn(List<String> values) {
			addCriterion("CarbonUnit in", values, "carbonunit");
			return (Criteria) this;
		}

		public Criteria andCarbonunitNotIn(List<String> values) {
			addCriterion("CarbonUnit not in", values, "carbonunit");
			return (Criteria) this;
		}

		public Criteria andCarbonunitBetween(String value1, String value2) {
			addCriterion("CarbonUnit between", value1, value2, "carbonunit");
			return (Criteria) this;
		}

		public Criteria andCarbonunitNotBetween(String value1, String value2) {
			addCriterion("CarbonUnit not between", value1, value2, "carbonunit");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIsNull() {
			addCriterion("CreatedTime is null");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIsNotNull() {
			addCriterion("CreatedTime is not null");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime =", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime <>", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeGreaterThan(LocalDateTime value) {
			addCriterion("CreatedTime >", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime >=", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeLessThan(LocalDateTime value) {
			addCriterion("CreatedTime <", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("CreatedTime <=", value, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeIn(List<LocalDateTime> values) {
			addCriterion("CreatedTime in", values, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotIn(List<LocalDateTime> values) {
			addCriterion("CreatedTime not in", values, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("CreatedTime between", value1, value2, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("CreatedTime not between", value1, value2, "createdtime");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIsNull() {
			addCriterion("CreatedBy is null");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIsNotNull() {
			addCriterion("CreatedBy is not null");
			return (Criteria) this;
		}

		public Criteria andCreatedbyEqualTo(String value) {
			addCriterion("CreatedBy =", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotEqualTo(String value) {
			addCriterion("CreatedBy <>", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyGreaterThan(String value) {
			addCriterion("CreatedBy >", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyGreaterThanOrEqualTo(String value) {
			addCriterion("CreatedBy >=", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLessThan(String value) {
			addCriterion("CreatedBy <", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLessThanOrEqualTo(String value) {
			addCriterion("CreatedBy <=", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyLike(String value) {
			addCriterion("CreatedBy like", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotLike(String value) {
			addCriterion("CreatedBy not like", value, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyIn(List<String> values) {
			addCriterion("CreatedBy in", values, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotIn(List<String> values) {
			addCriterion("CreatedBy not in", values, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyBetween(String value1, String value2) {
			addCriterion("CreatedBy between", value1, value2, "createdby");
			return (Criteria) this;
		}

		public Criteria andCreatedbyNotBetween(String value1, String value2) {
			addCriterion("CreatedBy not between", value1, value2, "createdby");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIsNull() {
			addCriterion("DeletedTime is null");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIsNotNull() {
			addCriterion("DeletedTime is not null");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime =", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime <>", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeGreaterThan(LocalDateTime value) {
			addCriterion("DeletedTime >", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeGreaterThanOrEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime >=", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeLessThan(LocalDateTime value) {
			addCriterion("DeletedTime <", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeLessThanOrEqualTo(LocalDateTime value) {
			addCriterion("DeletedTime <=", value, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeIn(List<LocalDateTime> values) {
			addCriterion("DeletedTime in", values, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotIn(List<LocalDateTime> values) {
			addCriterion("DeletedTime not in", values, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("DeletedTime between", value1, value2, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedtimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
			addCriterion("DeletedTime not between", value1, value2, "deletedtime");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIsNull() {
			addCriterion("DeletedBy is null");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIsNotNull() {
			addCriterion("DeletedBy is not null");
			return (Criteria) this;
		}

		public Criteria andDeletedbyEqualTo(String value) {
			addCriterion("DeletedBy =", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotEqualTo(String value) {
			addCriterion("DeletedBy <>", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyGreaterThan(String value) {
			addCriterion("DeletedBy >", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyGreaterThanOrEqualTo(String value) {
			addCriterion("DeletedBy >=", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLessThan(String value) {
			addCriterion("DeletedBy <", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLessThanOrEqualTo(String value) {
			addCriterion("DeletedBy <=", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyLike(String value) {
			addCriterion("DeletedBy like", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotLike(String value) {
			addCriterion("DeletedBy not like", value, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyIn(List<String> values) {
			addCriterion("DeletedBy in", values, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotIn(List<String> values) {
			addCriterion("DeletedBy not in", values, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyBetween(String value1, String value2) {
			addCriterion("DeletedBy between", value1, value2, "deletedby");
			return (Criteria) this;
		}

		public Criteria andDeletedbyNotBetween(String value1, String value2) {
			addCriterion("DeletedBy not between", value1, value2, "deletedby");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIsNull() {
			addCriterion("IsDeleted is null");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIsNotNull() {
			addCriterion("IsDeleted is not null");
			return (Criteria) this;
		}

		public Criteria andIsdeletedEqualTo(Boolean value) {
			addCriterion("IsDeleted =", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotEqualTo(Boolean value) {
			addCriterion("IsDeleted <>", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedGreaterThan(Boolean value) {
			addCriterion("IsDeleted >", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedGreaterThanOrEqualTo(Boolean value) {
			addCriterion("IsDeleted >=", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedLessThan(Boolean value) {
			addCriterion("IsDeleted <", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedLessThanOrEqualTo(Boolean value) {
			addCriterion("IsDeleted <=", value, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedIn(List<Boolean> values) {
			addCriterion("IsDeleted in", values, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotIn(List<Boolean> values) {
			addCriterion("IsDeleted not in", values, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedBetween(Boolean value1, Boolean value2) {
			addCriterion("IsDeleted between", value1, value2, "isdeleted");
			return (Criteria) this;
		}

		public Criteria andIsdeletedNotBetween(Boolean value1, Boolean value2) {
			addCriterion("IsDeleted not between", value1, value2, "isdeleted");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table Tzh_ExpectedEmissionIso
	 * @mbg.generated  Mon Apr 17 15:55:17 HKT 2023
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table Tzh_ExpectedEmissionIso
     *
     * @mbg.generated do_not_delete_during_merge Mon Sep 12 06:30:12 HKT 2022
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }
}