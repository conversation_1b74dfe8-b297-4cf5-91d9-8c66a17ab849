package com.csci.tzh.vo;

import com.csci.tzh.model.TzhProtocolSubCategory;
import lombok.Data;

@Data
public class TzhProtocolSubCategoryVO {

    private String id;

    private String protocolid;

    private String protocol;

    private String protocolsc;

    private String protocolen;

    private String categoryid;

    private String categoryname;

    private String categorynamesc;

    private String categorynameen;

    private String subcategoryid;

    private String subcategoryname;

    private String subcategorynamesc;

    private String subcategorynameen;

    private String carbonemissionlocationid;

    private String carbonemissionlocation;

    private String carbonemissionlocationsc;

    private String carbonemissionlocationen;
}
