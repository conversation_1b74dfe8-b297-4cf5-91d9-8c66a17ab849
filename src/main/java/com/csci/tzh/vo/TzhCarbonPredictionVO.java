package com.csci.tzh.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "碳中和-減排預測表 表单数据")
public class TzhCarbonPredictionVO {

    private String id;

    @Schema(description = "單位/部門/項目 id")
    private String organizationId;
    
    @Schema(description = "單位/部門/項目 名稱")
    private String organizationName;

    @Schema(description = "表單編碼")
    private String formCode;
    
    @Schema(description = "表單名稱")
    private String formName;

    @Schema(description = "汇报年份")
    private Integer reportingYear;

    @Schema(description = "汇报月份")
    private Integer reportingMonth;

    @Schema(description = "汇报日期")
    private LocalDateTime reportingDate;

    @Schema(description = "负责人账号")
    private String username;

    @Schema(description = "负责人姓名")
    private String userRealName;
    
    @Schema(description = "是否已提交")
    private Boolean isSubmitted;

    @Schema(description = "現階段審核人账号")
    private String currentAuditorUsername;
    
    @Schema(description = "到哪一個審核節點")
    private Integer auditNode;
    
    @Schema(description = "是否已審核")
    private Boolean isAudited;
    
    @Schema(description = "是否已刪除")
    private Boolean isDeleted;

    @Schema(description = "明細")
    private List<TzhCarbonPredictionDetail1VO> lstDetail1;
    
    @Schema(description = "匯總")
    private List<TzhCarbonPredictionDetail2VO> lstDetail2;
}
