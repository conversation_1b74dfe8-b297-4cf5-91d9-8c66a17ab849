package com.csci.tzh.vo;

import com.csci.tzh.model.FCdmsWaterCarbonFactor;
import com.csci.tzh.model.TzhEmissionReduction;
import com.csci.tzh.model.TzhEmissionReductionHead;
import lombok.Data;

@Data
public class TzhEmissionReductionHeadVO extends TzhEmissionReductionHead {

    private String region;

    private String regionsc;

    private String regionen;

    private String protocol;

    private String protocolsc;

    private String protocolen;

    private String categoryname;

    private String categorynamesc;

    private String categorynameen;

    private String carbonemissionlocation;

    private String carbonemissionlocationsc;

    private String carbonemissionlocationen;
}
