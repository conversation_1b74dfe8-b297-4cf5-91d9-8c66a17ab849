package com.csci.cohl.controller;

import com.csci.cohl.beans.dto.LoginDTO;
import com.csci.cohl.beans.vo.EsgOrganizationPermissionVO;
import com.csci.cohl.beans.vo.EsgOrganizationVO;
import com.csci.cohl.beans.vo.LoginVO;
import com.csci.cohl.beans.vo.TokenVO;
import com.csci.cohl.epidemic.annotation.UserLoginToken;
import com.csci.cohl.exception.ResultBody;
import com.csci.cohl.service.AuthService;
import com.csci.common.exception.ServiceException;
import com.csci.susdev.login.LoginFacade;
import com.csci.susdev.model.ResultBean;
import com.csci.susdev.util.ConvertBeanUtils;
import com.csci.susdev.util.StringUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

@RestController
@Tag(name = "鉴权中心")
@RequestMapping("/bi/auth")
public class BiAuthController {

    @Value(("${tzh.baseUrl}"))
    private String baseUrl;

    @Autowired
    private AuthService authService;
    @Autowired
    private LoginFacade loginFacade;

    @GetMapping("/login")
    @Operation(description = "登录")
    public void oAuthLogin(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String token = request.getParameter("token");
        String sitename = request.getParameter("sitename");
        String siteid = request.getParameter("siteid");
        String isheader = request.getParameter("isheader");
        String protocol = request.getParameter("protocol");
        String language = request.getParameter("language");
        String theme = request.getParameter("theme");
        if(isheader == null) isheader = "0";
        if(protocol == null) protocol = "GBT 51366";
        if(language == null) language = "";
        if(theme == null) theme = "blue";
        TokenVO tokenVO = authService.oAuthValidateToken(token);
        if(tokenVO!=null) {
            String[] arrUsername = tokenVO.getUsername().split("\\\\");
            String username = arrUsername[arrUsername.length-1];
            // 本地簽證-
            LoginVO loginVO = authService.oAuthLogin(username, sitename, siteid);
            if (StringUtil.isEmptyNotTrim(siteid)) {
                siteid = authService.getBySiteName(sitename);
            }
            response.sendRedirect(baseUrl
                    + "?theme=" + URLEncoder.encode(theme, "UTF-8")
                    + "&token=" + URLEncoder.encode(loginVO.getToken(), "UTF-8")
                    + "&sitename=" + URLEncoder.encode(sitename, "UTF-8")
                    + "&siteid=" + URLEncoder.encode(siteid, "UTF-8")
                    + "&user=" + URLEncoder.encode(username, "UTF-8")
                    + "&isheader=" + URLEncoder.encode(isheader, "UTF-8")
                    + "&protocol=" + URLEncoder.encode(protocol, "UTF-8")
                    + "&language=" + URLEncoder.encode(language, "UTF-8"));
        } else {
            response.sendRedirect(baseUrl);
        }
    }

    @GetMapping("/list-current-org-permission")
    @Operation(description = "获取账号下有权限的ESG組織列表")
    public ResultBody<List<EsgOrganizationPermissionVO>> listCurrentOrgPermission(String token) throws Exception {
        TokenVO tokenVO = authService.oAuthValidateToken(token);
        if (tokenVO == null) {
            throw new ServiceException("token无效，请检查");
        }
        String[] arrUsername = tokenVO.getUsername().split("\\\\");
        String username = arrUsername[arrUsername.length-1];
        return authService.listEsgOrgPermission(username);
    }

    @PostMapping("/login")
    @Operation(description = "登录")
    public ResultBody<LoginVO> login(@Validated @RequestBody LoginDTO dto) throws Exception {
        ResultBean<Map<String, Object>> resultBean = loginFacade.biLogin(ConvertBeanUtils.convert(dto, com.csci.susdev.vo.LoginVO.class));
        return ResultBody.success(resultBean.getData());
    }

    @RequestMapping("/captcha")
    @Operation(description = "驗證碼")
    public ResultBody<Map> captcha() throws Exception {
//        return authService.captcha();
        ResultBean<Map> captcha = loginFacade.captcha();
        return ResultBody.success(captcha.getData());
    }

    @UserLoginToken
    @PostMapping("/logout")
    @Operation(description = "退出登录")
    public ResultBody logout(HttpServletRequest request) {
        return ResultBody.success(loginFacade.logout((String) request.getAttribute("username")));
    }


    @UserLoginToken
    @PostMapping("/list-site")
    @Operation(description = "項目列表")
    public ResultBody<List<String>> listSite() {
        return authService.listSite();
    }

    @UserLoginToken
    @PostMapping("/list-esg-org")
    @Operation(description = "ESG組織列表")
    public ResultBody<List<EsgOrganizationVO>> listEsgOrg() {
        return authService.listEsgOrg();
    }

    @UserLoginToken
    @PostMapping("/list-esg-org-filter-menu")
    @Operation(description = "ESG組織列表（过滤没有权限、没有配置协议、没有配置菜单）")
    public ResultBody<List<EsgOrganizationVO>> listEsgOrgFilterMenu() {
        return authService.listEsgOrgFilterMenu();
    }
}
