package com.csci.cohl.controller;

import com.csci.cohl.beans.dto.*;
import com.csci.cohl.beans.vo.TzhBsBlockchainSignatureVO;
import com.csci.cohl.beans.vo.TzhBsIntermediateProcessingDetailsVO;
import com.csci.cohl.epidemic.annotation.UserLoginToken;
import com.csci.cohl.exception.ResultBody;
import com.csci.cohl.model.TzhInvoiceFile;
import com.csci.cohl.service.ITzhBsBigDataStatisticsService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Objects;


@RestController
@RequestMapping("/bi/tzh-bs-big-data-statistics")
@Tag(name = "大数据计算")
@UserLoginToken
public class BiTzhBsBigDataStatisticsController {

    @Autowired
    private ITzhBsBigDataStatisticsService tzhBsBigDataStatisticsService;

    @PostMapping("/row/get-blockchain-signature")
    @Operation(description = "区块链签名数据")
    public ResultBody<TzhBsBlockchainSignatureVO> getBlockchainSignature(@RequestBody TzhBsBlockchainSignatureDTO dto) {
        return tzhBsBigDataStatisticsService.getBlockchainSignature(dto);
    }

    @PostMapping("/row/get-intermediate-processing-details")
    @Operation(description = "计算中间过程明细数据")
    public ResultBody<List<TzhBsIntermediateProcessingDetailsVO>> getIntermediateProcessingDetails(@RequestBody TzhBsIntermediateProcessingDetailsDTO dto) {
        return tzhBsBigDataStatisticsService.getIntermediateProcessingDetails(dto);
    }

    //過時
    @PostMapping("/row/cdms/get-payment-ids")
    @Operation(description = "取得付款記錄Id列表")
    public ResultBody<List<String>> getCdmsPaymentIds(@RequestBody CdmsPdfDTO dto) {
        return tzhBsBigDataStatisticsService.getCdmsPaymentIds(dto);
    }

    //過時
    @PostMapping("/row/cdms/download-invoice-pdf")
    @Operation(description = "下載指定票據")
    public ResultBody<String> downloadCdmsInvoicePdf(@RequestBody TzhCmdsInvoicePdfDTO dto) throws Exception {
        return tzhBsBigDataStatisticsService.downloadCdmsInvoicePdf(dto);
    }

    @PostMapping("/row/cdms/download-pdf")
    @Operation(description = "下載Cdms票據")
    public ResultBody<List<String>> downloadCdmsPdf(@RequestBody CdmsPdfDTO dto) throws Exception {
        return tzhBsBigDataStatisticsService.downloadCdmsPdf(dto);
    }

    @PostMapping("/row/upload-invoice-pdf")
    @Operation(description = "上傳票據")
    public ResultBody<Integer> updateInvoicePdf(@ModelAttribute TzhInvoiceFileDTO dto, @RequestParam("pdf") MultipartFile multipartFile) throws IOException {
        return ResultBody.success(tzhBsBigDataStatisticsService.uploadEsgInvoicePdf(dto, multipartFile));
    }

    @PostMapping(value = "/row/download-invoice-pdf")
    @Operation(description = "下載票據")
    public ResponseEntity<org.springframework.core.io.Resource> downloadInvoicePdf(@RequestBody TzhInvoiceFileDTO dto) {
        List<TzhInvoiceFile> lstFile = tzhBsBigDataStatisticsService.getEsgInvoicePdf(dto);
        if(lstFile.size() == 0) {
            return ResponseEntity.ok().body(new InputStreamResource(new ByteArrayInputStream(new byte[]{})));
        } else {
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"Invoice_" + lstFile.get(0).getId() + ".pdf\"")
                    .contentType(MediaType.APPLICATION_PDF)
                    .body(new InputStreamResource(new ByteArrayInputStream(lstFile.get(0).getPdf())));
        }
    }

    @PostMapping(value = "/row/download-invoice-pdf/base64")
    @Operation(description = "以base64格式下載票據")
    public ResultBody<List<String>> downloadInvoicePdfBase64(@RequestBody TzhInvoiceFileDTO dto) {
        List<TzhInvoiceFile> lstFile = tzhBsBigDataStatisticsService.getEsgInvoicePdf(dto);
        List<String> lstBase64 = new ArrayList<>();
        for(TzhInvoiceFile file : lstFile) {
            lstBase64.add(Base64.getEncoder().encodeToString(file.getPdf()));
        }
        if(lstFile.size() == 0) {
            return ResultBody.error("找不到資料");
        } else {
            return ResultBody.success(lstBase64);
        }
    }

    @PostMapping(value = "/row/invoice-pdf/list-id")
    @Operation(description = "下載票據(獲取id列表)")
    public ResultBody<List<String>> getInvoicePdfIdList(@RequestBody TzhInvoiceFileDTO dto) {
        if(StringUtils.isEmpty(dto.getMaterialCode())) {
            dto.setMaterialCode(null);
        }
        if(StringUtils.isEmpty(dto.getMaterialAttribute())) {
            dto.setMaterialAttribute(null);
        }
        List<String> lstIds = tzhBsBigDataStatisticsService.getEsgInvoicePdfIds(dto);
        if(lstIds.size() == 0) {
            return ResultBody.error("找不到資料");
        } else {
            return ResultBody.success(lstIds);
        }
    }

    @PostMapping(value = "/row/invoice-pdf/download")
    @Operation(description = "以base64格式下載票據(根據id下載)")
    public ResultBody<String> getInvoicePdfBase64Count(@RequestBody TzhInvoiceFileIdDTO dto) {
        TzhInvoiceFile file = tzhBsBigDataStatisticsService.getEsgInvoicePdfById(dto);
        String base64 = Base64.getEncoder().encodeToString(file.getPdf());
        if(Objects.isNull(file)) {
            return ResultBody.error("找不到資料");
        } else {
            return ResultBody.success(base64);
        }
    }
}
