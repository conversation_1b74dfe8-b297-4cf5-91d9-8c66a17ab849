package com.csci.cohl.controller;

import com.csci.cohl.epidemic.annotation.UserLoginToken;
import com.csci.cohl.exception.ResultBody;
import com.csci.cohl.model.Dw830Bipvdb830esgDaily;
import com.csci.cohl.model.Dw830Bipvdb830esgSr;
import com.csci.cohl.model.Dw830Bipvdb830esgStationlist;
import com.csci.cohl.model.Dw830Bipvdb830esgTotal;
import com.csci.cohl.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


@RestController
@Tag(name = "830光伏")
@UserLoginToken
public class Photovoltaic830Controller {
    @Resource
    private Dw830Bipvdb830esgSrService dw830Bipvdb830esgSrService;
    @Resource
    private Dw830Bipvdb830esgDailyService dw830Bipvdb830esgDailyService;
    @Resource
    private Dw830Bipvdb830esgTotalService dw830Bipvdb830esgTotalService;
    @Resource
    private Dw830Bipvdb830esgStationlistService dw830Bipvdb830esgStationlistService;

    @PostMapping("/bi/photovoltaic830/get-power-gen-statistics")
    @Operation(summary = "发电统计")
    @Parameter(in = ParameterIn.HEADER, description = "token", name = "x-auth-token", schema = @Schema(type = "string"), required = true, example = "1")
    public ResultBody<Dw830Bipvdb830esgTotal> getPowerGenerationStatistics() {
        return ResultBody.success(dw830Bipvdb830esgTotalService.getPowerGenerationStatistics());
    }
    @PostMapping("/bi/photovoltaic830/get-energy-saving")
    @Operation(summary = "节能减排")
    @Parameter(in = ParameterIn.HEADER, description = "token", name = "x-auth-token", schema = @Schema(type = "string"), required = true, example = "1")
    public ResultBody<List<Dw830Bipvdb830esgSr>> getEnergySaving() {
        return ResultBody.success(dw830Bipvdb830esgSrService.getEnergySaving());
    }

    @PostMapping("/bi/photovoltaic830/get-power-gen-daily")
    @Operation(summary = "发电情况")
    @Parameter(in = ParameterIn.HEADER, description = "token", name = "x-auth-token", schema = @Schema(type = "string"), required = true, example = "1")
    public ResultBody<List<Dw830Bipvdb830esgDaily>> getPowerGeneration() {
        return ResultBody.success(dw830Bipvdb830esgDailyService.getPowerGeneration());
    }

    @PostMapping("/bi/photovoltaic830/get-power-station-list")
    @Operation(summary = "电站列表")
    @Parameter(in = ParameterIn.HEADER, description = "token", name = "x-auth-token", schema = @Schema(type = "string"), required = true, example = "1")
    public ResultBody<List<Dw830Bipvdb830esgStationlist>> getPowerStationList() {
        return ResultBody.success(dw830Bipvdb830esgStationlistService.getPowerStationList());
    }

}
