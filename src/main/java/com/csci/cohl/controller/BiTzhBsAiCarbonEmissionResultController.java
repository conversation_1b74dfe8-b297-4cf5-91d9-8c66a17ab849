package com.csci.cohl.controller;

import com.csci.cohl.beans.dto.TzhBsAiCarbonEmissionDTO;
import com.csci.cohl.epidemic.annotation.UserLoginToken;
import com.csci.cohl.service.ITzhBsAiCarbonEmissionResultService;
import com.csci.susdev.annotation.ExternalAuth;
import com.csci.susdev.model.ResultBean;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/bi/tzh-bs-ai-carbon-emission-result")
@Tag(name = "大屏AI碳排结果")
public class BiTzhBsAiCarbonEmissionResultController {

    @Autowired
    private ITzhBsAiCarbonEmissionResultService tzhBsAiCarbonEmissionResultService;

    @ExternalAuth
    @PostMapping("/row/query-carbon-emission-result")
    @Operation(summary = "AI查询碳排统计结果")
    public ResultBean<String> getAiCarbonEmissionResult(@RequestBody TzhBsAiCarbonEmissionDTO dto) {
        return new ResultBean<>(tzhBsAiCarbonEmissionResultService.getAiCarbonEmissionResult(dto));
    }




}
