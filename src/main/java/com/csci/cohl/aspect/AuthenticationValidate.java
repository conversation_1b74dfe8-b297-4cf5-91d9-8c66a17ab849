package com.csci.cohl.aspect;

import com.alibaba.fastjson.JSON;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.csci.cohl.epidemic.annotation.PassToken;
import com.csci.cohl.epidemic.annotation.UserLoginToken;
import com.csci.cohl.exception.NoRuleException;
import com.csci.cohl.exception.ResultBody;
import com.csci.cohl.service.AuthService;
import com.csci.cohl.service.impl.TzhBsApilogServiceImpl;
import com.csci.susdev.util.redis.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.lang.reflect.Method;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

@Slf4j
@Order(1)
@Aspect
//@Component
public class AuthenticationValidate {

    public static final String SECURE_HEADER = "Authorization";

    @Autowired
    private TzhBsApilogServiceImpl digitalMapNorthernApilogService;

    @Autowired
    private AuthService authService;

    @Value("${jwt.secret}")
    private String jwtSecret;

    @Value("${jwt.redisKey}")
    private String redisKey;

    @Value(("${jwt.expire}"))
    private Integer expire;

    @Autowired
    private RedisUtil redisUtil;

    @Pointcut("execution(public * com.csci.cohl.controller..*.*(..))")
    public void controllerLog(){}

    @Before("controllerLog()")
    public void logBeforeController(JoinPoint joinPoint) throws SocketException, UnknownHostException {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = ((ServletRequestAttributes)requestAttributes).getRequest();
        Signature signature = joinPoint.getSignature();

        // 记录下请求内容
        log.info(
                "\n REQUEST START-------------" +
                        "\n URL : " + request.getRequestURL().toString() +
                        "\n HTTP_METHOD : " + request.getMethod() +
//                        "\n PARAMS : " + Arrays.toString(joinPoint.getArgs()) +
                        "\n IP : " +  request.getRemoteAddr() +
                        "\n CLASS_METHOD : " + signature.getDeclaringTypeName() + "." + signature.getName() +
                        "\n REQUEST END------------------------------\n"
        );

        //检查是否有passtoken注释，有则跳过认证
        MethodSignature methodSignature = (MethodSignature) signature;

        Method method = methodSignature.getMethod();
        if (method.isAnnotationPresent(PassToken.class)) {
            PassToken passToken = method.getAnnotation(PassToken.class);
            if (passToken.required()) {
                return;
            }
        }

        Class<?> controller = method.getDeclaringClass();
       if (controller.isAnnotationPresent(UserLoginToken.class) && verifyToken(controller.getAnnotation(UserLoginToken.class), request)) {
           return;
       }

       if (method.isAnnotationPresent(UserLoginToken.class) && verifyToken(method.getAnnotation(UserLoginToken.class), request)) {
           return;
       }
    }

    @AfterReturning(returning = "returnOb", pointcut = "controllerLog()")
    public void doAfterReturning(JoinPoint joinPoint, Object returnOb) throws IOException {
        if (returnOb instanceof ResultBody) {
            ResultBody<?> resultBody = (ResultBody<?>) returnOb;
            if ("410".equals(resultBody.getCode())) {
                digitalMapNorthernApilogService.A8log(resultBody.getMessage());
            } else {
                digitalMapNorthernApilogService.log(resultBody.getMessage());
            }
        }
        log.info(
                "\n RESPONSE START----------" +
                        "\n RESULT : " + returnOb +
                        "\n RESPONSE END-----------\n"
        );
    }
            @Value("${server.servlet.context-path}")
            private String contextPath;
    private boolean verifyToken(UserLoginToken userLoginToken, HttpServletRequest request) {
        //取出请求头中的token
        String Authorization = request.getHeader(SECURE_HEADER);
        if (userLoginToken.required()) {
            // 执行认证
            if (Authorization == null) {
                throw new NoRuleException("403", "无用户认证，请重新登录");
            }

            String token = Authorization.replace("Bearer ", "");

            Map user;
            try {
                String userStr = JWT.decode(token).getAudience().get(0);
                user = JSON.parseObject(userStr);
            } catch (JWTDecodeException j) {
                throw new NoRuleException("404", "解析错误!");
            }

            // 验证 token
            JWTVerifier jwtVerifier = JWT.require(Algorithm.HMAC256(jwtSecret)).build();
            String username = (String) user.get("username");
            request.setAttribute("username", username);
            try {
                jwtVerifier.verify(token);
                Map result = (Map) redisUtil.get(redisKey + ":" + username);
                if (result == null || ! result.get("token").equals(token)) {
                    throw new NoRuleException("405", "认证已过期,请重新登录!");
                }

                String uri = request.getRequestURI();
                //todo 这里需要优化不应该这样去截取匹配
                String substring = uri.substring(uri.indexOf("/bi"));
                uri=uri.replace(contextPath+"/bi","");
                if (! authService.hasPermission(username, uri)) {
                    throw new NoRuleException("406", "您没有权限访问!");
                }
                String expireTime = (String) result.get("expireTime");
                SimpleDateFormat sf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
                Long expireTimeInt = sf.parse(expireTime).getTime() / 1000;
                long current = System.currentTimeMillis() / 1000;

                if (current > expireTimeInt) {
                    throw new NoRuleException("405", "用户认证已过期!");
                }

                if (current + (expire / 10) < expireTimeInt) {
                    result.put("time", sf.format(new Date(current * 1000))); //重装当前时间
                    result.put("expireTime", sf.format(new Date((current + expire) * 1000)));
                    redisUtil.set(redisKey + ":" + username, result, expire); //延长时间
                }
            } catch (JWTVerificationException e) {
                throw new NoRuleException("405", e.getMessage());
            } catch (ParseException e) {
                throw new NoRuleException("405",  e.getMessage());
            }
        }
        return true;
    }
}
