package com.csci.cohl.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("Tzh_Bs_UserRole")
public class TzhBsUserRole extends Model {

    private static final long serialVersionUID = 1L;

    private String userName;
    private String roleName;
}
