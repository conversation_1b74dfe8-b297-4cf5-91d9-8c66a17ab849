package com.csci.cohl.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("Tzh_SpoilageManagement")
public class TzhSpoilageManagement extends Model {

    private static final long serialVersionUID = 1L;

    @TableField("Id")
    private String Id;

    @TableField("SiteName")
    private String SiteName;

    @TableField("ProtocolId")
    private String ProtocolId;

    @TableField("MaterialName")
    private String MaterialName;

    @TableField("MaterialNameSC")
    private String MaterialNameSC;

    @TableField("MaterialNameEN")
    private String MaterialNameEN;

    @TableField("TargetQty")
    private String TargetQty;

    @TableField("ActualQty")
    private String ActualQty;

    @TableField("Unit")
    private String Unit;

    @TableField("Seq")
    private String Seq;

    @TableField("CreatedBy")
    private String CreatedBy;

    @TableField("CreatedTime")
    private LocalDateTime CreatedTime;

    @TableField("DeletedBy")
    private String DeletedBy;

    @TableField("DeletedTime")
    private LocalDateTime DeletedTime;

    @TableField("IsDeleted")
    private Boolean IsDeleted;
}
