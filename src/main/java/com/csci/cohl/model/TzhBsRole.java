package com.csci.cohl.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("Tzh_Bs_Role")
public class TzhBsRole extends Model {

    private static final long serialVersionUID = 1L;

    private String roleName;
    private String roleDesc;
}
