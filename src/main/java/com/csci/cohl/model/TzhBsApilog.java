package com.csci.cohl.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("Tzh_Bs_Apilog")
public class TzhBsApilog extends Model {

    private static final long serialVersionUID = 1L;

    /**
     * 用户名
     */
    @TableField("UserName")
    private String UserName;

    /**
     * api
     */
    @TableField("Api")
    private String Api;

    /**
     * IP
     */
    @TableField("Ip")
    private String Ip;

    /**
     * mac地址
     */
    @TableField("Mac")
    private String Mac;

    /**
     * 时间
     */
    @TableField("CreatedAt")
    private String CreatedAt;

    @TableField("Description")
    private String Description;

    @TableField("Type")
    private String Type;
}
