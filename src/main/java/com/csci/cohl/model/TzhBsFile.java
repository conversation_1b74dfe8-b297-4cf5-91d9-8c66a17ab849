package com.csci.cohl.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("Tzh_Bs_File")
public class TzhBsFile extends Model {

    private static final long serialVersionUID = 1L;

    @TableField("Id")
    private String id;

    @TableField("SiteName")
    private String siteName;

    @TableField("Section")
    private String section;

    @TableField("Category")
    private String category;

    @TableField("Name")
    private String name;

    @TableField("Type")
    private String type;

    @TableField("Data")
    private byte[] data;

    @TableField("CreatedBy")
    private String createdBy;

    @TableField("CreatedTime")
    private LocalDateTime createdTime;

    @TableField("DeletedBy")
    private String deletedBy;

    @TableField("DeletedTime")
    private LocalDateTime deletedTime;

    @TableField("IsDeleted")
    private Boolean isDeleted;
}
