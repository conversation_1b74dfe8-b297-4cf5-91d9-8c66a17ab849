package com.csci.cohl.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema
@Data
@TableName(value = "dw_830_bipvdb_830ESG_SR")
public class Dw830Bipvdb830esgSr {
    @TableField(value = "節能減排分類")
    @Schema(description = "節能減排分類")
    @JsonProperty("reduceType")
    private String 節能減排分類;

    @TableField(value = "節能減排量")
    @JsonProperty("reduceNum")
    @Schema(description = "節能減排量")
    private Double 節能減排量;

    @TableField(value = "calculate_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime calculateTime;
}
