package com.csci.cohl.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDate;
import java.util.Date;
import lombok.Data;

@Schema
@Data
@TableName(value = "dw_830_bipvdb_Zh_Total_POSandNV")
public class Dw830BipvdbZhTotalPOSandNV {
    @TableField(value = "收集時間")
    @Schema(description = "收集時間")
    private Date 收集時間;

    @TableField(value = "日用電量")
    @Schema(description = "日用電量")
    private Double 日用電量;

    @TableField(value = "日上網電量")
    @Schema(description = "日上網電量")
    private Double 日上網電量;

    @TableField(value = "月度用電量")
    @Schema(description = "月度用電量")
    private Double 月度用電量;

    @TableField(value = "月度上網電量")
    @Schema(description = "月度上網電量")
    private Double 月度上網電量;

    @TableField(value = "calculate_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDate calculateTime;
}
