package com.csci.cohl.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("Tzh_Protocol_SubCategory")
public class TzhProtocolSubCategory extends Model {

    private static final long serialVersionUID = 1L;

    @TableField("Id")
    private String Id;

    @TableField("CategoryId")
    private String Protocol;

    @TableField("SubCategoryName")
    private String SubCategoryName;

    @TableField("SubCategoryNameSC")
    private String SubCategoryNameSC;

    @TableField("SubCategoryNameEN")
    private String SubCategoryNameEN;
}
