package com.csci.cohl.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("Tzh_Invoice_File")
public class TzhInvoiceFile extends Model {

    private static final long serialVersionUID = 1L;

    @TableField("Id")
    private String Id;

    @TableField("SiteName")
    private String SiteName;

    @TableField("RecordYearMonth")
    private int RecordYearMonth;

    @TableField("CarbonEmissionLocation")
    private String CarbonEmissionLocation;

    @TableField("MaterialName")
    private String MaterialName;

    @TableField("MaterialCode")
    private String MaterialCode;

    @TableField("MaterialAttribute")
    private String MaterialAttribute;

    @TableField("Pdf")
    private byte[] Pdf;

    @TableField("CreatedBy")
    private String CreatedBy;

    @TableField("CreatedTime")
    private LocalDateTime CreatedTime;

    @TableField("DeletedBy")
    private String DeletedBy;

    @TableField("DeletedTime")
    private LocalDateTime DeletedTime;

    @TableField("IsDeleted")
    private Boolean IsDeleted;
}
