package com.csci.cohl.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("Tzh_ProjectDetail")
public class TzhProjectDetail extends Model {

    private static final long serialVersionUID = 1L;

    @TableField("Id")
    private String Id;

    @TableField("SiteName")
    private String SiteName;

    @TableField("ProtocolId")
    private String ProtocolId;

    @TableField("CarbonEmissionLocationId")
    private String CarbonEmissionLocationId;

    @TableField("EmissionReductionTarget")
    private String EmissionReductionTarget;

    @TableField("EpdWasteAccount")
    private String EpdWasteAccount;

    @TableField("HasFoodCourt")
    private String HasFoodCourt;

    @TableField("DataSourceId")
    private String DataSourceId;

    @TableField("WasterWaterCarbonFactor")
    private String WasterWaterCarbonFactor;

    @TableField("WasterWaterCarbonFactorUnit")
    private String WasterWaterCarbonFactorUnit;

    @TableField("CreatedBy")
    private String CreatedBy;

    @TableField("CreatedTime")
    private LocalDateTime CreatedTime;

    @TableField("DeletedBy")
    private String DeletedBy;

    @TableField("DeletedTime")
    private LocalDateTime DeletedTime;

    @TableField("IsDeleted")
    private Boolean IsDeleted;
}
