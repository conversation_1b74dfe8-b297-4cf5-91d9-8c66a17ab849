package com.csci.cohl.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("Tzh_Bs_Route")
public class TzhBsRoute extends Model {

    private static final long serialVersionUID = 1L;

    @TableField("Id")
    private String Id;

    @TableField("SiteName")
    private String SiteName;

    @TableField("ProtocolId")
    private String ProtocolId;

    @TableField("PageName")
    private String PageName;

    @TableField("Path")
    private String Path;

    @TableField("Name")
    private String Name;

    @TableField("Component")
    private String Component;

    @TableField("Title")
    private String Title;

    @TableField("Seq")
    private String Seq;

    @TableField("Level")
    private String Level;
}
