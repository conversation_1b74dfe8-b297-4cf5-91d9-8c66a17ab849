package com.csci.cohl.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("Tzh_Protocol_SubCategory")
public class TzhProtocolCategory extends Model {

    private static final long serialVersionUID = 1L;

    @TableField("Id")
    private String Id;

    @TableField("ProtocolId")
    private String ProtocolId;

    @TableField("CategoryName")
    private String CategoryName;

    @TableField("CategoryNameSC")
    private String CategoryNameSC;

    @TableField("CategoryNameEN")
    private String CategoryNameEN;
}
