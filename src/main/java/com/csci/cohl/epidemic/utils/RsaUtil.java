package com.csci.cohl.epidemic.utils;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import java.security.*;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

public class RsaUtil
{
	public static String publicKeyString = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCsX6T3E3xtiiFMyciCTvVp7Y96f0E133KmqV7kFbAwucfhHEBu0peZEa9WRxxKTHMs6KwJ1U7FvJBxl3tIGYlLSdnnteo0MShfPn9zonAq1Q23luJtKNplKWkCJweN8DDWjRQ5i5R93A+dzXr3x94zweQ+4rl4+uPEXRno5KtkfQIDAQAB";

	public static String privateKeyString = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKxfpPcTfG2KIUzJyIJO9Wntj3p/QTXfcqapXuQVsDC5x+EcQG7Sl5kRr1ZHHEpMcyzorAnVTsW8kHGXe0gZiUtJ2ee16jQxKF8+f3OicCrVDbeW4m0o2mUpaQInB43wMNaNFDmLlH3cD53NevfH3jPB5D7iuXj648RdGejkq2R9AgMBAAECgYA72oA5LQJ9NMQIWl6t5MXxsMQr6GkX0E2K0m/5KhDCcpgVsg4rjSOHyqzD/DA2GmK52tb2GSPfpHDRiKSNrhRORl1I6prMc5IEg8f9uS9P0d+tuTDZiq45cWdYbxlNBz/KYiurRryefI3Fa99DBt+LM+rT9o7QEqAooBxrv7BLDwJBAOTJjI3tPBB7A+JuT0NbXr2RlsOIMt9kis5Nbvpe4lEseTIWo0YDeWYrlJqeic2tMnOOZ5vo4ZTRcagpRF+utK8CQQDA4FQjQszS9FoKQqJkb4Tf3gDyJbCTUPEcvK/ZonnhezhZvFQ88HNIiQ48ooSkwv1LxSO37m9Ybz3L58kEcZyTAkEAyM6mUUPyPjzasflEFMizpQuOGl0G2dBzjJOmXpa9aaXxUidQc3lFKooBypxwM1hbOdW51rxWkroqWgCuhJTg/QJAOgQAKu8P7zBi2Q08DZvhyvjbLfsaRuWk8PDssDkIEkPfKlbUu9PTyXC4YJK99VVVnXH6EXxd76reWrQoqfaGkwJANX8emDtGmTbYr+NKSUSkSOUrGJtq4aDXPSC8ZDRgVx8qHFyzh1PHrRBdrz3PBnBTK51KbZhXaMgE41ENWlkK2g==";

    public static void main( String[] args ) throws Exception {
        //加密字符串
        String password="ABC##";
        System.out.println("原字符串为："+password);

        System.out.println("公钥为："+publicKeyString);
        System.out.println("私钥为："+privateKeyString);

        String passwordEn=encrypt(password,publicKeyString);
        System.out.println("加密后的字符串为："+passwordEn);
        String passwordDe=decrypt(passwordEn,privateKeyString);
        System.out.println("还原后的字符串为："+passwordDe);
    }
    /**
     * 随机生成密钥对
     * @throws NoSuchAlgorithmException
     */
    public static void genKeyPair() throws Exception {
        //KeyPairGenerator类用于生成公钥和密钥对，基于RSA算法生成对象
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("RSA");
        //初始化密钥对生成器，密钥大小为96-1024位
        keyPairGen.initialize(1024,new SecureRandom());
        //生成一个密钥对，保存在keyPair中
        KeyPair keyPair = keyPairGen.generateKeyPair();
        PrivateKey privateKey = keyPair.getPrivate();//得到私钥
        PublicKey publicKey = keyPair.getPublic();//得到公钥
        //得到公钥字符串
		publicKeyString = new String(Base64.encodeBase64(publicKey.getEncoded()));
        //得到私钥字符串
        privateKeyString = new String(Base64.encodeBase64(privateKey.getEncoded()));
    }
    /**
     * RSA公钥加密
     *
     * @param str
     *            加密字符串
     * @param publicKey
     *            公钥
     * @return 密文
     * @throws Exception
     *             加密过程中的异常信息
     */
    public static String encrypt(String str,String publicKey) throws Exception {
        //base64编码的公钥
        byte[] decoded = Base64.decodeBase64(publicKey);
        RSAPublicKey pubKey = (RSAPublicKey) KeyFactory.getInstance("RSA").generatePublic(new X509EncodedKeySpec(decoded));
        //Rsa加密
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE,pubKey);
        String outStr = Base64.encodeBase64String(cipher.doFinal(str.getBytes("UTF-8")));
        return outStr;
    }

    /**
     * RSA私钥解密
     *
     * @param str
     *            加密字符串
     * @param privateKey
     *            私钥
     * @return 铭文
     * @throws Exception
     *             解密过程中的异常信息
     */
    public static String decrypt(String str,String privateKey) throws Exception {
        //Base64解码加密后的字符串
        byte[] inputByte = Base64.decodeBase64(str.getBytes("UTF-8"));
        //Base64编码的私钥
        byte[] decoded = Base64.decodeBase64(privateKey);
        PrivateKey priKey = KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(decoded));
        //RSA解密
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE,priKey);
        String outStr = new String(cipher.doFinal(inputByte));
        return outStr;

    }
}

