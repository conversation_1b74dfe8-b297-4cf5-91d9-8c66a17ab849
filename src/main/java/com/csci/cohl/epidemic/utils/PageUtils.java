package com.csci.cohl.epidemic.utils;

import java.util.ArrayList;
import java.util.List;

public class PageUtils {

    static final String DEFAULT_ORDER = "ASC";

    public static String orderAsc(List<String> columns) {
        String orderSql = "";
        if(columns != null && columns.size() > 0) {
            List<String> orders = new ArrayList<>();
            for(int i=0; i<columns.size(); i++) {
                orders.add("ASC");
            }
            orderSql = PageUtils.order(columns, orders);
        }
        return orderSql;
    }

    public static String orderDesc(List<String> columns) {
        String orderSql = "";
        if(columns != null && columns.size() > 0) {
            List<String> orders = new ArrayList<>();
            for(int i=0; i<columns.size(); i++) {
                orders.add("DESC");
            }
            orderSql = PageUtils.order(columns, orders);
        }
        return orderSql;
    }

    public static String order(List<String> columns, List<String> orders) {

        if (columns == null || columns.size() == 0) {
            return "";
        }

        StringBuilder stringBuilder = new StringBuilder();

        for (int x = 0; x < columns.size(); x++) {

            String column = columns.get(x);
            String order = null;

            if (orders != null && orders.size() > x) {
                order = orders.get(x).toUpperCase();
                if (!(order.equals("ASC") || order.equals("DESC"))) {
                    throw new IllegalArgumentException("非法的排序策略：" + column);
                }
            }else {
                order = DEFAULT_ORDER;
            }

            // 判断列名称的合法性，防止SQL注入。只能是【字母，数字，下划线】
            if (!column.matches("[A-Za-z0-9_]+")) {
                throw new IllegalArgumentException("非法的排序字段名称：" + column);
            }

            if (x != 0) {
                stringBuilder.append(", ");
            }
            stringBuilder.append(column + " " + order);
        }
        return stringBuilder.toString();
    }
}
