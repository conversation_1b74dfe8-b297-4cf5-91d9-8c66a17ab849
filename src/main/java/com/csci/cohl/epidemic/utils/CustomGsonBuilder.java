package com.csci.cohl.epidemic.utils;

import com.google.gson.*;

import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.Date;

public class CustomGsonBuilder {
    // 创建GsonBuilder对象并注册Date类型的适配器
    private static final GsonBuilder gsonBuilder = new GsonBuilder().registerTypeAdapter(Date.class, new DateTypeAdapter());

    // 创建Gson对象
    public static Gson createGson() {
        return gsonBuilder.create();
    }

    // Date类型的适配器，用于序列化和反序列化Date对象
    static class DateTypeAdapter implements JsonSerializer<Date>, JsonDeserializer<Date> {

        // 反序列化，将JsonElement对象转换为Date对象
        @Override
        public Date deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
            return DateUtils.toDate(DateUtils.toLocalDateTime(json.getAsString()));
        }

        // 序列化，将Date对象转换为JsonElement对象
        @Override
        public JsonElement serialize(Date src, Type typeOfSrc, JsonSerializationContext context) {
            // 使用SimpleDateFormat将Date对象格式化为"yyyy-MM-dd HH:mm:ss"的字符串
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return new JsonPrimitive(simpleDateFormat.format(src));
        }
    }

}
