package com.csci.cohl.epidemic.config;

import org.jasypt.encryption.pbe.StandardPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.EnvironmentPBEConfig;
import org.junit.jupiter.api.Test;

public class EncryptConfig {

    static void en() {
        StandardPBEStringEncryptor standardPBEStringEncryptor = new StandardPBEStringEncryptor();
        EnvironmentPBEConfig config = new EnvironmentPBEConfig();
        config.setAlgorithm("PBEWithMD5AndDES"); //固定
        config.setPassword("11SVZWGSJIXD4POJQXFLBJSIKXDXR1I0");
        standardPBEStringEncryptor.setConfig(config);
        String plainText = "";
        String encryptedText = standardPBEStringEncryptor.encrypt(plainText);
        System.out.println(encryptedText);
        System.out.println("-----------------------");
        System.out.println(standardPBEStringEncryptor.decrypt("Tbi97Nleao0n/cY6EIAO9SspkHBMoFDdBuAkTXsa+qGDoXiIGSz8JBrjGbuA96pS3ouMpf5OjvRnG8UHhJDwDg=="));
        System.out.println(standardPBEStringEncryptor.decrypt("Xt/4pGSAu50mSekRXq5mf1zAGSGmoR7p"));
        System.out.println(standardPBEStringEncryptor.decrypt("CoQeCv+teM2UJWXDAvXrgNAUw60xHGd4"));
        System.out.println(standardPBEStringEncryptor.decrypt("pE2quI+xvK/5yqq3IJED9u2mHmWdX0SN"));
        System.out.println(standardPBEStringEncryptor.decrypt("ao8ER047WmbAgcklhZMayDbes1Wj9qN+"));
    }

    public static void main(String[] args) {
        en();
    }
}
