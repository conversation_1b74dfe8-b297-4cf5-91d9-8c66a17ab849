package com.csci.cohl.mapper;

import com.csci.cohl.model.TzhBsUserRole;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

public interface BiTzhBsUserRoleMapper extends BaseMapper<TzhBsUserRole> {

    @Select("SELECT COUNT(*) FROM Tzh_Bs_UserRole UR WHERE UserName=#{username} AND EXISTS(" +
            "SELECT * FROM Tzh_Bs_RolePermission RP WHERE RoleName=UR.RoleName AND EXISTS(" +
            "SELECT * FROM Tzh_Bs_Permission WHERE PermissionName=RP.PermissionName AND PermissionApi=#{route}" +
            "))")
    boolean hasPermission (String username, String route);
}
