//package com.csci.cohl.mapper;
//
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//import com.csci.cohl.beans.vo.TzhEmissionReductionDescriptionVO;
//import com.csci.cohl.model.TzhEmissionReductionDescription;
//import org.apache.ibatis.annotations.Param;
//import org.apache.ibatis.annotations.Select;
//
//import java.util.List;
//
//public interface TzhEmissionReductionDescriptionMapper extends BaseMapper<TzhEmissionReductionDescription> {
//    @Select("""
//            SELECT * FROM Tzh_EmissionReductionDescription ERD
//            LEFT JOIN Tzh_Protocol PT ON ERD.ProtocolId = PT.Id
//            WHERE ERD.IsDeleted = 0 AND ERD.SiteName = #{siteName} AND PT.NameEN = #{protocol}
//            ORDER BY Seq
//            """)
//    List<TzhEmissionReductionDescriptionVO> listVO(@Param("siteName") String siteName, @Param("protocol") String protocol);
//
//}
