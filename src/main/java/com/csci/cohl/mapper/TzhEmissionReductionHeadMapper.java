//package com.csci.cohl.mapper;
//
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//import com.csci.cohl.beans.vo.TzhEmissionReductionHeadVO;
//import com.csci.cohl.model.TzhEmissionReductionHead;
//import org.apache.ibatis.annotations.Param;
//import org.apache.ibatis.annotations.Select;
//
//import java.util.List;
//
//public interface TzhEmissionReductionHeadMapper extends BaseMapper<TzhEmissionReductionHead> {
//
//    @Select("""
//            SELECT ERH.Id, ERH.SiteName, C.CategoryName, C.CategoryNameSC, C.CategoryNameEN, ERH.Title, ERH.TitleSC,
//            ERH.TitleEN, L.Name AS CarbonEmissionLocation, L.NameSC AS CarbonEmissionLocationSC, L.NameEN AS CarbonEmissionLocationEN,
//            ERH.MethodDescription, ERH.MethodDescriptionSC, ERH.MethodDescriptionEN, ERH.CalculationDescription
//            FROM Tzh_EmissionReductionHead ERH
//            LEFT JOIN Tzh_Protocol_Category C ON C.Id = ERH.ProtocolCategoryId
//            LEFT JOIN Tzh_Protocol PT ON PT.Id = C.ProtocolId
//            LEFT JOIN Tzh_CarbonEmissionLocation L ON L.Id = ERH.CarbonEmissionLocationId
//            WHERE ERH.IsDeleted = 0
//            AND ERH.SiteName = #{siteName} AND PT.NameEN = #{protocol}
//            """)
//    List<TzhEmissionReductionHeadVO> listEmissionReductionHead(@Param("siteName") String siteName, @Param("protocol") String protocol);
//}
