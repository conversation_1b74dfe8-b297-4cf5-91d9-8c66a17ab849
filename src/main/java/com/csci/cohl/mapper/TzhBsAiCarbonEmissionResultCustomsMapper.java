package com.csci.cohl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csci.cohl.beans.dto.*;
import com.csci.cohl.beans.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface TzhBsAiCarbonEmissionResultCustomsMapper extends BaseMapper<Object> {


    @Select("""
        ${sqlParams}
    """)

    List<Map<String, Object>> getAiCarbonEmissionResult(@Param("sqlParams") String sql);
}
