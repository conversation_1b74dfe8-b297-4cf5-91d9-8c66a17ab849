package com.csci.cohl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csci.cohl.beans.dto.*;
import com.csci.cohl.beans.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TzhBsAmbientPerformanceCounselorMapper extends BaseMapper<Object> {

    /**
     * 查询能源使用排行榜
     * <AUTHOR>
     * @date 2025/1/7 9:09
     * @param dto
     * @return java.util.List<com.csci.cohl.beans.vo.TzhBsEnergyUseVO>
     */
    @Select("""
            WITH RecursiveOrganization AS (
                SELECT t.id, t.name, t.parent_id
                FROM t_organization t
                WHERE t.id = #{dto.organizationId}
                and t.is_deleted = 0
                UNION ALL
                SELECT u.id, u.name, u.parent_id
                FROM t_organization u
                INNER JOIN RecursiveOrganization ru ON u.parent_id = ru.id
                where u.is_deleted = 0
            )
            SELECT
              d.province as provinceSc,
              fp.name_sc as province,
              fp.name_en as provinceEn,
              ROUND(SUM(ISNULL(d.carbon_amount, 0)) * 1000, 2) AS carbonAmount,
              CAST(ROUND(SUM(ISNULL(d.carbon_amount, 0)) / SUM(SUM(ISNULL(d.carbon_amount, 0))) OVER () * 100, 6) AS DECIMAL(18, 6)) AS amountProportion
            FROM f_result_counselor_head h
            INNER JOIN RecursiveOrganization ro on h.organization_id = ro.id
            INNER JOIN f_result_counselor_provinces d on d.head_id = h.id
            LEFT JOIN f_provinces fp on fp.name = d.Province
            where h.is_active = 1
            and h.record_year_month >= #{dto.startMonth}
            and h.record_year_month <= #{dto.endMonth}
            and h.protocol = #{dto.protocol}
            and d.scope_main = '能源'
            and d.province is not null
            group by d.province,fp.name_sc,fp.name_en
            order by carbonAmount desc
    """)
    List<TzhBsEnergyUseVO> getEnergyUseRanking(@Param("dto") TzhBsEnergyUseDTO dto);

    /**
     * 查询废弃物产生量分布统计
     * <AUTHOR>
     * @date 2025/1/7 9:11
     * @param dto
     * @return java.util.List<com.csci.cohl.beans.vo.TzhBsWasteGenerationVO>
     */
    @Select("""
        WITH RecursiveOrganization AS (
            SELECT t.id, t.name, t.parent_id
            FROM t_organization t
            WHERE t.id = #{dto.organizationId}
            and t.is_deleted = 0
            UNION ALL
            SELECT u.id, u.name, u.parent_id
            FROM t_organization u
            INNER JOIN RecursiveOrganization ru ON u.parent_id = ru.id
            where u.is_deleted = 0
        )
        SELECT
          d.province as provinceSc,
          fp.name_sc as province,
          fp.name_en as provinceEn,
          ROUND(SUM(IIF(d.scope_main = '有害廢棄物', ISNULL(d.carbon_amount, 0), 0)), 2) AS hazardousWasteCarbonAmount,
          ROUND(SUM(IIF(d.scope_main = '無害廢棄物', ISNULL(d.carbon_amount, 0), 0)), 2) AS nonHazardousWasteCarbonAmount,
          ROUND(SUM(ISNULL(d.carbon_amount, 0)), 2) AS wasteCarbonAmount,
          CAST(ROUND(SUM(ISNULL(d.carbon_amount, 0)) / SUM(SUM(ISNULL(d.carbon_amount, 0))) OVER () * 100, 6) AS DECIMAL(18, 6)) AS proportionOfWaste
        FROM f_result_counselor_head h
        INNER JOIN RecursiveOrganization ro on h.organization_id = ro.id
        INNER JOIN f_result_counselor_provinces d on d.head_id = h.id
        LEFT JOIN f_provinces fp on fp.name = d.Province
        where h.is_active = 1
        and h.record_year_month >= #{dto.startMonth}
        and h.record_year_month <= #{dto.endMonth}
        and h.protocol = #{dto.protocol}
        and d.scope_main in ('無害廢棄物','有害廢棄物')
        and d.province is not null
        group by d.province,fp.name_sc,fp.name_en
        order by wasteCarbonAmount desc
    """)
    List<TzhBsWasteGenerationVO> getWasteGenerationDistributionStatistics(@Param("dto") TzhBsWasteGenerationDTO dto);


    /**
     * 查询碳排放（按年统计，1+5年）
     * <AUTHOR>
     * @date 2025/1/7 11:18
     * @param dto
     * @return java.util.List<com.csci.cohl.beans.vo.TzhBsCarbonEmissionYearVO>
     */
    @Select("""
        WITH RecursiveOrganization AS (
            SELECT t.id, t.name, t.parent_id
            FROM t_organization t
            WHERE t.id = #{dto.organizationId}
            and t.is_deleted = 0
            UNION ALL
            SELECT u.id, u.name, u.parent_id
            FROM t_organization u
            INNER JOIN RecursiveOrganization ru ON u.parent_id = ru.id
            where u.is_deleted = 0
        ),
        Years AS (
            SELECT
                YEAR(DATEADD(YEAR, -5, DATEFROMPARTS(YEAR(#{dto.queryYear}), MONTH(#{dto.queryYear}), 1))) AS recordYear
            UNION ALL
            SELECT
                YEAR(DATEADD(YEAR, 1, DATEFROMPARTS(recordYear, 1, 1))) AS recordYear
            FROM Years
            WHERE recordYear < YEAR(#{dto.queryYear})
        ),
        RESULT as (
        SELECT
         h.record_year,
         h.organization_id,
         SUM(ISNULL(d.carbon_amount, 0)) AS carbonAmount
        FROM RecursiveOrganization ro
        INNER JOIN f_result_counselor_head h
        ON h.is_active = 1
        and h.organization_id = ro.id
        and h.protocol = #{dto.protocol}
        INNER JOIN f_result_counselor_detail d on d.head_id = h.id and  d.scope_main = '溫室氣體'
        group by h.record_year,h.organization_id
        )
        SELECT
        y.recordYear as recordYear,
        SUM(r.carbonAmount) AS carbonAmount
        FROM Years y
        LEFT JOIN RESULT r on y.recordYear = r.record_year
        group by y.recordYear
        order by y.recordYear
    """)
    List<TzhBsCarbonEmissionYearVO> getCarbonEmissionYearSummary(@Param("dto") TzhBsCarbonEmissionYearDTO dto);

    /**
     * 查询原材料使用占比统计
     * <AUTHOR>
     * @date 2025/1/7 12:01
     * @param dto
     * @return java.util.List<com.csci.cohl.beans.vo.TzhBsRawMaterialUseVO>
     */
    @Select("""
        WITH RecursiveOrganization AS (
            SELECT
                t.id,
                t.name,
                t.parent_id
            FROM
                t_organization t
            WHERE
                t.id = #{dto.organizationId}
                AND t.is_deleted = 0
            UNION ALL
            SELECT
                u.id,
                u.name,
                u.parent_id
            FROM
                t_organization u
            INNER JOIN RecursiveOrganization ru ON u.parent_id = ru.id
            WHERE
                u.is_deleted = 0
        ),
        AggregatedData AS (
            -- 预计算聚合数据
            SELECT
                d.scope_main,
                d.scope_detail,
                SC.sub_category_name_sc,
                SC.sub_category_name_en,
                SUM(ISNULL(d.carbon_amount, 0)) AS rawMaterialUseAmount
            FROM
                f_result_counselor_head h
            INNER JOIN RecursiveOrganization ro ON h.organization_id = ro.id
            INNER JOIN f_result_counselor_detail d ON d.head_id = h.id
            LEFT JOIN t_protocol P ON P.name_en = #{dto.protocol} AND p.is_deleted = 0
            LEFT JOIN t_protocol_category C ON c.protocol_id = p.Id AND c.is_deleted = 0 AND c.category_name = '原材料使用'
            LEFT JOIN t_protocol_sub_category SC ON sc.category_id = c.id AND d.scope_detail = SC.sub_category_name AND sc.is_deleted = 0
            WHERE
                h.is_active = 1
                AND h.record_year_month BETWEEN #{dto.startMonth} AND #{dto.endMonth}
                AND h.protocol = #{dto.protocol}
                AND d.scope_main IN ('不可再生材料', '可再生材料', '回收/再利用材料')
            GROUP BY
                d.scope_main,
                d.scope_detail,
                SC.sub_category_name,
                SC.sub_category_name_sc,
                SC.sub_category_name_en
        )
        SELECT
            ad.scope_main AS categoryName,
            ad.scope_detail AS subCategoryName,
            ad.sub_category_name_sc,
            ad.sub_category_name_en,
            ad.rawMaterialUseAmount,
            SUM(ad.rawMaterialUseAmount) OVER () AS rawMaterialUseTotalAmount,
            CAST(
                ROUND(
                    ad.rawMaterialUseAmount / SUM(ad.rawMaterialUseAmount) OVER () * 100,
                    6
                ) AS DECIMAL(18, 6)
            ) AS rawMaterialUseProportion
        FROM
            AggregatedData ad
        ORDER BY
            ad.scope_main,
            ad.rawMaterialUseAmount DESC;
    """)
    List<TzhBsRawMaterialUseVO> getRawMaterialUseProportion(@Param("dto") TzhBsRawMaterialUseDTO dto);

    /**
     * 查询资源使用趋势统计
     * <AUTHOR>
     * @date 2025/1/7 15:27
     * @param dto
     * @return java.util.List<com.csci.cohl.beans.vo.TzhBsResourceUseTrendStatisticsVO>
     */
    @Select("""
        WITH RecursiveOrganization AS (
            SELECT t.id, t.name, t.parent_id
            FROM t_organization t
            WHERE t.id = #{dto.organizationId}
            and t.is_deleted = 0
            UNION ALL
            SELECT u.id, u.name, u.parent_id
            FROM t_organization u
            INNER JOIN RecursiveOrganization ru ON u.parent_id = ru.id
            where u.is_deleted = 0
        ),
        QuarterCTE AS (
             SELECT
                 #{dto.queryYear} AS year,
                 1 AS quarter
             UNION ALL
             SELECT
                 #{dto.queryYear} AS year,
                 quarter + 1 AS quarter
             FROM
                 QuarterCTE
             WHERE
                 quarter < 4
         )
        SELECT
          q.quarter as quarter,
          SUM(
              IIF(DATEPART(QUARTER, DATEFROMPARTS(h.record_year, h.record_month, 1)) = q.quarter and d.scope_detail = '總耗水量',
                ISNULL(d.carbon_amount, 0), 0)
          ) AS waterResourceAmount,
          SUM(
            IIF(DATEPART(QUARTER, DATEFROMPARTS(h.record_year, h.record_month, 1)) = q.quarter and d.scope_detail = '外購電力',
                ISNULL(d.carbon_amount, 0), 0)
          ) AS electricResourceAmount,
          SUM(
            IIF(DATEPART(QUARTER, DATEFROMPARTS(h.record_year, h.record_month, 1)) = q.quarter,
                ISNULL(d.carbon_amount, 0), 0)
          ) AS quarterResourceAmount
        FROM QuarterCTE q
        LEFT JOIN RecursiveOrganization ro on 1 = 1
        LEFT JOIN f_result_counselor_head h on h.is_active = 1
        and h.record_year = #{dto.queryYear}
        and h.protocol = #{dto.protocol}
        and h.organization_id = ro.id
        LEFT JOIN f_result_counselor_detail d on d.head_id = h.id
        AND d.scope_detail in ('總耗水量','外購電力')
        group by q.year, q.quarter
        order by q.year, q.quarter
    """)
    List<TzhBsResourceUseTrendStatisticsVO> getResourceUseTrendStatistics(@Param("dto") TzhBsResourceUseTrendStatisticsDTO dto);

    /**
     * 查询各类别密度
     * <AUTHOR>
     * @date 2025/1/7 16:21
     * @param dto
     * @return com.csci.cohl.beans.vo.TzhBsCategoryDensityVO
     */
    @Select("""
        WITH RecursiveOrganization AS (
            -- 递归查询组织结构
            SELECT
                t.id,
                t.name,
                t.parent_id
            FROM
                t_organization t
            WHERE
                t.id = #{dto.organizationId}
                AND t.is_deleted = 0
            UNION ALL
            SELECT
                u.id,
                u.name,
                u.parent_id
            FROM
                t_organization u
            INNER JOIN
                RecursiveOrganization ru ON u.parent_id = ru.id
            WHERE
                u.is_deleted = 0
        ),
        DensityData AS (
            -- 预计算密度数据
            SELECT
                ro.id AS organization_id,
                d.density_name,
                SUM(ISNULL(d.carbon_amount, 0)) AS carbon_amount
            FROM
                RecursiveOrganization ro
            LEFT JOIN
                f_result_counselor_head h
                ON h.organization_id = ro.id
                AND h.is_active = 1
                AND h.record_year_month BETWEEN #{dto.startMonth} AND #{dto.endMonth}
                AND h.protocol = #{dto.protocol}
            LEFT JOIN
                f_result_counselor_density d
                ON d.head_id = h.id
            GROUP BY
                ro.id,
                d.density_name
        )
        SELECT
            SUM(CASE WHEN density_name = '温室气体密度' THEN carbon_amount ELSE 0 END) AS greenhouseGasDensity,
            SUM(CASE WHEN density_name = '能源密度' THEN carbon_amount ELSE 0 END) AS energyDensity,
            SUM(CASE WHEN density_name = '有害废弃物密度' THEN carbon_amount ELSE 0 END) AS hazardousWasteCarbonDensity,
            SUM(CASE WHEN density_name = '无害废弃物密度' THEN carbon_amount ELSE 0 END) AS nonHazardousWasteCarbonDensity,
            SUM(CASE WHEN density_name = '废水排放密度' THEN carbon_amount ELSE 0 END) AS drainWaterDensity,
            SUM(CASE WHEN density_name = '用水密度' THEN carbon_amount ELSE 0 END) AS waterConsumptionDensity
        FROM
            DensityData;
    """)
    TzhBsCategoryDensityVO getCategoryDensity(@Param("dto") TzhBsCategoryDensityDTO dto);

    /**
     * 查询地图各省份数据
     * <AUTHOR>
     * @date 2025/1/7 16:33
     * @param dto
     * @return java.util.List<com.csci.cohl.beans.vo.TzhBsMapProvinceDataVO>
     */
    @Select("""
        WITH RecursiveOrganization AS (
            -- 递归查询组织结构
            SELECT
                t.id AS organization_id,
                t.name AS organization_name,
                t.parent_id
            FROM
                t_organization t
            WHERE
                t.id = #{dto.organizationId}
                AND t.is_deleted = 0
            UNION ALL
            SELECT
                u.id AS organization_id,
                u.name AS organization_name,
                u.parent_id
            FROM
                t_organization u
            INNER JOIN
                RecursiveOrganization ru ON u.parent_id = ru.organization_id
            WHERE
                u.is_deleted = 0
        ),
        FilteredData AS (
            -- 预计算过滤后的数据
            SELECT
                f.name AS provinceSc,
                f.name_sc AS province,
                f.name_en AS provinceEn,
                sum(d.carbon_amount) as carbon_amount
            FROM
                f_provinces f
            INNER JOIN
                RecursiveOrganization ro
                ON 1 = 1
            INNER JOIN
                f_result_counselor_head h
                ON h.organization_id = ro.organization_id
                AND h.is_active = 1
                AND h.record_year_month BETWEEN #{dto.startMonth} AND #{dto.endMonth}
                AND h.protocol = #{dto.protocol}
            INNER JOIN
                f_result_counselor_provinces d
                ON d.head_id = h.id
                AND d.scope_main = '溫室氣體'
                AND f.name = d.province
            GROUP BY f.name, f.name_sc, f.name_en
        )
        SELECT
            provinceSc,
            province,
            provinceEn,
            ROUND(carbon_amount, 2) AS carbonAmount,
            CAST(
                ROUND(
                    carbon_amount / SUM(carbon_amount) OVER () * 100,
                    6
                ) AS DECIMAL(18, 0)
            ) AS carbonAmountProportion
        FROM
            FilteredData
        ORDER BY
            carbonAmount DESC;
    """)
    List<TzhBsMapProvinceDataVO> getMapProvinceData(@Param("dto") TzhBsMapProvinceDataDTO dto);

    /**
     * 查询用水与污染
     * <AUTHOR>
     * @date 2025/1/7 16:34
     * @param dto
     * @return com.csci.cohl.beans.vo.TzhBsWaterUseAndPollutionVO
     */
    @Select("""
        WITH RecursiveOrganization AS (
            SELECT t.id, t.name, t.parent_id
            FROM t_organization t
            WHERE t.id = #{dto.organizationId}
            and t.is_deleted = 0
            UNION ALL
            SELECT u.id, u.name, u.parent_id
            FROM t_organization u
            INNER JOIN RecursiveOrganization ru ON u.parent_id = ru.id
            where u.is_deleted = 0
        )
        SELECT
            SUM(IIF(d.scope_detail = '廢水排放總量', ISNULL(d.carbon_amount, 0), 0)) as drainWaterAmount,
            SUM(IIF(d.scope_detail = '總耗水量', ISNULL(d.carbon_amount, 0), 0)) - 
                SUM(IIF(d.scope_detail = '廢水排放總量', ISNULL(d.carbon_amount, 0), 0)) as waterConsumptionAmount,
            SUM(IIF(d.scope_detail = '總回用水量', ISNULL(d.carbon_amount, 0), 0)) as backwaterTrendAmount,
            SUM(IIF(d.scope_detail = '總耗水量', ISNULL(d.carbon_amount, 0), 0)) as waterConsumptionTotalAmount
        FROM RecursiveOrganization ro
        LEFT JOIN f_result_counselor_head h
        on h.is_active = 1
        and h.record_year_month >= #{dto.startMonth}
        and h.record_year_month <= #{dto.endMonth}
        and h.protocol = #{dto.protocol}
        and h.organization_id = ro.id
        LEFT JOIN f_result_counselor_detail d on d.head_id = h.id
        and d.scope_main = '水資源'
    """)
    TzhBsWaterUseAndPollutionVO getWaterUseAndPollution(@Param("dto") TzhBsWaterUseAndPollutionDTO dto);

    /**
     * 查询项目分布
     * <AUTHOR>
     * @date 2025/1/7 16:41
     * @param dto
     * @return java.util.List<com.csci.cohl.beans.vo.TzhBsProjectDistributionVO>
     */
    @Select("""
        WITH RecursiveOrganization AS (
            SELECT t.id, t.name, t.parent_id
            FROM t_organization t
            WHERE t.id = #{dto.organizationId}
            and t.is_deleted = 0
            UNION ALL
            SELECT u.id, u.name, u.parent_id
            FROM t_organization u
            INNER JOIN RecursiveOrganization ru ON u.parent_id = ru.id
            where u.is_deleted = 0
        )
        SELECT
            ro.name as organizationName,
            SUM(ISNULL(d.carbon_amount, 0)) as carbonAmount
        FROM RecursiveOrganization ro
        INNER JOIN f_result_counselor_head h
        on h.is_active = 1
        and h.record_year_month >= #{dto.startMonth}
        and h.record_year_month <= #{dto.endMonth}
        and h.protocol = #{dto.protocol}
        and h.organization_id = ro.id
        INNER JOIN f_result_counselor_detail d on d.head_id = h.id
        AND d.scope_main = '溫室氣體'
        group by ro.name
        having SUM(ISNULL(d.carbon_amount, 0)) > 0
        order by carbonAmount desc
    """)
    List<TzhBsProjectDistributionVO> getProjectDistribution(@Param("dto") TzhBsProjectDistributionDTO dto);

    /**
     * 查询电力使用排行榜
     * <AUTHOR>
     * @date 2025/1/16 14:45
     * @param dto
     * @return java.util.List<com.csci.cohl.beans.vo.TzhBsElectricityUseVO>
     */
    @Select("""
        WITH RecursiveOrganization AS (
            SELECT t.id, t.name, t.parent_id
            FROM t_organization t
            WHERE t.id = #{dto.organizationId}
            and t.is_deleted = 0
            UNION ALL
            SELECT u.id, u.name, u.parent_id
            FROM t_organization u
            INNER JOIN RecursiveOrganization ru ON u.parent_id = ru.id
            where u.is_deleted = 0
        )
        SELECT
          d.province as provinceSc,
          fp.name_sc as province,
          fp.name_en as provinceEn,
          ROUND(SUM(ISNULL(d.carbon_amount, 0)) * 1000, 2) AS greenElectricityAmount,
          ROUND(SUM(ISNULL(d.carbon_amount, 0)) * 1000, 2) AS nonGreenElectricityAmount,
          ROUND(SUM(ISNULL(d.carbon_amount, 0)) * 1000, 2) AS electricityAmount,
          CAST(ROUND(SUM(ISNULL(d.carbon_amount, 0)) / SUM(SUM(ISNULL(d.carbon_amount, 0))) OVER () * 100, 6) AS DECIMAL(18, 6)) AS greenElectricityProportion
        FROM f_result_counselor_head h
        INNER JOIN RecursiveOrganization ro on h.organization_id = ro.id
        INNER JOIN f_result_counselor_provinces d on d.head_id = h.id
        LEFT JOIN f_provinces fp on fp.name = d.Province
        where h.is_active = 1
        and h.record_year_month >= #{dto.startMonth}
        and h.record_year_month <= #{dto.endMonth}
        and h.protocol = #{dto.protocol}
        and d.scope_main = '電力'
        and d.province is not null
        group by d.province,fp.name_sc,fp.name_en
        order by electricityAmount desc
    """)
    List<TzhBsElectricityUseVO> getElectricityUseRanking(@Param("dto") TzhBsElectricityUseDTO dto);

    /**
     * 查询水资源使用排行榜
     * <AUTHOR>
     * @date 2025/1/16 15:36
     * @param dto
     * @return java.util.List<com.csci.cohl.beans.vo.TzhBsWaterResourcesUseVO>
     */
    @Select("""
            WITH RecursiveOrganization AS (
                SELECT t.id, t.name, t.parent_id
                FROM t_organization t
                WHERE t.id = #{dto.organizationId}
                and t.is_deleted = 0
                UNION ALL
                SELECT u.id, u.name, u.parent_id
                FROM t_organization u
                INNER JOIN RecursiveOrganization ru ON u.parent_id = ru.id
                where u.is_deleted = 0
            )
            SELECT
              d.province as provinceSc,
              fp.name_sc as province,
              fp.name_en as provinceEn,
              ROUND(SUM(ISNULL(d.carbon_amount, 0)), 2) AS waterResourcesAmount,
              CAST(ROUND(SUM(ISNULL(d.carbon_amount, 0)) / SUM(SUM(ISNULL(d.carbon_amount, 0))) OVER () * 100, 6) AS DECIMAL(18, 6)) AS amountProportion
            FROM f_result_counselor_head h
            INNER JOIN RecursiveOrganization ro on h.organization_id = ro.id
            INNER JOIN f_result_counselor_provinces d on d.head_id = h.id
            LEFT JOIN f_provinces fp on fp.name = d.Province
            where h.is_active = 1
            and h.record_year_month >= #{dto.startMonth}
            and h.record_year_month <= #{dto.endMonth}
            and h.protocol = #{dto.protocol}
            and d.scope_main = '水資源'
            and  d.province is not null
            group by d.province,fp.name_sc,fp.name_en
            order by waterResourcesAmount desc
    """)
    List<TzhBsWaterResourcesUseVO> getWaterResourcesUseRanking(@Param("dto") TzhBsWaterResourcesUseDTO dto);

    /**
     * 查询温室气体排放总量排行榜
     * <AUTHOR>
     * @date 2025/1/16 16:26
     * @param dto
     * @return java.util.List<com.csci.cohl.beans.vo.TzhBsWaterResourcesUseVO>
     */
    @Select("""
        WITH RecursiveOrganization AS (
          SELECT t.id, t.name, t.parent_id, SUBSTRING(t.no, 1, 6) as organizationNo
          FROM t_organization t
          WHERE t.id = #{dto.organizationId}
          and t.is_deleted = 0
          UNION ALL
          SELECT u.id, u.name, u.parent_id, SUBSTRING(u.no, 1, 6) as organizationNo
          FROM t_organization u
          INNER JOIN RecursiveOrganization ru ON u.parent_id = ru.id
          where u.is_deleted = 0
        )
        SELECT
        ro.organizationNo,
        max(org.name) as organizationName,
        ROUND(SUM(ISNULL(d.carbon_amount, 0)) , 6) AS emissionsAmount
        FROM RecursiveOrganization ro
        LEFT JOIN t_organization org on org.is_deleted = 0 and org.[no] = ro.organizationNo
        LEFT JOIN  f_result_counselor_head h
        ON h.is_active = 1
        AND h.organization_id = ro.id
        and h.record_year_month >= #{dto.startMonth}
        and h.record_year_month <= #{dto.endMonth}
        and h.protocol = #{dto.protocol}
        LEFT JOIN f_result_counselor_detail d on d.head_id = h.id
        AND d.scope_main = '溫室氣體'
        group by ro.organizationNo
        having SUM(ISNULL(d.carbon_amount, 0)) > 0
    """)
    List<TzhBsGreenhouseGasEmissionsTotalVO> getGreenhouseGasEmissionsTotalRanking(@Param("dto") TzhBsGreenhouseGasEmissionsTotalDTO dto);

    /**
     * 查询温室气体排放量总计
     * <AUTHOR>
     * @date 2025/1/18 13:15
     * @param dto
     * @return java.util.List<com.csci.cohl.beans.vo.TzhBsGreenhouseGasEmissionsVO>
     */
    @Select("""
        WITH RecursiveOrganization AS (
            -- 递归查询组织结构
            SELECT
                t.id AS organization_id,
                t.name AS organization_name,
                t.parent_id
            FROM
                t_organization t
            WHERE
                t.id = #{dto.organizationId}
                AND t.is_deleted = 0
            UNION ALL
            SELECT
                u.id AS organization_id,
                u.name AS organization_name,
                u.parent_id
            FROM
                t_organization u
            INNER JOIN
                RecursiveOrganization ru ON u.parent_id = ru.organization_id
            WHERE
                u.is_deleted = 0
        ),
        SubCategory AS (
            -- 获取子分类信息
            SELECT
                p.name_en AS protocol_name,
                c.category_name,
                SC.sub_category_name,
                SC.sub_category_name_sc,
                SC.sub_category_name_en
            FROM
                t_protocol P
            LEFT JOIN
                t_protocol_category c
                ON c.protocol_id = p.Id
                AND c.is_deleted = 0
                AND c.category_name = '溫室氣體'
            LEFT JOIN
                t_protocol_sub_category SC
                ON sc.category_id = c.id
                AND sc.is_deleted = 0
            WHERE
                p.is_deleted = 0
                AND p.name_en = #{dto.protocol}
        ),
        FilteredData AS (
            -- 预计算过滤后的数据
            SELECT
                LEFT(sc.sub_category_name, CHARINDEX(':', sc.sub_category_name + ':') - 1) AS subCategoryName,
                LEFT(sc.sub_category_name_sc, CHARINDEX(':', sc.sub_category_name_sc + ':') - 1) AS subCategoryNameSc,
                LEFT(sc.sub_category_name_en, CHARINDEX(':', sc.sub_category_name_en + ':') - 1) AS subCategoryNameEn,
                d.carbon_amount,
                ro.organization_id
            FROM
                SubCategory sc
            INNER JOIN
                RecursiveOrganization ro
                ON 1 = 1 -- 这里需要明确逻辑，避免笛卡尔积
            LEFT JOIN
                f_result_counselor_head h
                ON h.organization_id = ro.organization_id
                AND h.is_active = 1
                AND h.record_year_month BETWEEN #{dto.startMonth} AND #{dto.endMonth}
                AND h.protocol = sc.protocol_name
            LEFT JOIN
                f_result_counselor_detail d
                ON d.head_id = h.id
                AND d.scope_main = sc.category_name
                AND d.scope_detail = sc.sub_category_name
        )
        SELECT
            subCategoryName,
            subCategoryNameSc,
            subCategoryNameEn,
            ROUND(SUM(carbon_amount) / 10000, 6) AS categoryEmissionsAmount,
            ROUND(SUM(SUM(carbon_amount)) OVER () / 10000, 6) AS emissionsAmount
        FROM
            FilteredData
        WHERE
            carbon_amount IS NOT NULL -- 过滤掉 NULL 值
        GROUP BY
            subCategoryName,
            subCategoryNameSc,
            subCategoryNameEn
        ORDER BY
            subCategoryName;
    """)
    List<TzhBsGreenhouseGasEmissionsVO> getGreenhouseGasEmissionsTotal(@Param("dto") TzhBsGreenhouseGasEmissionsTotalDTO dto);

    /**
     * 查询碳排放强度
     * <AUTHOR>
     * @date 2025/1/20 10:08
     * @param dto
     * @return java.util.List<com.csci.cohl.beans.vo.TzhBsCarbonEmissionYearVO>
     */
    @Select("""
        WITH RecursiveOrganization AS (
            SELECT t.id, t.name, t.parent_id
            FROM t_organization t
            WHERE t.id = #{dto.organizationId}
            and t.is_deleted = 0
            UNION ALL
            SELECT u.id, u.name, u.parent_id
            FROM t_organization u
            INNER JOIN RecursiveOrganization ru ON u.parent_id = ru.id
            where u.is_deleted = 0
        ),
        Years AS (
            SELECT
                YEAR(DATEADD(YEAR, -5, DATEFROMPARTS(YEAR(#{dto.queryYear}), MONTH(#{dto.queryYear}), 1))) AS recordYear
            UNION ALL
            SELECT
                YEAR(DATEADD(YEAR, 1, DATEFROMPARTS(recordYear, 1, 1))) AS recordYear
            FROM Years
            WHERE recordYear < YEAR(#{dto.queryYear})
        )
        SELECT
          y.recordYear as recordYear,
          SUM(ISNULL(d.carbon_amount, 0)) AS carbonAmount
        FROM Years y
        LEFT JOIN RecursiveOrganization ro on 1=1
        LEFT JOIN f_result_counselor_head h on h.is_active = 1
        and h.record_year = y.recordYear
        and h.protocol = #{dto.protocol}
        and h.organization_id = ro.id
        LEFT JOIN f_result_counselor_density d on d.head_id = h.id and d.density_name = '温室气体密度（范围1+2）'
        group by y.recordYear
        order by y.recordYear
    """)
    List<TzhBsCarbonEmissionYearVO> getCarbonEmissionIntensity(@Param("dto") TzhBsCarbonEmissionYearDTO dto);

    /**
     * 查询温室气体排放量明细
     * <AUTHOR>
     * @date 2025/1/20 11:31
     * @param dto
     * @return java.util.List<com.csci.cohl.beans.vo.TzhBsGreenhouseGasEmissionsVO>
     */
    @Select("""
        WITH RecursiveOrganization AS (
            -- 递归查询组织结构
            SELECT
                t.id AS organization_id,
                t.name AS organization_name,
                t.parent_id
            FROM
                t_organization t
            WHERE
                t.id = #{dto.organizationId}
                AND t.is_deleted = 0
            UNION ALL
            SELECT
                u.id AS organization_id,
                u.name AS organization_name,
                u.parent_id
            FROM
                t_organization u
            INNER JOIN
                RecursiveOrganization ru ON u.parent_id = ru.organization_id
            WHERE
                u.is_deleted = 0
        ),
        SubCategory AS (
            -- 获取子分类信息
            SELECT
                p.name_en AS protocol_name,
                c.category_name,
                SC.sub_category_name,
                SC.sub_category_name_sc,
                SC.sub_category_name_en
            FROM
                t_protocol P
            LEFT JOIN
                t_protocol_category C
                ON c.protocol_id = p.Id
                AND c.is_deleted = 0
                AND c.category_name = '溫室氣體'
            LEFT JOIN
                t_protocol_sub_category SC
                ON sc.category_id = c.id
                AND sc.is_deleted = 0
            WHERE
                p.is_deleted = 0
                AND p.name_en = #{dto.protocol}
        ),
        FilteredData AS (
            -- 预计算过滤后的数据
            SELECT
                sc.sub_category_name,
                sc.sub_category_name_sc,
                sc.sub_category_name_en,
                d.carbon_amount,
                ro.organization_id
            FROM
                SubCategory sc
            INNER JOIN
                RecursiveOrganization ro
                ON 1 = 1 -- 这里需要明确逻辑，避免笛卡尔积
            LEFT JOIN
                f_result_counselor_head h
                ON h.organization_id = ro.organization_id
                AND h.is_active = 1
                AND h.record_year_month BETWEEN #{dto.startMonth} AND #{dto.endMonth}
                AND h.protocol = sc.protocol_name
            LEFT JOIN
                f_result_counselor_detail d
                ON d.head_id = h.id
                AND d.scope_main = sc.category_name
                AND d.scope_detail = sc.sub_category_name
        )
        SELECT
            sub_category_name AS subCategoryName,
            sub_category_name_sc AS subCategoryNameSc,
            sub_category_name_en AS subCategoryNameEn,
            ROUND(SUM(carbon_amount), 6) AS categoryEmissionsAmount,
            ROUND(SUM(SUM(carbon_amount)) OVER (), 6) AS emissionsAmount
        FROM
            FilteredData
        WHERE
            carbon_amount IS NOT NULL -- 过滤掉 NULL 值
        GROUP BY
            sub_category_name,
            sub_category_name_sc,
            sub_category_name_en
        ORDER BY
            sub_category_name;
    """)
    List<TzhBsGreenhouseGasEmissionsVO> getGreenhouseGasEmissionsDetail(@Param("dto") TzhBsGreenhouseGasEmissionsTotalDTO dto);

    /**
     * 查询温室气体排放总量（按平台）
     * <AUTHOR>
     * @date 2025/1/20 12:17
     * @param dto
     * @return java.util.List<com.csci.cohl.beans.vo.TzhBsGreenhouseGasEmissionsDetailVO>
     */
    @Select("""
        WITH RecursiveOrganization AS (
            SELECT t.id, t.name, t.parent_id, SUBSTRING(t.no, 1, 6) as organizationNo
            FROM t_organization t
            WHERE t.id = #{dto.organizationId}
            and t.is_deleted = 0
            UNION ALL
            SELECT u.id, u.name, u.parent_id, SUBSTRING(u.no, 1, 6) as organizationNo
            FROM t_organization u
            INNER JOIN RecursiveOrganization ru ON u.parent_id = ru.id
            where u.is_deleted = 0
        ),
        SubCategory AS (
          select
            p.name_en as protocol_name,
            c.category_name,
            SC.sub_category_name,
            SC.sub_category_name_sc,
            SC.sub_category_name_en
          from t_protocol P
          LEFT JOIN t_protocol_category C ON c.protocol_id = p.Id  AND c.is_deleted = 0 and c.category_name = '溫室氣體'
          LEFT JOIN t_protocol_sub_category SC ON sc.category_id = c.id AND sc.is_deleted = 0
          where p.is_deleted = 0
          and p.name_en = #{dto.protocol}
        )
        SELECT
             ro.organizationNo,
             max(org.name) as organizationName,
             LEFT(sc.sub_category_name, CHARINDEX(':', sc.sub_category_name) - 1) as subCategoryName,
             LEFT(sc.sub_category_name_sc, CHARINDEX(':', sc.sub_category_name_sc) - 1) as subCategoryNameSc,
             LEFT(sc.sub_category_name_en, CHARINDEX(':', sc.sub_category_name_en) - 1) as subCategoryNameEn,
          ROUND(SUM(ISNULL(d.carbon_amount, 0)) , 6) AS categoryEmissionsAmount,
          ROUND(SUM(SUM(ISNULL(d.carbon_amount, 0))) OVER () , 6) as emissionsAmount
        FROM SubCategory sc
        INNER JOIN RecursiveOrganization ro on 1 = 1
        LEFT JOIN t_organization org on org.is_deleted = 0 and org.[no] = ro.organizationNo
        LEFT JOIN  f_result_counselor_head h
        ON h.is_active = 1
        AND h.organization_id = ro.id
        and h.record_year_month >= #{dto.startMonth}
        and h.record_year_month <= #{dto.endMonth}
        and h.protocol = sc.protocol_name
        LEFT JOIN f_result_counselor_detail d on d.head_id = h.id
        AND d.scope_main = sc.category_name
        AND d.scope_detail = sc.sub_category_name
        group by ro.organizationNo,LEFT(sc.sub_category_name, CHARINDEX(':', sc.sub_category_name) - 1), LEFT(sc.sub_category_name_sc, CHARINDEX(':', sc.sub_category_name_sc) - 1), LEFT(sc.sub_category_name_en, CHARINDEX(':', sc.sub_category_name_en) - 1)
        having SUM(ISNULL(d.carbon_amount, 0)) > 0
        order by organizationNo, subCategoryName
    """)
    List<TzhBsGreenhouseGasEmissionsDetailVO> getGreenhouseGasEmissionsTotalPlatform(@Param("dto") TzhBsGreenhouseGasEmissionsTotalDTO dto);

    /**
     * 查询香港、澳门地图各区数据
     * <AUTHOR>
     * @date 2025/1/23 8:59
     * @param dto
     * @return java.util.List<com.csci.cohl.beans.vo.TzhBsMapProvinceDataVO>
     */
    @Select("""
        WITH RecursiveOrganization AS (
            SELECT t.id, t.name, t.parent_id
            FROM t_organization t
            WHERE t.id = #{dto.organizationId}
            and t.is_deleted = 0
            UNION ALL
            SELECT u.id, u.name, u.parent_id
            FROM t_organization u
            INNER JOIN RecursiveOrganization ru ON u.parent_id = ru.id
            where u.is_deleted = 0
        ),
        FilteredData AS (
            SELECT
                d.area as provinceSc,
                d.area as province,
                d.area as provinceEn,
                sum(d.carbon_amount) as carbon_amount
            FROM RecursiveOrganization ro
            INNER JOIN f_result_counselor_head h
            on h.is_active = 1
            and h.record_year_month >= #{dto.startMonth}
            and h.record_year_month <= #{dto.endMonth}
            and h.protocol = #{dto.protocol}
            and h.organization_id = ro.id
            inner JOIN f_result_counselor_provinces d on d.head_id = h.id
            and d.scope_main = '溫室氣體'
            group by d.province, d.city, d.area
        )
        SELECT
            provinceSc,
            province,
            provinceEn,
            ROUND(carbon_amount, 2) AS carbonAmount,
            CAST(
                ROUND(
                    carbon_amount / SUM(carbon_amount) OVER () * 100,
                    6
                ) AS DECIMAL(18, 0)
            ) AS carbonAmountProportion
        FROM
            FilteredData
        ORDER BY
            carbonAmount DESC;
    """)
    List<TzhBsMapProvinceDataVO> getMapAreaData(@Param("dto") TzhBsMapProvinceDataDTO dto);

    /**
     * 世界地图
     * <AUTHOR>
     * @date 2025/2/27 11:15
     * @param dto
     * @return java.util.List<com.csci.cohl.beans.vo.TzhBsMapProvinceDataVO>
     */
    @Select("""
        WITH RecursiveOrganization AS (
            SELECT t.id, t.name, t.parent_id
            FROM t_organization t
            WHERE t.id = #{dto.organizationId}
            and t.is_deleted = 0
            UNION ALL
            SELECT u.id, u.name, u.parent_id
            FROM t_organization u
            INNER JOIN RecursiveOrganization ru ON u.parent_id = ru.id
            where u.is_deleted = 0
        ),
        FilteredData AS (
            SELECT
                f.continents as provinceSc,
                f.continents as province,
                f.continents as provinceEn,
                sum(d.carbon_amount) as carbon_amount
            FROM
                f_provinces f
            INNER JOIN
                RecursiveOrganization ro
                ON 1 = 1
            INNER JOIN
                f_result_counselor_head h
                ON h.organization_id = ro.id
                AND h.is_active = 1
                AND h.record_year_month BETWEEN #{dto.startMonth} AND #{dto.endMonth}
                AND h.protocol = #{dto.protocol}
            INNER JOIN
                f_result_counselor_provinces d
                ON d.head_id = h.id
                AND d.scope_main = '溫室氣體'
                AND f.name = d.province
            GROUP BY f.continents
        )
        SELECT
            provinceSc,
            province,
            provinceEn,
            ROUND(carbon_amount, 2) AS carbonAmount,
            CAST(
                ROUND(
                    carbon_amount / SUM(carbon_amount) OVER () * 100,
                    6
                ) AS DECIMAL(18, 0)
            ) AS carbonAmountProportion
        FROM
            FilteredData
        ORDER BY
            carbonAmount DESC;
    """)
    List<TzhBsMapProvinceDataVO> getMapWorldData(@Param("dto") TzhBsMapProvinceDataDTO dto);
}
