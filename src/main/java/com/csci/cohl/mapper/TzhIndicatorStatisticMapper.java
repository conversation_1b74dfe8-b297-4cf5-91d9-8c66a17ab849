package com.csci.cohl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csci.cohl.beans.vo.TzhIndicatorStatisticVO;
import com.csci.cohl.model.TzhIndicatorStatistic;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface TzhIndicatorStatisticMapper extends BaseMapper<TzhIndicatorStatistic> {

    @Select("""
            <script>
            SELECT IDS.*, PT.Name AS Protocol, PT.name_en AS ProtocolEN, PT.name_sc AS ProtocolSC
            FROM Tzh_IndicatorStatistic IDS
            LEFT JOIN t_protocol PT ON PT.id = IDS.ProtocolId 
            left join t_organization o  on o.name = IDS.SiteName and o.is_deleted = 0
            WHERE IDS.IsDeleted = 0
            AND IDS.SiteName = #{siteName}
            AND PT.name_en = #{protocol}
            <if test="siteId != null and siteId != ''">
                and o.id = #{siteId}
			</if>
            </script>
            """)
    List<TzhIndicatorStatisticVO> list(@Param("siteName") String siteName, @Param("protocol") String protocol, @Param("siteId") String siteId);

}
