package com.csci.cohl.beans.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@Schema(description = "查询原材料使用返回VO对象")
public class TzhBsRawMaterialUseVO {


    @Schema(description ="原材料主类名称")
    private String categoryName;
    @Schema(description ="原材料明细使用总量")
    private BigDecimal rawMaterialUseAmount;
    @Schema(description ="原材料明细使用占比")
    private BigDecimal rawMaterialUseProportion;
    @Schema(description ="原材料明细分类名称")
    private String subCategoryName;
    @Schema(description ="原材料明细分类名称简体")
    private String subCategoryNameSc;
    @Schema(description ="原材料明细分类名称英文")
    private String subCategoryNameEn;
    @Schema(description ="所有原材料使用总和")
    private BigDecimal rawMaterialUseTotalAmount;
    @Schema(description ="所有原材料转换单位后的总和")
    private BigDecimal convertTotalAmount;
    @Schema(description ="转换后单位")
    private String convertUnit;
    @Schema(description ="转换后单位(简体)")
    private String convertUnitSc;
    @Schema(description ="转换后单位(英文)")
    private String convertUnitEn;

}
