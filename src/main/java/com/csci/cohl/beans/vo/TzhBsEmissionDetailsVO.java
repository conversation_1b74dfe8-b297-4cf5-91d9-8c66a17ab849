package com.csci.cohl.beans.vo;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class TzhBsEmissionDetailsVO {

    private String CarbonEmissionLocation;
    private String CarbonEmissionLocationSC;
    private String CarbonEmissionLocationEN;
    private String CategoryName;
    private String CategoryNameSC;
    private String CategoryNameEN;
    private String SubCategoryName;
    private String SubCategoryNameSC;
    private String SubCategoryNameEN;
    private String CarbonAmount;
    private String CarbonAmountTotal;
    private String CarbonAmountProportion;
}
