package com.csci.cohl.beans.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
@Schema(description = "查询温室气体排放总量排行榜返回VO对象")
public class TzhBsGreenhouseGasEmissionsTotalVO {

    @Schema(description ="平台名称")
    private String organizationName;
    @Schema(description ="平台编号")
    private String organizationNo;
    @Schema(description ="排放总量")
    private BigDecimal emissionsAmount;
    @Schema(description ="明细列表分类数据")
    private List<TzhBsGreenhouseGasEmissionsDetailVO> detailVOList;

}
