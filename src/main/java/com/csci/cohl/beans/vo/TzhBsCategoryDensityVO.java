package com.csci.cohl.beans.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@Schema(description = "查询各类别密度返回VO对象")
public class TzhBsCategoryDensityVO {

    @Schema(description ="温室气体密度")
    private BigDecimal greenhouseGasDensity;
    @Schema(description ="能源密度")
    private BigDecimal energyDensity;
    @Schema(description ="有害废弃物密度")
    private BigDecimal hazardousWasteCarbonDensity;
    @Schema(description ="无害废弃物密度")
    private BigDecimal nonHazardousWasteCarbonDensity;
    @Schema(description ="排水密度")
    private BigDecimal drainWaterDensity;
    @Schema(description ="耗水密度")
    private BigDecimal waterConsumptionDensity;
}
