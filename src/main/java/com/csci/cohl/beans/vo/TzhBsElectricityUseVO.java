package com.csci.cohl.beans.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@Schema(description = "查询电力使用排行榜返回VO对象")
public class TzhBsElectricityUseVO {

    @Schema(description ="省份")
    private String province;
    @Schema(description ="省份（繁体）")
    private String provinceSc;
    @Schema(description ="省份（英文）")
    private String provinceEn;
    @Schema(description ="绿色电力总量")
    private BigDecimal greenElectricityAmount;
    @Schema(description ="非绿色电力总量")
    private BigDecimal nonGreenElectricityAmount;
    @Schema(description ="电力总量")
    private BigDecimal electricityAmount;
    @Schema(description ="绿色电力占比")
    private BigDecimal greenElectricityProportion;

}
