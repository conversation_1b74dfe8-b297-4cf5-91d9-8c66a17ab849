package com.csci.cohl.beans.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
@Schema(description = "查询温室气体排放量总计返回VO对象")
public class TzhBsGreenhouseGasEmissionsVO {

    @Schema(description ="分类名称")
    private String subCategoryName;
    @Schema(description ="分类名称简体")
    private String subCategoryNameSc;
    @Schema(description ="分类名称英文")
    private String subCategoryNameEn;
    @Schema(description ="分类排放总量")
    private BigDecimal categoryEmissionsAmount;
    @Schema(description ="排放总量")
    private BigDecimal emissionsAmount;
    @Schema(description ="对应范围明细列表")
    private List<TzhBsGreenhouseGasEmissionsVO> detailList;

}
