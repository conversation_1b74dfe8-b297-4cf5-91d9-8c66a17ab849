package com.csci.cohl.beans.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@Schema(description = "查询资源使用趋势统计返回VO对象")
public class TzhBsResourceUseTrendStatisticsVO {

    @Schema(description ="季度")
    private Integer quarter;
    @Schema(description ="水资源总量")
    private BigDecimal waterResourceAmount;
    @Schema(description ="电资源总量")
    private BigDecimal electricResourceAmount;
    @Schema(description ="期间资源总量")
    private BigDecimal quarterResourceAmount;
}
