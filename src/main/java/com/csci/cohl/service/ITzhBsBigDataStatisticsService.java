package com.csci.cohl.service;

import com.csci.cohl.beans.dto.*;
import com.csci.cohl.beans.vo.*;
import com.csci.cohl.exception.ResultBody;
import com.csci.cohl.model.TzhInvoiceFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface ITzhBsBigDataStatisticsService {

	ResultBody<TzhBsBlockchainSignatureVO> getBlockchainSignature(TzhBsBlockchainSignatureDTO dto);

	ResultBody<List<TzhBsIntermediateProcessingDetailsVO>> getIntermediateProcessingDetails(TzhBsIntermediateProcessingDetailsDTO dto);

	ResultBody<List<String>> getCdmsPaymentIds(CdmsPdfDTO dto);

	ResultBody<String> downloadCdmsInvoicePdf(TzhCmdsInvoicePdfDTO dto) throws Exception;

	ResultBody<List<String>> downloadCdmsPdf(CdmsPdfDTO dto) throws Exception;

	int uploadEsgInvoicePdf(TzhInvoiceFileDTO dto, MultipartFile multipartFile) throws IOException;

	int deleteInvoicePdf(TzhInvoiceFileDTO dto);

	List<TzhInvoiceFile> getEsgInvoicePdf(TzhInvoiceFileDTO dto);

	List<String> getEsgInvoicePdfIds(TzhInvoiceFileDTO dto);

	TzhInvoiceFile getEsgInvoicePdfById(TzhInvoiceFileIdDTO dto);
}
