package com.csci.cohl.service;

import com.csci.cohl.beans.dto.*;
import com.csci.cohl.beans.vo.TzhEmissionReductionDescriptionVO;
import com.csci.cohl.exception.ResultBody;
import com.csci.cohl.model.TzhPlanning;

import java.util.List;

public interface ITzhBsMainService {

	ResultBody<List<TzhEmissionReductionDescriptionVO>> getTzhEmissionReductionDescription(SiteNameDTO dto) throws Exception;

	ResultBody<List<TzhPlanning>> getPlanning(SiteNameDTO dto);
}
