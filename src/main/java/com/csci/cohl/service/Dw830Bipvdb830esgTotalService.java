package com.csci.cohl.service;

import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csci.cohl.mapper.Dw830Bipvdb830esgTotalMapper;
import com.csci.cohl.model.Dw830Bipvdb830esgTotal;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;

@Service
public class Dw830Bipvdb830esgTotalService extends ServiceImpl<Dw830Bipvdb830esgTotalMapper, Dw830Bipvdb830esgTotal> {

    @Resource
    private Dw830Bipvdb830esgMonthService dw830Bipvdb830esgMonthService;
    public Dw830Bipvdb830esgTotal getPowerGenerationStatistics() {
        Dw830Bipvdb830esgTotal powerGenerationStatistics = baseMapper.getPowerGenerationStatistics();
        BigDecimal totalPowerGenFrom202304 = dw830Bipvdb830esgMonthService.getTotalPowerGenFrom202304();
        powerGenerationStatistics.setTotalPowerGenFrom202304(totalPowerGenFrom202304);
        powerGenerationStatistics.setSelfUse(powerGenerationStatistics.getSumPowerGeneration().subtract(powerGenerationStatistics.getOnlineEleUsage()).setScale(2, RoundingMode.HALF_UP));
        powerGenerationStatistics.setPowerCapacityRatio(powerGenerationStatistics.getRealTimePower().divide(powerGenerationStatistics.getInstalledCapacity(), 2, RoundingMode.HALF_UP));
        return powerGenerationStatistics;
    }
}
