package com.csci.cohl.service;

import com.csci.cohl.beans.dto.*;
import com.csci.cohl.beans.vo.TzhBsRouteVO;
import com.csci.cohl.beans.vo.TzhProjectInfoVO;
import com.csci.cohl.beans.vo.TzhProtocolCategoryVO;
import com.csci.cohl.beans.vo.TzhProtocolSubCategoryVO;
import com.csci.cohl.exception.ResultBody;
import com.csci.cohl.model.TzhBsRoute;

import java.util.List;

public interface ITzhCommonService {
	public ResultBody<TzhProjectInfoVO> getProjectInfo(SiteNameDTO dto);
	public List<TzhProtocolSubCategoryVO> listProtocolSubCategory(ProtocolDTO dto);
	public List<TzhProtocolCategoryVO> listProtocolCategory(ProtocolDTO dto);
	public ResultBody<List<TzhBsRouteVO>> listRoute(SiteNameDTO dto);
}
