package com.csci.cohl.service.impl;

import com.csci.cohl.epidemic.utils.ClientInfo;
import com.csci.cohl.mapper.TzhBsApilogMapper;
import com.csci.cohl.model.TzhBsApilog;
import io.netty.util.internal.StringUtil;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

@Service
public class ApiLogImpl {

    @Resource
    private TzhBsApilogMapper tzhBsApilogMapper;

    private Integer before = 3600 * 1000;

    @Async
    public void logCreate(TzhBsApilog log) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = format.parse(log.getCreatedAt());
        String time = format.format(date.getTime() - before);
        String mac = tzhBsApilogMapper.getRecentMac(log.getIp(), time);
        if (StringUtil.isNullOrEmpty(mac)) {
            mac = ClientInfo.getMacAddress(log.getIp());
        }
        log.setMac(mac);
        tzhBsApilogMapper.insert(log);
    }
}
