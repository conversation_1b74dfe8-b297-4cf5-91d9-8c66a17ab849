package com.csci.cohl.service.impl;

import com.csci.cohl.epidemic.utils.ClientInfo;
import com.csci.cohl.model.TzhBsApilog;
import com.csci.cohl.service.ITzhBsApilogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-02
 */
@EnableAsync
@Service
public class TzhBsApilogServiceImpl extends BaseServiceImpl implements ITzhBsApilogService {

    Logger logger = LoggerFactory.getLogger(TzhBsApilogServiceImpl.class);

    @Autowired
    private ApiLogImpl apiLog;


    public void A8log(String desc) {
        logHandle(desc, "A8");
    }

    public void log(String desc) {
        logHandle(desc, "NORMAL");
    }

    public void logHandle(String desc, String type) {
        Object ou = request.getAttribute("username");
        String userName = "";
        if (ou != null) {
            userName = ou.toString();
        }

        try {
            String ip = ClientInfo.getIpAddress(request);
            String uri = request.getRequestURI();

            TzhBsApilog log = new TzhBsApilog();
            log.setUserName(userName);
            log.setApi(uri);
            log.setIp(ip);
            log.setType(type);
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            long current = System.currentTimeMillis() / 1000;
            log.setDescription(desc);
            log.setCreatedAt(sf.format(new Date(current * 1000)));
            apiLog.logCreate(log);
        } catch (Exception e) {
            logger.error("用户日志记录失败:"+e.getMessage());
        }
    }
}
