package com.csci.cohl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.csci.cohl.beans.dto.*;
import com.csci.cohl.beans.vo.*;
import com.csci.cohl.epidemic.utils.PageUtils;
import com.csci.cohl.epidemic.utils.UserInfo;
import com.csci.cohl.mapper.TzhBsBigDataStatisticsMapper;
import com.csci.cohl.mapper.BiTzhBsUserSiteMapper;
import com.csci.cohl.model.*;
import com.csci.cohl.epidemic.utils.HttpClientUtil;
import com.csci.cohl.epidemic.utils.JsonUtil;
import com.csci.cohl.exception.ResultBody;
import com.csci.cohl.exception.ResultPage;
import com.csci.cohl.service.*;
import com.csci.tzh.mapper.TzhInvoiceFileMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.netty.util.internal.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

@Service
public class TzhBsBigDataStatisticsServiceImpl extends BaseServiceImpl implements ITzhBsBigDataStatisticsService {

    @Resource
    private BiTzhBsUserSiteMapper biTzhBsUserSiteMapper;

    @Resource
    private TzhBsBigDataStatisticsMapper tzhBsBigDataStatisticsMapper;

    @Resource
    private TzhInvoiceFileMapper tzhInvoiceFileMapper;

    @Override
    public ResultBody<TzhBsBlockchainSignatureVO> getBlockchainSignature(TzhBsBlockchainSignatureDTO dto) {
        TzhBsBlockchainSignatureVO vo = tzhBsBigDataStatisticsMapper.getBlockchainSignature(dto.getCalculateDate());
        return ResultBody.success(vo);
    }

    @Override
    public ResultBody<List<TzhBsIntermediateProcessingDetailsVO>> getIntermediateProcessingDetails(TzhBsIntermediateProcessingDetailsDTO dto) {
        String username = (String) request.getAttribute("username");
        if (com.csci.susdev.util.StringUtil.isNotEmpty(dto.getSiteId())) {
            if(!biTzhBsUserSiteMapper.listSiteId(username).contains(dto.getSiteId())) return ResultBody.error("無法查詢該項目。");
        }else {
            if(!biTzhBsUserSiteMapper.listSite(username).contains(dto.getSiteName())) return ResultBody.error("無法查詢該項目。");
        }

        // 設定頁數，排序
        Page<TzhBsIntermediateProcessingDetailsVO> page = new Page<>(dto.getCurrent(), dto.getSize());
        if((dto.getAscs() == null || dto.getDescs().size() == 0) && (dto.getDescs() == null || dto.getAscs().size() == 0)) {
            ArrayList<String> defDesc = new ArrayList<>();
            defDesc.add("recordYearMonth");
            defDesc.add("materialName");
            dto.setDescs(defDesc);
        }
        page.setAscs(dto.getAscs());
        page.setDescs(dto.getDescs());

        String orderSql = "";
        if(dto.getAscs() != null && dto.getAscs().size() > 0)  {
            orderSql = PageUtils.orderAsc(dto.getAscs());
        }
        if(dto.getDescs() != null && dto.getDescs().size() > 0)  {
            orderSql = PageUtils.orderDesc(dto.getDescs());
        }

        // 設定查詢條件，進行查詢
        IPage<TzhBsIntermediateProcessingDetailsVO> iPage;
        if(LocalDate.now().compareTo(dto.getCalculateDate()) <= 0) {
            iPage = tzhBsBigDataStatisticsMapper.getIntermediateProcessingDetailsLatest(page, dto.getCalculateDate(),
                            dto.getCarbonEmissionLocation(), dto.getSiteName(), dto.getProtocol(), dto.getSiteId());
            if(!StringUtil.isNullOrEmpty(orderSql)) {
                iPage.setRecords(tzhBsBigDataStatisticsMapper.getIntermediateProcessingDetailsLatestOrderBy(dto.getCalculateDate(),
                        dto.getCarbonEmissionLocation(), orderSql, (int) ((dto.getCurrent() - 1) * dto.getSize()), (int) dto.getSize(),
                        dto.getSiteName(), dto.getProtocol(), dto.getSiteId()));
            }
        } else {
            iPage = tzhBsBigDataStatisticsMapper.getIntermediateProcessingDetails(page, dto.getCalculateDate(),
                            dto.getCarbonEmissionLocation(), dto.getSiteName(), dto.getProtocol(), dto.getSiteId());
            if(!StringUtil.isNullOrEmpty(orderSql)) {
                iPage.setRecords(tzhBsBigDataStatisticsMapper.getIntermediateProcessingDetailsOrderBy(dto.getCalculateDate(),
                        dto.getCarbonEmissionLocation(), orderSql, (int) ((dto.getCurrent() - 1) * dto.getSize()), (int) dto.getSize(),
                        dto.getSiteName(), dto.getProtocol(), dto.getSiteId()));
            }
        }
        return ResultPage.success(iPage);
    }

    @Override
    public ResultBody<List<String>> getCdmsPaymentIds(CdmsPdfDTO dto) {
        String username = (String) request.getAttribute("username");
        if(!biTzhBsUserSiteMapper.listSite(username).contains(dto.getSiteName())) return ResultBody.error("無法查詢該項目。");

        List<String> lstId;
        if(StringUtil.isNullOrEmpty(dto.getMaterialCode())) {
            lstId = tzhBsBigDataStatisticsMapper.getWasterWaterPaymentIds(dto.getRecordYearMonth(), dto.getCarbonEmissionLocation(), dto.getSiteName());
        } else {
            lstId = tzhBsBigDataStatisticsMapper.getMaterialPaymentIds(dto.getRecordYearMonth(), dto.getCarbonEmissionLocation(), dto.getMaterialCode(), dto.getSiteName());
        }
        return ResultBody.success(lstId);
    }

    @Override
    public ResultBody<String> downloadCdmsInvoicePdf(TzhCmdsInvoicePdfDTO dto) throws Exception {
        ObjectMapper objectMapper = JsonUtil.getObjectMapper();
        //用户登录
        String loginUrl = "https://cdms.3311csci.com/csci.perms/api/services/csci/permission/Login";
        String loginPayload = "{\"UserName\":\"report_download\",\"PassWord\":\"5nrtiLSjIqYeaPZ3q7jrWm2RtCI/3ypC5i3NXPaENEtobXUtPEe5qFHiNE8NQ4FHnfyswH1aLKUQKsJjtdcEb9nzlmEs7cLLuLM73e3p+Mjp4iGyORta3CvMYGKEQOOBzvlgAHfNtRgshE/9mUpZRjCTY0VMBTjCeEYCgKl0vFM=\",\"TenantId\":\"8\",\"OrganId\":\"35\"}";
        String loginResponse = HttpClientUtil.doPostJson(loginUrl, loginPayload);
        Map<String, Object> cdmsLoginResponseVO =  objectMapper.readValue(loginResponse, new TypeReference<Map<String, Object>>(){});
        Map<String, Object> cdmsLoginResponseTokenVO = (Map<String, Object>)cdmsLoginResponseVO.get("Token");

        //获取一次性访问凭据
        String onceTicketUrl = "https://cdms.3311csci.com/csci.perms/api/services/csci/permission/OnceTicket";
        String onceTicketPayload = "{}";
        Map<String, String> onceTicketHeader = new HashMap<>();
        onceTicketHeader.put("Authorization", (String) cdmsLoginResponseTokenVO.get("AccessToken"));
        String onceTicketResponse = HttpClientUtil.doPostJson(onceTicketUrl, onceTicketPayload, onceTicketHeader).replace("\"", "");

        if(!onceTicketResponse.contains("ot-")) {
            throw new Exception("获取一次性访问凭据失敗:" + onceTicketResponse);
        }
        String pdfLink = "https://cdms.3311csci.com/cdms4.reports/view/43/" + dto.getId() + "?token=" + onceTicketResponse;

        return ResultBody.success(pdfLink);
    }


    @Override
    public ResultBody<List<String>> downloadCdmsPdf(CdmsPdfDTO dto) throws Exception {
        String username = (String) request.getAttribute("username");
        if(!biTzhBsUserSiteMapper.listSite(username).contains(dto.getSiteName())) return ResultBody.error("無法查詢該項目。");

        //獲取PAYMENT ID & INVOICE ID
        List<String> lstInvoiceIdOfPaymentIdNull;
        List<String> lstPaymentId;
        if(StringUtil.isNullOrEmpty(dto.getMaterialCode())) {
            lstPaymentId = tzhBsBigDataStatisticsMapper.getWasterWaterPaymentIds(dto.getRecordYearMonth(), dto.getCarbonEmissionLocation(), dto.getSiteName());
            lstInvoiceIdOfPaymentIdNull = tzhBsBigDataStatisticsMapper.getWasterWaterInvoiceIdsOfPaymentIdNull(dto.getRecordYearMonth(), dto.getCarbonEmissionLocation(), dto.getSiteName());
        } else {
            lstPaymentId = tzhBsBigDataStatisticsMapper.getMaterialPaymentIds(dto.getRecordYearMonth(), dto.getCarbonEmissionLocation(), dto.getMaterialCode(), dto.getSiteName());
            lstInvoiceIdOfPaymentIdNull = tzhBsBigDataStatisticsMapper.getMaterialInvoiceIdsOfPaymentIdNull(dto.getRecordYearMonth(), dto.getCarbonEmissionLocation(), dto.getMaterialCode(), dto.getSiteName());
        }

        //在有付辦單ID 的情況下，獲取付辦單URL
        List<String> lstUrl = new ArrayList<>();
        ObjectMapper objectMapper = JsonUtil.getObjectMapper();

        for(String paymentId : lstPaymentId) {
            try {
                //用户登录
                String loginUrl = "https://cdms.3311csci.com/csci.perms/api/services/csci/permission/Login";
                String loginPayload = "{\"UserName\":\"report_download\",\"PassWord\":\"5nrtiLSjIqYeaPZ3q7jrWm2RtCI/3ypC5i3NXPaENEtobXUtPEe5qFHiNE8NQ4FHnfyswH1aLKUQKsJjtdcEb9nzlmEs7cLLuLM73e3p+Mjp4iGyORta3CvMYGKEQOOBzvlgAHfNtRgshE/9mUpZRjCTY0VMBTjCeEYCgKl0vFM=\",\"TenantId\":\"8\",\"OrganId\":\"35\"}";
                String loginResponse = HttpClientUtil.doPostJson(loginUrl, loginPayload);
                Map<String, Object> cdmsLoginResponseVO =  objectMapper.readValue(loginResponse, new TypeReference<Map<String, Object>>(){});
                Map<String, Object> cdmsLoginResponseTokenVO = (Map<String, Object>)cdmsLoginResponseVO.get("Token");

                //获取一次性访问凭据
                String onceTicketUrl = "https://cdms.3311csci.com/csci.perms/api/services/csci/permission/OnceTicket";
                String onceTicketPayload = "{}";
                Map<String, String> onceTicketHeader = new HashMap<>();
                onceTicketHeader.put("Authorization", (String) cdmsLoginResponseTokenVO.get("AccessToken"));
                String onceTicketResponse = HttpClientUtil.doPostJson(onceTicketUrl, onceTicketPayload, onceTicketHeader).replace("\"", "");

                if(onceTicketResponse.contains("ot-")) {
                    String pdfLink = "https://cdms.3311csci.com/cdms4.reports/view/43/" + paymentId + "?token=" + onceTicketResponse;
                    lstUrl.add(pdfLink);
                } else {
                    //在沒有付辦單URL的情況,提供發票URL
                    List<String> lstInvoiceId = tzhBsBigDataStatisticsMapper.getCdmsInvoiceIds(paymentId);
                    for(String invoiceId : lstInvoiceId) {
                        try {
                            String invoiceResponse = HttpClientUtil.doGet(onceTicketUrl);
                            List<String> lstInvoicePdfUrl = objectMapper.readValue(invoiceResponse, new TypeReference<List<String>>(){});
                            lstUrl.addAll(lstInvoicePdfUrl);
                        } catch (Exception ex) {
                            //lstUrl.add(ex.getMessage());
                        }
                    }
                }
            } catch (Exception ex) {
                //lstUrl.add(ex.getMessage());
            }
        }
        //在沒有付辦單ID 的情況下，獲取發票URL
        for(String invoiceId : lstInvoiceIdOfPaymentIdNull) {
            try {
                String invoiceResponse = HttpClientUtil.doGet("https://cdms.3311csci.com/csci.docstores/api/upload/GetDownloadUrls?bizType=MaterialPayment&bizId=" + invoiceId);
                List<String> lstInvoicePdfUrl = objectMapper.readValue(invoiceResponse, new TypeReference<List<String>>(){});
                lstUrl.addAll(lstInvoicePdfUrl);
            } catch (Exception ex) {
                //lstUrl.add(ex.getMessage());
            }
        }

        return ResultBody.success(lstUrl);
    }

    @Override
    @Transactional
    public int uploadEsgInvoicePdf(TzhInvoiceFileDTO dto, MultipartFile multipartFile) throws IOException {
        int result = 0;

        String username = UserInfo.getCurrentUser().get("username");

        // 生成模型
        TzhInvoiceFile x = new TzhInvoiceFile();
        x.setId(UUID.randomUUID().toString());
        x.setSiteName(dto.getSiteName());
        x.setRecordYearMonth(Integer.parseInt(dto.getRecordYearMonth()));
        x.setCarbonEmissionLocation(dto.getCarbonEmissionLocation());
        x.setMaterialName(dto.getMaterialName());
        x.setMaterialCode(dto.getMaterialCode());
        x.setPdf(multipartFile.getBytes());
        x.setCreatedBy(username);
        x.setCreatedTime(LocalDateTime.now());
        x.setIsDeleted(false);

        // 新增新的記錄
        result += tzhInvoiceFileMapper.insert(x);

        return result;
    }

    @Override
    @Transactional
    public int deleteInvoicePdf(TzhInvoiceFileDTO dto) {
        int result = 0;

        String username = UserInfo.getCurrentUser().get("username");

        UpdateWrapper<TzhInvoiceFile> wrapper = new UpdateWrapper<>();
        wrapper.eq("SiteName", dto.getSiteName());
        wrapper.eq("RecordYearMonth", dto.getRecordYearMonth());
        wrapper.eq("CarbonEmissionLocation", dto.getCarbonEmissionLocation());
        wrapper.eq("MaterialName", dto.getMaterialName());
        wrapper.eq("MaterialCode", dto.getMaterialCode());
        wrapper.eq("IsDeleted", false);
        wrapper.set("DeletedBy", username);
        wrapper.set("DeletedTime", LocalDateTime.now());
        wrapper.set("IsDeleted", true);
        result += tzhInvoiceFileMapper.update(null, wrapper);

        return result;
    }

    @Override
    public List<TzhInvoiceFile> getEsgInvoicePdf(TzhInvoiceFileDTO dto) {
        QueryWrapper<TzhInvoiceFile> wrapper = new QueryWrapper<>();
        wrapper.eq("SiteName", dto.getSiteName());
        wrapper.eq("RecordYearMonth", dto.getRecordYearMonth());
        wrapper.eq("CarbonEmissionLocation", dto.getCarbonEmissionLocation());
        wrapper.eq("MaterialName", dto.getMaterialName());
        if(!StringUtil.isNullOrEmpty(dto.getMaterialCode())) {
            wrapper.eq("MaterialCode", dto.getMaterialCode());
        }
        if(!StringUtil.isNullOrEmpty(dto.getMaterialAttribute())) {
            wrapper.eq("MaterialAttribute", dto.getMaterialAttribute());
        }
        wrapper.eq("IsDeleted", false);
        List<TzhInvoiceFile> lst = tzhInvoiceFileMapper.selectList(wrapper);
        return lst;
    }

    @Override
    public List<String> getEsgInvoicePdfIds(TzhInvoiceFileDTO dto) {
        return tzhBsBigDataStatisticsMapper.getEsgInvoiceIds(
                dto.getSiteName(),
                dto.getRecordYearMonth(),
                dto.getCarbonEmissionLocation(),
                dto.getMaterialName(),
                dto.getMaterialCode(),
                dto.getMaterialAttribute());
    }

    @Override
    public TzhInvoiceFile getEsgInvoicePdfById(TzhInvoiceFileIdDTO dto) {
        QueryWrapper<TzhInvoiceFile> wrapper = new QueryWrapper<>();
        wrapper.eq("Id", dto.getId());
        wrapper.eq("IsDeleted", false);
        List<TzhInvoiceFile> lst = tzhInvoiceFileMapper.selectList(wrapper);
        if(lst.size() == 0) {
            return null;
        } else {
            return lst.get(0);
        }
    }

}
