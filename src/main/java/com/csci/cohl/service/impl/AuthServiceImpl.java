package com.csci.cohl.service.impl;

import com.alibaba.fastjson.JSON;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.csci.cohl.beans.dto.LoginDTO;
import com.csci.cohl.beans.vo.EsgOrganizationPermissionVO;
import com.csci.cohl.beans.vo.EsgOrganizationVO;
import com.csci.cohl.beans.vo.LoginVO;
import com.csci.cohl.beans.vo.TokenVO;
import com.csci.cohl.epidemic.utils.*;
import com.csci.cohl.exception.ResultBody;
import com.csci.cohl.mapper.*;
import com.csci.cohl.service.AuthService;
import com.csci.common.util.CommonUtils;
import com.csci.susdev.model.User;
import com.csci.susdev.model.UserSession;
import com.csci.susdev.service.UserService;
import com.csci.susdev.service.UserSessionService;
import com.csci.susdev.util.ConvertBeanUtils;
import com.csci.susdev.util.StringUtil;
import com.csci.susdev.util.redis.RedisUtil;
import com.google.gson.Gson;
import com.wf.captcha.SpecCaptcha;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@EnableAsync
@Service
public class AuthServiceImpl extends BaseServiceImpl implements AuthService {

    @Value("${oa.login}")
    private String oaLoginUrl;

    @Value("${jwt.secret}")
    private String jwtSecret;

    @Autowired
    private RedisUtil redisUtil;

    @Value("${app.esg.redisKey}")
    private String redisKey;

    @Value(("${jwt.expire}"))
    private Integer expire;

    @Resource
    private UserService userService;

    @Resource
    private UserSessionService userSessionService;

    @Resource
    private BiTzhBsUserMapper biTzhBsUserMapper;

    @Resource
    private BiTzhBsUserRoleMapper biTzhBsUserRoleMapper;

    @Resource
    private BiTzhBsRolePermissionMapper biTzhBsRolepermissionMapper;

    @Resource
    private BiTzhBsUserSiteMapper biTzhBsUserSiteMapper;

    @Resource
    private EsgOrganizationMapper esgOrganizationMapper;

    @Value("${user.session.active.count}")
    private int sessionCount;

    @Autowired
    private Mail mail;

    @Value("${rsa.publicKey}")
    private String publicKeyString;

    @Value(("${rsa.privateKey}"))
    private String privateKeyString;

    @Value("${oauth.validateUrl}")
    private String validateUrl;

    private final Gson gson = CustomGsonBuilder.createGson();

    public TokenVO oAuthValidateToken(String token) throws Exception {
        final String url = validateUrl + token;
        //参考文档：https://mkyong.com/java/apache-httpclient-examples/
        HttpGet get = new HttpGet(url);
        get.addHeader("content-type", "application/json");

        //采用绕过验证的方式处理https请求
        SSLContext sslcontext = createIgnoreVerifySSL();

        //设置协议http和https对应的处理socket链接工厂的对象
        Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.INSTANCE)
                .register("https", new SSLConnectionSocketFactory(sslcontext))
                .build();
        PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
        HttpClients.custom().setConnectionManager(connManager);

        //创建自定义的httpclient对象
        try (CloseableHttpClient httpClient = HttpClients.custom().setConnectionManager(connManager).build(); //CloseableHttpClient httpClient = HttpClients.createDefault();
             CloseableHttpResponse response = httpClient.execute(get)){
            int statusCode = response.getStatusLine().getStatusCode();
            if(statusCode>=200 && statusCode<300) {
                String result = EntityUtils.toString(response.getEntity());
                TokenVO tokenVO = gson.fromJson(result, TokenVO.class);
                return tokenVO;
            }
        }

        return null;
    }

    public LoginVO oAuthLogin(String username, String sitename, String siteid) throws Exception {
        request.setAttribute("username", username);

        if (StringUtil.isNotEmpty(siteid)) {
            List<String> lstSite = biTzhBsUserSiteMapper.listSiteId(username);
            if(!lstSite.contains(siteid)) {
                throw new Exception("沒有該地盤權限");
            }
        }else {
            List<String> lstSite = biTzhBsUserSiteMapper.listSite(username);
            if(!lstSite.contains(sitename)) {
                throw new Exception("沒有該地盤權限");
            }
        }


        SimpleDateFormat sf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        long currentSecond = System.currentTimeMillis() / 1000;
        String currentTime = sf.format(new Date(currentSecond * 1000));
        String content = "登录账号:" + username + ",\nip:"+ ClientInfo.getIpAddress(request) + ",\n时间:"+ currentTime + "\n";

        Map user = new HashMap();
        user.put("username", username);
        user.put("time", currentTime);
        user.put("expire", expire);
        user.put("expireTime", sf.format(new Date((currentSecond + expire) * 1000)));


        UserSession prevSession = userSessionService.getUserSessionByUsername(username);
        String token = CommonUtils.generateGuid().toLowerCase();
        User userObj = userService.getUserByUsername(username);
        userSessionService.createUserSession(userObj, token, "", "");
        userSessionService.deleteUserSession(username, 100);
        user.put("token", token);

        /*
        Object prevUser = redisUtil.get(redisKey + ":" + username);
        if(prevUser != null) {
            // 在token到期前再次登入，不更新token
            Map prevUserMap = (Map) prevUser;
            user.put("token", prevUserMap.get("token"));
            redisUtil.set(redisKey + ":" + username, user, expire);
        } else {
            redisUtil.set(redisKey + ":" + username, user, expire);
        }
         */

        LoginVO vo = new LoginVO();
        vo.setToken((String) user.get("token"));
        return vo;
    }

    public static SSLContext createIgnoreVerifySSL() throws NoSuchAlgorithmException, KeyManagementException {
        SSLContext sc = SSLContext.getInstance("TLSv1.2");

        // 实现一个X509TrustManager接口，用于绕过验证，不用修改里面的方法
        X509TrustManager trustManager = new X509TrustManager() {
            @Override
            public void checkClientTrusted(
                    java.security.cert.X509Certificate[] paramArrayOfX509Certificate,
                    String paramString) throws CertificateException {
            }

            @Override
            public void checkServerTrusted(
                    java.security.cert.X509Certificate[] paramArrayOfX509Certificate,
                    String paramString) throws CertificateException {
            }

            @Override
            public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                return null;
            }
        };

        sc.init(null, new TrustManager[] { trustManager }, null);
        return sc;
    }

    private LoginVO handleLoginSuccess(String username, String displayName) {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        long currentSecond = System.currentTimeMillis() / 1000;
        String currentTime = sf.format(new Date(currentSecond * 1000));

        Map user = new HashMap();

        user.put("username", username);
        user.put("time", currentTime);
        user.put("expire", expire);
        user.put("expireTime", sf.format(new Date((currentSecond + expire) * 1000)));
        String token = JWT.create().withAudience(JSON.toJSONString(user))
                .sign(Algorithm.HMAC256(jwtSecret));
        user.put("token", token);

        Object prevUser = redisUtil.get(redisKey + ":" + username);
        if(prevUser != null) {
            // 在token到期前再次登入，不更新token
            Map prevUserMap = (Map) prevUser;
            user.put("token", prevUserMap.get("token"));
            redisUtil.set(redisKey + ":" + username, user, expire);
        } else {
            redisUtil.set(redisKey + ":" + username, user, expire);
        }

        LoginVO vo = new LoginVO();
        vo.setToken((String) user.get("token"));
        vo.setName(displayName);

        return vo;
    }

    @Override
    public ResultBody<Map> captcha() throws Exception {
        try {
            SpecCaptcha specCaptcha = new SpecCaptcha(130, 48, 5);
            String verCode = specCaptcha.text().toLowerCase();
            String key = UUID.randomUUID().toString();
            redisUtil.set(redisKey + "(verCode):" + key, verCode, 300);
            Map<String, String> map = new HashMap<>();
            map.put("key", key);
            map.put("image", specCaptcha.toBase64());
            return ResultBody.success(map);
        } catch (Exception e) {
            return ResultBody.error("生成驗證碼失敗!");
        }
    }

    @Override
    public boolean hasPermission(String username, String route) {
        return biTzhBsUserRoleMapper.hasPermission(username, route);
    }

    @Override
    public ResultBody logout() {
        String username = redisKey + ":" + request.getAttribute("username");
        redisUtil.del(username);
        if (! redisUtil.hasKey(username)) {
            return ResultBody.success();
        }
        return ResultBody.error("退出失败!");
    }

    @Override
    public ResultBody<List<String>> listSite() {
        String username = (String) request.getAttribute("username");
        List<String> lstSite = biTzhBsUserSiteMapper.listSite(username);
        return ResultBody.success(lstSite);
    }

    @Override
    public ResultBody<List<EsgOrganizationVO>> listEsgOrg() {
        String username = (String) request.getAttribute("username");
//        IRequestContext current = RequestContextManager.getCurrent();
//        IUserPrincipal currentUser = current.getCurrentUser();
//        username=currentUser.getUsername();
        List<EsgOrganizationVO> lstSite = esgOrganizationMapper.listEsgOrg(username);
        return ResultBody.success(lstSite);
    }

    @Override
    public ResultBody<List<EsgOrganizationPermissionVO>> listEsgOrgPermission(String username) {
        List<EsgOrganizationPermissionVO> lstSite = esgOrganizationMapper.listEsgOrgPermission(username);
        return ResultBody.success(lstSite);
    }

    @Override
    public String getBySiteName(String sitename) {
        String siteid = esgOrganizationMapper.getBySiteName(sitename);
        return siteid;
    }

    @Override
    public ResultBody<List<EsgOrganizationVO>> listEsgOrgFilterMenu() {
        // ESG組織列表（过滤没有权限、没有配置协议、没有配置菜单）
        String username = (String) request.getAttribute("username");
        List<EsgOrganizationVO> lstSite = esgOrganizationMapper.listEsgOrgFilterMenu(username);
        return ResultBody.success(lstSite);
    }
}
