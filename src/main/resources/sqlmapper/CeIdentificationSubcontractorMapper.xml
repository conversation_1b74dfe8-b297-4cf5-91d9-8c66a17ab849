<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.CeIdentificationSubcontractorMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.CeIdentificationSubcontractor">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 18 15:40:16 HKT 2023.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="ce_identification_head_id" jdbcType="VARCHAR" property="ceIdentificationHeadId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="duty" jdbcType="VARCHAR" property="duty" />
    <result column="duty_type" jdbcType="VARCHAR" property="dutyType" />
    <result column="ss_fossil_fuel" jdbcType="VARCHAR" property="ssFossilFuel" />
    <result column="ms_fossil_fuel_qty" jdbcType="VARCHAR" property="msFossilFuelQty" />
    <result column="ms_fossil_fuel_ton_km" jdbcType="VARCHAR" property="msFossilFuelTonKm" />
    <result column="ms_fossil_fuel_machine_team" jdbcType="VARCHAR" property="msFossilFuelMachineTeam" />
    <result column="process_emission" jdbcType="VARCHAR" property="processEmission" />
    <result column="electricity" jdbcType="VARCHAR" property="electricity" />
    <result column="outsourced_building_material" jdbcType="VARCHAR" property="outsourcedBuildingMaterial" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="VARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="VARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="VARCHAR" property="lastUpdateUserId" />
    <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 18 15:40:16 HKT 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 18 15:40:16 HKT 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 18 15:40:16 HKT 2023.
    -->
    id, ce_identification_head_id, type, name, duty, duty_type, ss_fossil_fuel, ms_fossil_fuel_qty, 
    ms_fossil_fuel_ton_km, ms_fossil_fuel_machine_team, process_emission, electricity, 
    outsourced_building_material, creation_time, create_username, create_user_id, last_update_time, 
    last_update_username, last_update_user_id, last_update_version
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.CeIdentificationSubcontractorExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 18 15:40:16 HKT 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_ce_identification_subcontractor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 18 15:40:16 HKT 2023.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_ce_identification_subcontractor
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 18 15:40:16 HKT 2023.
    -->
    delete from t_ce_identification_subcontractor
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.CeIdentificationSubcontractorExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 18 15:40:16 HKT 2023.
    -->
    delete from t_ce_identification_subcontractor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.CeIdentificationSubcontractor">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 18 15:40:16 HKT 2023.
    -->
    insert into t_ce_identification_subcontractor (id, ce_identification_head_id, type, 
      name, duty, duty_type, 
      ss_fossil_fuel, ms_fossil_fuel_qty, ms_fossil_fuel_ton_km, 
      ms_fossil_fuel_machine_team, process_emission, 
      electricity, outsourced_building_material, 
      creation_time, create_username, create_user_id, 
      last_update_time, last_update_username, last_update_user_id, 
      last_update_version)
    values (#{id,jdbcType=VARCHAR}, #{ceIdentificationHeadId,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{duty,jdbcType=VARCHAR}, #{dutyType,jdbcType=VARCHAR}, 
      #{ssFossilFuel,jdbcType=VARCHAR}, #{msFossilFuelQty,jdbcType=VARCHAR}, #{msFossilFuelTonKm,jdbcType=VARCHAR}, 
      #{msFossilFuelMachineTeam,jdbcType=VARCHAR}, #{processEmission,jdbcType=VARCHAR}, 
      #{electricity,jdbcType=VARCHAR}, #{outsourcedBuildingMaterial,jdbcType=VARCHAR}, 
      #{creationTime,jdbcType=TIMESTAMP}, #{createUsername,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, 
      #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUsername,jdbcType=VARCHAR}, #{lastUpdateUserId,jdbcType=VARCHAR}, 
      #{lastUpdateVersion,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.CeIdentificationSubcontractor">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 18 15:40:16 HKT 2023.
    -->
    insert into t_ce_identification_subcontractor
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="ceIdentificationHeadId != null">
        ce_identification_head_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="duty != null">
        duty,
      </if>
      <if test="dutyType != null">
        duty_type,
      </if>
      <if test="ssFossilFuel != null">
        ss_fossil_fuel,
      </if>
      <if test="msFossilFuelQty != null">
        ms_fossil_fuel_qty,
      </if>
      <if test="msFossilFuelTonKm != null">
        ms_fossil_fuel_ton_km,
      </if>
      <if test="msFossilFuelMachineTeam != null">
        ms_fossil_fuel_machine_team,
      </if>
      <if test="processEmission != null">
        process_emission,
      </if>
      <if test="electricity != null">
        electricity,
      </if>
      <if test="outsourcedBuildingMaterial != null">
        outsourced_building_material,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="ceIdentificationHeadId != null">
        #{ceIdentificationHeadId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="duty != null">
        #{duty,jdbcType=VARCHAR},
      </if>
      <if test="dutyType != null">
        #{dutyType,jdbcType=VARCHAR},
      </if>
      <if test="ssFossilFuel != null">
        #{ssFossilFuel,jdbcType=VARCHAR},
      </if>
      <if test="msFossilFuelQty != null">
        #{msFossilFuelQty,jdbcType=VARCHAR},
      </if>
      <if test="msFossilFuelTonKm != null">
        #{msFossilFuelTonKm,jdbcType=VARCHAR},
      </if>
      <if test="msFossilFuelMachineTeam != null">
        #{msFossilFuelMachineTeam,jdbcType=VARCHAR},
      </if>
      <if test="processEmission != null">
        #{processEmission,jdbcType=VARCHAR},
      </if>
      <if test="electricity != null">
        #{electricity,jdbcType=VARCHAR},
      </if>
      <if test="outsourcedBuildingMaterial != null">
        #{outsourcedBuildingMaterial,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.CeIdentificationSubcontractorExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 18 15:40:16 HKT 2023.
    -->
    select count(*) from t_ce_identification_subcontractor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 18 15:40:16 HKT 2023.
    -->
    update t_ce_identification_subcontractor
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=VARCHAR},
      </if>
      <if test="row.ceIdentificationHeadId != null">
        ce_identification_head_id = #{row.ceIdentificationHeadId,jdbcType=VARCHAR},
      </if>
      <if test="row.type != null">
        type = #{row.type,jdbcType=VARCHAR},
      </if>
      <if test="row.name != null">
        name = #{row.name,jdbcType=VARCHAR},
      </if>
      <if test="row.duty != null">
        duty = #{row.duty,jdbcType=VARCHAR},
      </if>
      <if test="row.dutyType != null">
        duty_type = #{row.dutyType,jdbcType=VARCHAR},
      </if>
      <if test="row.ssFossilFuel != null">
        ss_fossil_fuel = #{row.ssFossilFuel,jdbcType=VARCHAR},
      </if>
      <if test="row.msFossilFuelQty != null">
        ms_fossil_fuel_qty = #{row.msFossilFuelQty,jdbcType=VARCHAR},
      </if>
      <if test="row.msFossilFuelTonKm != null">
        ms_fossil_fuel_ton_km = #{row.msFossilFuelTonKm,jdbcType=VARCHAR},
      </if>
      <if test="row.msFossilFuelMachineTeam != null">
        ms_fossil_fuel_machine_team = #{row.msFossilFuelMachineTeam,jdbcType=VARCHAR},
      </if>
      <if test="row.processEmission != null">
        process_emission = #{row.processEmission,jdbcType=VARCHAR},
      </if>
      <if test="row.electricity != null">
        electricity = #{row.electricity,jdbcType=VARCHAR},
      </if>
      <if test="row.outsourcedBuildingMaterial != null">
        outsourced_building_material = #{row.outsourcedBuildingMaterial,jdbcType=VARCHAR},
      </if>
      <if test="row.creationTime != null">
        creation_time = #{row.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createUsername != null">
        create_username = #{row.createUsername,jdbcType=VARCHAR},
      </if>
      <if test="row.createUserId != null">
        create_user_id = #{row.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateTime != null">
        last_update_time = #{row.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.lastUpdateUsername != null">
        last_update_username = #{row.lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateUserId != null">
        last_update_user_id = #{row.lastUpdateUserId,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateVersion != null">
        last_update_version = #{row.lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 18 15:40:16 HKT 2023.
    -->
    update t_ce_identification_subcontractor
    set id = #{row.id,jdbcType=VARCHAR},
      ce_identification_head_id = #{row.ceIdentificationHeadId,jdbcType=VARCHAR},
      type = #{row.type,jdbcType=VARCHAR},
      name = #{row.name,jdbcType=VARCHAR},
      duty = #{row.duty,jdbcType=VARCHAR},
      duty_type = #{row.dutyType,jdbcType=VARCHAR},
      ss_fossil_fuel = #{row.ssFossilFuel,jdbcType=VARCHAR},
      ms_fossil_fuel_qty = #{row.msFossilFuelQty,jdbcType=VARCHAR},
      ms_fossil_fuel_ton_km = #{row.msFossilFuelTonKm,jdbcType=VARCHAR},
      ms_fossil_fuel_machine_team = #{row.msFossilFuelMachineTeam,jdbcType=VARCHAR},
      process_emission = #{row.processEmission,jdbcType=VARCHAR},
      electricity = #{row.electricity,jdbcType=VARCHAR},
      outsourced_building_material = #{row.outsourcedBuildingMaterial,jdbcType=VARCHAR},
      creation_time = #{row.creationTime,jdbcType=TIMESTAMP},
      create_username = #{row.createUsername,jdbcType=VARCHAR},
      create_user_id = #{row.createUserId,jdbcType=VARCHAR},
      last_update_time = #{row.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{row.lastUpdateUsername,jdbcType=VARCHAR},
      last_update_user_id = #{row.lastUpdateUserId,jdbcType=VARCHAR},
      last_update_version = #{row.lastUpdateVersion,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.CeIdentificationSubcontractor">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 18 15:40:16 HKT 2023.
    -->
    update t_ce_identification_subcontractor
    <set>
      <if test="ceIdentificationHeadId != null">
        ce_identification_head_id = #{ceIdentificationHeadId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="duty != null">
        duty = #{duty,jdbcType=VARCHAR},
      </if>
      <if test="dutyType != null">
        duty_type = #{dutyType,jdbcType=VARCHAR},
      </if>
      <if test="ssFossilFuel != null">
        ss_fossil_fuel = #{ssFossilFuel,jdbcType=VARCHAR},
      </if>
      <if test="msFossilFuelQty != null">
        ms_fossil_fuel_qty = #{msFossilFuelQty,jdbcType=VARCHAR},
      </if>
      <if test="msFossilFuelTonKm != null">
        ms_fossil_fuel_ton_km = #{msFossilFuelTonKm,jdbcType=VARCHAR},
      </if>
      <if test="msFossilFuelMachineTeam != null">
        ms_fossil_fuel_machine_team = #{msFossilFuelMachineTeam,jdbcType=VARCHAR},
      </if>
      <if test="processEmission != null">
        process_emission = #{processEmission,jdbcType=VARCHAR},
      </if>
      <if test="electricity != null">
        electricity = #{electricity,jdbcType=VARCHAR},
      </if>
      <if test="outsourcedBuildingMaterial != null">
        outsourced_building_material = #{outsourcedBuildingMaterial,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id = #{lastUpdateUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.CeIdentificationSubcontractor">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 18 15:40:16 HKT 2023.
    -->
    update t_ce_identification_subcontractor
    set ce_identification_head_id = #{ceIdentificationHeadId,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      duty = #{duty,jdbcType=VARCHAR},
      duty_type = #{dutyType,jdbcType=VARCHAR},
      ss_fossil_fuel = #{ssFossilFuel,jdbcType=VARCHAR},
      ms_fossil_fuel_qty = #{msFossilFuelQty,jdbcType=VARCHAR},
      ms_fossil_fuel_ton_km = #{msFossilFuelTonKm,jdbcType=VARCHAR},
      ms_fossil_fuel_machine_team = #{msFossilFuelMachineTeam,jdbcType=VARCHAR},
      process_emission = #{processEmission,jdbcType=VARCHAR},
      electricity = #{electricity,jdbcType=VARCHAR},
      outsourced_building_material = #{outsourcedBuildingMaterial,jdbcType=VARCHAR},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=VARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=VARCHAR},
      last_update_version = #{lastUpdateVersion,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>