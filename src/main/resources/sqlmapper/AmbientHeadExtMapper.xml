<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.AmbientHeadExtMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.AmbientHeadExt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:28:18 HKT 2024.
    -->
    <id column="id" jdbcType="NVARCHAR" property="id" />
    <result column="organization_id" jdbcType="NVARCHAR" property="organizationId" />
    <result column="year" jdbcType="INTEGER" property="year" />
    <result column="month" jdbcType="INTEGER" property="month" />
    <result column="is_active" jdbcType="BIT" property="isActive" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="NVARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="CHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="NVARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="CHAR" property="lastUpdateUserId" />
    <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:28:18 HKT 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:28:18 HKT 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:28:18 HKT 2024.
    -->
    id, organization_id, year, month, is_active, creation_time, create_username, create_user_id, 
    last_update_time, last_update_username, last_update_user_id, last_update_version
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.AmbientHeadExtExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:28:18 HKT 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_ambient_head_ext
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:28:18 HKT 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_ambient_head_ext
    where id = #{id,jdbcType=NVARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:28:18 HKT 2024.
    -->
    delete from t_ambient_head_ext
    where id = #{id,jdbcType=NVARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.AmbientHeadExtExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:28:18 HKT 2024.
    -->
    delete from t_ambient_head_ext
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.AmbientHeadExt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:28:18 HKT 2024.
    -->
    insert into t_ambient_head_ext (id, organization_id, year, 
      month, is_active, creation_time, 
      create_username, create_user_id, last_update_time, 
      last_update_username, last_update_user_id, last_update_version
      )
    values (#{id,jdbcType=NVARCHAR}, #{organizationId,jdbcType=NVARCHAR}, #{year,jdbcType=INTEGER}, 
      #{month,jdbcType=INTEGER}, #{isActive,jdbcType=BIT}, #{creationTime,jdbcType=TIMESTAMP}, 
      #{createUsername,jdbcType=NVARCHAR}, #{createUserId,jdbcType=CHAR}, #{lastUpdateTime,jdbcType=TIMESTAMP}, 
      #{lastUpdateUsername,jdbcType=NVARCHAR}, #{lastUpdateUserId,jdbcType=CHAR}, #{lastUpdateVersion,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.AmbientHeadExt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:28:18 HKT 2024.
    -->
    insert into t_ambient_head_ext
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="organizationId != null">
        organization_id,
      </if>
      <if test="year != null">
        year,
      </if>
      <if test="month != null">
        month,
      </if>
      <if test="isActive != null">
        is_active,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=NVARCHAR},
      </if>
      <if test="organizationId != null">
        #{organizationId,jdbcType=NVARCHAR},
      </if>
      <if test="year != null">
        #{year,jdbcType=INTEGER},
      </if>
      <if test="month != null">
        #{month,jdbcType=INTEGER},
      </if>
      <if test="isActive != null">
        #{isActive,jdbcType=BIT},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=CHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=CHAR},
      </if>
      <if test="lastUpdateVersion != null">
        #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.AmbientHeadExtExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:28:18 HKT 2024.
    -->
    select count(*) from t_ambient_head_ext
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:28:18 HKT 2024.
    -->
    update t_ambient_head_ext
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=NVARCHAR},
      </if>
      <if test="record.organizationId != null">
        organization_id = #{record.organizationId,jdbcType=NVARCHAR},
      </if>
      <if test="record.year != null">
        year = #{record.year,jdbcType=INTEGER},
      </if>
      <if test="record.month != null">
        month = #{record.month,jdbcType=INTEGER},
      </if>
      <if test="record.isActive != null">
        is_active = #{record.isActive,jdbcType=BIT},
      </if>
      <if test="record.creationTime != null">
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUsername != null">
        create_username = #{record.createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=CHAR},
      </if>
      <if test="record.lastUpdateTime != null">
        last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateUsername != null">
        last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="record.lastUpdateUserId != null">
        last_update_user_id = #{record.lastUpdateUserId,jdbcType=CHAR},
      </if>
      <if test="record.lastUpdateVersion != null">
        last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:28:18 HKT 2024.
    -->
    update t_ambient_head_ext
    set id = #{record.id,jdbcType=NVARCHAR},
      organization_id = #{record.organizationId,jdbcType=NVARCHAR},
      year = #{record.year,jdbcType=INTEGER},
      month = #{record.month,jdbcType=INTEGER},
      is_active = #{record.isActive,jdbcType=BIT},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      create_username = #{record.createUsername,jdbcType=NVARCHAR},
      create_user_id = #{record.createUserId,jdbcType=CHAR},
      last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{record.lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{record.lastUpdateUserId,jdbcType=CHAR},
      last_update_version = #{record.lastUpdateVersion,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.AmbientHeadExt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:28:18 HKT 2024.
    -->
    update t_ambient_head_ext
    <set>
      <if test="organizationId != null">
        organization_id = #{organizationId,jdbcType=NVARCHAR},
      </if>
      <if test="year != null">
        year = #{year,jdbcType=INTEGER},
      </if>
      <if test="month != null">
        month = #{month,jdbcType=INTEGER},
      </if>
      <if test="isActive != null">
        is_active = #{isActive,jdbcType=BIT},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=NVARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=CHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id = #{lastUpdateUserId,jdbcType=CHAR},
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=NVARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.AmbientHeadExt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:28:18 HKT 2024.
    -->
    update t_ambient_head_ext
    set organization_id = #{organizationId,jdbcType=NVARCHAR},
      year = #{year,jdbcType=INTEGER},
      month = #{month,jdbcType=INTEGER},
      is_active = #{isActive,jdbcType=BIT},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=NVARCHAR},
      create_user_id = #{createUserId,jdbcType=CHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=NVARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=CHAR},
      last_update_version = #{lastUpdateVersion,jdbcType=INTEGER}
    where id = #{id,jdbcType=NVARCHAR}
  </update>
</mapper>