<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.FfCmMobileDetailMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.FfCmMobileDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Sep 29 12:25:19 HKT 2023.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="head_id" jdbcType="VARCHAR" property="headId" />
    <result column="col_1" jdbcType="VARCHAR" property="col1" />
    <result column="col_2" jdbcType="VARCHAR" property="col2" />
    <result column="col_3" jdbcType="VARCHAR" property="col3" />
    <result column="col_4" jdbcType="VARCHAR" property="col4" />
    <result column="col_5" jdbcType="VARCHAR" property="col5" />
    <result column="col_6" jdbcType="VARCHAR" property="col6" />
    <result column="col_7" jdbcType="VARCHAR" property="col7" />
    <result column="col_8" jdbcType="VARCHAR" property="col8" />
    <result column="col_9" jdbcType="VARCHAR" property="col9" />
    <result column="col_10" jdbcType="VARCHAR" property="col10" />
    <result column="col_11" jdbcType="VARCHAR" property="col11" />
    <result column="col_12" jdbcType="VARCHAR" property="col12" />
    <result column="col_13" jdbcType="VARCHAR" property="col13" />
    <result column="col_14" jdbcType="VARCHAR" property="col14" />
    <result column="col_15" jdbcType="VARCHAR" property="col15" />
    <result column="col_16" jdbcType="VARCHAR" property="col16" />
    <result column="seq" jdbcType="INTEGER" property="seq" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Sep 29 12:25:19 HKT 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Sep 29 12:25:19 HKT 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Sep 29 12:25:19 HKT 2023.
    -->
    id, head_id, col_1, col_2, col_3, col_4, col_5, col_6, col_7, col_8, col_9, col_10, 
    col_11, col_12, col_13, col_14, col_15, col_16, seq, creation_time
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.FfCmMobileDetailExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Sep 29 12:25:19 HKT 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_ff_cm_mobile_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Sep 29 12:25:19 HKT 2023.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_ff_cm_mobile_detail
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Sep 29 12:25:19 HKT 2023.
    -->
    delete from t_ff_cm_mobile_detail
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.FfCmMobileDetailExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Sep 29 12:25:19 HKT 2023.
    -->
    delete from t_ff_cm_mobile_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.FfCmMobileDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Sep 29 12:25:19 HKT 2023.
    -->
    insert into t_ff_cm_mobile_detail (id, head_id, col_1, 
      col_2, col_3, col_4, col_5, 
      col_6, col_7, col_8, col_9, 
      col_10, col_11, col_12, 
      col_13, col_14, col_15, 
      col_16, seq, creation_time
      )
    values (#{id,jdbcType=VARCHAR}, #{headId,jdbcType=VARCHAR}, #{col1,jdbcType=VARCHAR}, 
      #{col2,jdbcType=VARCHAR}, #{col3,jdbcType=VARCHAR}, #{col4,jdbcType=VARCHAR}, #{col5,jdbcType=VARCHAR}, 
      #{col6,jdbcType=VARCHAR}, #{col7,jdbcType=VARCHAR}, #{col8,jdbcType=VARCHAR}, #{col9,jdbcType=VARCHAR}, 
      #{col10,jdbcType=VARCHAR}, #{col11,jdbcType=VARCHAR}, #{col12,jdbcType=VARCHAR}, 
      #{col13,jdbcType=VARCHAR}, #{col14,jdbcType=VARCHAR}, #{col15,jdbcType=VARCHAR}, 
      #{col16,jdbcType=VARCHAR}, #{seq,jdbcType=INTEGER}, #{creationTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.FfCmMobileDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Sep 29 12:25:19 HKT 2023.
    -->
    insert into t_ff_cm_mobile_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="headId != null">
        head_id,
      </if>
      <if test="col1 != null">
        col_1,
      </if>
      <if test="col2 != null">
        col_2,
      </if>
      <if test="col3 != null">
        col_3,
      </if>
      <if test="col4 != null">
        col_4,
      </if>
      <if test="col5 != null">
        col_5,
      </if>
      <if test="col6 != null">
        col_6,
      </if>
      <if test="col7 != null">
        col_7,
      </if>
      <if test="col8 != null">
        col_8,
      </if>
      <if test="col9 != null">
        col_9,
      </if>
      <if test="col10 != null">
        col_10,
      </if>
      <if test="col11 != null">
        col_11,
      </if>
      <if test="col12 != null">
        col_12,
      </if>
      <if test="col13 != null">
        col_13,
      </if>
      <if test="col14 != null">
        col_14,
      </if>
      <if test="col15 != null">
        col_15,
      </if>
      <if test="col16 != null">
        col_16,
      </if>
      <if test="seq != null">
        seq,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="headId != null">
        #{headId,jdbcType=VARCHAR},
      </if>
      <if test="col1 != null">
        #{col1,jdbcType=VARCHAR},
      </if>
      <if test="col2 != null">
        #{col2,jdbcType=VARCHAR},
      </if>
      <if test="col3 != null">
        #{col3,jdbcType=VARCHAR},
      </if>
      <if test="col4 != null">
        #{col4,jdbcType=VARCHAR},
      </if>
      <if test="col5 != null">
        #{col5,jdbcType=VARCHAR},
      </if>
      <if test="col6 != null">
        #{col6,jdbcType=VARCHAR},
      </if>
      <if test="col7 != null">
        #{col7,jdbcType=VARCHAR},
      </if>
      <if test="col8 != null">
        #{col8,jdbcType=VARCHAR},
      </if>
      <if test="col9 != null">
        #{col9,jdbcType=VARCHAR},
      </if>
      <if test="col10 != null">
        #{col10,jdbcType=VARCHAR},
      </if>
      <if test="col11 != null">
        #{col11,jdbcType=VARCHAR},
      </if>
      <if test="col12 != null">
        #{col12,jdbcType=VARCHAR},
      </if>
      <if test="col13 != null">
        #{col13,jdbcType=VARCHAR},
      </if>
      <if test="col14 != null">
        #{col14,jdbcType=VARCHAR},
      </if>
      <if test="col15 != null">
        #{col15,jdbcType=VARCHAR},
      </if>
      <if test="col16 != null">
        #{col16,jdbcType=VARCHAR},
      </if>
      <if test="seq != null">
        #{seq,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.FfCmMobileDetailExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Sep 29 12:25:19 HKT 2023.
    -->
    select count(*) from t_ff_cm_mobile_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Sep 29 12:25:19 HKT 2023.
    -->
    update t_ff_cm_mobile_detail
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=VARCHAR},
      </if>
      <if test="row.headId != null">
        head_id = #{row.headId,jdbcType=VARCHAR},
      </if>
      <if test="row.col1 != null">
        col_1 = #{row.col1,jdbcType=VARCHAR},
      </if>
      <if test="row.col2 != null">
        col_2 = #{row.col2,jdbcType=VARCHAR},
      </if>
      <if test="row.col3 != null">
        col_3 = #{row.col3,jdbcType=VARCHAR},
      </if>
      <if test="row.col4 != null">
        col_4 = #{row.col4,jdbcType=VARCHAR},
      </if>
      <if test="row.col5 != null">
        col_5 = #{row.col5,jdbcType=VARCHAR},
      </if>
      <if test="row.col6 != null">
        col_6 = #{row.col6,jdbcType=VARCHAR},
      </if>
      <if test="row.col7 != null">
        col_7 = #{row.col7,jdbcType=VARCHAR},
      </if>
      <if test="row.col8 != null">
        col_8 = #{row.col8,jdbcType=VARCHAR},
      </if>
      <if test="row.col9 != null">
        col_9 = #{row.col9,jdbcType=VARCHAR},
      </if>
      <if test="row.col10 != null">
        col_10 = #{row.col10,jdbcType=VARCHAR},
      </if>
      <if test="row.col11 != null">
        col_11 = #{row.col11,jdbcType=VARCHAR},
      </if>
      <if test="row.col12 != null">
        col_12 = #{row.col12,jdbcType=VARCHAR},
      </if>
      <if test="row.col13 != null">
        col_13 = #{row.col13,jdbcType=VARCHAR},
      </if>
      <if test="row.col14 != null">
        col_14 = #{row.col14,jdbcType=VARCHAR},
      </if>
      <if test="row.col15 != null">
        col_15 = #{row.col15,jdbcType=VARCHAR},
      </if>
      <if test="row.col16 != null">
        col_16 = #{row.col16,jdbcType=VARCHAR},
      </if>
      <if test="row.seq != null">
        seq = #{row.seq,jdbcType=INTEGER},
      </if>
      <if test="row.creationTime != null">
        creation_time = #{row.creationTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Sep 29 12:25:19 HKT 2023.
    -->
    update t_ff_cm_mobile_detail
    set id = #{row.id,jdbcType=VARCHAR},
      head_id = #{row.headId,jdbcType=VARCHAR},
      col_1 = #{row.col1,jdbcType=VARCHAR},
      col_2 = #{row.col2,jdbcType=VARCHAR},
      col_3 = #{row.col3,jdbcType=VARCHAR},
      col_4 = #{row.col4,jdbcType=VARCHAR},
      col_5 = #{row.col5,jdbcType=VARCHAR},
      col_6 = #{row.col6,jdbcType=VARCHAR},
      col_7 = #{row.col7,jdbcType=VARCHAR},
      col_8 = #{row.col8,jdbcType=VARCHAR},
      col_9 = #{row.col9,jdbcType=VARCHAR},
      col_10 = #{row.col10,jdbcType=VARCHAR},
      col_11 = #{row.col11,jdbcType=VARCHAR},
      col_12 = #{row.col12,jdbcType=VARCHAR},
      col_13 = #{row.col13,jdbcType=VARCHAR},
      col_14 = #{row.col14,jdbcType=VARCHAR},
      col_15 = #{row.col15,jdbcType=VARCHAR},
      col_16 = #{row.col16,jdbcType=VARCHAR},
      seq = #{row.seq,jdbcType=INTEGER},
      creation_time = #{row.creationTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.FfCmMobileDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Sep 29 12:25:19 HKT 2023.
    -->
    update t_ff_cm_mobile_detail
    <set>
      <if test="headId != null">
        head_id = #{headId,jdbcType=VARCHAR},
      </if>
      <if test="col1 != null">
        col_1 = #{col1,jdbcType=VARCHAR},
      </if>
      <if test="col2 != null">
        col_2 = #{col2,jdbcType=VARCHAR},
      </if>
      <if test="col3 != null">
        col_3 = #{col3,jdbcType=VARCHAR},
      </if>
      <if test="col4 != null">
        col_4 = #{col4,jdbcType=VARCHAR},
      </if>
      <if test="col5 != null">
        col_5 = #{col5,jdbcType=VARCHAR},
      </if>
      <if test="col6 != null">
        col_6 = #{col6,jdbcType=VARCHAR},
      </if>
      <if test="col7 != null">
        col_7 = #{col7,jdbcType=VARCHAR},
      </if>
      <if test="col8 != null">
        col_8 = #{col8,jdbcType=VARCHAR},
      </if>
      <if test="col9 != null">
        col_9 = #{col9,jdbcType=VARCHAR},
      </if>
      <if test="col10 != null">
        col_10 = #{col10,jdbcType=VARCHAR},
      </if>
      <if test="col11 != null">
        col_11 = #{col11,jdbcType=VARCHAR},
      </if>
      <if test="col12 != null">
        col_12 = #{col12,jdbcType=VARCHAR},
      </if>
      <if test="col13 != null">
        col_13 = #{col13,jdbcType=VARCHAR},
      </if>
      <if test="col14 != null">
        col_14 = #{col14,jdbcType=VARCHAR},
      </if>
      <if test="col15 != null">
        col_15 = #{col15,jdbcType=VARCHAR},
      </if>
      <if test="col16 != null">
        col_16 = #{col16,jdbcType=VARCHAR},
      </if>
      <if test="seq != null">
        seq = #{seq,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.FfCmMobileDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Sep 29 12:25:19 HKT 2023.
    -->
    update t_ff_cm_mobile_detail
    set head_id = #{headId,jdbcType=VARCHAR},
      col_1 = #{col1,jdbcType=VARCHAR},
      col_2 = #{col2,jdbcType=VARCHAR},
      col_3 = #{col3,jdbcType=VARCHAR},
      col_4 = #{col4,jdbcType=VARCHAR},
      col_5 = #{col5,jdbcType=VARCHAR},
      col_6 = #{col6,jdbcType=VARCHAR},
      col_7 = #{col7,jdbcType=VARCHAR},
      col_8 = #{col8,jdbcType=VARCHAR},
      col_9 = #{col9,jdbcType=VARCHAR},
      col_10 = #{col10,jdbcType=VARCHAR},
      col_11 = #{col11,jdbcType=VARCHAR},
      col_12 = #{col12,jdbcType=VARCHAR},
      col_13 = #{col13,jdbcType=VARCHAR},
      col_14 = #{col14,jdbcType=VARCHAR},
      col_15 = #{col15,jdbcType=VARCHAR},
      col_16 = #{col16,jdbcType=VARCHAR},
      seq = #{seq,jdbcType=INTEGER},
      creation_time = #{creationTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>