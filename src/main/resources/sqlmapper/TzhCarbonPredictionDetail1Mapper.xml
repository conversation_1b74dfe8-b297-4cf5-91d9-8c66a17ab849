<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.TzhCarbonPredictionDetail1Mapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.TzhCarbonPredictionDetail1">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:35:09 HKT 2022.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="head_id" jdbcType="VARCHAR" property="headId" />
    <result column="col_1" jdbcType="VARCHAR" property="col1" />
    <result column="col_2" jdbcType="VARCHAR" property="col2" />
    <result column="col_3" jdbcType="VARCHAR" property="col3" />
    <result column="col_4" jdbcType="VARCHAR" property="col4" />
    <result column="col_5" jdbcType="VARCHAR" property="col5" />
    <result column="col_6" jdbcType="VARCHAR" property="col6" />
    <result column="seq" jdbcType="INTEGER" property="seq" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:35:09 HKT 2022.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:35:09 HKT 2022.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:35:09 HKT 2022.
    -->
    id, head_id, col_1, col_2, col_3, col_4, col_5, col_6, seq
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.TzhCarbonPredictionDetail1Example" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:35:09 HKT 2022.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_tzh_carbon_prediction_detail_1
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:35:09 HKT 2022.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_tzh_carbon_prediction_detail_1
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:35:09 HKT 2022.
    -->
    delete from t_tzh_carbon_prediction_detail_1
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.TzhCarbonPredictionDetail1Example">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:35:09 HKT 2022.
    -->
    delete from t_tzh_carbon_prediction_detail_1
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.TzhCarbonPredictionDetail1">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:35:09 HKT 2022.
    -->
    insert into t_tzh_carbon_prediction_detail_1 (id, head_id, col_1, 
      col_2, col_3, col_4, col_5, 
      col_6, seq)
    values (#{id,jdbcType=VARCHAR}, #{headId,jdbcType=VARCHAR}, #{col1,jdbcType=VARCHAR}, 
      #{col2,jdbcType=VARCHAR}, #{col3,jdbcType=VARCHAR}, #{col4,jdbcType=VARCHAR}, #{col5,jdbcType=VARCHAR}, 
      #{col6,jdbcType=VARCHAR}, #{seq,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.TzhCarbonPredictionDetail1">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:35:09 HKT 2022.
    -->
    insert into t_tzh_carbon_prediction_detail_1
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="headId != null">
        head_id,
      </if>
      <if test="col1 != null">
        col_1,
      </if>
      <if test="col2 != null">
        col_2,
      </if>
      <if test="col3 != null">
        col_3,
      </if>
      <if test="col4 != null">
        col_4,
      </if>
      <if test="col5 != null">
        col_5,
      </if>
      <if test="col6 != null">
        col_6,
      </if>
      <if test="seq != null">
        seq,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="headId != null">
        #{headId,jdbcType=VARCHAR},
      </if>
      <if test="col1 != null">
        #{col1,jdbcType=VARCHAR},
      </if>
      <if test="col2 != null">
        #{col2,jdbcType=VARCHAR},
      </if>
      <if test="col3 != null">
        #{col3,jdbcType=VARCHAR},
      </if>
      <if test="col4 != null">
        #{col4,jdbcType=VARCHAR},
      </if>
      <if test="col5 != null">
        #{col5,jdbcType=VARCHAR},
      </if>
      <if test="col6 != null">
        #{col6,jdbcType=VARCHAR},
      </if>
      <if test="seq != null">
        #{seq,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.TzhCarbonPredictionDetail1Example" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:35:09 HKT 2022.
    -->
    select count(*) from t_tzh_carbon_prediction_detail_1
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:35:09 HKT 2022.
    -->
    update t_tzh_carbon_prediction_detail_1
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=VARCHAR},
      </if>
      <if test="row.headId != null">
        head_id = #{row.headId,jdbcType=VARCHAR},
      </if>
      <if test="row.col1 != null">
        col_1 = #{row.col1,jdbcType=VARCHAR},
      </if>
      <if test="row.col2 != null">
        col_2 = #{row.col2,jdbcType=VARCHAR},
      </if>
      <if test="row.col3 != null">
        col_3 = #{row.col3,jdbcType=VARCHAR},
      </if>
      <if test="row.col4 != null">
        col_4 = #{row.col4,jdbcType=VARCHAR},
      </if>
      <if test="row.col5 != null">
        col_5 = #{row.col5,jdbcType=VARCHAR},
      </if>
      <if test="row.col6 != null">
        col_6 = #{row.col6,jdbcType=VARCHAR},
      </if>
      <if test="row.seq != null">
        seq = #{row.seq,jdbcType=INTEGER},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:35:09 HKT 2022.
    -->
    update t_tzh_carbon_prediction_detail_1
    set id = #{row.id,jdbcType=VARCHAR},
      head_id = #{row.headId,jdbcType=VARCHAR},
      col_1 = #{row.col1,jdbcType=VARCHAR},
      col_2 = #{row.col2,jdbcType=VARCHAR},
      col_3 = #{row.col3,jdbcType=VARCHAR},
      col_4 = #{row.col4,jdbcType=VARCHAR},
      col_5 = #{row.col5,jdbcType=VARCHAR},
      col_6 = #{row.col6,jdbcType=VARCHAR},
      seq = #{row.seq,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.TzhCarbonPredictionDetail1">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:35:09 HKT 2022.
    -->
    update t_tzh_carbon_prediction_detail_1
    <set>
      <if test="headId != null">
        head_id = #{headId,jdbcType=VARCHAR},
      </if>
      <if test="col1 != null">
        col_1 = #{col1,jdbcType=VARCHAR},
      </if>
      <if test="col2 != null">
        col_2 = #{col2,jdbcType=VARCHAR},
      </if>
      <if test="col3 != null">
        col_3 = #{col3,jdbcType=VARCHAR},
      </if>
      <if test="col4 != null">
        col_4 = #{col4,jdbcType=VARCHAR},
      </if>
      <if test="col5 != null">
        col_5 = #{col5,jdbcType=VARCHAR},
      </if>
      <if test="col6 != null">
        col_6 = #{col6,jdbcType=VARCHAR},
      </if>
      <if test="seq != null">
        seq = #{seq,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.TzhCarbonPredictionDetail1">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 26 12:35:09 HKT 2022.
    -->
    update t_tzh_carbon_prediction_detail_1
    set head_id = #{headId,jdbcType=VARCHAR},
      col_1 = #{col1,jdbcType=VARCHAR},
      col_2 = #{col2,jdbcType=VARCHAR},
      col_3 = #{col3,jdbcType=VARCHAR},
      col_4 = #{col4,jdbcType=VARCHAR},
      col_5 = #{col5,jdbcType=VARCHAR},
      col_6 = #{col6,jdbcType=VARCHAR},
      seq = #{seq,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>