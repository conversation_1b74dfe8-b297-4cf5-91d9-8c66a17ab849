<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.SocialPerfTwoActivityMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.SocialPerfTwoActivity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 19 10:34:43 HKT 2023.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="head_id" jdbcType="VARCHAR" property="headId" />
    <result column="organization_id" jdbcType="VARCHAR" property="organizationId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="date" jdbcType="TIMESTAMP" property="date" />
    <result column="staff_count" jdbcType="VARCHAR" property="staffCount" />
    <result column="hour_count" jdbcType="VARCHAR" property="hourCount" />
    <result column="service_target" jdbcType="VARCHAR" property="serviceTarget" />
    <result column="beneficiary_organization" jdbcType="VARCHAR" property="beneficiaryOrganization" />
    <result column="no_of_beneficiaries" jdbcType="VARCHAR" property="noOfBeneficiaries" />
    <result column="total_donation" jdbcType="VARCHAR" property="totalDonation" />
    <result column="donation_type" jdbcType="VARCHAR" property="donationType" />
    <result column="donation_qty" jdbcType="VARCHAR" property="donationQty" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="VARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="VARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="VARCHAR" property="lastUpdateUserId" />
    <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 19 10:34:43 HKT 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 19 10:34:43 HKT 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 19 10:34:43 HKT 2023.
    -->
    id, head_id, organization_id, type, name, date, staff_count, hour_count, service_target, 
    beneficiary_organization, no_of_beneficiaries, total_donation, donation_type, donation_qty, 
    creation_time, create_username, create_user_id, last_update_time, last_update_username, 
    last_update_user_id, last_update_version
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.SocialPerfTwoActivityExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 19 10:34:43 HKT 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_social_perf_two_activity
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 19 10:34:43 HKT 2023.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_social_perf_two_activity
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 19 10:34:43 HKT 2023.
    -->
    delete from t_social_perf_two_activity
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.SocialPerfTwoActivityExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 19 10:34:43 HKT 2023.
    -->
    delete from t_social_perf_two_activity
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.SocialPerfTwoActivity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 19 10:34:43 HKT 2023.
    -->
    insert into t_social_perf_two_activity (id, head_id, organization_id, 
      type, name, date, 
      staff_count, hour_count, service_target, 
      beneficiary_organization, no_of_beneficiaries, 
      total_donation, donation_type, donation_qty, 
      creation_time, create_username, create_user_id, 
      last_update_time, last_update_username, last_update_user_id, 
      last_update_version)
    values (#{id,jdbcType=VARCHAR}, #{headId,jdbcType=VARCHAR}, #{organizationId,jdbcType=VARCHAR}, 
      #{type,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{date,jdbcType=TIMESTAMP}, 
      #{staffCount,jdbcType=VARCHAR}, #{hourCount,jdbcType=VARCHAR}, #{serviceTarget,jdbcType=VARCHAR}, 
      #{beneficiaryOrganization,jdbcType=VARCHAR}, #{noOfBeneficiaries,jdbcType=VARCHAR}, 
      #{totalDonation,jdbcType=VARCHAR}, #{donationType,jdbcType=VARCHAR}, #{donationQty,jdbcType=VARCHAR}, 
      #{creationTime,jdbcType=TIMESTAMP}, #{createUsername,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, 
      #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUsername,jdbcType=VARCHAR}, #{lastUpdateUserId,jdbcType=VARCHAR}, 
      #{lastUpdateVersion,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.SocialPerfTwoActivity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 19 10:34:43 HKT 2023.
    -->
    insert into t_social_perf_two_activity
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="headId != null">
        head_id,
      </if>
      <if test="organizationId != null">
        organization_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="date != null">
        date,
      </if>
      <if test="staffCount != null">
        staff_count,
      </if>
      <if test="hourCount != null">
        hour_count,
      </if>
      <if test="serviceTarget != null">
        service_target,
      </if>
      <if test="beneficiaryOrganization != null">
        beneficiary_organization,
      </if>
      <if test="noOfBeneficiaries != null">
        no_of_beneficiaries,
      </if>
      <if test="totalDonation != null">
        total_donation,
      </if>
      <if test="donationType != null">
        donation_type,
      </if>
      <if test="donationQty != null">
        donation_qty,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="headId != null">
        #{headId,jdbcType=VARCHAR},
      </if>
      <if test="organizationId != null">
        #{organizationId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="date != null">
        #{date,jdbcType=TIMESTAMP},
      </if>
      <if test="staffCount != null">
        #{staffCount,jdbcType=VARCHAR},
      </if>
      <if test="hourCount != null">
        #{hourCount,jdbcType=VARCHAR},
      </if>
      <if test="serviceTarget != null">
        #{serviceTarget,jdbcType=VARCHAR},
      </if>
      <if test="beneficiaryOrganization != null">
        #{beneficiaryOrganization,jdbcType=VARCHAR},
      </if>
      <if test="noOfBeneficiaries != null">
        #{noOfBeneficiaries,jdbcType=VARCHAR},
      </if>
      <if test="totalDonation != null">
        #{totalDonation,jdbcType=VARCHAR},
      </if>
      <if test="donationType != null">
        #{donationType,jdbcType=VARCHAR},
      </if>
      <if test="donationQty != null">
        #{donationQty,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.SocialPerfTwoActivityExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 19 10:34:43 HKT 2023.
    -->
    select count(*) from t_social_perf_two_activity
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 19 10:34:43 HKT 2023.
    -->
    update t_social_perf_two_activity
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=VARCHAR},
      </if>
      <if test="row.headId != null">
        head_id = #{row.headId,jdbcType=VARCHAR},
      </if>
      <if test="row.organizationId != null">
        organization_id = #{row.organizationId,jdbcType=VARCHAR},
      </if>
      <if test="row.type != null">
        type = #{row.type,jdbcType=VARCHAR},
      </if>
      <if test="row.name != null">
        name = #{row.name,jdbcType=VARCHAR},
      </if>
      <if test="row.date != null">
        date = #{row.date,jdbcType=TIMESTAMP},
      </if>
      <if test="row.staffCount != null">
        staff_count = #{row.staffCount,jdbcType=VARCHAR},
      </if>
      <if test="row.hourCount != null">
        hour_count = #{row.hourCount,jdbcType=VARCHAR},
      </if>
      <if test="row.serviceTarget != null">
        service_target = #{row.serviceTarget,jdbcType=VARCHAR},
      </if>
      <if test="row.beneficiaryOrganization != null">
        beneficiary_organization = #{row.beneficiaryOrganization,jdbcType=VARCHAR},
      </if>
      <if test="row.noOfBeneficiaries != null">
        no_of_beneficiaries = #{row.noOfBeneficiaries,jdbcType=VARCHAR},
      </if>
      <if test="row.totalDonation != null">
        total_donation = #{row.totalDonation,jdbcType=VARCHAR},
      </if>
      <if test="row.donationType != null">
        donation_type = #{row.donationType,jdbcType=VARCHAR},
      </if>
      <if test="row.donationQty != null">
        donation_qty = #{row.donationQty,jdbcType=VARCHAR},
      </if>
      <if test="row.creationTime != null">
        creation_time = #{row.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createUsername != null">
        create_username = #{row.createUsername,jdbcType=VARCHAR},
      </if>
      <if test="row.createUserId != null">
        create_user_id = #{row.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateTime != null">
        last_update_time = #{row.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.lastUpdateUsername != null">
        last_update_username = #{row.lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateUserId != null">
        last_update_user_id = #{row.lastUpdateUserId,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateVersion != null">
        last_update_version = #{row.lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 19 10:34:43 HKT 2023.
    -->
    update t_social_perf_two_activity
    set id = #{row.id,jdbcType=VARCHAR},
      head_id = #{row.headId,jdbcType=VARCHAR},
      organization_id = #{row.organizationId,jdbcType=VARCHAR},
      type = #{row.type,jdbcType=VARCHAR},
      name = #{row.name,jdbcType=VARCHAR},
      date = #{row.date,jdbcType=TIMESTAMP},
      staff_count = #{row.staffCount,jdbcType=VARCHAR},
      hour_count = #{row.hourCount,jdbcType=VARCHAR},
      service_target = #{row.serviceTarget,jdbcType=VARCHAR},
      beneficiary_organization = #{row.beneficiaryOrganization,jdbcType=VARCHAR},
      no_of_beneficiaries = #{row.noOfBeneficiaries,jdbcType=VARCHAR},
      total_donation = #{row.totalDonation,jdbcType=VARCHAR},
      donation_type = #{row.donationType,jdbcType=VARCHAR},
      donation_qty = #{row.donationQty,jdbcType=VARCHAR},
      creation_time = #{row.creationTime,jdbcType=TIMESTAMP},
      create_username = #{row.createUsername,jdbcType=VARCHAR},
      create_user_id = #{row.createUserId,jdbcType=VARCHAR},
      last_update_time = #{row.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{row.lastUpdateUsername,jdbcType=VARCHAR},
      last_update_user_id = #{row.lastUpdateUserId,jdbcType=VARCHAR},
      last_update_version = #{row.lastUpdateVersion,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.SocialPerfTwoActivity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 19 10:34:43 HKT 2023.
    -->
    update t_social_perf_two_activity
    <set>
      <if test="headId != null">
        head_id = #{headId,jdbcType=VARCHAR},
      </if>
      <if test="organizationId != null">
        organization_id = #{organizationId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="date != null">
        date = #{date,jdbcType=TIMESTAMP},
      </if>
      <if test="staffCount != null">
        staff_count = #{staffCount,jdbcType=VARCHAR},
      </if>
      <if test="hourCount != null">
        hour_count = #{hourCount,jdbcType=VARCHAR},
      </if>
      <if test="serviceTarget != null">
        service_target = #{serviceTarget,jdbcType=VARCHAR},
      </if>
      <if test="beneficiaryOrganization != null">
        beneficiary_organization = #{beneficiaryOrganization,jdbcType=VARCHAR},
      </if>
      <if test="noOfBeneficiaries != null">
        no_of_beneficiaries = #{noOfBeneficiaries,jdbcType=VARCHAR},
      </if>
      <if test="totalDonation != null">
        total_donation = #{totalDonation,jdbcType=VARCHAR},
      </if>
      <if test="donationType != null">
        donation_type = #{donationType,jdbcType=VARCHAR},
      </if>
      <if test="donationQty != null">
        donation_qty = #{donationQty,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id = #{lastUpdateUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.SocialPerfTwoActivity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Dec 19 10:34:43 HKT 2023.
    -->
    update t_social_perf_two_activity
    set head_id = #{headId,jdbcType=VARCHAR},
      organization_id = #{organizationId,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      date = #{date,jdbcType=TIMESTAMP},
      staff_count = #{staffCount,jdbcType=VARCHAR},
      hour_count = #{hourCount,jdbcType=VARCHAR},
      service_target = #{serviceTarget,jdbcType=VARCHAR},
      beneficiary_organization = #{beneficiaryOrganization,jdbcType=VARCHAR},
      no_of_beneficiaries = #{noOfBeneficiaries,jdbcType=VARCHAR},
      total_donation = #{totalDonation,jdbcType=VARCHAR},
      donation_type = #{donationType,jdbcType=VARCHAR},
      donation_qty = #{donationQty,jdbcType=VARCHAR},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=VARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=VARCHAR},
      last_update_version = #{lastUpdateVersion,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>