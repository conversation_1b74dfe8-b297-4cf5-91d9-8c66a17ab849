<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.AmbientDetailExtMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.AmbientDetailExt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:27:50 HKT 2024.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="head_id" jdbcType="NVARCHAR" property="headId" />
    <result column="category" jdbcType="NVARCHAR" property="category" />
    <result column="category_digest" jdbcType="NVARCHAR" property="categoryDigest" />
    <result column="type" jdbcType="NVARCHAR" property="type" />
    <result column="type2" jdbcType="NVARCHAR" property="type2" />
    <result column="unit" jdbcType="NVARCHAR" property="unit" />
    <result column="unit_code" jdbcType="NVARCHAR" property="unitCode" />
    <result column="month_value_1" jdbcType="NVARCHAR" property="monthValue1" />
    <result column="month_value_2" jdbcType="NVARCHAR" property="monthValue2" />
    <result column="month_value_3" jdbcType="NVARCHAR" property="monthValue3" />
    <result column="season_value_1" jdbcType="NVARCHAR" property="seasonValue1" />
    <result column="month_value_4" jdbcType="NVARCHAR" property="monthValue4" />
    <result column="month_value_5" jdbcType="NVARCHAR" property="monthValue5" />
    <result column="month_value_6" jdbcType="NVARCHAR" property="monthValue6" />
    <result column="season_value_2" jdbcType="NVARCHAR" property="seasonValue2" />
    <result column="month_value_7" jdbcType="NVARCHAR" property="monthValue7" />
    <result column="month_value_8" jdbcType="NVARCHAR" property="monthValue8" />
    <result column="month_value_9" jdbcType="NVARCHAR" property="monthValue9" />
    <result column="season_value_3" jdbcType="NVARCHAR" property="seasonValue3" />
    <result column="month_value_10" jdbcType="NVARCHAR" property="monthValue10" />
    <result column="month_value_11" jdbcType="NVARCHAR" property="monthValue11" />
    <result column="month_value_12" jdbcType="NVARCHAR" property="monthValue12" />
    <result column="season_value_4" jdbcType="NVARCHAR" property="seasonValue4" />
    <result column="year_total_value" jdbcType="NVARCHAR" property="yearTotalValue" />
    <result column="remark" jdbcType="NVARCHAR" property="remark" />
    <result column="seq" jdbcType="INTEGER" property="seq" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:27:50 HKT 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:27:50 HKT 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:27:50 HKT 2024.
    -->
    id, head_id, category, category_digest, type, type2, unit, unit_code, month_value_1, 
    month_value_2, month_value_3, season_value_1, month_value_4, month_value_5, month_value_6, 
    season_value_2, month_value_7, month_value_8, month_value_9, season_value_3, month_value_10, 
    month_value_11, month_value_12, season_value_4, year_total_value, remark, seq, creation_time
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.AmbientDetailExtExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:27:50 HKT 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_ambient_detail_ext
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:27:50 HKT 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_ambient_detail_ext
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:27:50 HKT 2024.
    -->
    delete from t_ambient_detail_ext
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.AmbientDetailExtExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:27:50 HKT 2024.
    -->
    delete from t_ambient_detail_ext
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.AmbientDetailExt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:27:50 HKT 2024.
    -->
    insert into t_ambient_detail_ext (id, head_id, category, 
      category_digest, type, type2, 
      unit, unit_code, month_value_1, 
      month_value_2, month_value_3, season_value_1, 
      month_value_4, month_value_5, month_value_6, 
      season_value_2, month_value_7, month_value_8, 
      month_value_9, season_value_3, month_value_10, 
      month_value_11, month_value_12, season_value_4, 
      year_total_value, remark, seq, 
      creation_time)
    values (#{id,jdbcType=CHAR}, #{headId,jdbcType=NVARCHAR}, #{category,jdbcType=NVARCHAR}, 
      #{categoryDigest,jdbcType=NVARCHAR}, #{type,jdbcType=NVARCHAR}, #{type2,jdbcType=NVARCHAR}, 
      #{unit,jdbcType=NVARCHAR}, #{unitCode,jdbcType=NVARCHAR}, #{monthValue1,jdbcType=NVARCHAR}, 
      #{monthValue2,jdbcType=NVARCHAR}, #{monthValue3,jdbcType=NVARCHAR}, #{seasonValue1,jdbcType=NVARCHAR}, 
      #{monthValue4,jdbcType=NVARCHAR}, #{monthValue5,jdbcType=NVARCHAR}, #{monthValue6,jdbcType=NVARCHAR}, 
      #{seasonValue2,jdbcType=NVARCHAR}, #{monthValue7,jdbcType=NVARCHAR}, #{monthValue8,jdbcType=NVARCHAR}, 
      #{monthValue9,jdbcType=NVARCHAR}, #{seasonValue3,jdbcType=NVARCHAR}, #{monthValue10,jdbcType=NVARCHAR}, 
      #{monthValue11,jdbcType=NVARCHAR}, #{monthValue12,jdbcType=NVARCHAR}, #{seasonValue4,jdbcType=NVARCHAR}, 
      #{yearTotalValue,jdbcType=NVARCHAR}, #{remark,jdbcType=NVARCHAR}, #{seq,jdbcType=INTEGER}, 
      #{creationTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.AmbientDetailExt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:27:50 HKT 2024.
    -->
    insert into t_ambient_detail_ext
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="headId != null">
        head_id,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="categoryDigest != null">
        category_digest,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="type2 != null">
        type2,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="unitCode != null">
        unit_code,
      </if>
      <if test="monthValue1 != null">
        month_value_1,
      </if>
      <if test="monthValue2 != null">
        month_value_2,
      </if>
      <if test="monthValue3 != null">
        month_value_3,
      </if>
      <if test="seasonValue1 != null">
        season_value_1,
      </if>
      <if test="monthValue4 != null">
        month_value_4,
      </if>
      <if test="monthValue5 != null">
        month_value_5,
      </if>
      <if test="monthValue6 != null">
        month_value_6,
      </if>
      <if test="seasonValue2 != null">
        season_value_2,
      </if>
      <if test="monthValue7 != null">
        month_value_7,
      </if>
      <if test="monthValue8 != null">
        month_value_8,
      </if>
      <if test="monthValue9 != null">
        month_value_9,
      </if>
      <if test="seasonValue3 != null">
        season_value_3,
      </if>
      <if test="monthValue10 != null">
        month_value_10,
      </if>
      <if test="monthValue11 != null">
        month_value_11,
      </if>
      <if test="monthValue12 != null">
        month_value_12,
      </if>
      <if test="seasonValue4 != null">
        season_value_4,
      </if>
      <if test="yearTotalValue != null">
        year_total_value,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="seq != null">
        seq,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="headId != null">
        #{headId,jdbcType=NVARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=NVARCHAR},
      </if>
      <if test="categoryDigest != null">
        #{categoryDigest,jdbcType=NVARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=NVARCHAR},
      </if>
      <if test="type2 != null">
        #{type2,jdbcType=NVARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=NVARCHAR},
      </if>
      <if test="unitCode != null">
        #{unitCode,jdbcType=NVARCHAR},
      </if>
      <if test="monthValue1 != null">
        #{monthValue1,jdbcType=NVARCHAR},
      </if>
      <if test="monthValue2 != null">
        #{monthValue2,jdbcType=NVARCHAR},
      </if>
      <if test="monthValue3 != null">
        #{monthValue3,jdbcType=NVARCHAR},
      </if>
      <if test="seasonValue1 != null">
        #{seasonValue1,jdbcType=NVARCHAR},
      </if>
      <if test="monthValue4 != null">
        #{monthValue4,jdbcType=NVARCHAR},
      </if>
      <if test="monthValue5 != null">
        #{monthValue5,jdbcType=NVARCHAR},
      </if>
      <if test="monthValue6 != null">
        #{monthValue6,jdbcType=NVARCHAR},
      </if>
      <if test="seasonValue2 != null">
        #{seasonValue2,jdbcType=NVARCHAR},
      </if>
      <if test="monthValue7 != null">
        #{monthValue7,jdbcType=NVARCHAR},
      </if>
      <if test="monthValue8 != null">
        #{monthValue8,jdbcType=NVARCHAR},
      </if>
      <if test="monthValue9 != null">
        #{monthValue9,jdbcType=NVARCHAR},
      </if>
      <if test="seasonValue3 != null">
        #{seasonValue3,jdbcType=NVARCHAR},
      </if>
      <if test="monthValue10 != null">
        #{monthValue10,jdbcType=NVARCHAR},
      </if>
      <if test="monthValue11 != null">
        #{monthValue11,jdbcType=NVARCHAR},
      </if>
      <if test="monthValue12 != null">
        #{monthValue12,jdbcType=NVARCHAR},
      </if>
      <if test="seasonValue4 != null">
        #{seasonValue4,jdbcType=NVARCHAR},
      </if>
      <if test="yearTotalValue != null">
        #{yearTotalValue,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=NVARCHAR},
      </if>
      <if test="seq != null">
        #{seq,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.AmbientDetailExtExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:27:50 HKT 2024.
    -->
    select count(*) from t_ambient_detail_ext
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:27:50 HKT 2024.
    -->
    update t_ambient_detail_ext
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.headId != null">
        head_id = #{record.headId,jdbcType=NVARCHAR},
      </if>
      <if test="record.category != null">
        category = #{record.category,jdbcType=NVARCHAR},
      </if>
      <if test="record.categoryDigest != null">
        category_digest = #{record.categoryDigest,jdbcType=NVARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=NVARCHAR},
      </if>
      <if test="record.type2 != null">
        type2 = #{record.type2,jdbcType=NVARCHAR},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=NVARCHAR},
      </if>
      <if test="record.unitCode != null">
        unit_code = #{record.unitCode,jdbcType=NVARCHAR},
      </if>
      <if test="record.monthValue1 != null">
        month_value_1 = #{record.monthValue1,jdbcType=NVARCHAR},
      </if>
      <if test="record.monthValue2 != null">
        month_value_2 = #{record.monthValue2,jdbcType=NVARCHAR},
      </if>
      <if test="record.monthValue3 != null">
        month_value_3 = #{record.monthValue3,jdbcType=NVARCHAR},
      </if>
      <if test="record.seasonValue1 != null">
        season_value_1 = #{record.seasonValue1,jdbcType=NVARCHAR},
      </if>
      <if test="record.monthValue4 != null">
        month_value_4 = #{record.monthValue4,jdbcType=NVARCHAR},
      </if>
      <if test="record.monthValue5 != null">
        month_value_5 = #{record.monthValue5,jdbcType=NVARCHAR},
      </if>
      <if test="record.monthValue6 != null">
        month_value_6 = #{record.monthValue6,jdbcType=NVARCHAR},
      </if>
      <if test="record.seasonValue2 != null">
        season_value_2 = #{record.seasonValue2,jdbcType=NVARCHAR},
      </if>
      <if test="record.monthValue7 != null">
        month_value_7 = #{record.monthValue7,jdbcType=NVARCHAR},
      </if>
      <if test="record.monthValue8 != null">
        month_value_8 = #{record.monthValue8,jdbcType=NVARCHAR},
      </if>
      <if test="record.monthValue9 != null">
        month_value_9 = #{record.monthValue9,jdbcType=NVARCHAR},
      </if>
      <if test="record.seasonValue3 != null">
        season_value_3 = #{record.seasonValue3,jdbcType=NVARCHAR},
      </if>
      <if test="record.monthValue10 != null">
        month_value_10 = #{record.monthValue10,jdbcType=NVARCHAR},
      </if>
      <if test="record.monthValue11 != null">
        month_value_11 = #{record.monthValue11,jdbcType=NVARCHAR},
      </if>
      <if test="record.monthValue12 != null">
        month_value_12 = #{record.monthValue12,jdbcType=NVARCHAR},
      </if>
      <if test="record.seasonValue4 != null">
        season_value_4 = #{record.seasonValue4,jdbcType=NVARCHAR},
      </if>
      <if test="record.yearTotalValue != null">
        year_total_value = #{record.yearTotalValue,jdbcType=NVARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=NVARCHAR},
      </if>
      <if test="record.seq != null">
        seq = #{record.seq,jdbcType=INTEGER},
      </if>
      <if test="record.creationTime != null">
        creation_time = #{record.creationTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:27:50 HKT 2024.
    -->
    update t_ambient_detail_ext
    set id = #{record.id,jdbcType=CHAR},
      head_id = #{record.headId,jdbcType=NVARCHAR},
      category = #{record.category,jdbcType=NVARCHAR},
      category_digest = #{record.categoryDigest,jdbcType=NVARCHAR},
      type = #{record.type,jdbcType=NVARCHAR},
      type2 = #{record.type2,jdbcType=NVARCHAR},
      unit = #{record.unit,jdbcType=NVARCHAR},
      unit_code = #{record.unitCode,jdbcType=NVARCHAR},
      month_value_1 = #{record.monthValue1,jdbcType=NVARCHAR},
      month_value_2 = #{record.monthValue2,jdbcType=NVARCHAR},
      month_value_3 = #{record.monthValue3,jdbcType=NVARCHAR},
      season_value_1 = #{record.seasonValue1,jdbcType=NVARCHAR},
      month_value_4 = #{record.monthValue4,jdbcType=NVARCHAR},
      month_value_5 = #{record.monthValue5,jdbcType=NVARCHAR},
      month_value_6 = #{record.monthValue6,jdbcType=NVARCHAR},
      season_value_2 = #{record.seasonValue2,jdbcType=NVARCHAR},
      month_value_7 = #{record.monthValue7,jdbcType=NVARCHAR},
      month_value_8 = #{record.monthValue8,jdbcType=NVARCHAR},
      month_value_9 = #{record.monthValue9,jdbcType=NVARCHAR},
      season_value_3 = #{record.seasonValue3,jdbcType=NVARCHAR},
      month_value_10 = #{record.monthValue10,jdbcType=NVARCHAR},
      month_value_11 = #{record.monthValue11,jdbcType=NVARCHAR},
      month_value_12 = #{record.monthValue12,jdbcType=NVARCHAR},
      season_value_4 = #{record.seasonValue4,jdbcType=NVARCHAR},
      year_total_value = #{record.yearTotalValue,jdbcType=NVARCHAR},
      remark = #{record.remark,jdbcType=NVARCHAR},
      seq = #{record.seq,jdbcType=INTEGER},
      creation_time = #{record.creationTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.AmbientDetailExt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:27:50 HKT 2024.
    -->
    update t_ambient_detail_ext
    <set>
      <if test="headId != null">
        head_id = #{headId,jdbcType=NVARCHAR},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=NVARCHAR},
      </if>
      <if test="categoryDigest != null">
        category_digest = #{categoryDigest,jdbcType=NVARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=NVARCHAR},
      </if>
      <if test="type2 != null">
        type2 = #{type2,jdbcType=NVARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=NVARCHAR},
      </if>
      <if test="unitCode != null">
        unit_code = #{unitCode,jdbcType=NVARCHAR},
      </if>
      <if test="monthValue1 != null">
        month_value_1 = #{monthValue1,jdbcType=NVARCHAR},
      </if>
      <if test="monthValue2 != null">
        month_value_2 = #{monthValue2,jdbcType=NVARCHAR},
      </if>
      <if test="monthValue3 != null">
        month_value_3 = #{monthValue3,jdbcType=NVARCHAR},
      </if>
      <if test="seasonValue1 != null">
        season_value_1 = #{seasonValue1,jdbcType=NVARCHAR},
      </if>
      <if test="monthValue4 != null">
        month_value_4 = #{monthValue4,jdbcType=NVARCHAR},
      </if>
      <if test="monthValue5 != null">
        month_value_5 = #{monthValue5,jdbcType=NVARCHAR},
      </if>
      <if test="monthValue6 != null">
        month_value_6 = #{monthValue6,jdbcType=NVARCHAR},
      </if>
      <if test="seasonValue2 != null">
        season_value_2 = #{seasonValue2,jdbcType=NVARCHAR},
      </if>
      <if test="monthValue7 != null">
        month_value_7 = #{monthValue7,jdbcType=NVARCHAR},
      </if>
      <if test="monthValue8 != null">
        month_value_8 = #{monthValue8,jdbcType=NVARCHAR},
      </if>
      <if test="monthValue9 != null">
        month_value_9 = #{monthValue9,jdbcType=NVARCHAR},
      </if>
      <if test="seasonValue3 != null">
        season_value_3 = #{seasonValue3,jdbcType=NVARCHAR},
      </if>
      <if test="monthValue10 != null">
        month_value_10 = #{monthValue10,jdbcType=NVARCHAR},
      </if>
      <if test="monthValue11 != null">
        month_value_11 = #{monthValue11,jdbcType=NVARCHAR},
      </if>
      <if test="monthValue12 != null">
        month_value_12 = #{monthValue12,jdbcType=NVARCHAR},
      </if>
      <if test="seasonValue4 != null">
        season_value_4 = #{seasonValue4,jdbcType=NVARCHAR},
      </if>
      <if test="yearTotalValue != null">
        year_total_value = #{yearTotalValue,jdbcType=NVARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=NVARCHAR},
      </if>
      <if test="seq != null">
        seq = #{seq,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.AmbientDetailExt">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 09 18:27:50 HKT 2024.
    -->
    update t_ambient_detail_ext
    set head_id = #{headId,jdbcType=NVARCHAR},
      category = #{category,jdbcType=NVARCHAR},
      category_digest = #{categoryDigest,jdbcType=NVARCHAR},
      type = #{type,jdbcType=NVARCHAR},
      type2 = #{type2,jdbcType=NVARCHAR},
      unit = #{unit,jdbcType=NVARCHAR},
      unit_code = #{unitCode,jdbcType=NVARCHAR},
      month_value_1 = #{monthValue1,jdbcType=NVARCHAR},
      month_value_2 = #{monthValue2,jdbcType=NVARCHAR},
      month_value_3 = #{monthValue3,jdbcType=NVARCHAR},
      season_value_1 = #{seasonValue1,jdbcType=NVARCHAR},
      month_value_4 = #{monthValue4,jdbcType=NVARCHAR},
      month_value_5 = #{monthValue5,jdbcType=NVARCHAR},
      month_value_6 = #{monthValue6,jdbcType=NVARCHAR},
      season_value_2 = #{seasonValue2,jdbcType=NVARCHAR},
      month_value_7 = #{monthValue7,jdbcType=NVARCHAR},
      month_value_8 = #{monthValue8,jdbcType=NVARCHAR},
      month_value_9 = #{monthValue9,jdbcType=NVARCHAR},
      season_value_3 = #{seasonValue3,jdbcType=NVARCHAR},
      month_value_10 = #{monthValue10,jdbcType=NVARCHAR},
      month_value_11 = #{monthValue11,jdbcType=NVARCHAR},
      month_value_12 = #{monthValue12,jdbcType=NVARCHAR},
      season_value_4 = #{seasonValue4,jdbcType=NVARCHAR},
      year_total_value = #{yearTotalValue,jdbcType=NVARCHAR},
      remark = #{remark,jdbcType=NVARCHAR},
      seq = #{seq,jdbcType=INTEGER},
      creation_time = #{creationTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>