<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.UserMapper">
  <resultMap id="BaseResultMap" type="com.csci.susdev.model.User">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 16 12:02:28 HKT 2024.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="gender" jdbcType="INTEGER" property="gender" />
    <result column="domain_prefix" jdbcType="VARCHAR" property="domainPrefix" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="staff_no" jdbcType="VARCHAR" property="staffNo" />
    <result column="position" jdbcType="VARCHAR" property="position" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="birth_date" jdbcType="TIMESTAMP" property="birthDate" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="create_username" jdbcType="VARCHAR" property="createUsername" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="last_update_username" jdbcType="VARCHAR" property="lastUpdateUsername" />
    <result column="last_update_user_id" jdbcType="VARCHAR" property="lastUpdateUserId" />
    <result column="last_update_version" jdbcType="INTEGER" property="lastUpdateVersion" />
    <result column="is_ad_account" jdbcType="BIT" property="isAdAccount" />
    <result column="is_enabled" jdbcType="BIT" property="isEnabled" />
    <result column="is_readonly" jdbcType="BIT" property="isReadonly" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 16 12:02:28 HKT 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 16 12:02:28 HKT 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 16 12:02:28 HKT 2024.
    -->
    id, name, gender, domain_prefix, username, password, email, mobile, staff_no, position, 
    company_name, birth_date, description, creation_time, create_username, create_user_id, 
    last_update_time, last_update_username, last_update_user_id, last_update_version, 
    is_ad_account, is_enabled, is_readonly
  </sql>
  <select id="selectByExample" parameterType="com.csci.susdev.model.UserExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 16 12:02:28 HKT 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from t_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 16 12:02:28 HKT 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_user
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 16 12:02:28 HKT 2024.
    -->
    delete from t_user
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.csci.susdev.model.UserExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 16 12:02:28 HKT 2024.
    -->
    delete from t_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.csci.susdev.model.User">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 16 12:02:28 HKT 2024.
    -->
    insert into t_user (id, name, gender, 
      domain_prefix, username, password, 
      email, mobile, staff_no, 
      position, company_name, birth_date, 
      description, creation_time, create_username, 
      create_user_id, last_update_time, last_update_username, 
      last_update_user_id, last_update_version, is_ad_account, 
      is_enabled, is_readonly)
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{gender,jdbcType=INTEGER}, 
      #{domainPrefix,jdbcType=VARCHAR}, #{username,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR}, 
      #{email,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{staffNo,jdbcType=VARCHAR}, 
      #{position,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, #{birthDate,jdbcType=TIMESTAMP}, 
      #{description,jdbcType=VARCHAR}, #{creationTime,jdbcType=TIMESTAMP}, #{createUsername,jdbcType=VARCHAR}, 
      #{createUserId,jdbcType=VARCHAR}, #{lastUpdateTime,jdbcType=TIMESTAMP}, #{lastUpdateUsername,jdbcType=VARCHAR}, 
      #{lastUpdateUserId,jdbcType=VARCHAR}, #{lastUpdateVersion,jdbcType=INTEGER}, #{isAdAccount,jdbcType=BIT}, 
      #{isEnabled,jdbcType=BIT}, #{isReadonly,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.csci.susdev.model.User">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 16 12:02:28 HKT 2024.
    -->
    insert into t_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="domainPrefix != null">
        domain_prefix,
      </if>
      <if test="username != null">
        username,
      </if>
      <if test="password != null">
        password,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="staffNo != null">
        staff_no,
      </if>
      <if test="position != null">
        position,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="birthDate != null">
        birth_date,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username,
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id,
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version,
      </if>
      <if test="isAdAccount != null">
        is_ad_account,
      </if>
      <if test="isEnabled != null">
        is_enabled,
      </if>
      <if test="isReadonly != null">
        is_readonly,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=INTEGER},
      </if>
      <if test="domainPrefix != null">
        #{domainPrefix,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="staffNo != null">
        #{staffNo,jdbcType=VARCHAR},
      </if>
      <if test="position != null">
        #{position,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="birthDate != null">
        #{birthDate,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        #{lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        #{lastUpdateUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="isAdAccount != null">
        #{isAdAccount,jdbcType=BIT},
      </if>
      <if test="isEnabled != null">
        #{isEnabled,jdbcType=BIT},
      </if>
      <if test="isReadonly != null">
        #{isReadonly,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.csci.susdev.model.UserExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 16 12:02:28 HKT 2024.
    -->
    select count(*) from t_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 16 12:02:28 HKT 2024.
    -->
    update t_user
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=VARCHAR},
      </if>
      <if test="row.name != null">
        name = #{row.name,jdbcType=VARCHAR},
      </if>
      <if test="row.gender != null">
        gender = #{row.gender,jdbcType=INTEGER},
      </if>
      <if test="row.domainPrefix != null">
        domain_prefix = #{row.domainPrefix,jdbcType=VARCHAR},
      </if>
      <if test="row.username != null">
        username = #{row.username,jdbcType=VARCHAR},
      </if>
      <if test="row.password != null">
        password = #{row.password,jdbcType=VARCHAR},
      </if>
      <if test="row.email != null">
        email = #{row.email,jdbcType=VARCHAR},
      </if>
      <if test="row.mobile != null">
        mobile = #{row.mobile,jdbcType=VARCHAR},
      </if>
      <if test="row.staffNo != null">
        staff_no = #{row.staffNo,jdbcType=VARCHAR},
      </if>
      <if test="row.position != null">
        position = #{row.position,jdbcType=VARCHAR},
      </if>
      <if test="row.companyName != null">
        company_name = #{row.companyName,jdbcType=VARCHAR},
      </if>
      <if test="row.birthDate != null">
        birth_date = #{row.birthDate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.description != null">
        description = #{row.description,jdbcType=VARCHAR},
      </if>
      <if test="row.creationTime != null">
        creation_time = #{row.creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createUsername != null">
        create_username = #{row.createUsername,jdbcType=VARCHAR},
      </if>
      <if test="row.createUserId != null">
        create_user_id = #{row.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateTime != null">
        last_update_time = #{row.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.lastUpdateUsername != null">
        last_update_username = #{row.lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateUserId != null">
        last_update_user_id = #{row.lastUpdateUserId,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateVersion != null">
        last_update_version = #{row.lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="row.isAdAccount != null">
        is_ad_account = #{row.isAdAccount,jdbcType=BIT},
      </if>
      <if test="row.isEnabled != null">
        is_enabled = #{row.isEnabled,jdbcType=BIT},
      </if>
      <if test="row.isReadonly != null">
        is_readonly = #{row.isReadonly,jdbcType=BIT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 16 12:02:28 HKT 2024.
    -->
    update t_user
    set id = #{row.id,jdbcType=VARCHAR},
      name = #{row.name,jdbcType=VARCHAR},
      gender = #{row.gender,jdbcType=INTEGER},
      domain_prefix = #{row.domainPrefix,jdbcType=VARCHAR},
      username = #{row.username,jdbcType=VARCHAR},
      password = #{row.password,jdbcType=VARCHAR},
      email = #{row.email,jdbcType=VARCHAR},
      mobile = #{row.mobile,jdbcType=VARCHAR},
      staff_no = #{row.staffNo,jdbcType=VARCHAR},
      position = #{row.position,jdbcType=VARCHAR},
      company_name = #{row.companyName,jdbcType=VARCHAR},
      birth_date = #{row.birthDate,jdbcType=TIMESTAMP},
      description = #{row.description,jdbcType=VARCHAR},
      creation_time = #{row.creationTime,jdbcType=TIMESTAMP},
      create_username = #{row.createUsername,jdbcType=VARCHAR},
      create_user_id = #{row.createUserId,jdbcType=VARCHAR},
      last_update_time = #{row.lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{row.lastUpdateUsername,jdbcType=VARCHAR},
      last_update_user_id = #{row.lastUpdateUserId,jdbcType=VARCHAR},
      last_update_version = #{row.lastUpdateVersion,jdbcType=INTEGER},
      is_ad_account = #{row.isAdAccount,jdbcType=BIT},
      is_enabled = #{row.isEnabled,jdbcType=BIT},
      is_readonly = #{row.isReadonly,jdbcType=BIT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.User">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 16 12:02:28 HKT 2024.
    -->
    update t_user
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=INTEGER},
      </if>
      <if test="domainPrefix != null">
        domain_prefix = #{domainPrefix,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        password = #{password,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="staffNo != null">
        staff_no = #{staffNo,jdbcType=VARCHAR},
      </if>
      <if test="position != null">
        position = #{position,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="birthDate != null">
        birth_date = #{birthDate,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUsername != null">
        last_update_username = #{lastUpdateUsername,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateUserId != null">
        last_update_user_id = #{lastUpdateUserId,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateVersion != null">
        last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      </if>
      <if test="isAdAccount != null">
        is_ad_account = #{isAdAccount,jdbcType=BIT},
      </if>
      <if test="isEnabled != null">
        is_enabled = #{isEnabled,jdbcType=BIT},
      </if>
      <if test="isReadonly != null">
        is_readonly = #{isReadonly,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.User">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 16 12:02:28 HKT 2024.
    -->
    update t_user
    set name = #{name,jdbcType=VARCHAR},
      gender = #{gender,jdbcType=INTEGER},
      domain_prefix = #{domainPrefix,jdbcType=VARCHAR},
      username = #{username,jdbcType=VARCHAR},
      password = #{password,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      staff_no = #{staffNo,jdbcType=VARCHAR},
      position = #{position,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      birth_date = #{birthDate,jdbcType=TIMESTAMP},
      description = #{description,jdbcType=VARCHAR},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      create_username = #{createUsername,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      last_update_username = #{lastUpdateUsername,jdbcType=VARCHAR},
      last_update_user_id = #{lastUpdateUserId,jdbcType=VARCHAR},
      last_update_version = #{lastUpdateVersion,jdbcType=INTEGER},
      is_ad_account = #{isAdAccount,jdbcType=BIT},
      is_enabled = #{isEnabled,jdbcType=BIT},
      is_readonly = #{isReadonly,jdbcType=BIT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>