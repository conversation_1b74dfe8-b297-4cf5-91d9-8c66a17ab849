<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.susdev.mapper.TzhBsOrgMenuMapper">
    <resultMap id="BaseResultMap" type="com.csci.susdev.model.TzhBsOrgMenu">
        <!--@mbg.generated-->
        <!--@Table Tzh_Bs_Org_Menu-->
        <id column="Id" jdbcType="VARCHAR" property="id"/>
        <result column="organization_id" jdbcType="VARCHAR" property="organizationId"/>
        <result column="protocol_id" jdbcType="VARCHAR" property="protocolId"/>
        <result column="module" jdbcType="VARCHAR" property="module"/>
        <result column="menu_id" jdbcType="VARCHAR" property="menuId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="seq" jdbcType="TINYINT" property="seq"/>
        <result column="is_deleted" jdbcType="BIT" property="isDeleted"/>
    </resultMap>
    <resultMap id="resMap" type="com.csci.susdev.vo.TzhBsOrgMenuVO" extends="BaseResultMap">
        <result column="module" jdbcType="VARCHAR" property="module"/>
        <result column="organizationName" jdbcType="VARCHAR" property="organizationName"/>
        <result column="protocolName" property="protocolName"/>
        <result column="title_en" property="titleEn"/>
        <result column="id" property="id"/>
        <result column="menuId" property="menuId"/>
        <result column="organizationId" property="organizationId"/>
        <result column="protocolId" property="protocolId"/>

        <association property="menu" javaType="com.csci.susdev.model.TzhBsMenu"
                     column="menuId" select="com.csci.susdev.mapper.TzhBsMenuMapper.selectByPrimaryKey"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        Id,
        organization_id,
        protocol_id,
        module,
        menu_id,
        remark,
        seq,
        is_deleted
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Object" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from Tzh_Bs_Org_Menu
        where Id = #{id,jdbcType=OTHER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Object">
        <!--@mbg.generated-->
        delete
        from Tzh_Bs_Org_Menu
        where Id = #{id,jdbcType=OTHER}
    </delete>
    <insert id="insert" parameterType="com.csci.susdev.model.TzhBsOrgMenu">
        <!--@mbg.generated-->
        insert into Tzh_Bs_Org_Menu (Id, organization_id, protocol_id,
                                     [module], menu_id, remark,
                                     seq, is_deleted)
        values (#{id,jdbcType=OTHER}, #{organizationId,jdbcType=VARCHAR}, #{protocolId,jdbcType=OTHER},
                #{module,jdbcType=VARCHAR}, #{menuId,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
                #{seq,jdbcType=TINYINT}, #{isDeleted,jdbcType=BIT})
    </insert>
    <insert id="insertSelective" parameterType="com.csci.susdev.model.TzhBsOrgMenu">
        <!--@mbg.generated-->
        insert into Tzh_Bs_Org_Menu
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">
                Id,
            </if>
            <if test="organizationId != null and organizationId != ''">
                organization_id,
            </if>
            <if test="protocolId != null and protocolId !=''">
                protocol_id,
            </if>
            <if test="module != null and module != ''">
                module,
            </if>
            <if test="menuId != null and menuId != ''">
                menu_id,
            </if>
            <if test="remark != null and remark != ''">
                remark,
            </if>
            <if test="seq != null and seq !=''">
                seq,
            </if>
            <if test="isDeleted != null and isDeleted !=''">
                is_deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">
                #{id,jdbcType=OTHER},
            </if>
            <if test="organizationId != null and organizationId != ''">
                #{organizationId,jdbcType=VARCHAR},
            </if>
            <if test="protocolId != null and protocolId != ''">
                #{protocolId,jdbcType=OTHER},
            </if>
            <if test="module != null and module != ''">
                #{module,jdbcType=VARCHAR},
            </if>
            <if test="menuId != null and menuId != ''">
                #{menuId,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != ''">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="seq != null and seq != ''">
                #{seq,jdbcType=TINYINT},
            </if>
            <if test="isDeleted != null and isDeleted != ''">
                #{isDeleted,jdbcType=BIT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.csci.susdev.model.TzhBsOrgMenu">
        <!--@mbg.generated-->
        update Tzh_Bs_Org_Menu
        <set>
            <if test="organizationId != null">
                organization_id = #{organizationId,jdbcType=VARCHAR},
            </if>
            <if test="protocolId != null">
                protocol_id = #{protocolId,jdbcType=OTHER},
            </if>
            <if test="module != null">
                module = #{module,jdbcType=VARCHAR},
            </if>
            <if test="menuId != null">
                menu_id = #{menuId,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="seq != null">
                seq = #{seq,jdbcType=TINYINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=BIT},
            </if>
        </set>
        where Id = #{id,jdbcType=OTHER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.csci.susdev.model.TzhBsOrgMenu">
        <!--@mbg.generated-->
        update Tzh_Bs_Org_Menu
        set organization_id = #{organizationId,jdbcType=VARCHAR},
            protocol_id     = #{protocolId,jdbcType=OTHER},
            [module]        = #{module,jdbcType=VARCHAR},
            menu_id         = #{menuId,jdbcType=VARCHAR},
            remark          = #{remark,jdbcType=VARCHAR},
            seq             = #{seq,jdbcType=TINYINT},
            is_deleted      = #{isDeleted,jdbcType=BIT}
        where Id = #{id,jdbcType=OTHER}
    </update>

    <delete id="logicallyDelete">
        update Tzh_Bs_Org_Menu
        set is_deleted = 1
        where id = #{id}
    </delete>

    <select id="queryAll" resultMap="resMap" parameterType="com.csci.susdev.qo.TzhBsOrgMenuQO">
        select
        om.id,
        o.id as organizationId,
        p.id as protocolId,
        m.id as menuId,
        o.name as organizationName,om.module,p.name_en as protocolName,m.title_en ,om.seq,om.remark,om.is_deleted
        from Tzh_Bs_Org_Menu om
        left join Tzh_Bs_Menu m on om.menu_id = m.id
        left join t_organization o on om.organization_id = o.id
        left join t_protocol p on om.protocol_id = p.id
        <where>
            m.is_deleted =0 and om.is_deleted =0 and o.is_deleted =0 and p.is_deleted =0
            <if test="organizationName != null and organizationName != ''">
              AND  o.name like concat('%', #{organizationName}, '%')
            </if>
        </where>
        order by om.seq
    </select>
    <sql id="Join_Column_List">
        <!--@mbg.generated-->
        Tzh_Bs_Org_Menu.Id as Tzh_Bs_Org_Menu_Id,
        Tzh_Bs_Org_Menu.organization_id as Tzh_Bs_Org_Menu_organization_id,
        Tzh_Bs_Org_Menu.protocol_id as Tzh_Bs_Org_Menu_protocol_id,
        Tzh_Bs_Org_Menu.module as Tzh_Bs_Org_Menu_module,
        Tzh_Bs_Org_Menu.menu_id as Tzh_Bs_Org_Menu_menu_id,
        Tzh_Bs_Org_Menu.remark as Tzh_Bs_Org_Menu_remark,
        Tzh_Bs_Org_Menu.seq as Tzh_Bs_Org_Menu_seq,
        Tzh_Bs_Org_Menu.is_deleted as Tzh_Bs_Org_Menu_is_deleted
    </sql>
    <resultMap id="JoinResultMap" type="com.csci.susdev.model.TzhBsOrgMenu">
        <!--@mbg.generated-->
        <id column="Tzh_Bs_Org_Menu_Id" property="id"/>
        <result column="Tzh_Bs_Org_Menu_organization_id" property="organizationId"/>
        <result column="Tzh_Bs_Org_Menu_protocol_id" property="protocolId"/>
        <result column="Tzh_Bs_Org_Menu_module" property="module"/>
        <result column="Tzh_Bs_Org_Menu_menu_id" property="menuId"/>
        <result column="Tzh_Bs_Org_Menu_remark" property="remark"/>
        <result column="Tzh_Bs_Org_Menu_seq" property="seq"/>
        <result column="Tzh_Bs_Org_Menu_is_deleted" property="isDeleted"/>
    </resultMap>
</mapper>
