<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.csci.susdev.mapper.OrganizationMapperCustom">
    <select id="selectUserDepartments" resultMap="com.csci.susdev.mapper.OrganizationMapper.BaseResultMap">
        select *
        from (select *
              from t_organization
              where is_deleted = 0
                and exists(select 1
                           from t_user_organization
                           where organization_id = t_organization.id
                             and user_id = #{userId})) t
        where not exists(select 1 from t_organization where parent_id = t.id)
        order by t.no
    </select>

    <select id="selectCompanyDept" resultType="com.csci.susdev.vo.CompanyDeptVO">
        select o.id, o.name, o.no, (select name from t_organization where id = o.parent_id) as companyName, o.unit_code as unitCode,
                -- 完全连续匹配优先
                CASE
                WHEN o.name LIKE CONCAT('%', #{keyword}, '%') THEN 1
                WHEN o.name LIKE CONCAT('%', #{keywordSc}, '%') THEN 1
                ELSE 2
                END all_match,
                -- 匹配字符数量越多越好
                CASE
                WHEN (LEN(o.name) - LEN(REPLACE(o.name COLLATE Chinese_PRC_CI_AI, REPLACE(#{pattern}, '%', ''), ''))) > (LEN(o.name) - LEN(REPLACE(o.name COLLATE Chinese_PRC_CI_AI, REPLACE(#{patternSc}, '%', ''), '')))
                THEN (LEN(o.name) - LEN(REPLACE(o.name COLLATE Chinese_PRC_CI_AI, REPLACE(#{pattern}, '%', ''), '')))
                ELSE (LEN(o.name) - LEN(REPLACE(o.name COLLATE Chinese_PRC_CI_AI, REPLACE(#{patternSc}, '%', ''), '')))
                END num_match,
                -- 总体匹配位置越靠前越好
                CASE
                WHEN CHARINDEX(SUBSTRING(#{pattern}, 2, 1), o.name) > CHARINDEX(SUBSTRING(#{patternSc}, 2, 1), o.name) THEN CHARINDEX(SUBSTRING(#{patternSc}, 2, 1), o.name)
                ELSE CHARINDEX(SUBSTRING(#{pattern}, 2, 1), o.name)
                END before_match
        from t_organization o
        where o.is_deleted = 0
        <if test="keyword != null and keyword != '' and keywordSc != null and keywordSc != '' and pattern != null and pattern != '' and patternSc != null and patternSc != '' ">
        and (
        o.name like concat('%', #{keyword}, '%') or o.unit_code like concat('%', #{keyword}, '%')
        or o.name like concat('%', #{keywordSc}, '%') or o.unit_code like concat('%', #{keywordSc}, '%')
        or o.name like #{pattern} or o.unit_code like #{pattern}
        or o.name like #{patternSc} or o.unit_code like  #{patternSc}
        or o.id = #{keyword}
        )
        </if>
        <if test="userId != null and userId != ''">
        and exists (
        select 1 from t_organization _o
        inner join t_user_organization _uo on _o.id = _uo.organization_id
        inner join t_user _u on _u.id = _uo.user_id
        where _o.is_deleted = 0 and _u.id = #{userId}
        and o.no like concat(_o.no,'%')
        )
        </if>
        <if test="showLeafOnly != null and showLeafOnly != false ">
        and (select count(_o.id) from t_organization _o where _o.no like o.no + '%' and _o.is_deleted = 0 and _o.no != o.no) = 0
        </if>
        <if test="showCompanyOnly != null and showCompanyOnly != false ">
            and LEN(o.no) &lt;= 6
        </if>
        order by
        <choose>
            <when test="pattern != null and pattern != '' and patternSc != null and patternSc != '' ">
                all_match, num_match desc, before_match, o.no
            </when>
            <otherwise>
                o.no ASC
            </otherwise>
        </choose>
    </select>

    <select id="selectLeafChildrenByNo" resultMap="com.csci.susdev.mapper.OrganizationMapper.BaseResultMap">
        select *
        from t_organization t
        where is_deleted = 0
          and no like concat(#{no}, '%')
          and not exists(select 1 from t_organization where parent_id = t.id and is_deleted = 0)
        order by no
    </select>

    <select id="listFuzzySearch" resultType="com.csci.susdev.vo.CompanyPlatformVO">
        select
        t.id,
        t.name,
        t.no,
        t.parentName,
        t.platformCompany,
        IIF(t.similarityTc > t.similaritySc, t.similarityTc, t.similaritySc) as similarity,
        t.allMatch,
        IIF(t.num_match_tc > t.num_match_sc, t.num_match_tc, t.num_match_sc) as numMatch,
        IIF(t.before_match_tc > t.before_match_sc, t.before_match_sc, t.before_match_tc) as beforeMatch
        from (
        select
            o.id,
            o.name,
            o.no,
            uo.name as parentName,
            po.name as platformCompany,
            ROUND(dbo.FN_Resemble(#{keyword}, o.name), 3) as similarityTc,
            ROUND(dbo.FN_Resemble(#{keywordSc}, o.name), 3) as similaritySc,
            -- 完全连续匹配优先
            CASE
            WHEN o.name LIKE CONCAT('%', #{keyword}, '%') THEN 1
            WHEN o.name LIKE CONCAT('%', #{keywordSc}, '%') THEN 1
            ELSE 2
            END allMatch,
            -- 匹配字符数量越多越好
            (LEN(o.name) - LEN(REPLACE(o.name, #{keyword}, ''))) as num_match_tc,
            (LEN(o.name) - LEN(REPLACE(o.name, #{keywordSc}, ''))) as num_match_sc,
            -- 总体匹配位置越靠前越好
            CHARINDEX(SUBSTRING(#{keyword}, 1, 1), o.name) as before_match_tc,
            CHARINDEX(SUBSTRING(#{keywordSc}, 1, 1), o.name) as before_match_sc
            FROM t_organization o
            LEFT JOIN t_organization uo on left(o.no, len(o.no) - 3 ) = uo.no and uo.is_deleted = 0
            LEFT JOIN t_organization po on left(o.no, 6) = po.no and po.is_deleted = 0
            where o.is_deleted = 0
        )t
        where 1 = 1
        <if test="keyword != null and keyword != '' and keywordSc != null and keywordSc != ''">
            and (t.similarityTc > 0 or t.similaritySc > 0)
        </if>
        order by
        <choose>
            <when test="keyword != null and keyword != '' and keywordSc != null and keywordSc != '' ">
                similarity desc, allMatch, numMatch desc, beforeMatch, t.no
            </when>
            <otherwise>
                o.no ASC
            </otherwise>
        </choose>

    </select>
</mapper>