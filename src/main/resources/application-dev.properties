server.port=8091
openapi.request.host=https://esg.csci.com.hk/service
server.servlet.context-path=
server.forward-headers-strategy=native
server.tomcat.keep-alive-timeout=600000

datasource.susdev.hikari.maximum-pool-size=20
datasource.susdev.hikari.minimum-idle=5
datasource.susdev.hikari.jdbc-url=*****************************************************************************************************;
datasource.susdev.hikari.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
datasource.susdev.hikari.username=esg
datasource.susdev.hikari.password=7kL8Q57S3hK5R7ls
datasource.susdev.hikari.connection-timeout=60000
datasource.susdev.hikari.connection-test-query=select 1

#tan zhong he data source
datasource.susdev.tanzhonghe.maximum-pool-size=20
datasource.susdev.tanzhonghe.minimum-idle=5
datasource.susdev.tanzhonghe.jdbc-url=*****************************************************************************************************;
datasource.susdev.tanzhonghe.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
datasource.susdev.tanzhonghe.username=esg
datasource.susdev.tanzhonghe.password=7kL8Q57S3hK5R7ls
datasource.susdev.tanzhonghe.connection-timeout=60000
datasource.susdev.tanzhonghe.connection-test-query=select 1

#bi data source
#datasource.susdev.bi.maximum-pool-size=20
#datasource.susdev.bi.minimum-idle=5
#datasource.susdev.bi.jdbc-url=*****************************************************************************************;
#datasource.susdev.bi.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
#datasource.susdev.bi.username=esguser
#datasource.susdev.bi.password=Csci3311#
#datasource.susdev.bi.connection-timeout=60000
#datasource.susdev.bi.connection-test-query=select 1
#bi data source
datasource.susdev.bi.maximum-pool-size=20
datasource.susdev.bi.minimum-idle=5
datasource.susdev.bi.jdbc-url=*****************************************************************************************************;
datasource.susdev.bi.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
datasource.susdev.bi.username=esg
datasource.susdev.bi.password=7kL8Q57S3hK5R7ls
datasource.susdev.bi.connection-timeout=60000
datasource.susdev.bi.connection-test-query=select 1
#notification
notification.apiAddress=https://msg.csci.com.hk/mc/message/save
notification.fromAppName=ESG
notification.active=false

# Oauth2 config
oauth2.url=https://auth.csci.com.hk/api/token?
oauth2.url.accessToken=grant_type=password&appid={0}&secret={1}&username={2}&password={3}
oauth2.url.validateToken=grant_type=validate&appid={0}&secret={1}&access_token={2}
oauth2.url.refreshToken=grant_type=refresh_token&appid={0}&secret={1}&refresh_token={2}
oauth2.appid=111
oauth2.appSecret=cptbtptpbcptdtptp

home.page=http://localhost:8080

# feishu sso config
feishu.sso.app.url=https://api.csci.com.hk/zhtappsso/api/Token
feishu.sso.app.validate=https://api.csci.com.hk/zhtappsso/api/ValidateToken
feishu.sso.app.code=fin-stat
feishu.sso.app.secret=FSal5YzYz0ZjSZXtYnrWZejs5SodHSZ8

# baidu ocr config
baidu.ocr.url=https://aip.baidubce.com/oauth/2.0/token
baidu.ocr.clientId=Gf9lOhMBGRGZ7Pk8BafM3XiB
baidu.ocr.clientSecret=fYGOElGvsWmMAdPrR72dHC91T8LA2v11
baidu.ocr.grantType=client_credentials

app.esg.redisKey=dev-susdev
app.tzhbs.redisKey=dev-tzhbs

# REDIS (RedisProperties)
spring.redis.database=0
spring.redis.host=**********
spring.redis.port=6379
spring.redis.password=Passw0rd
spring.redis.timeout=10000
spring.redis.lettuce.pool.max-active= 100
spring.redis.lettuce.pool.max-wait= -1
spring.redis.lettuce.pool.min-idle= 10
spring.redis.lettuce.pool.max-idle= 50

# rsa key
rsa.publicKey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCsX6T3E3xtiiFMyciCTvVp7Y96f0E133KmqV7kFbAwucfhHEBu0peZEa9WRxxKTHMs6KwJ1U7FvJBxl3tIGYlLSdnnteo0MShfPn9zonAq1Q23luJtKNplKWkCJweN8DDWjRQ5i5R93A+dzXr3x94zweQ+4rl4+uPEXRno5KtkfQIDAQAB
rsa.privateKey=MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKxfpPcTfG2KIUzJyIJO9Wntj3p/QTXfcqapXuQVsDC5x+EcQG7Sl5kRr1ZHHEpMcyzorAnVTsW8kHGXe0gZiUtJ2ee16jQxKF8+f3OicCrVDbeW4m0o2mUpaQInB43wMNaNFDmLlH3cD53NevfH3jPB5D7iuXj648RdGejkq2R9AgMBAAECgYA72oA5LQJ9NMQIWl6t5MXxsMQr6GkX0E2K0m/5KhDCcpgVsg4rjSOHyqzD/DA2GmK52tb2GSPfpHDRiKSNrhRORl1I6prMc5IEg8f9uS9P0d+tuTDZiq45cWdYbxlNBz/KYiurRryefI3Fa99DBt+LM+rT9o7QEqAooBxrv7BLDwJBAOTJjI3tPBB7A+JuT0NbXr2RlsOIMt9kis5Nbvpe4lEseTIWo0YDeWYrlJqeic2tMnOOZ5vo4ZTRcagpRF+utK8CQQDA4FQjQszS9FoKQqJkb4Tf3gDyJbCTUPEcvK/ZonnhezhZvFQ88HNIiQ48ooSkwv1LxSO37m9Ybz3L58kEcZyTAkEAyM6mUUPyPjzasflEFMizpQuOGl0G2dBzjJOmXpa9aaXxUidQc3lFKooBypxwM1hbOdW51rxWkroqWgCuhJTg/QJAOgQAKu8P7zBi2Q08DZvhyvjbLfsaRuWk8PDssDkIEkPfKlbUu9PTyXC4YJK99VVVnXH6EXxd76reWrQoqfaGkwJANX8emDtGmTbYr+NKSUSkSOUrGJtq4aDXPSC8ZDRgVx8qHFyzh1PHrRBdrz3PBnBTK51KbZhXaMgE41ENWlkK2g==

# rsa ext key
rsa.ext.publicKey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCGXG423pFrAhgDGtS1u70ggF+7XHIQc4Jfsv0OzrCob6Wyt8pmD0PJJipXw3NW82Q/kxnDxaE6fxdiQlLK0uLqDKEibxjuJ2tSmGCJmykD4gzM0GobY06ukH076dNhvap5d3oKfeG8XCpYH9lI2SvXcSWbpGRT1reU940xsO5onwIDAQAB
rsa.ext.privateKey=MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAIZcbjbekWsCGAMa1LW7vSCAX7tcchBzgl+y/Q7OsKhvpbK3ymYPQ8kmKlfDc1bzZD+TGcPFoTp/F2JCUsrS4uoMoSJvGO4na1KYYImbKQPiDMzQahtjTq6QfTvp02G9qnl3egp94bxcKlgf2UjZK9dxJZukZFPWt5T3jTGw7mifAgMBAAECgYBBwTBrB+dw0BiCRHo+6j73jfvLzMVBydXPEeCIg5yvAdy8pveVlPjekx/7zXo/3mN8PXhJeAmcgCAohT7RZf2IQGsRkMNs4viPdQf0tmgybye53uCfolJnAhYs/vlNPfw+0b759c/7Cfd75OrACrACqnEcbhsehebBuwrCuWm4WQJBAP+jdSnNiR1hkm37al0fURTZPC4nYlE9Ka9nIGhBf/bduv6mJXRGcGC29rq9wQ8TKTOMbiRseTVUHOJCZIGeiOsCQQCGjRHh69jyl40+urRxbOW9EaZvbbuTyci4dy5r+grniYXfh/NFT5c4EXgLSIYscpe6dZR4ovJaJhmatDPy8TIdAkEAweoLKQ+ZL+lguv7YuxTTW35BsTz8zmUX5s7SfWMaH3gorZv4k1APVL3VQOhJtxawzUJ8FjMWaoaIdnUoak6IywJATMtlYnm2+DbxgdUUOgy6TyAsyzppLh+kNUyorS4oXSBLzVoNygh0OacWyfHZyrKY0O5dEEGIa1WFlZu2brmlLQJAfBZviLtWBqx1XUVEn0XlUeeJxGMwwAf6DM0ozAyi8dMwzJt7sPmpFfMeZ80qzzF8ilu51UdByI3qSiOPccOG+A==

# rpa robot
rpa.robot.active=true

# user session timeout in minutes
user.session.timeout=7200

# max file size limit
spring.servlet.multipart.max-file-size=25MB
spring.servlet.multipart.max-request-size=25MB

# user session manager
user.session.active.count=1

springdoc.swagger-ui.enabled=true
springdoc.api-docs.enabled=true


#bi

oa.login= https://api.csci.com.hk/adverif/api/Verify
# tzh properties
tzh.baseUrl=https://cfp.csci.com.hk/determine

# oauth properties
oauth.validateUrl=https://auth.csci.com.hk/api/token/validate/


# jwt properties
jwt.secret=B0KNAPV0HZT02YRF0J3CMWAVPT5XC5RD
jwt.redisKey=susdev
jwt.expire=259200

# email properties
email.host=smtp.csci.com.hk
email.from=<EMAIL>
email.password=r-@sbV3<
email.formName=???
email.receive=<EMAIL>
email.receiveName=???


# logging properties
logging.level.root=warn
logging.level.web=info
logging.level.org.springframework=info
logging.level.com.baomidou=info
logging.file.path=/logs/tzh_bs/8189

# alert properties
alert.feishu.url=https://open.feishu.cn/open-apis/bot/v2/hook/d510cbda-d4b4-4598-8528-7a8c9c191e5e


mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.auto-mapping-behavior= full
#mybatis-plus.configuration.log-impl= org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.nologging.NoLoggingImpl
mybatis-plus.mapper-locations=classpath*:mapper/**/*.xml,classpath*:*mapper/**/*.xml
mybatis-plus.global-config.db-config.logic-not-delete-value=1
mybatis-plus.global-config.db-config.logic-delete-value=0


susdev.authentication.enabled=true

#minio
minio.server=http://***********
minio.port=31452
minio.accessKey=WU9VUkFDQ0VTU0tFWQ==
minio.secretKey=WU9VUlNFQ1JFVEtFWQ==
minio.bucket=test
minio.urlPrefix=/minio/

# hiAgent
hiagent.url=https://hiagent.3311csci.com
hiagent.get-app-config=/api/proxy/api/v1/get_app_config_preview
hiagent.create-conversation=/api/proxy/api/v1/create_conversation
hiagent.chat-query=/api/proxy/api/v1/chat_query
hiagent.get-suggested-questions=/api/proxy/api/v1/get_suggested_questions
hiagent.get_message_info=/api/proxy/api/v1/get_message_info
hiagent.get_conversation_messages=/api/proxy/api/v1/get_conversation_messages
hiagent.get_conversation_list=/api/proxy/api/v1/get_conversation_list
hiagent.query_again=/api/proxy/api/v1/query_again
hiagent.stop_message=/api/proxy/api/v1/stop_message
hiagent.update_conversation=/api/proxy/api/v1/update_conversation
hiagent.esg.apikey=cvrj68jbg4roomp7ticg
hiagent.esg.trip.ocrApikey=d0gke6bbg4roomp9rj50
hiagent.esg.energy.ocrApikey=d18h3e16lovqj16ts8gg

#tiocr
tiocr.base-url=http://************:60099
tiocr.smart_structural_ocr_v3=/youtu/ocrapi/smart_structural_ocr_v3