<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <property name="LOG_HOME" value="logs"/>
    <property name="LOG_KEEP_DAYS" value="60"/>

    <appender name="FILE-AUDIT"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <!--临界值过滤器，当日志级别低于临界值时，日志会被拒绝-->
            <level>INFO</level>
        </filter>
        <file>${LOG_HOME}/sus-dev.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>
                <!--%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n-->
                %d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level<!--%caller{1}--> - %msg%n
            </Pattern>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- rollover daily -->
            <fileNamePattern>${LOG_HOME}/archived/sus-dev.%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>20MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!--保存30天-->
            <maxHistory>${LOG_KEEP_DAYS}</maxHistory>
        </rollingPolicy>
    </appender>

    <appender name="FILE-ERROR"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <!--设置日志级别,过滤掉info日志,只输入error日志-->
            <level>ERROR</level>
        </filter>
        <file>${LOG_HOME}/error-sus-dev.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>
                <!--%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n-->
                %d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level<!--%caller{1}--> - %msg%n
            </Pattern>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- rollover daily -->
            <fileNamePattern>${LOG_HOME}/archived/error-sus-dev.%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>20MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!--保存30天-->
            <maxHistory>${LOG_KEEP_DAYS}</maxHistory>
        </rollingPolicy>
    </appender>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- encoders are  by default assigned the type
             ch.qos.logback.classic.encoder.PatternLayoutEncoder -->
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %caller{1} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="ASYNC-AUDIT" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>512</queueSize>
        <appender-ref ref="FILE-AUDIT"/>
    </appender>
    <appender name="ASYNC-ERROR" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>512</queueSize>
        <appender-ref ref="FILE-ERROR"/>
    </appender>

    <!--logger等同于root-->
    <logger name="com.csci" level="debug"
            additivity="false">
        <appender-ref ref="ASYNC-AUDIT"/>
        <appender-ref ref="ASYNC-ERROR"/>
        <appender-ref ref="STDOUT"/>
    </logger>

    <logger name="java.sql" level="debug">
        <appender-ref ref="STDOUT"/>
    </logger>

    <logger name="org.springframework" level="info">
        <appender-ref ref="STDOUT"/>
    </logger>

    <root level="debug">
    </root>

</configuration>