package com.csci.cohl.service;

import com.csci.cohl.exception.ResultBody;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
@ActiveProfiles("bsj")
class AuthServiceTest {
    @Resource
    private AuthService authService;

    @Test
    void listSite() {
        ResultBody<List<String>> listResultBody = authService.listSite();
    }
}
