package com.csci.susdev.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.csci.common.util.CustomGsonBuilder;
import com.csci.susdev.BaseTest;
import com.csci.susdev.handler.AmbientHandler;
import com.csci.susdev.model.AmbientHead;
import com.csci.susdev.model.Organization;
import com.csci.susdev.service.AmbientDetailService;
import com.csci.susdev.service.AmbientService;
import com.csci.susdev.service.OrganizationService;
import com.csci.susdev.service.SocialPerfTwoDetailService;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.vo.AmbientDetailVO;
import com.csci.susdev.vo.SocialPerfTwoDetailVO;
import com.google.gson.Gson;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.Objects;

class PureExportControllerTest extends BaseTest {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(PureExportControllerTest.class);

    private final Gson gson = CustomGsonBuilder.createGson();

    @Resource
    private SocialPerfTwoDetailService socialPerfTwoDetailService;

    @Test
    void transferData() {
        List<SocialPerfTwoDetailVO> lstDetailVO = socialPerfTwoDetailService.listSocialPerfTwoDetail("0e3391af-800c-40d6-9cb8-50527293f16d");
        Map<String, List<SocialPerfTwoDetailVO>> map = PureExportController.transferData(lstDetailVO);
        logger.info("map: {}", gson.toJson(map));
    }


    @Test
    void exportHKAmbient() {

        // 查出所有香港公司
        OrganizationService organizationService = SpringContextUtil.getBean(OrganizationService.class);
        Organization organization = organizationService.getOrganizationByName("中建香港");
        List<Organization> lstOrgs = organizationService.listOrganizationTreeByParentId(organization.getId());

        AmbientService ambientService = SpringContextUtil.getBean(AmbientService.class);
        AmbientDetailService ambientDetailService = SpringContextUtil.getBean(AmbientDetailService.class);

        for (Organization org : lstOrgs) {

            System.out.println(org.getName());

            // 查询出环境绩效
            AmbientHead ambientHead = null;
            try {
                ambientHead = ambientService.findAmbientHead(org.getId(), 2022, 1);
            } catch (Exception e) {
                logger.error("findAmbientHead error: {}", e.getMessage(), e);
            }

            if (Objects.isNull(ambientHead)) {
                continue;
            }

            File file = new File("C:\\Users\\<USER>\\Desktop\\2022-export-esg-ambient\\" + org.getName() + ".xlsx");
            try (
                    InputStream is = this.getClass().getClassLoader().getResourceAsStream("template/ambient_tplt.xlsx");
                    ExcelWriter excelWriter = EasyExcel.write(file).registerWriteHandler(new AmbientHandler(ambientHead.getId())).withTemplate(is).build()) {
                // List<AmbientPerfDetailVO> lstDetail = ambientPerfDetailService.listAmbientPerfDetail(headId);
                List<AmbientDetailVO> lstAmbient = ambientDetailService.listAmbientDetailByHeadId(ambientHead.getId());
                FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                WriteSheet writeSheet = EasyExcel.writerSheet().build();
                excelWriter.fill(lstAmbient, fillConfig, writeSheet);
            } catch (Exception e) {
                logger.error("exportHKAmbient error: {}", e.getMessage(), e);
            }

        }

    }
}