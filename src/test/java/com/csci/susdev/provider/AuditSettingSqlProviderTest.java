package com.csci.susdev.provider;

import com.csci.susdev.BaseTest;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class AuditSettingSqlProviderTest extends BaseTest {

    /** 日志记录对象 */
    private static final Logger logger = LoggerFactory.getLogger(AuditSettingSqlProviderTest.class);

    @Test
    void listAuditSettingDetailSql() {
        Map<String, Object> mapParam = new HashMap<>();
        mapParam.put("userId", "123");
        logger.info("{}", new AuditSettingSqlProvider().listAuditSettingDetailSql(mapParam));
    }
}