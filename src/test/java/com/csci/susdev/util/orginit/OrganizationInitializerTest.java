package com.csci.susdev.util.orginit;

import com.csci.susdev.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

@ActiveProfiles("prod")
class OrganizationInitializerTest extends BaseTest {

    @Resource
    private OrganizationInitializer organizationInitializer;

    @Test
    void testInit() {
        organizationInitializer.init();
    }

    @Test
    void initByOrgName() {
        // organizationInitializer.initByOrgName("", "中建海龙");
        organizationInitializer.initByOrgName("江苏公司", "江苏无锡市锡东新城商务区水岸佳苑E区安置房新建工程一期一标段施工总承包");
        organizationInitializer.initByOrgName("江苏公司", "南京市江宁区高新园公共租赁住房（人才公寓）工程");
        organizationInitializer.initByOrgName("江苏公司", "江苏无锡瑞景道规划中学新建工程EPC工程总承包");
        organizationInitializer.initByOrgName("江苏公司", "江苏泰州海陵区[2021]4-1号地块安置房政府定购项目");
        organizationInitializer.initByOrgName("江苏公司", "江苏宿迁市苏州宿迁工业园区拓园发展融投资带动承包项目");
        organizationInitializer.initByOrgName("江苏公司", "先锋岛生态组团（基础设施）一期工程");
        organizationInitializer.initByOrgName("江苏公司", "盐城市亭湖区长坝三期安置房政府定购项目");
        organizationInitializer.initByOrgName("江苏公司", "盐城市亭湖区20200801号地块安置房政府定购项目");
        organizationInitializer.initByOrgName("江苏公司", "南京市江宁区科创园南区活力智岛二期项目");
    }

    @Test
    void initByFileName() {
        organizationInitializer.initByFileName("orgs/OrgsTemplate.xlsx");
    }
}