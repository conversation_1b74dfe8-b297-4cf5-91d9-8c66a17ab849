package com.csci.susdev.service;

import com.csci.susdev.BaseTest;
import com.csci.susdev.model.Role;
import com.csci.susdev.model.User;
import com.csci.susdev.model.UserRole;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

// @ActiveProfiles("prod")
class UserRoleServiceTest extends BaseTest {

    @Resource
    private UserRoleService userRoleService;

    @Resource
    private UserService userService;

    @Resource
    private RoleService roleService;

    @Test
    void saveUserRole() {
        User user = userService.getUserByUsername("tao_li");
        Role role = roleService.getRoleByCode("SuperAdmin");

        UserRole userRole = new UserRole();
        // userRole.setId();
        userRole.setUserId(user.getId());
        userRole.setRoleId(role.getId());
        // userRole.setCreationTime();
        // userRole.setCreateUsername();
        // userRole.setCreateUserId();
        // userRole.setLastUpdateTime();
        // userRole.setLastUpdateUsername();
        // userRole.setLastUpdateUserId();
        userRole.setIsDeleted(Boolean.FALSE);

        userRoleService.saveUserRole(userRole);
    }

    @Test
    void hasRole() {
        userRoleService.hasRole("tao_li", "OrgTree");
    }
}