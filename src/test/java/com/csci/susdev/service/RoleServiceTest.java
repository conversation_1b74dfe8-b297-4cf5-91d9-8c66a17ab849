package com.csci.susdev.service;

import com.csci.susdev.BaseTest;
import com.csci.susdev.vo.RoleVO;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

@ActiveProfiles("dev")
class RoleServiceTest extends BaseTest {

    @Resource
    RoleService roleService;

    @Test
    void listRolesByUsername() {
        roleService.listRoleByUsername("tao_li");
    }

    @Test
    void saveRole() {
        RoleVO roleVO = new RoleVO();
        // roleVO.setId();
        roleVO.setCode("SuperAdmin");
        roleVO.setName("超级管理员");
        roleVO.setDescription("超级管理员");
        // roleVO.setLastUpdateVersion();
        // roleVO.setPermissions();
        // roleVO.setPermissionIds();
        roleService.saveRole(roleVO);
    }
}