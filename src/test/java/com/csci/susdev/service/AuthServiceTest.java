package com.csci.susdev.service;

import com.csci.susdev.model.Operation;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import java.util.List;

@SpringBootTest
@ActiveProfiles("bsj")
class AuthServiceTest {
    @Resource
    private IAuthService authService;
    @Test
    void getOperationsByRoles() {
        List<Operation> operations = authService.getOperationsByRoles(List.of("40a0ca0a-3d91-4b9c-8553-aaadd18c4705"));
        System.out.println("operations = " + operations);
    }
}
