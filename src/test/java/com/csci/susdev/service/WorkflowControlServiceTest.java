package com.csci.susdev.service;

import com.csci.susdev.BaseTest;
import com.csci.susdev.model.Form;
import com.csci.susdev.model.FormExample;
import com.csci.susdev.model.User;
import com.csci.susdev.qo.WorkflowControlQO;
import com.csci.susdev.util.SpringContextUtil;
import com.csci.susdev.vo.WorkflowControlVO;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;

@ActiveProfiles("prod")
class WorkflowControlServiceTest extends BaseTest {

    @Resource
    private WorkflowControlService workflowControlService;

    @Resource
    FormService formService;

    @Test
    void getWorkflowControlByBusinessId() {
        WorkflowControlVO workflowControlVO = workflowControlService.getWorkflowControlByBusinessId("ff20518e-38e7-4fdb-8f40-4c362a39071c");
        assertNotNull(workflowControlVO);
    }

    @Test
    void listWorkflowControlByPage() {

        List<Form> lstForm = formService.selectByExample(new FormExample());
        Form form = lstForm.get(0);

        WorkflowControlQO workflowControlQO = new WorkflowControlQO();
        workflowControlQO.setYear(2022);
        workflowControlQO.setFormId(form.getId());
        workflowControlQO.setSubmitStatus(0);
        // workflowControlQO.setOrganizationId("11111111111111111111111111111111");
        workflowControlService.listWorkflowControlByPage(workflowControlQO);
    }

    @Test
    void selectUnSubmitOrganizationOfAmbient() {
        FormService formService = SpringContextUtil.getBean(FormService.class);
        Form form = formService.findFormByCode("ambient");
        UserService userService = SpringContextUtil.getBean(UserService.class);
        User user = userService.getUserByUsername("tao_li");
        WorkflowControlQO workflowControlQO = new WorkflowControlQO();
        workflowControlQO.setYear(2022);
        workflowControlQO.setFormId(form.getId());
        workflowControlQO.setUserId(user.getId());
        workflowControlService.selectUnSubmitWorkflowControl(workflowControlQO);
    }
}